import { CapacitorHttp } from "@capacitor/core";

export const logout = async (accessToken: string): Promise<any> => {
    console.log("Logging out...");

    const url = `https://www.wellnessmetrix.com/april/Dictionary.nsf/april/Dictionary.nsf?Logout=null`;

    try {
        const response = await CapacitorHttp.get({
            url,
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        console.log("Logout response:", response);

        if (response.status !== 200) {
            throw new Error(`Failed to logout. Status: ${response.status}`);
        }

        if (response.data) {
            console.log("Logout Data:", response.data);
            return response.data;
        } else {
            throw new Error("No data returned from the server.");
        }
    } catch (error: any) {
        console.log("LOGOUT ERROR", error);
        throw new Error(`Error during logout: ${error.message || error}`);
    }
};
