import React, { useState } from "react";
import { useHistory } from "react-router-dom";
import {
    App<PERSON><PERSON>,
    Too<PERSON>bar,
    IconButton,
    Typography,
    Box,
    Button,
} from "@mui/material";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import dayjs from "dayjs";
import TableComponent from "./TableComponent";
import { LocalizationProvider } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
const DynamicTables = () => {
    const history = useHistory();
    console.log("COMPONENT:DYNAMIC TABLES")
    // Sample data for tables
    const sampleData = [
        {
            id: 1,
            duration: "2 Years",
            stability_index: 95.6,
            signed_by: "<PERSON>",
            date_signed: "2024-12-15",
            comments: "Approved after review",
        },
        {
            id: 2,
            duration: "1 Year",
            stability_index: 88.4,
            signed_by: "<PERSON>",
            date_signed: "2024-12-14",
            comments: "Requires minor edits",
        },
        {
            id: 3,
            duration: "6 Months",
            stability_index: 78.3,
            signed_by: "<PERSON>",
            date_signed: "2024-12-10",
            comments: "Pending signature",
        },
        {
            id: 4,
            duration: "3 Months",
            stability_index: 85.2,
            signed_by: "Anna Brown",
            date_signed: "2024-12-15",
            comments: "Reviewed and accepted",
        },
    ];

    const [selectedDate, setSelectedDate] = useState(null);
    const [filteredData, setFilteredData] = useState(sampleData);

    // Handle date change
    const handleDateChange = (date: any) => {
        setSelectedDate(date);
        if (date) {
            const formattedDate = dayjs(date).format("YYYY-MM-DD");
            setFilteredData(sampleData.filter((row) => row.date_signed === formattedDate));
        } else {
            setFilteredData(sampleData);
        }
    };

    // Group data by date for multiple tables
    const groupedData = filteredData.reduce((acc: any, item: any) => {
        if (!acc[item.date_signed]) {
            acc[item.date_signed] = [];
        }
        acc[item.date_signed].push(item);
        return acc;
    }, {});

    // Print tables
    const handlePrint = () => {
        window.print();
    };

    return (
        <LocalizationProvider dateAdapter={AdapterDayjs}>
            <div>
                <Box>
                    {/* App Bar
                    <AppBar position="static" color="primary">
                        <Toolbar>
                            <IconButton edge="start" color="inherit" onClick={() => history.push("/")}>
                                <ArrowBackIcon />
                            </IconButton>
                            <Typography variant="h6">Activity Tables</Typography>
                        </Toolbar>
                    </AppBar> */}

                    {/* Content */}
                    <Box padding={2}>
                        {/* Date Selector */}
                        <Box display="flex" justifyContent="space-between" alignItems="center" marginBottom={3}>
                            <DatePicker
                                label="Select Date"
                                value={selectedDate}
                                onChange={handleDateChange}
                                //@ts-ignore
                                renderInput={(params: any) => (
                                    <input
                                        {...params.inputProps}
                                        type="text"
                                        style={{
                                            padding: "8px",
                                            fontSize: "16px",
                                            border: "1px solid #ccc",
                                            borderRadius: "4px",
                                            width: "200px",
                                        }}
                                    />
                                )}
                            />
                            <Button variant="contained" color="primary" onClick={handlePrint}>
                                Print Tables
                            </Button>
                        </Box>

                        {/* Tables */}
                        {Object.keys(groupedData).length === 0 ? (
                            <Typography variant="h6" color="textSecondary" align="center">
                                No data available for the selected date.
                            </Typography>
                        ) : (
                            Object.keys(groupedData).map((date) => (
                                <TableComponent
                                    key={date}
                                    rows={groupedData[date]}
                                    title={`Date: ${dayjs(date).format("MMMM D, YYYY")}`}
                                />
                            ))
                        )}
                    </Box>
                </Box>
            </div>
        </LocalizationProvider>
    );
};

export default DynamicTables;
