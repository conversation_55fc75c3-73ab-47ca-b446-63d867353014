com.wellmetrix.wellmetrixprovider.app-play-services-base-18.0.1-0 C:\Users\<USER>\.gradle\caches\8.9\transforms\1b43ee28020c0b2d0ddc25e34972ceb2\transformed\play-services-base-18.0.1\res
com.wellmetrix.wellmetrixprovider.app-lifecycle-runtime-2.6.1-1 C:\Users\<USER>\.gradle\caches\8.9\transforms\1b6aeb841faf77c48f79b258a538c817\transformed\lifecycle-runtime-2.6.1\res
com.wellmetrix.wellmetrixprovider.app-fragment-1.6.2-2 C:\Users\<USER>\.gradle\caches\8.9\transforms\272367bf5319cf2bfbf609b5d9828f86\transformed\fragment-1.6.2\res
com.wellmetrix.wellmetrixprovider.app-play-services-basement-18.1.0-3 C:\Users\<USER>\.gradle\caches\8.9\transforms\28aaba99947946d399258e439182cfe1\transformed\play-services-basement-18.1.0\res
com.wellmetrix.wellmetrixprovider.app-webkit-1.9.0-4 C:\Users\<USER>\.gradle\caches\8.9\transforms\2a3c9d49aa24b6d1ffcb78c63ed48a5a\transformed\webkit-1.9.0\res
com.wellmetrix.wellmetrixprovider.app-emoji2-views-helper-1.2.0-5 C:\Users\<USER>\.gradle\caches\8.9\transforms\3d008fdf93d7f27e2f23f6afffc0aaad\transformed\emoji2-views-helper-1.2.0\res
com.wellmetrix.wellmetrixprovider.app-browser-1.4.0-6 C:\Users\<USER>\.gradle\caches\8.9\transforms\3f9c330ef4f6885447f839ef57ba1bbf\transformed\browser-1.4.0\res
com.wellmetrix.wellmetrixprovider.app-firebase-common-20.4.2-7 C:\Users\<USER>\.gradle\caches\8.9\transforms\4634e7a34cac4085f2f71ebed9045a1b\transformed\firebase-common-20.4.2\res
com.wellmetrix.wellmetrixprovider.app-appcompat-resources-1.6.1-8 C:\Users\<USER>\.gradle\caches\8.9\transforms\5135ef5465b6d259ae2d29fe7564d535\transformed\appcompat-resources-1.6.1\res
com.wellmetrix.wellmetrixprovider.app-annotation-experimental-1.3.0-9 C:\Users\<USER>\.gradle\caches\8.9\transforms\6688f90d010687505df84e1fa6a8fcd8\transformed\annotation-experimental-1.3.0\res
com.wellmetrix.wellmetrixprovider.app-core-runtime-2.2.0-10 C:\Users\<USER>\.gradle\caches\8.9\transforms\7e4b3f5ddbe65c52c015ea992d1cfaa5\transformed\core-runtime-2.2.0\res
com.wellmetrix.wellmetrixprovider.app-core-ktx-1.12.0-11 C:\Users\<USER>\.gradle\caches\8.9\transforms\873bc401e2c41cf1461c051231c31823\transformed\core-ktx-1.12.0\res
com.wellmetrix.wellmetrixprovider.app-lifecycle-viewmodel-2.6.1-12 C:\Users\<USER>\.gradle\caches\8.9\transforms\92e617c3d680f48a076a676c2bedcd7a\transformed\lifecycle-viewmodel-2.6.1\res
com.wellmetrix.wellmetrixprovider.app-core-1.12.0-13 C:\Users\<USER>\.gradle\caches\8.9\transforms\99505e3f6474bdf8a578561614447e5e\transformed\core-1.12.0\res
com.wellmetrix.wellmetrixprovider.app-coordinatorlayout-1.2.0-14 C:\Users\<USER>\.gradle\caches\8.9\transforms\9f5d58573c9dff7bc7a91d6a295decf5\transformed\coordinatorlayout-1.2.0\res
com.wellmetrix.wellmetrixprovider.app-core-splashscreen-1.0.1-15 C:\Users\<USER>\.gradle\caches\8.9\transforms\b040dbb15142231338a9469429dc3dac\transformed\core-splashscreen-1.0.1\res
com.wellmetrix.wellmetrixprovider.app-lifecycle-livedata-2.6.1-16 C:\Users\<USER>\.gradle\caches\8.9\transforms\b61d6bbe38732e28e0b55729faeec186\transformed\lifecycle-livedata-2.6.1\res
com.wellmetrix.wellmetrixprovider.app-appcompat-1.6.1-17 C:\Users\<USER>\.gradle\caches\8.9\transforms\cfa929e238e2c59333af3756454deca9\transformed\appcompat-1.6.1\res
com.wellmetrix.wellmetrixprovider.app-lifecycle-process-2.6.1-18 C:\Users\<USER>\.gradle\caches\8.9\transforms\d4b574b42109befa3412f700e5ce3a6b\transformed\lifecycle-process-2.6.1\res
com.wellmetrix.wellmetrixprovider.app-startup-runtime-1.1.1-19 C:\Users\<USER>\.gradle\caches\8.9\transforms\d5f67cf8ab6c20577f25f9c7a8daf4fc\transformed\startup-runtime-1.1.1\res
com.wellmetrix.wellmetrixprovider.app-lifecycle-livedata-core-2.6.1-20 C:\Users\<USER>\.gradle\caches\8.9\transforms\d9882c6c6300e216203661867e675fcb\transformed\lifecycle-livedata-core-2.6.1\res
com.wellmetrix.wellmetrixprovider.app-savedstate-1.2.1-21 C:\Users\<USER>\.gradle\caches\8.9\transforms\da31bbbd398a29428a45c9dca59071e0\transformed\savedstate-1.2.1\res
com.wellmetrix.wellmetrixprovider.app-profileinstaller-1.3.0-22 C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\res
com.wellmetrix.wellmetrixprovider.app-emoji2-1.2.0-23 C:\Users\<USER>\.gradle\caches\8.9\transforms\dc4e29fa4ee28d48c5b619f978911597\transformed\emoji2-1.2.0\res
com.wellmetrix.wellmetrixprovider.app-activity-1.8.0-24 C:\Users\<USER>\.gradle\caches\8.9\transforms\e610305554e9015432ba1e298d43f01a\transformed\activity-1.8.0\res
com.wellmetrix.wellmetrixprovider.app-lifecycle-viewmodel-savedstate-2.6.1-25 C:\Users\<USER>\.gradle\caches\8.9\transforms\fbff70d4b295d89bd9cafbcb89df0972\transformed\lifecycle-viewmodel-savedstate-2.6.1\res
com.wellmetrix.wellmetrixprovider.app-pngs-26 C:\work\wellmetrixproviderionic\android\app\build\generated\res\pngs\release
com.wellmetrix.wellmetrixprovider.app-resValues-27 C:\work\wellmetrixproviderionic\android\app\build\generated\res\resValues\release
com.wellmetrix.wellmetrixprovider.app-packageReleaseResources-28 C:\work\wellmetrixproviderionic\android\app\build\intermediates\incremental\release\packageReleaseResources\merged.dir
com.wellmetrix.wellmetrixprovider.app-packageReleaseResources-29 C:\work\wellmetrixproviderionic\android\app\build\intermediates\incremental\release\packageReleaseResources\stripped.dir
com.wellmetrix.wellmetrixprovider.app-release-30 C:\work\wellmetrixproviderionic\android\app\build\intermediates\merged_res\release\mergeReleaseResources
com.wellmetrix.wellmetrixprovider.app-main-31 C:\work\wellmetrixproviderionic\android\app\src\main\res
com.wellmetrix.wellmetrixprovider.app-release-32 C:\work\wellmetrixproviderionic\android\app\src\release\res
com.wellmetrix.wellmetrixprovider.app-release-33 C:\work\wellmetrixproviderionic\android\capacitor-cordova-android-plugins\build\intermediates\packaged_res\release\packageReleaseResources
com.wellmetrix.wellmetrixprovider.app-release-34 C:\work\wellmetrixproviderionic\node_modules\@capacitor-community\bluetooth-le\android\build\intermediates\packaged_res\release\packageReleaseResources
com.wellmetrix.wellmetrixprovider.app-release-35 C:\work\wellmetrixproviderionic\node_modules\@capacitor-community\http\android\build\intermediates\packaged_res\release\packageReleaseResources
com.wellmetrix.wellmetrixprovider.app-release-36 C:\work\wellmetrixproviderionic\node_modules\@capacitor-firebase\authentication\android\build\intermediates\packaged_res\release\packageReleaseResources
com.wellmetrix.wellmetrixprovider.app-release-37 C:\work\wellmetrixproviderionic\node_modules\@capacitor\android\capacitor\build\intermediates\packaged_res\release\packageReleaseResources
com.wellmetrix.wellmetrixprovider.app-release-38 C:\work\wellmetrixproviderionic\node_modules\@capacitor\app\android\build\intermediates\packaged_res\release\packageReleaseResources
com.wellmetrix.wellmetrixprovider.app-release-39 C:\work\wellmetrixproviderionic\node_modules\@capacitor\haptics\android\build\intermediates\packaged_res\release\packageReleaseResources
com.wellmetrix.wellmetrixprovider.app-release-40 C:\work\wellmetrixproviderionic\node_modules\@capacitor\keyboard\android\build\intermediates\packaged_res\release\packageReleaseResources
com.wellmetrix.wellmetrixprovider.app-release-41 C:\work\wellmetrixproviderionic\node_modules\@capacitor\status-bar\android\build\intermediates\packaged_res\release\packageReleaseResources
