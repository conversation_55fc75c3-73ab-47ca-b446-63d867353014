import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Typography } from '@material-ui/core';
import React, { useEffect } from 'react';
// import { BackHandler,View } from "react-native";
import { ArrowBack } from '@material-ui/icons';
import { useHistory } from "react-router-dom";
const BackHandler = (props: any) => {
    const history = useHistory();

    const backAction = () => {
        history.goBack();
        return true;
    };
    return <div style={{
        display: "flex",
        flexDirection: "row",
        margin: "1em",
        justifyContent: "flex-start",
        alignItems: "center",
    }}>
        <Chip avatar={<ArrowBack fontSize={'large'} />} label="Go back" onClick={() => {
            if (props.goBack) {
                props.goBack()
            } else {
                history.goBack()
            }
        }} />
    </div>
}
export default BackHandler