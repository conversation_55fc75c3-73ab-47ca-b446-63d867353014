import { CapacitorHttp } from "@capacitor/core";

export const getProfile = async (accessToken:string): Promise<string> => {
   console.log("Getting Profile");

    try {
        const response:any = await CapacitorHttp.get({
            url: 'https://apps.exermetrix.com/april/Dictionary.nsf/API.xsp/authenticate',
            headers: {
                Authorization: `Bearer ${accessToken}`,
                // "Content-Type": "application/x-www-form-urlencoded"
            }
        });

        console.log("Token exchange response:", response);
        if(response.error) throw new Error(response.data.ErrorMessage)
        if (response.data) {
            console.log("USER ",response.data)
            return response.data
        } else {
            throw new Error("Token Exchange Failed");
        }
    } catch (error:any) {
        console.log("GET PROFILE ERROR ",error);
        throw new Error(`${error}`);
    }
}