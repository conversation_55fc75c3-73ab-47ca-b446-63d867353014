import { CapacitorHttp } from "@capacitor/core";

export const getPatientsByProvider = async (accessToken: string): Promise<any> => {
    console.log("Fetching patients by provider...");

    try {
        const response: any = await CapacitorHttp.get({
            url: 'https://apps.exermetrix.com/april/Dictionary.nsf/API.xsp/patients/byProvider',
            headers: {
                Authorization: `Bearer ${accessToken}`,
                // Cookie: 'SessionID=6939553DA378F290F51683C266E028668C18DC23',
            },
        });

        console.log("Patients by provider response:", response);

        if (response.error) throw new Error(response.data?.ErrorMessage || "Unknown error");

        return response.data.item;
    } catch (error: any) {
        console.log("GET PATIENTS ERROR", error);
        throw new Error(`${error}`);
    }
};

export interface Patient {
    id: number;
    firstName: string;
    lastName: string;
    middleName?: string;
    name: string;
    email: string;
    dob: string;
    heightFeet: number;
    heightInches: number;
    weight: number;
    mrn: string;
    gender: string;
    provider: string;
    status: 'active' | 'inactive';
    remoteAccess: boolean;
    lastAppointment?: string;
    docId? : string
    patientId? : string
  }
  

export const mapApiPatientToLocal = (item: any, index: number): Patient => ({
    id: index + 1, // Or use a unique value like `parseInt(item.patientId, 36)` if you want
    firstName: item.firstName.trim(),
    lastName: item.lastName.trim(),
    middleName: item.middleName?.trim() || '',
    name: item.fullName?.trim() || `${item.firstName} ${item.lastName}`,
    email: item.emailAddress || '',
    dob: item.dateOfBirth,
    heightFeet: item.heightFeetComponent,
    heightInches: item.heightInchComponent,
    weight: item.WeightValue,
    mrn: item.mrn || '',
    gender: item.gender || '',
    provider: item.PracticeNoteId || 'Unknown',
    status: item.userStatus === 'Inactive' ? 'inactive' : 'active',
    remoteAccess: item.RemoteAccess?.toLowerCase() === 'yes',
    lastAppointment: item.dateOfLastVisit || 'N/A',
    docId : item.docId || "",
    patientId : item.patientId
  });
  
