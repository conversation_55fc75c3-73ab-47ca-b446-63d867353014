//@ts-nocheck
import { CapacitorHttp } from '@capacitor/core';

const TOKEN_URL = "https://oidc.wellnessmetrix.com/auth/protocol/OIDC/token";
const LOGIN_URL = "https://oidc.wellnessmetrix.com/auth/protocol/oidc/auth";
const CLIENT_ID = "ExerMetrixOIDC";
// const CLIENT_SECRET = encodeURIComponent("&dEssVN5BMV@93*72*S0sNPZRJ4GR8@a@cfNHrZp"); // do not double encode let urlsearchparams handle it
const CLIENT_SECRET = "*dEssVN5BMV#93*72*S0sNPZRJ4GR8#4#cfNHrZp";
const SCOPE = "openid email Domino.user.all";
const REDIRECT_URL = "https://apps.exermetrix.com/auth/protocol/oidc";

class OAuthService {
    private accessToken: string | null;

    constructor() {
        this.accessToken = null;
    }

    async authenticateUser(username: string, password: string): Promise<string> {
        console.log("Starting authentication for user:", username);
        const codeVerifier = await this.generateCodeVerifier();
        const codeChallenge = await this.generateCodeChallenge(codeVerifier);
    
        const params = new URLSearchParams({
            client_id: CLIENT_ID,
            client_secret: CLIENT_SECRET,
            response_type: "code",
            redirect_uri: REDIRECT_URL,
            scope: SCOPE,
            username,
            password,
            code_challenge: codeChallenge,
            code_challenge_method: "S256"
        }).toString();
    
        const url = `${LOGIN_URL}?${params}`;
        console.log("🔹 Authentication request URL:", url);
    
        try {
            const response = await CapacitorHttp.get({
                url: url,
                headers: {
                    "User-Agent": "Mozilla/5.0 (Linux; Android 10) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/88.0.4324.181 Mobile Safari/537.36",
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                    "Referer": "https://oidc.wellnessmetrix.com/",
                    "Origin": "https://oidc.wellnessmetrix.com/",
                    "Content-Type": "application/x-www-form-urlencoded",
                },
            });
    
            console.log("🚀 Authentication Response:", response);
    
            // if (response.status !== 302) {
            //     throw new Error(`❌ Unexpected status code: ${response.status}`);
            // }
            // if(response.error) throw new Error(response.data) // uncomment in prod
            const redirectUrl = response.headers["location"] || response.headers["Location"] || response.url;
            if (!redirectUrl) {
                throw new Error("❌ No redirect URL found. Login failed.");
            }
    
            console.log("✅ Extracted redirect URL:", redirectUrl);
            const authorizationCode = this.extractQueryParam(redirectUrl, "code");
            if (!authorizationCode) {
                throw new Error("❌ Authorization code not found in redirect URL.");
            }
    
            console.log("✅ Extracted authorization code:", authorizationCode);
            return this.exchangeCodeForToken(authorizationCode, codeVerifier);
        } catch (error) {
            console.error("❌ Login Failed:", error);
            throw new Error(`${error.ErrorMessage}`);
        }
    }
    

    async exchangeCodeForToken(authorizationCode: string, codeVerifier: string): Promise<string> {
        console.log("Exchanging authorization code for access token...");
        // const credentials = btoa(`${CLIENT_ID}:${CLIENT_SECRET}`);
        const credentials = btoa(unescape(encodeURIComponent(`${CLIENT_ID}:${CLIENT_SECRET}`)));

        const data = new URLSearchParams({
            grant_type: "authorization_code",
            code: authorizationCode,
            redirect_uri: REDIRECT_URL,
            code_verifier: codeVerifier
        }).toString();
        const curlCommand = `curl -X POST "${TOKEN_URL}" \
        -H "Authorization: Basic ${credentials}" \
        -H "Content-Type: application/x-www-form-urlencoded" \
        --data "${data}"`;
    console.log("🔥 Equivalent curl command for token exchange:", curlCommand);

        try {
            const response = await CapacitorHttp.post({
                url: TOKEN_URL,
                headers: {
                    Authorization: `Basic ${credentials}`,
                    "Content-Type": "application/x-www-form-urlencoded"
                },
                data: data
            });

            console.log("Token exchange response:", response);
            if (response.data.access_token) {
                this.accessToken = response.data.access_token;
                console.log("Login Successful! Access Token:", this.accessToken);
                return this.accessToken;
            } else {
                throw new Error("Token Exchange Failed");
            }
        } catch (error) {
            console.error("Token Request Failed:", error);
            throw new Error(`Token Request Failed: ${error.message}`);
        }
    }

    async generateCodeVerifier(): Promise<string> {
        const array = new Uint8Array(32);
        window.crypto.getRandomValues(array);
        const verifier = this.base64UrlEncode(array);
        console.log("Generated PKCE Code Verifier:", verifier);
        return verifier;
    }

    async generateCodeChallenge(codeVerifier: string): Promise<string> {
        const encoder = new TextEncoder();
        const data = encoder.encode(codeVerifier);
        const hashBuffer = await window.crypto.subtle.digest("SHA-256", data);
        const challenge = this.base64UrlEncode(new Uint8Array(hashBuffer));
        console.log("Generated PKCE Code Challenge:", challenge);
        return challenge;
    }

    base64UrlEncode(array: Uint8Array): string {
        return btoa(String.fromCharCode.apply(null, array))
            .replace(/\+/g, "-")
            .replace(/\//g, "_")
            .replace(/=+$/, "");
    }

    extractQueryParam(url: string, param: string): string | null {
        const query = new URL(url).searchParams;
        const value = query.get(param);
        console.log(`Extracted query param '${param}':`, value);
        return value;
    }
}

export default OAuthService;
