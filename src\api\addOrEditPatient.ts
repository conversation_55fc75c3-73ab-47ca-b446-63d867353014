import { CapacitorHttp } from "@capacitor/core";

export const addOrEditPatient = async (accessToken: string, patientData: any,flag:'add' | 'edit'): Promise<any> => {
    console.log("Adding or editing patient from curl...");

    // if(patientData.RemoteAccess === "No"){
    //     delete patientData.mrn
    //     delete patientData.emailAddress
    //     delete patientData.docId
    // }
    try {
        const response: any = await CapacitorHttp.post({
            url: 'https://apps.exermetrix.com/april/Dictionary.nsf/API.xsp/patients/addEditPatient',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${accessToken}`,
            },
            data: JSON.stringify(patientData),
        });

        console.log("Add/Edit patient (from curl) response:", response);

        if (response.error) throw new Error(response.data?.ErrorMessage || "Unknown error");

        return response.data;
    } catch (error: any) {
        console.log("ADD/EDIT PATIENT (FROM CURL) ERROR", error);
        throw new Error(`${error}`);
    }
};
