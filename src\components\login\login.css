    /* Utilities */
    
    .hidden {
        display: none;
    }
    #loginWrapper {
        flex:1;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 100%;
    }
    .login {
        flex:1;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: column;
        padding: 2em;
    }
    .welcomeDoor {
        flex:1;
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 1em;
    }
    
    .welcomeDoor>svg {
        height: 100%;
        width: 100%;
        cursor: pointer;
    }
    
    .phoneOrEmailInput {
        flex:1;
        display: flex;
        justify-content: center;
        align-items: center;
        max-width: 95%;
        min-width: 40%;
        width: auto;
        border-radius: 40px;
        /* background-color:rgba(247, 176, 8, 0.34); */
        padding: 1em;
        transition: all 0.2s ease-in-out;
    }
    
    .inputWrap {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        /* padding: 10px; */
        border-radius: 5px;
        border: 1px solid var(--primary-main);

    }
    
    .materialInput>input {
        font-size: 20px;
        transition: all 0.2s ease-in-out;
        color: #264653bd;
        padding-left: 0.5em;

    }
    
    .materialInput>input:focus {
        font-size: 25px;
        
    }
    
    .materialInput>input::placeholder {
        font-size: 15px;
    }
    
     .phoneCode {
        height: 1.5em;
        width: 1.5em;
        padding: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: var(--primary-main);
        border-radius: 5px;
        font-weight: bold;
        color: #f9f9f9;
        font-size: 16px;
        margin: 1em;
    }
    
    .socialLogin {
        padding: 0.2em;
        justify-content: center;
        align-items: flex-end;
        width: 100%;
        display: flex;
    }
    
    .socialLogin>svg {
        height: 50px;
        width: 50px;
        margin: 20px;
        transition: all 0.2s ease-in;
        cursor: pointer;
    }
    
    .socialLogin>svg:hover {
        height: 30px;
        width: 30px;
        margin: 20px;
    }
    
    .submitButton {
        height: 1.5em;
        width: 1.5em;
        padding: 10px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #F7B008;
        border-radius: 50%;
        margin: 10px;
        transition: all 0.3s ease-in-out;
        cursor: pointer;
    }
    
    .submitButton>svg {
        height: 1em;
        width: 1em;
        fill: #ffffff;
    }
    /* Animaton */
    
    .enter,
    .appear {
        transform: scale(0);
    }
    
    .enter.enter-active,
    .appear.appear-active {
        transform: scale(1);
        transition: transform 1000ms;
    }
    
    .exit {
        transform: scale(1);
    }
    
    .exit.exit-active {
        transform: scale(0);
        transition: transform 1000ms;
    }
    
    .loginTransition-enter {
        opacity: 0;
        transform: scale(0.9);
    }
    
    .loginTransition-enter-active {
        opacity: 1;
        transform: translateX(0);
        transition: opacity 300ms, transform 300ms;
    }
    
    .loginTransition-exit {
        opacity: 1;
    }
    
    .loginTransition-exit-active {
        opacity: 0;
        transform: scale(0.9);
        transition: opacity 300ms, transform 300ms;
    }
    
    .otp {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-direction: row;
        max-width: 30%;
    }
    
    .otp>.otpInput {
        margin-left: 0.2em;
        margin-right: 0.2em;
        transition: all 0.1s ease-in;
        color:#2a9d8f;
    }
    
    .otp>.otpInput>input {
        text-align: center;
        font-size: 32px;
        transition: all 0.1s ease-in;
    }
    
    .otp>.otpInput>input:focus {
        font-size: 38px;
        transition: all 0.1s ease-in;
    }
    
    .otp>.otpInput>input::placeholder{
        color:rgba(96, 125, 139, 0.46);
    }
    
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
        -webkit-appearance: none;
        margin: 0;
    }
    
    input[type=number] {
        -moz-appearance: textfield;
    }
    
    .message {
        flex: 0.1;
        color: #cdcdcd;
        transition: all 0.1s ease-in;
        font-weight: 600;
        justify-content: center;
        align-items: center;
        margin: 1em;
    }
    
    .email {
        color: #4CAF50;
    }
    
    .mobile {
        color: #4CAF50;
    }
    
    .error {
        color: #F44336;
    }
    
    .passwordInput {
        display: flex;
        flex: 1;
        justify-content: center;
        align-items: center;
        max-width: 50%;
        margin: 1em;
    }
    
    .submit {
        flex: 1;
        padding: 0.2em;
    }
    
    .subHead {
        font-size: 20px;
        color: #264653bd;
        padding: 0.2em;
    }
    
    @media only screen and (max-width: 600px) {
        .phoneOrEmailInput {
            min-width: 100%;
        }
        .otp,
        .passwordInput {
            max-width: 100%;
        }
    }