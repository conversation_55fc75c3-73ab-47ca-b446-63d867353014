import { CapacitorHttp } from "@capacitor/core";

export const resetPassword = async (userName: string): Promise<any> => {
    console.log("Resetting Password...");

    const url = `https://www.wellnessmetrix.com/april/AppInfo.nsf/forgotPassword.xsp/resetPW`;

    try {
        const response = await CapacitorHttp.post({
            url,
            headers: {
                'Content-Type': 'application/json'
            },
            data: {
                userName
            }
        });

        console.log("Reset Password response:", response);

        if (response.status !== 200) {
            throw new Error(`Failed to reset password. Status: ${response.status}`);
        }

        if (response.data) {
            console.log("Reset Password Data:", response.data);
            return response.data;
        } else {
            throw new Error("No data returned from the server.");
        }
    } catch (error: any) {
        console.log("RESET PASSWORD ERROR", error);
        throw new Error(`Error resetting password: ${error.message || error}`);
    }
};

