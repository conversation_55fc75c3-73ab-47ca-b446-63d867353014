{"logs": [{"outputFile": "com.wellmetrix.wellmetrixprovider.app-mergeDebugResources-28:/values-te/values-te.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\99505e3f6474bdf8a578561614447e5e\\transformed\\core-1.12.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,265,367,468,574,681,805", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "152,260,362,463,569,676,800,901"}, "to": {"startLines": "29,30,31,32,33,34,35,59", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2859,2961,3069,3171,3272,3378,3485,6319", "endColumns": "101,107,101,100,105,106,123,100", "endOffsets": "2956,3064,3166,3267,3373,3480,3604,6415"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\28aaba99947946d399258e439182cfe1\\transformed\\play-services-basement-18.1.0\\res\\values-te\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "44", "startColumns": "4", "startOffsets": "4585", "endColumns": "139", "endOffsets": "4720"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\3f9c330ef4f6885447f839ef57ba1bbf\\transformed\\browser-1.4.0\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,166,272,383", "endColumns": "110,105,110,106", "endOffsets": "161,267,378,485"}, "to": {"startLines": "54,55,56,57", "startColumns": "4,4,4,4", "startOffsets": "5801,5912,6018,6129", "endColumns": "110,105,110,106", "endOffsets": "5907,6013,6124,6231"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\cfa929e238e2c59333af3756454deca9\\transformed\\appcompat-1.6.1\\res\\values-te\\values-te.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,447,537,642,761,839,915,1006,1099,1194,1288,1388,1481,1576,1671,1762,1853,1942,2056,2160,2259,2374,2479,2594,2756,2859", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "217,329,442,532,637,756,834,910,1001,1094,1189,1283,1383,1476,1571,1666,1757,1848,1937,2051,2155,2254,2369,2474,2589,2751,2854,2937"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,222,334,447,537,642,761,839,915,1006,1099,1194,1288,1388,1481,1576,1671,1762,1853,1942,2056,2160,2259,2374,2479,2594,2756,6236", "endColumns": "116,111,112,89,104,118,77,75,90,92,94,93,99,92,94,94,90,90,88,113,103,98,114,104,114,161,102,82", "endOffsets": "217,329,442,532,637,756,834,910,1001,1094,1189,1283,1383,1476,1571,1666,1757,1848,1937,2051,2155,2254,2369,2474,2589,2751,2854,6314"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.9\\transforms\\1b43ee28020c0b2d0ddc25e34972ceb2\\transformed\\play-services-base-18.0.1\\res\\values-te\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,299,449,575,686,819,940,1041,1137,1282,1390,1539,1667,1814,1973,2033,2099", "endColumns": "105,149,125,110,132,120,100,95,144,107,148,127,146,158,59,65,77", "endOffsets": "298,448,574,685,818,939,1040,1136,1281,1389,1538,1666,1813,1972,2032,2098,2176"}, "to": {"startLines": "36,37,38,39,40,41,42,43,45,46,47,48,49,50,51,52,53", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3609,3719,3873,4003,4118,4255,4380,4485,4725,4874,4986,5139,5271,5422,5585,5649,5719", "endColumns": "109,153,129,114,136,124,104,99,148,111,152,131,150,162,63,69,81", "endOffsets": "3714,3868,3998,4113,4250,4375,4480,4580,4869,4981,5134,5266,5417,5580,5644,5714,5796"}}]}]}