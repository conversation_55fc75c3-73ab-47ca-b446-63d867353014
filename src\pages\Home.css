
.homeContainer {
    display: block;
    flex-direction: column;
    height: 100%;
    padding-top: 7em;
    /* overflow-y: scroll; */
}

.moodsContainer {
    display: flex;
    justify-content: flex-start;
    flex-direction: column;
    overflow: auto;
    white-space: nowrap;
}
.headBar{
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: row;
    padding-left: 1em;
    padding-right: 1em;
    /* margin-top: 1em; */
}
.moodsContainer > .moodsBody{
    flex:1;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    padding-right: 1em;
}
.moodsBody > .moodsCard {
    flex : 1 ;
    height: 5em;
    min-width : 5em;
    border-radius: 0.7em;

    display: flex;
    justify-content: center;
    align-items: center;
    margin: 1em;
    /* background-color: #607D8B; */
    background: #ffffff;
    flex-direction: column;
    gap: 0.5em;
    padding: 1em;
    color:#607D8B;
    /* box-shadow: 0 0px 20px rgba(100, 99, 97, 0.25), 0 0px 10px rgba(102, 101, 100, 0.22); */
    box-shadow: 0 10px 20px rgba(0,0,0,0.19), 0 6px 6px rgba(0,0,0,0.23);
}
.roomContainer{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 1em;
    margin-top: 1.618em;
    justify-content: center;
    align-items: center;
}
.roomCard{
    height: auto;
    border-radius: 0.7em;
    display: flex;
    /* background-color: #607D8B; */
    color:#607D8B;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap:0.5em;
    font-size: 24px;
    font-weight: bold;
    font-variant-caps: all-petite-caps;
    width: calc(50% - 3em);
    padding: 1em;
}

.roomCarousel{
    display: flex;
    flex-direction: column;
}

.carouselIndicator{
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.6em;
    padding: 1.618em;
}
.indicator{
    height: 10px;
    width: 10px;
    background-color: #607D8B;
    border-radius: 50%;
}
.indicatorActive{
    background-color: #F7B008;
    height: 15px;
    width: 15px;
}
.chipBox{
    display: flex;
    flex-direction: column;
    gap: 0.2em;
    overflow-x: auto;
    padding: 1em;
    padding-right: 2em;
}
.chips{
    display: flex;
    flex-direction: row;
    gap: 1em;
    /* flex-wrap: wrap; */
}

.activityBox{
    display: flex;
    flex-direction: column;
}
.cardGrid{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap:1.2em;
    margin-top: 2em;
}

@media (min-width: 600px) {
    .cardGrid {
        flex-direction: row;
        justify-content: flex-start;
        align-items: flex-start;
    }
}