import type { CapacitorConfig } from '@capacitor/cli';

const config: CapacitorConfig = {
  "appId": "com.wellmetrix.wellmetrixprovider",
  "appName": "wellmetrixprovider",
  // "bundledWebRuntime": false,
  // "npmClient": "npm",
  "webDir": "dist",
  "plugins": {
    "SplashScreen": {
      "launchShowDuration": 0
    }
  },
  // "server": {
  //   "url": "http://192.168.137.1:8100",
  //   "cleartext": true
  // }
}

export default config;
