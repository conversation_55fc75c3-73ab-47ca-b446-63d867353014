//@ts-nocheck
import { Ava<PERSON>, Button, Chip, IconButton, Switch, Typography } from '@material-ui/core';
import { CloseTwoTone, EditOutlined, LocationCityOutlined, LockOutlined, LockTwoTone, PinDrop, Settings, SettingsApplicationsOutlined, Share } from '@material-ui/icons';
import React from 'react';
import { useDispatch } from 'react-redux';
import { logout } from '../../redux/slices/userSlice';

import './Profile.css';

const Profile: React.FC = () => {
    const dispatch = useDispatch()
    return <>
        <div className={'profileContainer'}>
            <div className={'profileImage'}>
                <Avatar className={'shadow1'} style={{ height: "100px", width: "100px" }}>S</Avatar>
                <Chip avatar={<EditOutlined />} label="Edit Profile" />
            </div>
            <div className={'headlines'}>
                You have consumed 132 units of electricity today
            </div>
            <div className={'menuContent'}>
                <nav className={'menuList'}>
                    <div className={'menuItem'}>

                        <PinDrop fontSize={'large'} />
                        <div style={{ display: "flex", flexDirection: "row", gap: "0.5em", alignItems: "center" }}>
                            <Typography className={'menuItemText'}>Geofencing </Typography>
                            <Switch
                                onChange={(e) => true}
                                inputProps={{ 'aria-label': 'Turn On Appliance' }}
                                checked={true}
                                color={'primary'}
                                size={'medium'}
                            />
                        </div>

                    </div>
                    <div className={'menuItem'}>
                        <LockOutlined fontSize={'large'} color={'primary'} />
                        <div style={{ display: "flex", flexDirection: "row", gap: "0.5em", alignItems: "center" }}>
                            <Typography className={'menuItemText'}>Armour Mode</Typography>
                            <Switch
                                onChange={(e) => true}
                                inputProps={{ 'aria-label': 'Turn On Appliance' }}
                                checked={true}
                                color={'primary'}
                                size={'medium'}
                            />
                        </div>

                    </div>
                    <div className={'menuItem'}>
                        <Share fontSize={'large'} />
                        <Typography>
                            Share Your Home
                        </Typography>
                    </div>
                    <div className={'menuItem'}>
                        <div style={{ display: "flex", flexDirection: "row", gap: "0.5em", alignItems: "center" }}>
                            <Settings fontSize={'large'} />
                        </div>
                        <Typography>
                            Settings
                        </Typography> 
                    </div>
                    <div className={'menuItem'} style={{ color: "#D32F2F" }} onClick={() => dispatch(logout(null))}><CloseTwoTone style={{marginRight:"0.5em"}}/>Logout</div>
                </nav>

            </div>

        </div>
    </>
}

export default Profile;