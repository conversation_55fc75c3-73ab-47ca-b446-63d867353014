// src/components/PatientDashboard.tsx
import React, { useState } from 'react';
import PatientList from './PatientList';
import { Patient, TestData } from './types'

// import './styles.css' //style for project level adjustments

const initialPatients: Patient[] = [
    { id: 1, name: '<PERSON>', email: '<EMAIL>', dob: '1990-05-15', lastAppointment: '2023-10-26', remoteAccess: true },
    { id: 2, name: '<PERSON>', email: '<EMAIL>', dob: '1985-12-10', lastAppointment: '2023-10-20', remoteAccess: false },
    { id: 3, name: '<PERSON>', email: '<EMAIL>', dob: '1992-08-03', lastAppointment: '2023-10-24', remoteAccess: true }
];


const sampleData: TestData[] = [
    { id: 1, testType: "Core Bridges", time: "00:01", value: 30, notes: 'felt strong' },
    { id: 2, testType: "Core Bridges", time: "00:05", value: 22, notes: 'not strong here' },
    { id: 3, testType: "Core Bridges", time: "00:07", value: 30, notes: ' felt ok' },
    { id: 4, testType: "Core Squat", time: "00:02", value: 65, notes: 'good with weight' },
    { id: 5, testType: "Core Squat", time: "00:05", value: 55, notes: 'weight was hard' },
    { id: 6, testType: "Core Pushup", time: "00:01", value: 5, notes: 'did partials' },
    { id: 7, testType: "Cervical Flexion", time: "00:01", value: 20, notes: "limited" },
    { id: 8, testType: "Cervical Flexion", time: "00:06", value: 30, notes: "felt ok" },
    { id: 9, testType: "Cervical Rotation", time: "00:01", value: 55, notes: 'good here' }
    , { id: 10, testType: "Cervical Rotation", time: "00:05", value: 50, notes: 'still okay' },
    { id: 11, testType: 'Hip Abduction', time: "00:01", value: 33, notes: "had discomfort" },
    { id: 12, testType: "Hip Abduction", time: "00:05", value: 25, notes: "did half movement" },

    { id: 13, testType: 'Knee Flexion', time: "00:03", value: 21, notes: 'pain during this' },
    { id: 14, testType: 'Knee Flexion', time: "00:05", value: 44, notes: "felt better this time" }

    , { id: 15, testType: "Ankle Dorsiflexion", time: "00:02", value: 5, notes: 'very stiff' },
    { id: 16, testType: "Ankle Dorsiflexion", time: "00:06", value: 6, notes: "barely better" }

    , { id: 17, testType: "Knee Standing Flexion", time: '00:02', value: 75, notes: 'normal' },
    { id: 18, testType: "Ankle Plantarflexion", time: "00:01", value: 15, notes: "easy test" },
    { id: 19, testType: "Core Squat w/ Assistance", time: '00:02', value: 3, notes: 'needed a lot help' },
    { id: 20, testType: "Cervical Extension", time: "00:01", value: 77, notes: 'felt strong' },
    { id: 21, testType: 'Cervical Lateral Bending', time: '00:03', value: 12, notes: "couldnt move" },
    { id: 22, testType: 'Cervical Chin Tuck', time: '00:04', value: 13, notes: 'partial mvmt' },
    { id: 23, testType: 'Hip External Rotation', time: "00:03", value: 11, notes: 'pain with external' },
    { id: 24, testType: "Knee Extension", time: '00:02', value: 78, notes: "ok test" }
];


function PatientDashboard() {

    const [patients, setPatients] = useState<Patient[]>(initialPatients)


    return (
        <>

            <PatientList patients={patients} setPatients={setPatients} sampleData={sampleData} />
        </>

    )
}

export default PatientDashboard