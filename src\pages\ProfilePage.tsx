import React, { useState } from "react";
import {
    Avatar,
    Box,
    Button,
    Divider,
    IconButton,
    Text<PERSON>ield,
    Typography,
} from "@mui/material";
import { useSpring, animated } from "react-spring";
import CameraAltIcon from "@mui/icons-material/CameraAlt";
import SettingsIcon from "@mui/icons-material/Settings";
import LogoutIcon from "@mui/icons-material/Logout";
import BackHandler from "../components/backHandler";
import RandomQuote from "../components/RandomQuote";

const ProfileSettings: React.FC = () => {
    const [editing, setEditing] = useState(false);
    const [changingPassword, setChangingPassword] = useState(false);
    const [profilePic, setProfilePic] = useState<string | null>(null);
    const [username, setUsername] = useState("John Doe");
    const [email, setEmail] = useState("<EMAIL>");
    const [oldPassword, setOldPassword] = useState("");
    const [newPassword, setNewPassword] = useState("");
    const [confirmPassword, setConfirmPassword] = useState("");

    const springProps = useSpring({
        opacity: 1,
        transform: "translateY(0px)",
        from: { opacity: 0, transform: "translateY(20px)" },
        config: { tension: 220, friction: 20 },
    });

    const handleProfilePicChange = (event: React.ChangeEvent<HTMLInputElement>) => {
        if (event.target.files && event.target.files[0]) {
            const reader = new FileReader();
            reader.onload = (e) => {
                setProfilePic(e.target?.result as string);
            };
            reader.readAsDataURL(event.target.files[0]);
        }
    };

    const handleEditToggle = () => {
        setEditing(!editing);
    };

    const handleSave = () => {
        setEditing(false);
        // Add save logic here
    };

    const handleLogout = () => {
        // Logout logic
    };

    const handlePasswordChange = () => {
        if (newPassword !== confirmPassword) {
            alert("New password and confirm password do not match!");
            return;
        }
        // Add password update logic here
        alert("Password updated successfully!");
        setOldPassword("");
        setNewPassword("");
        setConfirmPassword("");
        setChangingPassword(false);
    };

    return (
        <animated.div style={springProps}>
            <Box
                sx={{
                    maxWidth: 600,
                    margin: "auto",
                    p: 3,
                    borderRadius: 2,
                    // boxShadow: 3,
                    bgcolor: "background.paper",
                }}
            >
                {/* Profile Section */}
                <Box display="flex" flexDirection="column" alignItems="center" mb={4}>
                    <Box position="relative">
                        <Avatar
                            src={profilePic || ""}
                            sx={{
                                width: 100,
                                height: 100,
                                fontSize: 40,
                                bgcolor: "primary.main",
                            }}
                        >
                            {!profilePic && username[0].toUpperCase()}
                        </Avatar>
                        <IconButton
                            sx={{
                                position: "absolute",
                                bottom: 0,
                                right: 0,
                                bgcolor: "white",
                                border: "1px solid",
                                boxShadow: 1,
                            }}
                            component="label"
                        >
                            <CameraAltIcon />
                            <input
                                hidden
                                accept="image/*"
                                type="file"
                                onChange={handleProfilePicChange}
                            />
                        </IconButton>
                    </Box>
                    <Typography variant="h5" mt={2} gutterBottom>
                        {username}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                        {email}
                    </Typography>
                </Box>

                {/* Profile Edit Section */}
                {/* <Divider />
                <Box mt={3}>
                    <Typography variant="h6" gutterBottom>
                        Profile Settings
                    </Typography>
                    <Box display="flex" flexDirection="column" gap={2}>
                        <TextField
                            label="Username"
                            value={username}
                            onChange={(e) => setUsername(e.target.value)}
                            disabled={!editing}
                            fullWidth
                        />
                        <TextField
                            label="Email"
                            value={email}
                            onChange={(e) => setEmail(e.target.value)}
                            disabled={!editing}
                            fullWidth
                        />
                    </Box>
                    <Box display="flex" justifyContent="flex-end" mt={2}>
                        {editing ? (
                            <Button variant="contained" color="primary" onClick={handleSave}>
                                Save
                            </Button>
                        ) : (
                            <Button variant="outlined" color="primary" onClick={handleEditToggle}>
                                Edit Profile
                            </Button>
                        )}
                    </Box>
                </Box> */}

                {/* Password Reset Section */}
                <Divider sx={{ my: 3 }} />
                <Box>
                    <Typography variant="h6" gutterBottom>
                        Security
                    </Typography>
                    {!changingPassword ? (
                        <Button
                            startIcon={<SettingsIcon />}
                            variant="outlined"
                            fullWidth
                            sx={{ mb: 2 }}
                            onClick={() => setChangingPassword(true)}
                        >
                            Change Password
                        </Button>
                    ) : (
                        <Box display="flex" flexDirection="column" gap={2}>
                            <TextField
                                label="Old Password"
                                type="password"
                                value={oldPassword}
                                onChange={(e) => setOldPassword(e.target.value)}
                                fullWidth
                            />
                            <TextField
                                label="New Password"
                                type="password"
                                value={newPassword}
                                onChange={(e) => setNewPassword(e.target.value)}
                                fullWidth
                            />
                            <TextField
                                label="Confirm New Password"
                                type="password"
                                value={confirmPassword}
                                onChange={(e) => setConfirmPassword(e.target.value)}
                                fullWidth
                            />
                            <Box display="flex" justifyContent="flex-end" gap={2}>
                                <Button
                                    variant="outlined"
                                    color="secondary"
                                    onClick={() => setChangingPassword(false)}
                                >
                                    Cancel
                                </Button>
                                <Button
                                    variant="contained"
                                    color="primary"
                                    onClick={handlePasswordChange}
                                >
                                    Save Password
                                </Button>
                            </Box>
                        </Box>
                    )}
                </Box>

                {/* Device Settings Section */}
                <Divider sx={{ my: 3 }} />
                <Box>
                    <Typography variant="h6" gutterBottom>
                        Device Settings
                    </Typography>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                        Manage connected devices and motion data preferences.
                    </Typography>
                    <Button
                        startIcon={<SettingsIcon />}
                        variant="outlined"
                        fullWidth
                        sx={{ mb: 2 }}
                    >
                        Device Preferences
                    </Button>
                </Box>

                {/* App Settings Section */}
                {/* <Divider sx={{ my: 3 }} />
                <Box>
                    <Typography variant="h6" gutterBottom>
                        General Settings
                    </Typography>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                        Customize app behavior and appearance.
                    </Typography>
                    <Button
                        startIcon={<SettingsIcon />}
                        variant="outlined"
                        fullWidth
                        sx={{ mb: 2 }}
                    >
                        App Preferences
                    </Button>
                </Box> */}

                {/* Logout Section */}
                <Divider sx={{ my: 3 }} />
                {/* <RandomQuote/> */}
                <Button
                    startIcon={<LogoutIcon />}
                    variant="contained"
                    color="secondary"
                    fullWidth
                    onClick={handleLogout}
                >
                    Logout
                </Button>
       
            </Box>
        </animated.div>
    );
};

export default ProfileSettings;
