//@ts-nocheck
import { Typography, Box, Switch } from "@material-ui/core";
import { WifiOutlined, CloudOutlined } from "@material-ui/icons";
import React, { useEffect, useRef, useState } from "react";
import { ConnectedAppliance, DeviceDetails } from "../deviceSetup/BleShow";
import { createGesture, Gesture, GestureDetail, IonToggle } from '@ionic/react';
import './ApplianceSetup.css';
import { useTheme } from "@material-ui/core/styles";
import { useDispatch, useSelector } from "react-redux";
import { toggleRoomModal, updateDragValue, selectDevices, isRoomModalOpen, updateApplianceState } from "../../redux/slices/home/<USER>";
import { useHistory } from "react-router-dom";

export type AppliancePropTypes = {
    appliance: ConnectedAppliance,
}

const ApplianceCard: React.FC<AppliancePropTypes> = ({ appliance }) => {
    const newRef = useRef<HTMLDivElement>(null);
    const dispatch = useDispatch();
    const roomModalOpen = useSelector(isRoomModalOpen);
    const devices: DeviceDetails[] = useSelector(selectDevices);
    const theme = useTheme();
    const history = useHistory();
    useEffect(() => {
        dispatch(toggleRoomModal(false));
        let gesture = createGesture({
            el: newRef.current!,
            threshold: 0,
            gestureName: 'my-gesture',
            onMove: (ev: GestureDetail) => {
                newRef.current!.style.transform = `translate(${ev.deltaX}px,${ev.deltaY}px)`;
                newRef.current!.style.zIndex = `999999`
                dispatch(toggleRoomModal(true));
                dispatch(updateDragValue({ x: ev.currentX, y: ev.currentY, appliance: appliance, dropped: false }));
            },
            onStart: (ev) => {
                newRef.current!.style.transition = "0.2s all linear";
                newRef.current!.style.border = `2px solid #F7B008`;
                newRef.current!.style.borderRadius = `10px`;
                newRef.current!.style.padding = `0.4em`;
            },
            onEnd: (ev) => {
                newRef.current!.style.transform = 'translate(0,0)'
                newRef.current!.style.zIndex = `initial`
                newRef.current!.style.border = `initial`;
                newRef.current!.style.borderRadius = `initial`;
                newRef.current!.style.padding = `initial`;
                dispatch(updateDragValue({ x: ev.currentX, y: ev.currentY, appliance: appliance, dropped: true }));
                dispatch(toggleRoomModal(false));
            }
        });
        gesture.enable();
    }, [])

    function getRoomName() {
        let roomName = "";
        devices.forEach((device, index) => {
            device.connectedAppliances?.forEach((applianceObj, index) => {
                if (applianceObj === appliance) {

                }
            })
        })

    }

    const handleStateChange = (event: any) => {
        dispatch(updateApplianceState({
            appliance: {
                ...appliance,
                offlineState: {
                    online: true,
                    on: event.target.checked
                }
            }
        }))
    };


    return <>
        <div className={'warningWrapper'}>

            <Typography style={{ color: "#ffffff", marginLeft: "0.2em", display: "flex", flexDirection: "row", justifyContent: "space-between", alignItems: "center" }}>
                {!appliance.room ? "Drag to assign room" : `Assigned to ${appliance.room.roomName}`}
                <Switch
                    onChange={(e) => handleStateChange(e)}
                    inputProps={{ 'aria-label': 'Turn On Appliance' }}
                    checked={appliance.offlineState?.on}
                    color={'primary'}
                    size={'medium'}
                />
            </Typography>

            <div onClick={() => {
                history.push(`/control/${appliance.uid}`)
            }} ref={newRef}><Box boxShadow={2} className={'dragElement'}>
                    <Typography>{appliance.applianceName}</Typography>
                    <div className={'indicatorBar'}>
                        <WifiOutlined style={{ color: appliance.offlineState?.online ? "#2A9D8F" : "#607D8B" }} />
                        <CloudOutlined style={{ color: "#607D8B" }} />
                    </div>
                </Box></div>
        </div>
    </>
}

export default ApplianceCard;

