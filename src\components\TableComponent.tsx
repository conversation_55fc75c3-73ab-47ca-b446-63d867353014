import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Typography,
} from "@mui/material";

const TableComponent = ({ rows = [], title = "Table" }) => {
  return (
    <div style={{ marginBottom: "20px" }}>
      <Typography variant="h6" gutterBottom>
        {title}
      </Typography>
      <TableContainer component={Paper} style={{ margin: "20px 0" }}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell><strong>Duration</strong></TableCell>
              <TableCell><strong>Stability Index</strong></TableCell>
              <TableCell><strong>Signed By</strong></TableCell>
              <TableCell><strong>Date Signed</strong></TableCell>
              <TableCell><strong>Comments</strong></TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {rows.map((row:any, index:Number) => (
              //@ts-ignore
              <TableRow key={index}>
                <TableCell>{row.duration}</TableCell>
                <TableCell>{row.stability_index}</TableCell>
                <TableCell>{row.signed_by}</TableCell>
                <TableCell>{row.date_signed}</TableCell>
                <TableCell>{row.comments}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </div>
  );
};

export default TableComponent;
