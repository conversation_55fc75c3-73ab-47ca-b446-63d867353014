//@ts-nocheck
import { FirebaseAuthentication } from '@ionic-native/firebase-authentication';
import React, { useLayoutEffect,useState ,useEffect} from 'react';
import { useDispatch, useSelector } from 'react-redux';
// import { ReactComponent as Logo } from '../../assets/logo.svg';
import HBM from '../HBM/HBM';
import './root.css';
import Login from '../login/login';
import {selectUser,login,logout} from '../../redux/slices/userSlice'
const _ROOT_ = (props: any) => {
    const user = useSelector(selectUser); 
    const dispatch = useDispatch();

    useEffect(() => {
        
        FirebaseAuthentication.onAuthStateChanged().subscribe((userInfo) => {
            
            
            if (userInfo) {
                dispatch(login(userInfo));
            }else{
                dispatch(logout(null));
            }
        })
    },[user.value])
    return (user.value===null? <>
        <div style={{
            boxShadow: `0 0px 50px rgba(247, 176, 8, 0.35), 0 0px 6px rgba(247, 176, 8, 0.35)`,
            height: "40px",
            width: "40px",
            position: "fixed",
            top: "30px",
            left: "30px",
            padding: "5px",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            borderRadius: "50%"
        }}>
            {/* <Logo></Logo> */}
            </div><Login></Login></> : <><HBM></HBM></>)
    // return <HBM></HBM>
}

export default _ROOT_;
