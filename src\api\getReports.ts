import { CapacitorHttp } from "@capacitor/core";

export const getReports = async (accessToken: string, patientId: string): Promise<any> => {
    console.log("Fetching Reports...");

    try {
        const response = await CapacitorHttp.post({
            url: 'https://apps.exermetrix.com/april/Dictionary.nsf/getReports.xsp/getListNew',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${accessToken}`
            },
            data: {
                patientId
            }
        });

        console.log("Reports response:", response);

        if (response.status !== 200) {
            throw new Error(`Failed to fetch reports. Status: ${response.status}`);
        }

        if (response.data) {
            console.log("Reports Data:", response.data);
            return response.data;
        } else {
            throw new Error("No data returned from the server.");
        }
    } catch (error: any) {
        console.log("GET REPORTS ERROR", error);
        throw new Error(`Error fetching reports: ${error.message || error}`);
    }
};
