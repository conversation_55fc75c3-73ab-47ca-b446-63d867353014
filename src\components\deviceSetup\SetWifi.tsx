//@ts-ignore
// @ts-nocheck
import React from 'react';
import './deviceSetup.css'
// import { BLE } from "@ionic-native/ble";
import { IonInput,IonItem, IonButton} from '@ionic/react';
import { WifiWizard2 } from '@ionic-native/wifi-wizard-2';

export default class SetWifi extends React.Component{
    constructor(props){
        super(props);
        this.state={
            wifi_id: "",
            wifi_pass: "",
        }
    }
    componentDidMount(){
        
            WifiWizard2.getConnectedSSID().then(success=>{
                this.setState({...this.state,wifi_id:success});
            })
        
    };
    render(){
        return <>
        this is wifi setup
        <IonItem>
        <input
            value={this.state.wifi_id}
            placeholder="Enter wifi name"
            clearInput={true}
            type="text"
            onIonChange={(e) => this.setState({...this.state, wifi_id: e.detail.value })}
        />
        </IonItem>
        <IonItem>
        <input
            value={this.state.wifi_pass}
            placeholder="Enter wifi password"
            clearInput={true}
            type="password"
            onIonChange={(e) => this.setState({...this.state, wifi_pass: e.detail.value })}
        />
        </IonItem>
        <IonButton onClick={()=>this.props.connectDevice(this.state.wifi_id,this.state.wifi_pass)}>
            SUBMIT
        </IonButton>
        </>
    }
}