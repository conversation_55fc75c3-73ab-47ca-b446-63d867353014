//@ts-nocheck
import React from "react";
import { <PERSON><PERSON><PERSON>, Button, Box, CircularProgress, IconButton, Drawer, List, ListItem, ListItemText } from '@mui/material';
import { useHistory } from "react-router";
import { useDispatch } from "react-redux";
import {logout } from "../redux/slices/userSlice.js"


export const SideBar: React.FC = ({ menuOpen, toggleMenu }) => {
    const history = useHistory();
const dispatch = useDispatch()
    return <>

        {/* Drawer Menu (Hamburger Menu) */}
        <Drawer
            anchor="right"
            open={menuOpen}
            onClose={toggleMenu} // Close the drawer when tapping outside
            sx={{
                width: 250, // Set a fixed width to avoid overflow
                flexShrink: 0,
                '& .MuiDrawer-paper': {
                    width: 250, // Set the drawer width to 250px or a value that suits your design
                    height: '100%',
                    backgroundColor: '#333', // Dark background color
                    color: '#fff', // Light text and icon color
                    padding: '2em',
                    display: 'flex',
                    flexDirection: 'column',
                    justifyContent: 'space-between',
                },
            }}
        >
            <List>
                
                <ListItem button onClick={() => { history.push('/devicesetup'); toggleMenu(); }}>
                    <ListItemText primary="Setup Device" sx={{ color: '#fff' }} />
                </ListItem>
                {/* 
                <ListItem button onClick={() => { history.push('/patientresults'); toggleMenu(); }}>
                    <ListItemText primary="View Results" sx={{ color: '#fff' }} />
                </ListItem> */}
                {/* <ListItem button onClick={() => { history.push('/findsensor'); toggleMenu(); }}>
                    <ListItemText primary="Find Sensor" sx={{ color: '#fff' }} />
                </ListItem>
                <ListItem button onClick={() => { history.push('/batterylevel'); toggleMenu(); }}>
                    <ListItemText primary="Battery Level" sx={{ color: '#fff' }} />
                </ListItem> */}
                <ListItem button onClick={() => { history.push('/settings'); toggleMenu(); }}>
                    <ListItemText primary="Settings" sx={{ color: '#fff' }} />
                </ListItem>
                <ListItem button onClick={() => { dispatch(logout(null)); toggleMenu(); }}>
                    <ListItemText primary="Logout" sx={{ color: '#fff' }} />
                </ListItem>
            </List>
        </Drawer>
    </>
}