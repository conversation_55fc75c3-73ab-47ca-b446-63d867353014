import { createSlice } from '@reduxjs/toolkit';
import { FirebaseAuthentication } from '@ionic-native/firebase-authentication';
import {getAuth, signOut} from "firebase/auth"
import firebase from "../../firebase"
export const UserSlice = createSlice({
    name: 'user',
    initialState: {
        value: null,
        addresses: ['413 KMS Homes, Near Swaraj Hypermarket, 16, 1st Cross Rd, Tavarekere, Cashier Layout, 1st Stage, BTM Layout 1, Bengaluru, Karnataka 560029, India shivanshu - 8107082793'],
        name: null,
    },
    reducers: {
        login: (state, action) => {
            // 
            // set fetched purchase
            console.log("From Redux",JSON.stringify(action.payload))
            state.value = action.payload;
        },
        logout:(state,action)=>{
            console.log("Bye Bye");
            let auth = getAuth(firebase)
            signOut(auth)
            state.value=null
        },
        addAddress: (state, action) => {
            let prevState = state.addresses;
            let nextState = prevState.concat(action.payload);
            
            return nextState;
        },
        deleteAddress: (state, action) => {
            let prevState = state.addresses;
            let nextState = prevState.filter((ele, index) => {
                if (ele.id === action.payload.id) {
                    return false
                }else{
                    return true
                }
            });
            
            return nextState;
        },
        setName : (state,action)=>{
            state.name = action.payload
        },


    }
})
export const addAddressThunk = (data) => {
    return async (dispatch, getState) => {
        let prevState = getState();
        let nextState = prevState.addresses.push(data);
        // firebase.database().ref('users/addresses')
        //     .set(data)
        //     .then((res) => {
        //         dispatch(addAddress(data))
        //     })
        //     .catch((e) => {
                
        //     })
    }
}
export const selectUser = (state)=>{
    return state.user
};
export const { login, logout,addAddress , deleteAddress, setName } = UserSlice.actions;
export default UserSlice.reducer;