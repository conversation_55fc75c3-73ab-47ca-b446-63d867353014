import { CapacitorHttp } from "@capacitor/core";

export const getTestListForPatients = async (
  accessToken: string,
  patientId: string
): Promise<any> => {
  console.log("Fetching test list for patient...");

  try {
    const url = `https://apps.exermetrix.com/april/Dictionary.nsf/getJoints.xsp/getDefaultJoints?patientId=${encodeURIComponent(patientId)}`;

    const response: any = await CapacitorHttp.get({
      url,
      headers: {
        Authorization: `Bearer ${accessToken}`,
        // Cookie: 'SessionID=27A478074D6C36CFE0E53A3417C4AF8798038DE7' // Uncomment if required
      },
    });

    console.log("Test list response:", response);

    if (response.error) {
      throw new Error(response.data?.ErrorMessage || "Unknown error from test list endpoint");
    }

    return response.data;
  } catch (error: any) {
    console.error("GET TEST LIST ERROR:", error);
    throw new Error(`Test list fetch failed: ${error.message || error}`);
  }
};
