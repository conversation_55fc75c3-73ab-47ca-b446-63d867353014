function fE(){import.meta.url,import("_").catch(()=>1),async function*(){}().next()}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))o(r);new MutationObserver(r=>{for(const i of r)if(i.type==="childList")for(const s of i.addedNodes)s.tagName==="LINK"&&s.rel==="modulepreload"&&o(s)}).observe(document,{childList:!0,subtree:!0});function n(r){const i={};return r.integrity&&(i.integrity=r.integrity),r.referrerPolicy&&(i.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?i.credentials="include":r.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function o(r){if(r.ep)return;r.ep=!0;const i=n(r);fetch(r.href,i)}})();function wr(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var kf={exports:{}},Xi={},Ef={exports:{}},j={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xr=Symbol.for("react.element"),Ag=Symbol.for("react.portal"),zg=Symbol.for("react.fragment"),Bg=Symbol.for("react.strict_mode"),Dg=Symbol.for("react.profiler"),Mg=Symbol.for("react.provider"),Ng=Symbol.for("react.context"),jg=Symbol.for("react.forward_ref"),Hg=Symbol.for("react.suspense"),Vg=Symbol.for("react.memo"),Fg=Symbol.for("react.lazy"),hu=Symbol.iterator;function Wg(e){return e===null||typeof e!="object"?null:(e=hu&&e[hu]||e["@@iterator"],typeof e=="function"?e:null)}var Sf={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},Cf=Object.assign,Tf={};function wo(e,t,n){this.props=e,this.context=t,this.refs=Tf,this.updater=n||Sf}wo.prototype.isReactComponent={};wo.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};wo.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function $f(){}$f.prototype=wo.prototype;function Aa(e,t,n){this.props=e,this.context=t,this.refs=Tf,this.updater=n||Sf}var za=Aa.prototype=new $f;za.constructor=Aa;Cf(za,wo.prototype);za.isPureReactComponent=!0;var pu=Array.isArray,Pf=Object.prototype.hasOwnProperty,Ba={current:null},_f={key:!0,ref:!0,__self:!0,__source:!0};function If(e,t,n){var o,r={},i=null,s=null;if(t!=null)for(o in t.ref!==void 0&&(s=t.ref),t.key!==void 0&&(i=""+t.key),t)Pf.call(t,o)&&!_f.hasOwnProperty(o)&&(r[o]=t[o]);var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){for(var l=Array(a),c=0;c<a;c++)l[c]=arguments[c+2];r.children=l}if(e&&e.defaultProps)for(o in a=e.defaultProps,a)r[o]===void 0&&(r[o]=a[o]);return{$$typeof:xr,type:e,key:i,ref:s,props:r,_owner:Ba.current}}function Ug(e,t){return{$$typeof:xr,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Da(e){return typeof e=="object"&&e!==null&&e.$$typeof===xr}function Qg(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var mu=/\/+/g;function js(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Qg(""+e.key):t.toString(36)}function oi(e,t,n,o,r){var i=typeof e;(i==="undefined"||i==="boolean")&&(e=null);var s=!1;if(e===null)s=!0;else switch(i){case"string":case"number":s=!0;break;case"object":switch(e.$$typeof){case xr:case Ag:s=!0}}if(s)return s=e,r=r(s),e=o===""?"."+js(s,0):o,pu(r)?(n="",e!=null&&(n=e.replace(mu,"$&/")+"/"),oi(r,t,n,"",function(c){return c})):r!=null&&(Da(r)&&(r=Ug(r,n+(!r.key||s&&s.key===r.key?"":(""+r.key).replace(mu,"$&/")+"/")+e)),t.push(r)),1;if(s=0,o=o===""?".":o+":",pu(e))for(var a=0;a<e.length;a++){i=e[a];var l=o+js(i,a);s+=oi(i,t,n,l,r)}else if(l=Wg(e),typeof l=="function")for(e=l.call(e),a=0;!(i=e.next()).done;)i=i.value,l=o+js(i,a++),s+=oi(i,t,n,l,r);else if(i==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return s}function Ar(e,t,n){if(e==null)return e;var o=[],r=0;return oi(e,o,"","",function(i){return t.call(n,i,r++)}),o}function qg(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Ae={current:null},ri={transition:null},Kg={ReactCurrentDispatcher:Ae,ReactCurrentBatchConfig:ri,ReactCurrentOwner:Ba};function Rf(){throw Error("act(...) is not supported in production builds of React.")}j.Children={map:Ar,forEach:function(e,t,n){Ar(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Ar(e,function(){t++}),t},toArray:function(e){return Ar(e,function(t){return t})||[]},only:function(e){if(!Da(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};j.Component=wo;j.Fragment=zg;j.Profiler=Dg;j.PureComponent=Aa;j.StrictMode=Bg;j.Suspense=Hg;j.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Kg;j.act=Rf;j.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var o=Cf({},e.props),r=e.key,i=e.ref,s=e._owner;if(t!=null){if(t.ref!==void 0&&(i=t.ref,s=Ba.current),t.key!==void 0&&(r=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)Pf.call(t,l)&&!_f.hasOwnProperty(l)&&(o[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)o.children=n;else if(1<l){a=Array(l);for(var c=0;c<l;c++)a[c]=arguments[c+2];o.children=a}return{$$typeof:xr,type:e.type,key:r,ref:i,props:o,_owner:s}};j.createContext=function(e){return e={$$typeof:Ng,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Mg,_context:e},e.Consumer=e};j.createElement=If;j.createFactory=function(e){var t=If.bind(null,e);return t.type=e,t};j.createRef=function(){return{current:null}};j.forwardRef=function(e){return{$$typeof:jg,render:e}};j.isValidElement=Da;j.lazy=function(e){return{$$typeof:Fg,_payload:{_status:-1,_result:e},_init:qg}};j.memo=function(e,t){return{$$typeof:Vg,type:e,compare:t===void 0?null:t}};j.startTransition=function(e){var t=ri.transition;ri.transition={};try{e()}finally{ri.transition=t}};j.unstable_act=Rf;j.useCallback=function(e,t){return Ae.current.useCallback(e,t)};j.useContext=function(e){return Ae.current.useContext(e)};j.useDebugValue=function(){};j.useDeferredValue=function(e){return Ae.current.useDeferredValue(e)};j.useEffect=function(e,t){return Ae.current.useEffect(e,t)};j.useId=function(){return Ae.current.useId()};j.useImperativeHandle=function(e,t,n){return Ae.current.useImperativeHandle(e,t,n)};j.useInsertionEffect=function(e,t){return Ae.current.useInsertionEffect(e,t)};j.useLayoutEffect=function(e,t){return Ae.current.useLayoutEffect(e,t)};j.useMemo=function(e,t){return Ae.current.useMemo(e,t)};j.useReducer=function(e,t,n){return Ae.current.useReducer(e,t,n)};j.useRef=function(e){return Ae.current.useRef(e)};j.useState=function(e){return Ae.current.useState(e)};j.useSyncExternalStore=function(e,t,n){return Ae.current.useSyncExternalStore(e,t,n)};j.useTransition=function(){return Ae.current.useTransition()};j.version="18.3.1";Ef.exports=j;var Xe=Ef.exports;const b=wr(Xe);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Yg=Xe,Xg=Symbol.for("react.element"),Gg=Symbol.for("react.fragment"),Zg=Object.prototype.hasOwnProperty,Jg=Yg.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,ev={key:!0,ref:!0,__self:!0,__source:!0};function Lf(e,t,n){var o,r={},i=null,s=null;n!==void 0&&(i=""+n),t.key!==void 0&&(i=""+t.key),t.ref!==void 0&&(s=t.ref);for(o in t)Zg.call(t,o)&&!ev.hasOwnProperty(o)&&(r[o]=t[o]);if(e&&e.defaultProps)for(o in t=e.defaultProps,t)r[o]===void 0&&(r[o]=t[o]);return{$$typeof:Xg,type:e,key:i,ref:s,props:r,_owner:Jg.current}}Xi.Fragment=Gg;Xi.jsx=Lf;Xi.jsxs=Lf;kf.exports=Xi;var D=kf.exports,Of={exports:{}},Je={},Af={exports:{}},zf={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(_,C){var E=_.length;_.push(C);e:for(;0<E;){var z=E-1>>>1,L=_[z];if(0<r(L,C))_[z]=C,_[E]=L,E=z;else break e}}function n(_){return _.length===0?null:_[0]}function o(_){if(_.length===0)return null;var C=_[0],E=_.pop();if(E!==C){_[0]=E;e:for(var z=0,L=_.length,W=L>>>1;z<W;){var H=2*(z+1)-1,ce=_[H],se=H+1,he=_[se];if(0>r(ce,E))se<L&&0>r(he,ce)?(_[z]=he,_[se]=E,z=se):(_[z]=ce,_[H]=E,z=H);else if(se<L&&0>r(he,E))_[z]=he,_[se]=E,z=se;else break e}}return C}function r(_,C){var E=_.sortIndex-C.sortIndex;return E!==0?E:_.id-C.id}if(typeof performance=="object"&&typeof performance.now=="function"){var i=performance;e.unstable_now=function(){return i.now()}}else{var s=Date,a=s.now();e.unstable_now=function(){return s.now()-a}}var l=[],c=[],u=1,d=null,f=3,p=!1,v=!1,x=!1,R=typeof setTimeout=="function"?setTimeout:null,m=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function g(_){for(var C=n(c);C!==null;){if(C.callback===null)o(c);else if(C.startTime<=_)o(c),C.sortIndex=C.expirationTime,t(l,C);else break;C=n(c)}}function w(_){if(x=!1,g(_),!v)if(n(l)!==null)v=!0,J(T);else{var C=n(c);C!==null&&kt(w,C.startTime-_)}}function T(_,C){v=!1,x&&(x=!1,m($),$=-1),p=!0;var E=f;try{for(g(C),d=n(l);d!==null&&(!(d.expirationTime>C)||_&&!V());){var z=d.callback;if(typeof z=="function"){d.callback=null,f=d.priorityLevel;var L=z(d.expirationTime<=C);C=e.unstable_now(),typeof L=="function"?d.callback=L:d===n(l)&&o(l),g(C)}else o(l);d=n(l)}if(d!==null)var W=!0;else{var H=n(c);H!==null&&kt(w,H.startTime-C),W=!1}return W}finally{d=null,f=E,p=!1}}var P=!1,k=null,$=-1,I=5,B=-1;function V(){return!(e.unstable_now()-B<I)}function de(){if(k!==null){var _=e.unstable_now();B=_;var C=!0;try{C=k(!0,_)}finally{C?fe():(P=!1,k=null)}}else P=!1}var fe;if(typeof h=="function")fe=function(){h(de)};else if(typeof MessageChannel<"u"){var ie=new MessageChannel,K=ie.port2;ie.port1.onmessage=de,fe=function(){K.postMessage(null)}}else fe=function(){R(de,0)};function J(_){k=_,P||(P=!0,fe())}function kt(_,C){$=R(function(){_(e.unstable_now())},C)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(_){_.callback=null},e.unstable_continueExecution=function(){v||p||(v=!0,J(T))},e.unstable_forceFrameRate=function(_){0>_||125<_?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):I=0<_?Math.floor(1e3/_):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(_){switch(f){case 1:case 2:case 3:var C=3;break;default:C=f}var E=f;f=C;try{return _()}finally{f=E}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(_,C){switch(_){case 1:case 2:case 3:case 4:case 5:break;default:_=3}var E=f;f=_;try{return C()}finally{f=E}},e.unstable_scheduleCallback=function(_,C,E){var z=e.unstable_now();switch(typeof E=="object"&&E!==null?(E=E.delay,E=typeof E=="number"&&0<E?z+E:z):E=z,_){case 1:var L=-1;break;case 2:L=250;break;case 5:L=**********;break;case 4:L=1e4;break;default:L=5e3}return L=E+L,_={id:u++,callback:C,priorityLevel:_,startTime:E,expirationTime:L,sortIndex:-1},E>z?(_.sortIndex=E,t(c,_),n(l)===null&&_===n(c)&&(x?(m($),$=-1):x=!0,kt(w,E-z))):(_.sortIndex=L,t(l,_),v||p||(v=!0,J(T))),_},e.unstable_shouldYield=V,e.unstable_wrapCallback=function(_){var C=f;return function(){var E=f;f=C;try{return _.apply(this,arguments)}finally{f=E}}}})(zf);Af.exports=zf;var tv=Af.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var nv=Xe,Ze=tv;function S(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var Bf=new Set,Zo={};function On(e,t){lo(e,t),lo(e+"Capture",t)}function lo(e,t){for(Zo[e]=t,e=0;e<t.length;e++)Bf.add(t[e])}var Mt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Pl=Object.prototype.hasOwnProperty,ov=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,gu={},vu={};function rv(e){return Pl.call(vu,e)?!0:Pl.call(gu,e)?!1:ov.test(e)?vu[e]=!0:(gu[e]=!0,!1)}function iv(e,t,n,o){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return o?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function sv(e,t,n,o){if(t===null||typeof t>"u"||iv(e,t,n,o))return!0;if(o)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function ze(e,t,n,o,r,i,s){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=o,this.attributeNamespace=r,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=s}var Ce={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Ce[e]=new ze(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Ce[t]=new ze(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Ce[e]=new ze(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Ce[e]=new ze(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Ce[e]=new ze(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Ce[e]=new ze(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Ce[e]=new ze(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Ce[e]=new ze(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Ce[e]=new ze(e,5,!1,e.toLowerCase(),null,!1,!1)});var Ma=/[\-:]([a-z])/g;function Na(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Ma,Na);Ce[t]=new ze(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Ma,Na);Ce[t]=new ze(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Ma,Na);Ce[t]=new ze(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Ce[e]=new ze(e,1,!1,e.toLowerCase(),null,!1,!1)});Ce.xlinkHref=new ze("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Ce[e]=new ze(e,1,!1,e.toLowerCase(),null,!0,!0)});function ja(e,t,n,o){var r=Ce.hasOwnProperty(t)?Ce[t]:null;(r!==null?r.type!==0:o||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(sv(t,n,r,o)&&(n=null),o||r===null?rv(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):r.mustUseProperty?e[r.propertyName]=n===null?r.type===3?!1:"":n:(t=r.attributeName,o=r.attributeNamespace,n===null?e.removeAttribute(t):(r=r.type,n=r===3||r===4&&n===!0?"":""+n,o?e.setAttributeNS(o,t,n):e.setAttribute(t,n))))}var Vt=nv.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,zr=Symbol.for("react.element"),Hn=Symbol.for("react.portal"),Vn=Symbol.for("react.fragment"),Ha=Symbol.for("react.strict_mode"),_l=Symbol.for("react.profiler"),Df=Symbol.for("react.provider"),Mf=Symbol.for("react.context"),Va=Symbol.for("react.forward_ref"),Il=Symbol.for("react.suspense"),Rl=Symbol.for("react.suspense_list"),Fa=Symbol.for("react.memo"),Ut=Symbol.for("react.lazy"),Nf=Symbol.for("react.offscreen"),yu=Symbol.iterator;function Co(e){return e===null||typeof e!="object"?null:(e=yu&&e[yu]||e["@@iterator"],typeof e=="function"?e:null)}var oe=Object.assign,Hs;function zo(e){if(Hs===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Hs=t&&t[1]||""}return"\n"+Hs+e}var Vs=!1;function Fs(e,t){if(!e||Vs)return"";Vs=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var o=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){o=c}e.call(t.prototype)}else{try{throw Error()}catch(c){o=c}e()}}catch(c){if(c&&o&&typeof c.stack=="string"){for(var r=c.stack.split("\n"),i=o.stack.split("\n"),s=r.length-1,a=i.length-1;1<=s&&0<=a&&r[s]!==i[a];)a--;for(;1<=s&&0<=a;s--,a--)if(r[s]!==i[a]){if(s!==1||a!==1)do if(s--,a--,0>a||r[s]!==i[a]){var l="\n"+r[s].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=s&&0<=a);break}}}finally{Vs=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?zo(e):""}function lv(e){switch(e.tag){case 5:return zo(e.type);case 16:return zo("Lazy");case 13:return zo("Suspense");case 19:return zo("SuspenseList");case 0:case 2:case 15:return e=Fs(e.type,!1),e;case 11:return e=Fs(e.type.render,!1),e;case 1:return e=Fs(e.type,!0),e;default:return""}}function Ll(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Vn:return"Fragment";case Hn:return"Portal";case _l:return"Profiler";case Ha:return"StrictMode";case Il:return"Suspense";case Rl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Mf:return(e.displayName||"Context")+".Consumer";case Df:return(e._context.displayName||"Context")+".Provider";case Va:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Fa:return t=e.displayName||null,t!==null?t:Ll(e.type)||"Memo";case Ut:t=e._payload,e=e._init;try{return Ll(e(t))}catch(n){}}return null}function av(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Ll(t);case 8:return t===Ha?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function an(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function jf(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function cv(e){var t=jf(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),o=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var r=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return r.call(this)},set:function(s){o=""+s,i.call(this,s)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return o},setValue:function(s){o=""+s},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Br(e){e._valueTracker||(e._valueTracker=cv(e))}function Hf(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),o="";return e&&(o=jf(e)?e.checked?"true":"false":e.value),e=o,e!==n?(t.setValue(e),!0):!1}function gi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch(t){return e.body}}function Ol(e,t){var n=t.checked;return oe({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n!=null?n:e._wrapperState.initialChecked})}function bu(e,t){var n=t.defaultValue==null?"":t.defaultValue,o=t.checked!=null?t.checked:t.defaultChecked;n=an(t.value!=null?t.value:n),e._wrapperState={initialChecked:o,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Vf(e,t){t=t.checked,t!=null&&ja(e,"checked",t,!1)}function Al(e,t){Vf(e,t);var n=an(t.value),o=t.type;if(n!=null)o==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(o==="submit"||o==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?zl(e,t.type,n):t.hasOwnProperty("defaultValue")&&zl(e,t.type,an(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function wu(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var o=t.type;if(!(o!=="submit"&&o!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function zl(e,t,n){(t!=="number"||gi(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Bo=Array.isArray;function Jn(e,t,n,o){if(e=e.options,t){t={};for(var r=0;r<n.length;r++)t["$"+n[r]]=!0;for(n=0;n<e.length;n++)r=t.hasOwnProperty("$"+e[n].value),e[n].selected!==r&&(e[n].selected=r),r&&o&&(e[n].defaultSelected=!0)}else{for(n=""+an(n),t=null,r=0;r<e.length;r++){if(e[r].value===n){e[r].selected=!0,o&&(e[r].defaultSelected=!0);return}t!==null||e[r].disabled||(t=e[r])}t!==null&&(t.selected=!0)}}function Bl(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(S(91));return oe({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function xu(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(S(92));if(Bo(n)){if(1<n.length)throw Error(S(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:an(n)}}function Ff(e,t){var n=an(t.value),o=an(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),o!=null&&(e.defaultValue=""+o)}function ku(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Wf(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Dl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Wf(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Dr,Uf=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,o,r){MSApp.execUnsafeLocalFunction(function(){return e(t,n,o,r)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Dr=Dr||document.createElement("div"),Dr.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Dr.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Jo(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Vo={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},uv=["Webkit","ms","Moz","O"];Object.keys(Vo).forEach(function(e){uv.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Vo[t]=Vo[e]})});function Qf(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Vo.hasOwnProperty(e)&&Vo[e]?(""+t).trim():t+"px"}function qf(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var o=n.indexOf("--")===0,r=Qf(n,t[n],o);n==="float"&&(n="cssFloat"),o?e.setProperty(n,r):e[n]=r}}var dv=oe({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Ml(e,t){if(t){if(dv[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(S(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(S(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(S(61))}if(t.style!=null&&typeof t.style!="object")throw Error(S(62))}}function Nl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var jl=null;function Wa(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var Hl=null,eo=null,to=null;function Eu(e){if(e=Sr(e)){if(typeof Hl!="function")throw Error(S(280));var t=e.stateNode;t&&(t=ts(t),Hl(e.stateNode,e.type,t))}}function Kf(e){eo?to?to.push(e):to=[e]:eo=e}function Yf(){if(eo){var e=eo,t=to;if(to=eo=null,Eu(e),t)for(e=0;e<t.length;e++)Eu(t[e])}}function Xf(e,t){return e(t)}function Gf(){}var Ws=!1;function Zf(e,t,n){if(Ws)return e(t,n);Ws=!0;try{return Xf(e,t,n)}finally{Ws=!1,(eo!==null||to!==null)&&(Gf(),Yf())}}function er(e,t){var n=e.stateNode;if(n===null)return null;var o=ts(n);if(o===null)return null;n=o[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(o=!o.disabled)||(e=e.type,o=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!o;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(S(231,t,typeof n));return n}var Vl=!1;if(Mt)try{var To={};Object.defineProperty(To,"passive",{get:function(){Vl=!0}}),window.addEventListener("test",To,To),window.removeEventListener("test",To,To)}catch(e){Vl=!1}function fv(e,t,n,o,r,i,s,a,l){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(u){this.onError(u)}}var Fo=!1,vi=null,yi=!1,Fl=null,hv={onError:function(e){Fo=!0,vi=e}};function pv(e,t,n,o,r,i,s,a,l){Fo=!1,vi=null,fv.apply(hv,arguments)}function mv(e,t,n,o,r,i,s,a,l){if(pv.apply(this,arguments),Fo){if(Fo){var c=vi;Fo=!1,vi=null}else throw Error(S(198));yi||(yi=!0,Fl=c)}}function An(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Jf(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Su(e){if(An(e)!==e)throw Error(S(188))}function gv(e){var t=e.alternate;if(!t){if(t=An(e),t===null)throw Error(S(188));return t!==e?null:e}for(var n=e,o=t;;){var r=n.return;if(r===null)break;var i=r.alternate;if(i===null){if(o=r.return,o!==null){n=o;continue}break}if(r.child===i.child){for(i=r.child;i;){if(i===n)return Su(r),e;if(i===o)return Su(r),t;i=i.sibling}throw Error(S(188))}if(n.return!==o.return)n=r,o=i;else{for(var s=!1,a=r.child;a;){if(a===n){s=!0,n=r,o=i;break}if(a===o){s=!0,o=r,n=i;break}a=a.sibling}if(!s){for(a=i.child;a;){if(a===n){s=!0,n=i,o=r;break}if(a===o){s=!0,o=i,n=r;break}a=a.sibling}if(!s)throw Error(S(189))}}if(n.alternate!==o)throw Error(S(190))}if(n.tag!==3)throw Error(S(188));return n.stateNode.current===n?e:t}function eh(e){return e=gv(e),e!==null?th(e):null}function th(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=th(e);if(t!==null)return t;e=e.sibling}return null}var nh=Ze.unstable_scheduleCallback,Cu=Ze.unstable_cancelCallback,vv=Ze.unstable_shouldYield,yv=Ze.unstable_requestPaint,ae=Ze.unstable_now,bv=Ze.unstable_getCurrentPriorityLevel,Ua=Ze.unstable_ImmediatePriority,oh=Ze.unstable_UserBlockingPriority,bi=Ze.unstable_NormalPriority,wv=Ze.unstable_LowPriority,rh=Ze.unstable_IdlePriority,Gi=null,Pt=null;function xv(e){if(Pt&&typeof Pt.onCommitFiberRoot=="function")try{Pt.onCommitFiberRoot(Gi,e,void 0,(e.current.flags&128)===128)}catch(t){}}var yt=Math.clz32?Math.clz32:Sv,kv=Math.log,Ev=Math.LN2;function Sv(e){return e>>>=0,e===0?32:31-(kv(e)/Ev|0)|0}var Mr=64,Nr=4194304;function Do(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function wi(e,t){var n=e.pendingLanes;if(n===0)return 0;var o=0,r=e.suspendedLanes,i=e.pingedLanes,s=n&268435455;if(s!==0){var a=s&~r;a!==0?o=Do(a):(i&=s,i!==0&&(o=Do(i)))}else s=n&~r,s!==0?o=Do(s):i!==0&&(o=Do(i));if(o===0)return 0;if(t!==0&&t!==o&&!(t&r)&&(r=o&-o,i=t&-t,r>=i||r===16&&(i&4194240)!==0))return t;if(o&4&&(o|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=o;0<t;)n=31-yt(t),r=1<<n,o|=e[n],t&=~r;return o}function Cv(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Tv(e,t){for(var n=e.suspendedLanes,o=e.pingedLanes,r=e.expirationTimes,i=e.pendingLanes;0<i;){var s=31-yt(i),a=1<<s,l=r[s];l===-1?(!(a&n)||a&o)&&(r[s]=Cv(a,t)):l<=t&&(e.expiredLanes|=a),i&=~a}}function Wl(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function ih(){var e=Mr;return Mr<<=1,!(Mr&4194240)&&(Mr=64),e}function Us(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function kr(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-yt(t),e[t]=n}function $v(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var o=e.eventTimes;for(e=e.expirationTimes;0<n;){var r=31-yt(n),i=1<<r;t[r]=0,o[r]=-1,e[r]=-1,n&=~i}}function Qa(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var o=31-yt(n),r=1<<o;r&t|e[o]&t&&(e[o]|=t),n&=~r}}var U=0;function sh(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var lh,qa,ah,ch,uh,Ul=!1,jr=[],Zt=null,Jt=null,en=null,tr=new Map,nr=new Map,qt=[],Pv="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Tu(e,t){switch(e){case"focusin":case"focusout":Zt=null;break;case"dragenter":case"dragleave":Jt=null;break;case"mouseover":case"mouseout":en=null;break;case"pointerover":case"pointerout":tr.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":nr.delete(t.pointerId)}}function $o(e,t,n,o,r,i){return e===null||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:o,nativeEvent:i,targetContainers:[r]},t!==null&&(t=Sr(t),t!==null&&qa(t)),e):(e.eventSystemFlags|=o,t=e.targetContainers,r!==null&&t.indexOf(r)===-1&&t.push(r),e)}function _v(e,t,n,o,r){switch(t){case"focusin":return Zt=$o(Zt,e,t,n,o,r),!0;case"dragenter":return Jt=$o(Jt,e,t,n,o,r),!0;case"mouseover":return en=$o(en,e,t,n,o,r),!0;case"pointerover":var i=r.pointerId;return tr.set(i,$o(tr.get(i)||null,e,t,n,o,r)),!0;case"gotpointercapture":return i=r.pointerId,nr.set(i,$o(nr.get(i)||null,e,t,n,o,r)),!0}return!1}function dh(e){var t=bn(e.target);if(t!==null){var n=An(t);if(n!==null){if(t=n.tag,t===13){if(t=Jf(n),t!==null){e.blockedOn=t,uh(e.priority,function(){ah(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function ii(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Ql(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var o=new n.constructor(n.type,n);jl=o,n.target.dispatchEvent(o),jl=null}else return t=Sr(n),t!==null&&qa(t),e.blockedOn=n,!1;t.shift()}return!0}function $u(e,t,n){ii(e)&&n.delete(t)}function Iv(){Ul=!1,Zt!==null&&ii(Zt)&&(Zt=null),Jt!==null&&ii(Jt)&&(Jt=null),en!==null&&ii(en)&&(en=null),tr.forEach($u),nr.forEach($u)}function Po(e,t){e.blockedOn===t&&(e.blockedOn=null,Ul||(Ul=!0,Ze.unstable_scheduleCallback(Ze.unstable_NormalPriority,Iv)))}function or(e){function t(r){return Po(r,e)}if(0<jr.length){Po(jr[0],e);for(var n=1;n<jr.length;n++){var o=jr[n];o.blockedOn===e&&(o.blockedOn=null)}}for(Zt!==null&&Po(Zt,e),Jt!==null&&Po(Jt,e),en!==null&&Po(en,e),tr.forEach(t),nr.forEach(t),n=0;n<qt.length;n++)o=qt[n],o.blockedOn===e&&(o.blockedOn=null);for(;0<qt.length&&(n=qt[0],n.blockedOn===null);)dh(n),n.blockedOn===null&&qt.shift()}var no=Vt.ReactCurrentBatchConfig,xi=!0;function Rv(e,t,n,o){var r=U,i=no.transition;no.transition=null;try{U=1,Ka(e,t,n,o)}finally{U=r,no.transition=i}}function Lv(e,t,n,o){var r=U,i=no.transition;no.transition=null;try{U=4,Ka(e,t,n,o)}finally{U=r,no.transition=i}}function Ka(e,t,n,o){if(xi){var r=Ql(e,t,n,o);if(r===null)tl(e,t,o,ki,n),Tu(e,o);else if(_v(r,e,t,n,o))o.stopPropagation();else if(Tu(e,o),t&4&&-1<Pv.indexOf(e)){for(;r!==null;){var i=Sr(r);if(i!==null&&lh(i),i=Ql(e,t,n,o),i===null&&tl(e,t,o,ki,n),i===r)break;r=i}r!==null&&o.stopPropagation()}else tl(e,t,o,null,n)}}var ki=null;function Ql(e,t,n,o){if(ki=null,e=Wa(o),e=bn(e),e!==null)if(t=An(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Jf(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return ki=e,null}function fh(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(bv()){case Ua:return 1;case oh:return 4;case bi:case wv:return 16;case rh:return 536870912;default:return 16}default:return 16}}var Yt=null,Ya=null,si=null;function hh(){if(si)return si;var e,t=Ya,n=t.length,o,r="value"in Yt?Yt.value:Yt.textContent,i=r.length;for(e=0;e<n&&t[e]===r[e];e++);var s=n-e;for(o=1;o<=s&&t[n-o]===r[i-o];o++);return si=r.slice(e,1<o?1-o:void 0)}function li(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Hr(){return!0}function Pu(){return!1}function et(e){function t(n,o,r,i,s){this._reactName=n,this._targetInst=r,this.type=o,this.nativeEvent=i,this.target=s,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(i):i[a]);return this.isDefaultPrevented=(i.defaultPrevented!=null?i.defaultPrevented:i.returnValue===!1)?Hr:Pu,this.isPropagationStopped=Pu,this}return oe(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Hr)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Hr)},persist:function(){},isPersistent:Hr}),t}var xo={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Xa=et(xo),Er=oe({},xo,{view:0,detail:0}),Ov=et(Er),Qs,qs,_o,Zi=oe({},Er,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Ga,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==_o&&(_o&&e.type==="mousemove"?(Qs=e.screenX-_o.screenX,qs=e.screenY-_o.screenY):qs=Qs=0,_o=e),Qs)},movementY:function(e){return"movementY"in e?e.movementY:qs}}),_u=et(Zi),Av=oe({},Zi,{dataTransfer:0}),zv=et(Av),Bv=oe({},Er,{relatedTarget:0}),Ks=et(Bv),Dv=oe({},xo,{animationName:0,elapsedTime:0,pseudoElement:0}),Mv=et(Dv),Nv=oe({},xo,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),jv=et(Nv),Hv=oe({},xo,{data:0}),Iu=et(Hv),Vv={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Fv={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Wv={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Uv(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Wv[e])?!!t[e]:!1}function Ga(){return Uv}var Qv=oe({},Er,{key:function(e){if(e.key){var t=Vv[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=li(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Fv[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Ga,charCode:function(e){return e.type==="keypress"?li(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?li(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),qv=et(Qv),Kv=oe({},Zi,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ru=et(Kv),Yv=oe({},Er,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Ga}),Xv=et(Yv),Gv=oe({},xo,{propertyName:0,elapsedTime:0,pseudoElement:0}),Zv=et(Gv),Jv=oe({},Zi,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),e0=et(Jv),t0=[9,13,27,32],Za=Mt&&"CompositionEvent"in window,Wo=null;Mt&&"documentMode"in document&&(Wo=document.documentMode);var n0=Mt&&"TextEvent"in window&&!Wo,ph=Mt&&(!Za||Wo&&8<Wo&&11>=Wo),Lu=" ",Ou=!1;function mh(e,t){switch(e){case"keyup":return t0.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function gh(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Fn=!1;function o0(e,t){switch(e){case"compositionend":return gh(t);case"keypress":return t.which!==32?null:(Ou=!0,Lu);case"textInput":return e=t.data,e===Lu&&Ou?null:e;default:return null}}function r0(e,t){if(Fn)return e==="compositionend"||!Za&&mh(e,t)?(e=hh(),si=Ya=Yt=null,Fn=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return ph&&t.locale!=="ko"?null:t.data;default:return null}}var i0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Au(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!i0[e.type]:t==="textarea"}function vh(e,t,n,o){Kf(o),t=Ei(t,"onChange"),0<t.length&&(n=new Xa("onChange","change",null,n,o),e.push({event:n,listeners:t}))}var Uo=null,rr=null;function s0(e){Ph(e,0)}function Ji(e){var t=Qn(e);if(Hf(t))return e}function l0(e,t){if(e==="change")return t}var yh=!1;if(Mt){var Ys;if(Mt){var Xs="oninput"in document;if(!Xs){var zu=document.createElement("div");zu.setAttribute("oninput","return;"),Xs=typeof zu.oninput=="function"}Ys=Xs}else Ys=!1;yh=Ys&&(!document.documentMode||9<document.documentMode)}function Bu(){Uo&&(Uo.detachEvent("onpropertychange",bh),rr=Uo=null)}function bh(e){if(e.propertyName==="value"&&Ji(rr)){var t=[];vh(t,rr,e,Wa(e)),Zf(s0,t)}}function a0(e,t,n){e==="focusin"?(Bu(),Uo=t,rr=n,Uo.attachEvent("onpropertychange",bh)):e==="focusout"&&Bu()}function c0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ji(rr)}function u0(e,t){if(e==="click")return Ji(t)}function d0(e,t){if(e==="input"||e==="change")return Ji(t)}function f0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var xt=typeof Object.is=="function"?Object.is:f0;function ir(e,t){if(xt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),o=Object.keys(t);if(n.length!==o.length)return!1;for(o=0;o<n.length;o++){var r=n[o];if(!Pl.call(t,r)||!xt(e[r],t[r]))return!1}return!0}function Du(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Mu(e,t){var n=Du(e);e=0;for(var o;n;){if(n.nodeType===3){if(o=e+n.textContent.length,e<=t&&o>=t)return{node:n,offset:t-e};e=o}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=Du(n)}}function wh(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?wh(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function xh(){for(var e=window,t=gi();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch(o){n=!1}if(n)e=t.contentWindow;else break;t=gi(e.document)}return t}function Ja(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function h0(e){var t=xh(),n=e.focusedElem,o=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&wh(n.ownerDocument.documentElement,n)){if(o!==null&&Ja(n)){if(t=o.start,e=o.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var r=n.textContent.length,i=Math.min(o.start,r);o=o.end===void 0?i:Math.min(o.end,r),!e.extend&&i>o&&(r=o,o=i,i=r),r=Mu(n,i);var s=Mu(n,o);r&&s&&(e.rangeCount!==1||e.anchorNode!==r.node||e.anchorOffset!==r.offset||e.focusNode!==s.node||e.focusOffset!==s.offset)&&(t=t.createRange(),t.setStart(r.node,r.offset),e.removeAllRanges(),i>o?(e.addRange(t),e.extend(s.node,s.offset)):(t.setEnd(s.node,s.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var p0=Mt&&"documentMode"in document&&11>=document.documentMode,Wn=null,ql=null,Qo=null,Kl=!1;function Nu(e,t,n){var o=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Kl||Wn==null||Wn!==gi(o)||(o=Wn,"selectionStart"in o&&Ja(o)?o={start:o.selectionStart,end:o.selectionEnd}:(o=(o.ownerDocument&&o.ownerDocument.defaultView||window).getSelection(),o={anchorNode:o.anchorNode,anchorOffset:o.anchorOffset,focusNode:o.focusNode,focusOffset:o.focusOffset}),Qo&&ir(Qo,o)||(Qo=o,o=Ei(ql,"onSelect"),0<o.length&&(t=new Xa("onSelect","select",null,t,n),e.push({event:t,listeners:o}),t.target=Wn)))}function Vr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Un={animationend:Vr("Animation","AnimationEnd"),animationiteration:Vr("Animation","AnimationIteration"),animationstart:Vr("Animation","AnimationStart"),transitionend:Vr("Transition","TransitionEnd")},Gs={},kh={};Mt&&(kh=document.createElement("div").style,"AnimationEvent"in window||(delete Un.animationend.animation,delete Un.animationiteration.animation,delete Un.animationstart.animation),"TransitionEvent"in window||delete Un.transitionend.transition);function es(e){if(Gs[e])return Gs[e];if(!Un[e])return e;var t=Un[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in kh)return Gs[e]=t[n];return e}var Eh=es("animationend"),Sh=es("animationiteration"),Ch=es("animationstart"),Th=es("transitionend"),$h=new Map,ju="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function fn(e,t){$h.set(e,t),On(t,[e])}for(var Zs=0;Zs<ju.length;Zs++){var Js=ju[Zs],m0=Js.toLowerCase(),g0=Js[0].toUpperCase()+Js.slice(1);fn(m0,"on"+g0)}fn(Eh,"onAnimationEnd");fn(Sh,"onAnimationIteration");fn(Ch,"onAnimationStart");fn("dblclick","onDoubleClick");fn("focusin","onFocus");fn("focusout","onBlur");fn(Th,"onTransitionEnd");lo("onMouseEnter",["mouseout","mouseover"]);lo("onMouseLeave",["mouseout","mouseover"]);lo("onPointerEnter",["pointerout","pointerover"]);lo("onPointerLeave",["pointerout","pointerover"]);On("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));On("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));On("onBeforeInput",["compositionend","keypress","textInput","paste"]);On("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));On("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));On("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Mo="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),v0=new Set("cancel close invalid load scroll toggle".split(" ").concat(Mo));function Hu(e,t,n){var o=e.type||"unknown-event";e.currentTarget=n,mv(o,t,void 0,e),e.currentTarget=null}function Ph(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var o=e[n],r=o.event;o=o.listeners;e:{var i=void 0;if(t)for(var s=o.length-1;0<=s;s--){var a=o[s],l=a.instance,c=a.currentTarget;if(a=a.listener,l!==i&&r.isPropagationStopped())break e;Hu(r,a,c),i=l}else for(s=0;s<o.length;s++){if(a=o[s],l=a.instance,c=a.currentTarget,a=a.listener,l!==i&&r.isPropagationStopped())break e;Hu(r,a,c),i=l}}}if(yi)throw e=Fl,yi=!1,Fl=null,e}function X(e,t){var n=t[Jl];n===void 0&&(n=t[Jl]=new Set);var o=e+"__bubble";n.has(o)||(_h(t,e,2,!1),n.add(o))}function el(e,t,n){var o=0;t&&(o|=4),_h(n,e,o,t)}var Fr="_reactListening"+Math.random().toString(36).slice(2);function sr(e){if(!e[Fr]){e[Fr]=!0,Bf.forEach(function(n){n!=="selectionchange"&&(v0.has(n)||el(n,!1,e),el(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[Fr]||(t[Fr]=!0,el("selectionchange",!1,t))}}function _h(e,t,n,o){switch(fh(t)){case 1:var r=Rv;break;case 4:r=Lv;break;default:r=Ka}n=r.bind(null,t,n,e),r=void 0,!Vl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(r=!0),o?r!==void 0?e.addEventListener(t,n,{capture:!0,passive:r}):e.addEventListener(t,n,!0):r!==void 0?e.addEventListener(t,n,{passive:r}):e.addEventListener(t,n,!1)}function tl(e,t,n,o,r){var i=o;if(!(t&1)&&!(t&2)&&o!==null)e:for(;;){if(o===null)return;var s=o.tag;if(s===3||s===4){var a=o.stateNode.containerInfo;if(a===r||a.nodeType===8&&a.parentNode===r)break;if(s===4)for(s=o.return;s!==null;){var l=s.tag;if((l===3||l===4)&&(l=s.stateNode.containerInfo,l===r||l.nodeType===8&&l.parentNode===r))return;s=s.return}for(;a!==null;){if(s=bn(a),s===null)return;if(l=s.tag,l===5||l===6){o=i=s;continue e}a=a.parentNode}}o=o.return}Zf(function(){var c=i,u=Wa(n),d=[];e:{var f=$h.get(e);if(f!==void 0){var p=Xa,v=e;switch(e){case"keypress":if(li(n)===0)break e;case"keydown":case"keyup":p=qv;break;case"focusin":v="focus",p=Ks;break;case"focusout":v="blur",p=Ks;break;case"beforeblur":case"afterblur":p=Ks;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":p=_u;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":p=zv;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":p=Xv;break;case Eh:case Sh:case Ch:p=Mv;break;case Th:p=Zv;break;case"scroll":p=Ov;break;case"wheel":p=e0;break;case"copy":case"cut":case"paste":p=jv;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":p=Ru}var x=(t&4)!==0,R=!x&&e==="scroll",m=x?f!==null?f+"Capture":null:f;x=[];for(var h=c,g;h!==null;){g=h;var w=g.stateNode;if(g.tag===5&&w!==null&&(g=w,m!==null&&(w=er(h,m),w!=null&&x.push(lr(h,w,g)))),R)break;h=h.return}0<x.length&&(f=new p(f,v,null,n,u),d.push({event:f,listeners:x}))}}if(!(t&7)){e:{if(f=e==="mouseover"||e==="pointerover",p=e==="mouseout"||e==="pointerout",f&&n!==jl&&(v=n.relatedTarget||n.fromElement)&&(bn(v)||v[Nt]))break e;if((p||f)&&(f=u.window===u?u:(f=u.ownerDocument)?f.defaultView||f.parentWindow:window,p?(v=n.relatedTarget||n.toElement,p=c,v=v?bn(v):null,v!==null&&(R=An(v),v!==R||v.tag!==5&&v.tag!==6)&&(v=null)):(p=null,v=c),p!==v)){if(x=_u,w="onMouseLeave",m="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(x=Ru,w="onPointerLeave",m="onPointerEnter",h="pointer"),R=p==null?f:Qn(p),g=v==null?f:Qn(v),f=new x(w,h+"leave",p,n,u),f.target=R,f.relatedTarget=g,w=null,bn(u)===c&&(x=new x(m,h+"enter",v,n,u),x.target=g,x.relatedTarget=R,w=x),R=w,p&&v)t:{for(x=p,m=v,h=0,g=x;g;g=Dn(g))h++;for(g=0,w=m;w;w=Dn(w))g++;for(;0<h-g;)x=Dn(x),h--;for(;0<g-h;)m=Dn(m),g--;for(;h--;){if(x===m||m!==null&&x===m.alternate)break t;x=Dn(x),m=Dn(m)}x=null}else x=null;p!==null&&Vu(d,f,p,x,!1),v!==null&&R!==null&&Vu(d,R,v,x,!0)}}e:{if(f=c?Qn(c):window,p=f.nodeName&&f.nodeName.toLowerCase(),p==="select"||p==="input"&&f.type==="file")var T=l0;else if(Au(f))if(yh)T=d0;else{T=c0;var P=a0}else(p=f.nodeName)&&p.toLowerCase()==="input"&&(f.type==="checkbox"||f.type==="radio")&&(T=u0);if(T&&(T=T(e,c))){vh(d,T,n,u);break e}P&&P(e,f,c),e==="focusout"&&(P=f._wrapperState)&&P.controlled&&f.type==="number"&&zl(f,"number",f.value)}switch(P=c?Qn(c):window,e){case"focusin":(Au(P)||P.contentEditable==="true")&&(Wn=P,ql=c,Qo=null);break;case"focusout":Qo=ql=Wn=null;break;case"mousedown":Kl=!0;break;case"contextmenu":case"mouseup":case"dragend":Kl=!1,Nu(d,n,u);break;case"selectionchange":if(p0)break;case"keydown":case"keyup":Nu(d,n,u)}var k;if(Za)e:{switch(e){case"compositionstart":var $="onCompositionStart";break e;case"compositionend":$="onCompositionEnd";break e;case"compositionupdate":$="onCompositionUpdate";break e}$=void 0}else Fn?mh(e,n)&&($="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&($="onCompositionStart");$&&(ph&&n.locale!=="ko"&&(Fn||$!=="onCompositionStart"?$==="onCompositionEnd"&&Fn&&(k=hh()):(Yt=u,Ya="value"in Yt?Yt.value:Yt.textContent,Fn=!0)),P=Ei(c,$),0<P.length&&($=new Iu($,e,null,n,u),d.push({event:$,listeners:P}),k?$.data=k:(k=gh(n),k!==null&&($.data=k)))),(k=n0?o0(e,n):r0(e,n))&&(c=Ei(c,"onBeforeInput"),0<c.length&&(u=new Iu("onBeforeInput","beforeinput",null,n,u),d.push({event:u,listeners:c}),u.data=k))}Ph(d,t)})}function lr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Ei(e,t){for(var n=t+"Capture",o=[];e!==null;){var r=e,i=r.stateNode;r.tag===5&&i!==null&&(r=i,i=er(e,n),i!=null&&o.unshift(lr(e,i,r)),i=er(e,t),i!=null&&o.push(lr(e,i,r))),e=e.return}return o}function Dn(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Vu(e,t,n,o,r){for(var i=t._reactName,s=[];n!==null&&n!==o;){var a=n,l=a.alternate,c=a.stateNode;if(l!==null&&l===o)break;a.tag===5&&c!==null&&(a=c,r?(l=er(n,i),l!=null&&s.unshift(lr(n,l,a))):r||(l=er(n,i),l!=null&&s.push(lr(n,l,a)))),n=n.return}s.length!==0&&e.push({event:t,listeners:s})}var y0=/\r\n?/g,b0=/\u0000|\uFFFD/g;function Fu(e){return(typeof e=="string"?e:""+e).replace(y0,"\n").replace(b0,"")}function Wr(e,t,n){if(t=Fu(t),Fu(e)!==t&&n)throw Error(S(425))}function Si(){}var Yl=null,Xl=null;function Gl(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Zl=typeof setTimeout=="function"?setTimeout:void 0,w0=typeof clearTimeout=="function"?clearTimeout:void 0,Wu=typeof Promise=="function"?Promise:void 0,x0=typeof queueMicrotask=="function"?queueMicrotask:typeof Wu<"u"?function(e){return Wu.resolve(null).then(e).catch(k0)}:Zl;function k0(e){setTimeout(function(){throw e})}function nl(e,t){var n=t,o=0;do{var r=n.nextSibling;if(e.removeChild(n),r&&r.nodeType===8)if(n=r.data,n==="/$"){if(o===0){e.removeChild(r),or(t);return}o--}else n!=="$"&&n!=="$?"&&n!=="$!"||o++;n=r}while(n);or(t)}function tn(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Uu(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var ko=Math.random().toString(36).slice(2),$t="__reactFiber$"+ko,ar="__reactProps$"+ko,Nt="__reactContainer$"+ko,Jl="__reactEvents$"+ko,E0="__reactListeners$"+ko,S0="__reactHandles$"+ko;function bn(e){var t=e[$t];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Nt]||n[$t]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Uu(e);e!==null;){if(n=e[$t])return n;e=Uu(e)}return t}e=n,n=e.parentNode}return null}function Sr(e){return e=e[$t]||e[Nt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Qn(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(S(33))}function ts(e){return e[ar]||null}var ea=[],qn=-1;function hn(e){return{current:e}}function Z(e){0>qn||(e.current=ea[qn],ea[qn]=null,qn--)}function Y(e,t){qn++,ea[qn]=e.current,e.current=t}var cn={},_e=hn(cn),je=hn(!1),Tn=cn;function ao(e,t){var n=e.type.contextTypes;if(!n)return cn;var o=e.stateNode;if(o&&o.__reactInternalMemoizedUnmaskedChildContext===t)return o.__reactInternalMemoizedMaskedChildContext;var r={},i;for(i in n)r[i]=t[i];return o&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=r),r}function He(e){return e=e.childContextTypes,e!=null}function Ci(){Z(je),Z(_e)}function Qu(e,t,n){if(_e.current!==cn)throw Error(S(168));Y(_e,t),Y(je,n)}function Ih(e,t,n){var o=e.stateNode;if(t=t.childContextTypes,typeof o.getChildContext!="function")return n;o=o.getChildContext();for(var r in o)if(!(r in t))throw Error(S(108,av(e)||"Unknown",r));return oe({},n,o)}function Ti(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||cn,Tn=_e.current,Y(_e,e),Y(je,je.current),!0}function qu(e,t,n){var o=e.stateNode;if(!o)throw Error(S(169));n?(e=Ih(e,t,Tn),o.__reactInternalMemoizedMergedChildContext=e,Z(je),Z(_e),Y(_e,e)):Z(je),Y(je,n)}var Lt=null,ns=!1,ol=!1;function Rh(e){Lt===null?Lt=[e]:Lt.push(e)}function C0(e){ns=!0,Rh(e)}function pn(){if(!ol&&Lt!==null){ol=!0;var e=0,t=U;try{var n=Lt;for(U=1;e<n.length;e++){var o=n[e];do o=o(!0);while(o!==null)}Lt=null,ns=!1}catch(r){throw Lt!==null&&(Lt=Lt.slice(e+1)),nh(Ua,pn),r}finally{U=t,ol=!1}}return null}var Kn=[],Yn=0,$i=null,Pi=0,lt=[],at=0,$n=null,zt=1,Bt="";function vn(e,t){Kn[Yn++]=Pi,Kn[Yn++]=$i,$i=e,Pi=t}function Lh(e,t,n){lt[at++]=zt,lt[at++]=Bt,lt[at++]=$n,$n=e;var o=zt;e=Bt;var r=32-yt(o)-1;o&=~(1<<r),n+=1;var i=32-yt(t)+r;if(30<i){var s=r-r%5;i=(o&(1<<s)-1).toString(32),o>>=s,r-=s,zt=1<<32-yt(t)+r|n<<r|o,Bt=i+e}else zt=1<<i|n<<r|o,Bt=e}function ec(e){e.return!==null&&(vn(e,1),Lh(e,1,0))}function tc(e){for(;e===$i;)$i=Kn[--Yn],Kn[Yn]=null,Pi=Kn[--Yn],Kn[Yn]=null;for(;e===$n;)$n=lt[--at],lt[at]=null,Bt=lt[--at],lt[at]=null,zt=lt[--at],lt[at]=null}var Ge=null,Ye=null,ee=!1,vt=null;function Oh(e,t){var n=ut(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Ku(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,Ge=e,Ye=tn(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,Ge=e,Ye=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=$n!==null?{id:zt,overflow:Bt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=ut(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,Ge=e,Ye=null,!0):!1;default:return!1}}function ta(e){return(e.mode&1)!==0&&(e.flags&128)===0}function na(e){if(ee){var t=Ye;if(t){var n=t;if(!Ku(e,t)){if(ta(e))throw Error(S(418));t=tn(n.nextSibling);var o=Ge;t&&Ku(e,t)?Oh(o,n):(e.flags=e.flags&-4097|2,ee=!1,Ge=e)}}else{if(ta(e))throw Error(S(418));e.flags=e.flags&-4097|2,ee=!1,Ge=e}}}function Yu(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;Ge=e}function Ur(e){if(e!==Ge)return!1;if(!ee)return Yu(e),ee=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Gl(e.type,e.memoizedProps)),t&&(t=Ye)){if(ta(e))throw Ah(),Error(S(418));for(;t;)Oh(e,t),t=tn(t.nextSibling)}if(Yu(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(S(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ye=tn(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ye=null}}else Ye=Ge?tn(e.stateNode.nextSibling):null;return!0}function Ah(){for(var e=Ye;e;)e=tn(e.nextSibling)}function co(){Ye=Ge=null,ee=!1}function nc(e){vt===null?vt=[e]:vt.push(e)}var T0=Vt.ReactCurrentBatchConfig;function Io(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(S(309));var o=n.stateNode}if(!o)throw Error(S(147,e));var r=o,i=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===i?t.ref:(t=function(s){var a=r.refs;s===null?delete a[i]:a[i]=s},t._stringRef=i,t)}if(typeof e!="string")throw Error(S(284));if(!n._owner)throw Error(S(290,e))}return e}function Qr(e,t){throw e=Object.prototype.toString.call(t),Error(S(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Xu(e){var t=e._init;return t(e._payload)}function zh(e){function t(m,h){if(e){var g=m.deletions;g===null?(m.deletions=[h],m.flags|=16):g.push(h)}}function n(m,h){if(!e)return null;for(;h!==null;)t(m,h),h=h.sibling;return null}function o(m,h){for(m=new Map;h!==null;)h.key!==null?m.set(h.key,h):m.set(h.index,h),h=h.sibling;return m}function r(m,h){return m=sn(m,h),m.index=0,m.sibling=null,m}function i(m,h,g){return m.index=g,e?(g=m.alternate,g!==null?(g=g.index,g<h?(m.flags|=2,h):g):(m.flags|=2,h)):(m.flags|=1048576,h)}function s(m){return e&&m.alternate===null&&(m.flags|=2),m}function a(m,h,g,w){return h===null||h.tag!==6?(h=ul(g,m.mode,w),h.return=m,h):(h=r(h,g),h.return=m,h)}function l(m,h,g,w){var T=g.type;return T===Vn?u(m,h,g.props.children,w,g.key):h!==null&&(h.elementType===T||typeof T=="object"&&T!==null&&T.$$typeof===Ut&&Xu(T)===h.type)?(w=r(h,g.props),w.ref=Io(m,h,g),w.return=m,w):(w=pi(g.type,g.key,g.props,null,m.mode,w),w.ref=Io(m,h,g),w.return=m,w)}function c(m,h,g,w){return h===null||h.tag!==4||h.stateNode.containerInfo!==g.containerInfo||h.stateNode.implementation!==g.implementation?(h=dl(g,m.mode,w),h.return=m,h):(h=r(h,g.children||[]),h.return=m,h)}function u(m,h,g,w,T){return h===null||h.tag!==7?(h=Cn(g,m.mode,w,T),h.return=m,h):(h=r(h,g),h.return=m,h)}function d(m,h,g){if(typeof h=="string"&&h!==""||typeof h=="number")return h=ul(""+h,m.mode,g),h.return=m,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case zr:return g=pi(h.type,h.key,h.props,null,m.mode,g),g.ref=Io(m,null,h),g.return=m,g;case Hn:return h=dl(h,m.mode,g),h.return=m,h;case Ut:var w=h._init;return d(m,w(h._payload),g)}if(Bo(h)||Co(h))return h=Cn(h,m.mode,g,null),h.return=m,h;Qr(m,h)}return null}function f(m,h,g,w){var T=h!==null?h.key:null;if(typeof g=="string"&&g!==""||typeof g=="number")return T!==null?null:a(m,h,""+g,w);if(typeof g=="object"&&g!==null){switch(g.$$typeof){case zr:return g.key===T?l(m,h,g,w):null;case Hn:return g.key===T?c(m,h,g,w):null;case Ut:return T=g._init,f(m,h,T(g._payload),w)}if(Bo(g)||Co(g))return T!==null?null:u(m,h,g,w,null);Qr(m,g)}return null}function p(m,h,g,w,T){if(typeof w=="string"&&w!==""||typeof w=="number")return m=m.get(g)||null,a(h,m,""+w,T);if(typeof w=="object"&&w!==null){switch(w.$$typeof){case zr:return m=m.get(w.key===null?g:w.key)||null,l(h,m,w,T);case Hn:return m=m.get(w.key===null?g:w.key)||null,c(h,m,w,T);case Ut:var P=w._init;return p(m,h,g,P(w._payload),T)}if(Bo(w)||Co(w))return m=m.get(g)||null,u(h,m,w,T,null);Qr(h,w)}return null}function v(m,h,g,w){for(var T=null,P=null,k=h,$=h=0,I=null;k!==null&&$<g.length;$++){k.index>$?(I=k,k=null):I=k.sibling;var B=f(m,k,g[$],w);if(B===null){k===null&&(k=I);break}e&&k&&B.alternate===null&&t(m,k),h=i(B,h,$),P===null?T=B:P.sibling=B,P=B,k=I}if($===g.length)return n(m,k),ee&&vn(m,$),T;if(k===null){for(;$<g.length;$++)k=d(m,g[$],w),k!==null&&(h=i(k,h,$),P===null?T=k:P.sibling=k,P=k);return ee&&vn(m,$),T}for(k=o(m,k);$<g.length;$++)I=p(k,m,$,g[$],w),I!==null&&(e&&I.alternate!==null&&k.delete(I.key===null?$:I.key),h=i(I,h,$),P===null?T=I:P.sibling=I,P=I);return e&&k.forEach(function(V){return t(m,V)}),ee&&vn(m,$),T}function x(m,h,g,w){var T=Co(g);if(typeof T!="function")throw Error(S(150));if(g=T.call(g),g==null)throw Error(S(151));for(var P=T=null,k=h,$=h=0,I=null,B=g.next();k!==null&&!B.done;$++,B=g.next()){k.index>$?(I=k,k=null):I=k.sibling;var V=f(m,k,B.value,w);if(V===null){k===null&&(k=I);break}e&&k&&V.alternate===null&&t(m,k),h=i(V,h,$),P===null?T=V:P.sibling=V,P=V,k=I}if(B.done)return n(m,k),ee&&vn(m,$),T;if(k===null){for(;!B.done;$++,B=g.next())B=d(m,B.value,w),B!==null&&(h=i(B,h,$),P===null?T=B:P.sibling=B,P=B);return ee&&vn(m,$),T}for(k=o(m,k);!B.done;$++,B=g.next())B=p(k,m,$,B.value,w),B!==null&&(e&&B.alternate!==null&&k.delete(B.key===null?$:B.key),h=i(B,h,$),P===null?T=B:P.sibling=B,P=B);return e&&k.forEach(function(de){return t(m,de)}),ee&&vn(m,$),T}function R(m,h,g,w){if(typeof g=="object"&&g!==null&&g.type===Vn&&g.key===null&&(g=g.props.children),typeof g=="object"&&g!==null){switch(g.$$typeof){case zr:e:{for(var T=g.key,P=h;P!==null;){if(P.key===T){if(T=g.type,T===Vn){if(P.tag===7){n(m,P.sibling),h=r(P,g.props.children),h.return=m,m=h;break e}}else if(P.elementType===T||typeof T=="object"&&T!==null&&T.$$typeof===Ut&&Xu(T)===P.type){n(m,P.sibling),h=r(P,g.props),h.ref=Io(m,P,g),h.return=m,m=h;break e}n(m,P);break}else t(m,P);P=P.sibling}g.type===Vn?(h=Cn(g.props.children,m.mode,w,g.key),h.return=m,m=h):(w=pi(g.type,g.key,g.props,null,m.mode,w),w.ref=Io(m,h,g),w.return=m,m=w)}return s(m);case Hn:e:{for(P=g.key;h!==null;){if(h.key===P)if(h.tag===4&&h.stateNode.containerInfo===g.containerInfo&&h.stateNode.implementation===g.implementation){n(m,h.sibling),h=r(h,g.children||[]),h.return=m,m=h;break e}else{n(m,h);break}else t(m,h);h=h.sibling}h=dl(g,m.mode,w),h.return=m,m=h}return s(m);case Ut:return P=g._init,R(m,h,P(g._payload),w)}if(Bo(g))return v(m,h,g,w);if(Co(g))return x(m,h,g,w);Qr(m,g)}return typeof g=="string"&&g!==""||typeof g=="number"?(g=""+g,h!==null&&h.tag===6?(n(m,h.sibling),h=r(h,g),h.return=m,m=h):(n(m,h),h=ul(g,m.mode,w),h.return=m,m=h),s(m)):n(m,h)}return R}var uo=zh(!0),Bh=zh(!1),_i=hn(null),Ii=null,Xn=null,oc=null;function rc(){oc=Xn=Ii=null}function ic(e){var t=_i.current;Z(_i),e._currentValue=t}function oa(e,t,n){for(;e!==null;){var o=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,o!==null&&(o.childLanes|=t)):o!==null&&(o.childLanes&t)!==t&&(o.childLanes|=t),e===n)break;e=e.return}}function oo(e,t){Ii=e,oc=Xn=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Me=!0),e.firstContext=null)}function ft(e){var t=e._currentValue;if(oc!==e)if(e={context:e,memoizedValue:t,next:null},Xn===null){if(Ii===null)throw Error(S(308));Xn=e,Ii.dependencies={lanes:0,firstContext:e}}else Xn=Xn.next=e;return t}var wn=null;function sc(e){wn===null?wn=[e]:wn.push(e)}function Dh(e,t,n,o){var r=t.interleaved;return r===null?(n.next=n,sc(t)):(n.next=r.next,r.next=n),t.interleaved=n,jt(e,o)}function jt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var Qt=!1;function lc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Mh(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Dt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function nn(e,t,n){var o=e.updateQueue;if(o===null)return null;if(o=o.shared,F&2){var r=o.pending;return r===null?t.next=t:(t.next=r.next,r.next=t),o.pending=t,jt(e,n)}return r=o.interleaved,r===null?(t.next=t,sc(o)):(t.next=r.next,r.next=t),o.interleaved=t,jt(e,n)}function ai(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var o=t.lanes;o&=e.pendingLanes,n|=o,t.lanes=n,Qa(e,n)}}function Gu(e,t){var n=e.updateQueue,o=e.alternate;if(o!==null&&(o=o.updateQueue,n===o)){var r=null,i=null;if(n=n.firstBaseUpdate,n!==null){do{var s={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};i===null?r=i=s:i=i.next=s,n=n.next}while(n!==null);i===null?r=i=t:i=i.next=t}else r=i=t;n={baseState:o.baseState,firstBaseUpdate:r,lastBaseUpdate:i,shared:o.shared,effects:o.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Ri(e,t,n,o){var r=e.updateQueue;Qt=!1;var i=r.firstBaseUpdate,s=r.lastBaseUpdate,a=r.shared.pending;if(a!==null){r.shared.pending=null;var l=a,c=l.next;l.next=null,s===null?i=c:s.next=c,s=l;var u=e.alternate;u!==null&&(u=u.updateQueue,a=u.lastBaseUpdate,a!==s&&(a===null?u.firstBaseUpdate=c:a.next=c,u.lastBaseUpdate=l))}if(i!==null){var d=r.baseState;s=0,u=c=l=null,a=i;do{var f=a.lane,p=a.eventTime;if((o&f)===f){u!==null&&(u=u.next={eventTime:p,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var v=e,x=a;switch(f=t,p=n,x.tag){case 1:if(v=x.payload,typeof v=="function"){d=v.call(p,d,f);break e}d=v;break e;case 3:v.flags=v.flags&-65537|128;case 0:if(v=x.payload,f=typeof v=="function"?v.call(p,d,f):v,f==null)break e;d=oe({},d,f);break e;case 2:Qt=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,f=r.effects,f===null?r.effects=[a]:f.push(a))}else p={eventTime:p,lane:f,tag:a.tag,payload:a.payload,callback:a.callback,next:null},u===null?(c=u=p,l=d):u=u.next=p,s|=f;if(a=a.next,a===null){if(a=r.shared.pending,a===null)break;f=a,a=f.next,f.next=null,r.lastBaseUpdate=f,r.shared.pending=null}}while(!0);if(u===null&&(l=d),r.baseState=l,r.firstBaseUpdate=c,r.lastBaseUpdate=u,t=r.shared.interleaved,t!==null){r=t;do s|=r.lane,r=r.next;while(r!==t)}else i===null&&(r.shared.lanes=0);_n|=s,e.lanes=s,e.memoizedState=d}}function Zu(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var o=e[t],r=o.callback;if(r!==null){if(o.callback=null,o=n,typeof r!="function")throw Error(S(191,r));r.call(o)}}}var Cr={},_t=hn(Cr),cr=hn(Cr),ur=hn(Cr);function xn(e){if(e===Cr)throw Error(S(174));return e}function ac(e,t){switch(Y(ur,t),Y(cr,e),Y(_t,Cr),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Dl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Dl(t,e)}Z(_t),Y(_t,t)}function fo(){Z(_t),Z(cr),Z(ur)}function Nh(e){xn(ur.current);var t=xn(_t.current),n=Dl(t,e.type);t!==n&&(Y(cr,e),Y(_t,n))}function cc(e){cr.current===e&&(Z(_t),Z(cr))}var te=hn(0);function Li(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var rl=[];function uc(){for(var e=0;e<rl.length;e++)rl[e]._workInProgressVersionPrimary=null;rl.length=0}var ci=Vt.ReactCurrentDispatcher,il=Vt.ReactCurrentBatchConfig,Pn=0,ne=null,me=null,ve=null,Oi=!1,qo=!1,dr=0,$0=0;function Te(){throw Error(S(321))}function dc(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!xt(e[n],t[n]))return!1;return!0}function fc(e,t,n,o,r,i){if(Pn=i,ne=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ci.current=e===null||e.memoizedState===null?R0:L0,e=n(o,r),qo){i=0;do{if(qo=!1,dr=0,25<=i)throw Error(S(301));i+=1,ve=me=null,t.updateQueue=null,ci.current=O0,e=n(o,r)}while(qo)}if(ci.current=Ai,t=me!==null&&me.next!==null,Pn=0,ve=me=ne=null,Oi=!1,t)throw Error(S(300));return e}function hc(){var e=dr!==0;return dr=0,e}function Ct(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ve===null?ne.memoizedState=ve=e:ve=ve.next=e,ve}function ht(){if(me===null){var e=ne.alternate;e=e!==null?e.memoizedState:null}else e=me.next;var t=ve===null?ne.memoizedState:ve.next;if(t!==null)ve=t,me=e;else{if(e===null)throw Error(S(310));me=e,e={memoizedState:me.memoizedState,baseState:me.baseState,baseQueue:me.baseQueue,queue:me.queue,next:null},ve===null?ne.memoizedState=ve=e:ve=ve.next=e}return ve}function fr(e,t){return typeof t=="function"?t(e):t}function sl(e){var t=ht(),n=t.queue;if(n===null)throw Error(S(311));n.lastRenderedReducer=e;var o=me,r=o.baseQueue,i=n.pending;if(i!==null){if(r!==null){var s=r.next;r.next=i.next,i.next=s}o.baseQueue=r=i,n.pending=null}if(r!==null){i=r.next,o=o.baseState;var a=s=null,l=null,c=i;do{var u=c.lane;if((Pn&u)===u)l!==null&&(l=l.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),o=c.hasEagerState?c.eagerState:e(o,c.action);else{var d={lane:u,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};l===null?(a=l=d,s=o):l=l.next=d,ne.lanes|=u,_n|=u}c=c.next}while(c!==null&&c!==i);l===null?s=o:l.next=a,xt(o,t.memoizedState)||(Me=!0),t.memoizedState=o,t.baseState=s,t.baseQueue=l,n.lastRenderedState=o}if(e=n.interleaved,e!==null){r=e;do i=r.lane,ne.lanes|=i,_n|=i,r=r.next;while(r!==e)}else r===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ll(e){var t=ht(),n=t.queue;if(n===null)throw Error(S(311));n.lastRenderedReducer=e;var o=n.dispatch,r=n.pending,i=t.memoizedState;if(r!==null){n.pending=null;var s=r=r.next;do i=e(i,s.action),s=s.next;while(s!==r);xt(i,t.memoizedState)||(Me=!0),t.memoizedState=i,t.baseQueue===null&&(t.baseState=i),n.lastRenderedState=i}return[i,o]}function jh(){}function Hh(e,t){var n=ne,o=ht(),r=t(),i=!xt(o.memoizedState,r);if(i&&(o.memoizedState=r,Me=!0),o=o.queue,pc(Wh.bind(null,n,o,e),[e]),o.getSnapshot!==t||i||ve!==null&&ve.memoizedState.tag&1){if(n.flags|=2048,hr(9,Fh.bind(null,n,o,r,t),void 0,null),ye===null)throw Error(S(349));Pn&30||Vh(n,t,r)}return r}function Vh(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ne.updateQueue,t===null?(t={lastEffect:null,stores:null},ne.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Fh(e,t,n,o){t.value=n,t.getSnapshot=o,Uh(t)&&Qh(e)}function Wh(e,t,n){return n(function(){Uh(t)&&Qh(e)})}function Uh(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!xt(e,n)}catch(o){return!0}}function Qh(e){var t=jt(e,1);t!==null&&bt(t,e,1,-1)}function Ju(e){var t=Ct();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:fr,lastRenderedState:e},t.queue=e,e=e.dispatch=I0.bind(null,ne,e),[t.memoizedState,e]}function hr(e,t,n,o){return e={tag:e,create:t,destroy:n,deps:o,next:null},t=ne.updateQueue,t===null?(t={lastEffect:null,stores:null},ne.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(o=n.next,n.next=e,e.next=o,t.lastEffect=e)),e}function qh(){return ht().memoizedState}function ui(e,t,n,o){var r=Ct();ne.flags|=e,r.memoizedState=hr(1|t,n,void 0,o===void 0?null:o)}function os(e,t,n,o){var r=ht();o=o===void 0?null:o;var i=void 0;if(me!==null){var s=me.memoizedState;if(i=s.destroy,o!==null&&dc(o,s.deps)){r.memoizedState=hr(t,n,i,o);return}}ne.flags|=e,r.memoizedState=hr(1|t,n,i,o)}function ed(e,t){return ui(8390656,8,e,t)}function pc(e,t){return os(2048,8,e,t)}function Kh(e,t){return os(4,2,e,t)}function Yh(e,t){return os(4,4,e,t)}function Xh(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Gh(e,t,n){return n=n!=null?n.concat([e]):null,os(4,4,Xh.bind(null,t,e),n)}function mc(){}function Zh(e,t){var n=ht();t=t===void 0?null:t;var o=n.memoizedState;return o!==null&&t!==null&&dc(t,o[1])?o[0]:(n.memoizedState=[e,t],e)}function Jh(e,t){var n=ht();t=t===void 0?null:t;var o=n.memoizedState;return o!==null&&t!==null&&dc(t,o[1])?o[0]:(e=e(),n.memoizedState=[e,t],e)}function ep(e,t,n){return Pn&21?(xt(n,t)||(n=ih(),ne.lanes|=n,_n|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Me=!0),e.memoizedState=n)}function P0(e,t){var n=U;U=n!==0&&4>n?n:4,e(!0);var o=il.transition;il.transition={};try{e(!1),t()}finally{U=n,il.transition=o}}function tp(){return ht().memoizedState}function _0(e,t,n){var o=rn(e);if(n={lane:o,action:n,hasEagerState:!1,eagerState:null,next:null},np(e))op(t,n);else if(n=Dh(e,t,n,o),n!==null){var r=Le();bt(n,e,o,r),rp(n,t,o)}}function I0(e,t,n){var o=rn(e),r={lane:o,action:n,hasEagerState:!1,eagerState:null,next:null};if(np(e))op(t,r);else{var i=e.alternate;if(e.lanes===0&&(i===null||i.lanes===0)&&(i=t.lastRenderedReducer,i!==null))try{var s=t.lastRenderedState,a=i(s,n);if(r.hasEagerState=!0,r.eagerState=a,xt(a,s)){var l=t.interleaved;l===null?(r.next=r,sc(t)):(r.next=l.next,l.next=r),t.interleaved=r;return}}catch(c){}finally{}n=Dh(e,t,r,o),n!==null&&(r=Le(),bt(n,e,o,r),rp(n,t,o))}}function np(e){var t=e.alternate;return e===ne||t!==null&&t===ne}function op(e,t){qo=Oi=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function rp(e,t,n){if(n&4194240){var o=t.lanes;o&=e.pendingLanes,n|=o,t.lanes=n,Qa(e,n)}}var Ai={readContext:ft,useCallback:Te,useContext:Te,useEffect:Te,useImperativeHandle:Te,useInsertionEffect:Te,useLayoutEffect:Te,useMemo:Te,useReducer:Te,useRef:Te,useState:Te,useDebugValue:Te,useDeferredValue:Te,useTransition:Te,useMutableSource:Te,useSyncExternalStore:Te,useId:Te,unstable_isNewReconciler:!1},R0={readContext:ft,useCallback:function(e,t){return Ct().memoizedState=[e,t===void 0?null:t],e},useContext:ft,useEffect:ed,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,ui(4194308,4,Xh.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ui(4194308,4,e,t)},useInsertionEffect:function(e,t){return ui(4,2,e,t)},useMemo:function(e,t){var n=Ct();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var o=Ct();return t=n!==void 0?n(t):t,o.memoizedState=o.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},o.queue=e,e=e.dispatch=_0.bind(null,ne,e),[o.memoizedState,e]},useRef:function(e){var t=Ct();return e={current:e},t.memoizedState=e},useState:Ju,useDebugValue:mc,useDeferredValue:function(e){return Ct().memoizedState=e},useTransition:function(){var e=Ju(!1),t=e[0];return e=P0.bind(null,e[1]),Ct().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var o=ne,r=Ct();if(ee){if(n===void 0)throw Error(S(407));n=n()}else{if(n=t(),ye===null)throw Error(S(349));Pn&30||Vh(o,t,n)}r.memoizedState=n;var i={value:n,getSnapshot:t};return r.queue=i,ed(Wh.bind(null,o,i,e),[e]),o.flags|=2048,hr(9,Fh.bind(null,o,i,n,t),void 0,null),n},useId:function(){var e=Ct(),t=ye.identifierPrefix;if(ee){var n=Bt,o=zt;n=(o&~(1<<32-yt(o)-1)).toString(32)+n,t=":"+t+"R"+n,n=dr++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=$0++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},L0={readContext:ft,useCallback:Zh,useContext:ft,useEffect:pc,useImperativeHandle:Gh,useInsertionEffect:Kh,useLayoutEffect:Yh,useMemo:Jh,useReducer:sl,useRef:qh,useState:function(){return sl(fr)},useDebugValue:mc,useDeferredValue:function(e){var t=ht();return ep(t,me.memoizedState,e)},useTransition:function(){var e=sl(fr)[0],t=ht().memoizedState;return[e,t]},useMutableSource:jh,useSyncExternalStore:Hh,useId:tp,unstable_isNewReconciler:!1},O0={readContext:ft,useCallback:Zh,useContext:ft,useEffect:pc,useImperativeHandle:Gh,useInsertionEffect:Kh,useLayoutEffect:Yh,useMemo:Jh,useReducer:ll,useRef:qh,useState:function(){return ll(fr)},useDebugValue:mc,useDeferredValue:function(e){var t=ht();return me===null?t.memoizedState=e:ep(t,me.memoizedState,e)},useTransition:function(){var e=ll(fr)[0],t=ht().memoizedState;return[e,t]},useMutableSource:jh,useSyncExternalStore:Hh,useId:tp,unstable_isNewReconciler:!1};function mt(e,t){if(e&&e.defaultProps){t=oe({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function ra(e,t,n,o){t=e.memoizedState,n=n(o,t),n=n==null?t:oe({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var rs={isMounted:function(e){return(e=e._reactInternals)?An(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var o=Le(),r=rn(e),i=Dt(o,r);i.payload=t,n!=null&&(i.callback=n),t=nn(e,i,r),t!==null&&(bt(t,e,r,o),ai(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var o=Le(),r=rn(e),i=Dt(o,r);i.tag=1,i.payload=t,n!=null&&(i.callback=n),t=nn(e,i,r),t!==null&&(bt(t,e,r,o),ai(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Le(),o=rn(e),r=Dt(n,o);r.tag=2,t!=null&&(r.callback=t),t=nn(e,r,o),t!==null&&(bt(t,e,o,n),ai(t,e,o))}};function td(e,t,n,o,r,i,s){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(o,i,s):t.prototype&&t.prototype.isPureReactComponent?!ir(n,o)||!ir(r,i):!0}function ip(e,t,n){var o=!1,r=cn,i=t.contextType;return typeof i=="object"&&i!==null?i=ft(i):(r=He(t)?Tn:_e.current,o=t.contextTypes,i=(o=o!=null)?ao(e,r):cn),t=new t(n,i),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=rs,e.stateNode=t,t._reactInternals=e,o&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=r,e.__reactInternalMemoizedMaskedChildContext=i),t}function nd(e,t,n,o){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,o),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,o),t.state!==e&&rs.enqueueReplaceState(t,t.state,null)}function ia(e,t,n,o){var r=e.stateNode;r.props=n,r.state=e.memoizedState,r.refs={},lc(e);var i=t.contextType;typeof i=="object"&&i!==null?r.context=ft(i):(i=He(t)?Tn:_e.current,r.context=ao(e,i)),r.state=e.memoizedState,i=t.getDerivedStateFromProps,typeof i=="function"&&(ra(e,t,i,n),r.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof r.getSnapshotBeforeUpdate=="function"||typeof r.UNSAFE_componentWillMount!="function"&&typeof r.componentWillMount!="function"||(t=r.state,typeof r.componentWillMount=="function"&&r.componentWillMount(),typeof r.UNSAFE_componentWillMount=="function"&&r.UNSAFE_componentWillMount(),t!==r.state&&rs.enqueueReplaceState(r,r.state,null),Ri(e,n,r,o),r.state=e.memoizedState),typeof r.componentDidMount=="function"&&(e.flags|=4194308)}function ho(e,t){try{var n="",o=t;do n+=lv(o),o=o.return;while(o);var r=n}catch(i){r="\nError generating stack: "+i.message+"\n"+i.stack}return{value:e,source:t,stack:r,digest:null}}function al(e,t,n){return{value:e,source:null,stack:n!=null?n:null,digest:t!=null?t:null}}function sa(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var A0=typeof WeakMap=="function"?WeakMap:Map;function sp(e,t,n){n=Dt(-1,n),n.tag=3,n.payload={element:null};var o=t.value;return n.callback=function(){Bi||(Bi=!0,ga=o),sa(e,t)},n}function lp(e,t,n){n=Dt(-1,n),n.tag=3;var o=e.type.getDerivedStateFromError;if(typeof o=="function"){var r=t.value;n.payload=function(){return o(r)},n.callback=function(){sa(e,t)}}var i=e.stateNode;return i!==null&&typeof i.componentDidCatch=="function"&&(n.callback=function(){sa(e,t),typeof o!="function"&&(on===null?on=new Set([this]):on.add(this));var s=t.stack;this.componentDidCatch(t.value,{componentStack:s!==null?s:""})}),n}function od(e,t,n){var o=e.pingCache;if(o===null){o=e.pingCache=new A0;var r=new Set;o.set(t,r)}else r=o.get(t),r===void 0&&(r=new Set,o.set(t,r));r.has(n)||(r.add(n),e=K0.bind(null,e,t,n),t.then(e,e))}function rd(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function id(e,t,n,o,r){return e.mode&1?(e.flags|=65536,e.lanes=r,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Dt(-1,1),t.tag=2,nn(n,t,1))),n.lanes|=1),e)}var z0=Vt.ReactCurrentOwner,Me=!1;function Re(e,t,n,o){t.child=e===null?Bh(t,null,n,o):uo(t,e.child,n,o)}function sd(e,t,n,o,r){n=n.render;var i=t.ref;return oo(t,r),o=fc(e,t,n,o,i,r),n=hc(),e!==null&&!Me?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~r,Ht(e,t,r)):(ee&&n&&ec(t),t.flags|=1,Re(e,t,o,r),t.child)}function ld(e,t,n,o,r){if(e===null){var i=n.type;return typeof i=="function"&&!Ec(i)&&i.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=i,ap(e,t,i,o,r)):(e=pi(n.type,null,o,t,t.mode,r),e.ref=t.ref,e.return=t,t.child=e)}if(i=e.child,!(e.lanes&r)){var s=i.memoizedProps;if(n=n.compare,n=n!==null?n:ir,n(s,o)&&e.ref===t.ref)return Ht(e,t,r)}return t.flags|=1,e=sn(i,o),e.ref=t.ref,e.return=t,t.child=e}function ap(e,t,n,o,r){if(e!==null){var i=e.memoizedProps;if(ir(i,o)&&e.ref===t.ref)if(Me=!1,t.pendingProps=o=i,(e.lanes&r)!==0)e.flags&131072&&(Me=!0);else return t.lanes=e.lanes,Ht(e,t,r)}return la(e,t,n,o,r)}function cp(e,t,n){var o=t.pendingProps,r=o.children,i=e!==null?e.memoizedState:null;if(o.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Y(Zn,qe),qe|=n;else{if(!(n&1073741824))return e=i!==null?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Y(Zn,qe),qe|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},o=i!==null?i.baseLanes:n,Y(Zn,qe),qe|=o}else i!==null?(o=i.baseLanes|n,t.memoizedState=null):o=n,Y(Zn,qe),qe|=o;return Re(e,t,r,n),t.child}function up(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function la(e,t,n,o,r){var i=He(n)?Tn:_e.current;return i=ao(t,i),oo(t,r),n=fc(e,t,n,o,i,r),o=hc(),e!==null&&!Me?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~r,Ht(e,t,r)):(ee&&o&&ec(t),t.flags|=1,Re(e,t,n,r),t.child)}function ad(e,t,n,o,r){if(He(n)){var i=!0;Ti(t)}else i=!1;if(oo(t,r),t.stateNode===null)di(e,t),ip(t,n,o),ia(t,n,o,r),o=!0;else if(e===null){var s=t.stateNode,a=t.memoizedProps;s.props=a;var l=s.context,c=n.contextType;typeof c=="object"&&c!==null?c=ft(c):(c=He(n)?Tn:_e.current,c=ao(t,c));var u=n.getDerivedStateFromProps,d=typeof u=="function"||typeof s.getSnapshotBeforeUpdate=="function";d||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==o||l!==c)&&nd(t,s,o,c),Qt=!1;var f=t.memoizedState;s.state=f,Ri(t,o,s,r),l=t.memoizedState,a!==o||f!==l||je.current||Qt?(typeof u=="function"&&(ra(t,n,u,o),l=t.memoizedState),(a=Qt||td(t,n,a,o,f,l,c))?(d||typeof s.UNSAFE_componentWillMount!="function"&&typeof s.componentWillMount!="function"||(typeof s.componentWillMount=="function"&&s.componentWillMount(),typeof s.UNSAFE_componentWillMount=="function"&&s.UNSAFE_componentWillMount()),typeof s.componentDidMount=="function"&&(t.flags|=4194308)):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=o,t.memoizedState=l),s.props=o,s.state=l,s.context=c,o=a):(typeof s.componentDidMount=="function"&&(t.flags|=4194308),o=!1)}else{s=t.stateNode,Mh(e,t),a=t.memoizedProps,c=t.type===t.elementType?a:mt(t.type,a),s.props=c,d=t.pendingProps,f=s.context,l=n.contextType,typeof l=="object"&&l!==null?l=ft(l):(l=He(n)?Tn:_e.current,l=ao(t,l));var p=n.getDerivedStateFromProps;(u=typeof p=="function"||typeof s.getSnapshotBeforeUpdate=="function")||typeof s.UNSAFE_componentWillReceiveProps!="function"&&typeof s.componentWillReceiveProps!="function"||(a!==d||f!==l)&&nd(t,s,o,l),Qt=!1,f=t.memoizedState,s.state=f,Ri(t,o,s,r);var v=t.memoizedState;a!==d||f!==v||je.current||Qt?(typeof p=="function"&&(ra(t,n,p,o),v=t.memoizedState),(c=Qt||td(t,n,c,o,f,v,l)||!1)?(u||typeof s.UNSAFE_componentWillUpdate!="function"&&typeof s.componentWillUpdate!="function"||(typeof s.componentWillUpdate=="function"&&s.componentWillUpdate(o,v,l),typeof s.UNSAFE_componentWillUpdate=="function"&&s.UNSAFE_componentWillUpdate(o,v,l)),typeof s.componentDidUpdate=="function"&&(t.flags|=4),typeof s.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=o,t.memoizedState=v),s.props=o,s.state=v,s.context=l,o=c):(typeof s.componentDidUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),typeof s.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),o=!1)}return aa(e,t,n,o,i,r)}function aa(e,t,n,o,r,i){up(e,t);var s=(t.flags&128)!==0;if(!o&&!s)return r&&qu(t,n,!1),Ht(e,t,i);o=t.stateNode,z0.current=t;var a=s&&typeof n.getDerivedStateFromError!="function"?null:o.render();return t.flags|=1,e!==null&&s?(t.child=uo(t,e.child,null,i),t.child=uo(t,null,a,i)):Re(e,t,a,i),t.memoizedState=o.state,r&&qu(t,n,!0),t.child}function dp(e){var t=e.stateNode;t.pendingContext?Qu(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Qu(e,t.context,!1),ac(e,t.containerInfo)}function cd(e,t,n,o,r){return co(),nc(r),t.flags|=256,Re(e,t,n,o),t.child}var ca={dehydrated:null,treeContext:null,retryLane:0};function ua(e){return{baseLanes:e,cachePool:null,transitions:null}}function fp(e,t,n){var o=t.pendingProps,r=te.current,i=!1,s=(t.flags&128)!==0,a;if((a=s)||(a=e!==null&&e.memoizedState===null?!1:(r&2)!==0),a?(i=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(r|=1),Y(te,r&1),e===null)return na(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(s=o.children,e=o.fallback,i?(o=t.mode,i=t.child,s={mode:"hidden",children:s},!(o&1)&&i!==null?(i.childLanes=0,i.pendingProps=s):i=ls(s,o,0,null),e=Cn(e,o,n,null),i.return=t,e.return=t,i.sibling=e,t.child=i,t.child.memoizedState=ua(n),t.memoizedState=ca,e):gc(t,s));if(r=e.memoizedState,r!==null&&(a=r.dehydrated,a!==null))return B0(e,t,s,o,a,r,n);if(i){i=o.fallback,s=t.mode,r=e.child,a=r.sibling;var l={mode:"hidden",children:o.children};return!(s&1)&&t.child!==r?(o=t.child,o.childLanes=0,o.pendingProps=l,t.deletions=null):(o=sn(r,l),o.subtreeFlags=r.subtreeFlags&14680064),a!==null?i=sn(a,i):(i=Cn(i,s,n,null),i.flags|=2),i.return=t,o.return=t,o.sibling=i,t.child=o,o=i,i=t.child,s=e.child.memoizedState,s=s===null?ua(n):{baseLanes:s.baseLanes|n,cachePool:null,transitions:s.transitions},i.memoizedState=s,i.childLanes=e.childLanes&~n,t.memoizedState=ca,o}return i=e.child,e=i.sibling,o=sn(i,{mode:"visible",children:o.children}),!(t.mode&1)&&(o.lanes=n),o.return=t,o.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=o,t.memoizedState=null,o}function gc(e,t){return t=ls({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function qr(e,t,n,o){return o!==null&&nc(o),uo(t,e.child,null,n),e=gc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function B0(e,t,n,o,r,i,s){if(n)return t.flags&256?(t.flags&=-257,o=al(Error(S(422))),qr(e,t,s,o)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(i=o.fallback,r=t.mode,o=ls({mode:"visible",children:o.children},r,0,null),i=Cn(i,r,s,null),i.flags|=2,o.return=t,i.return=t,o.sibling=i,t.child=o,t.mode&1&&uo(t,e.child,null,s),t.child.memoizedState=ua(s),t.memoizedState=ca,i);if(!(t.mode&1))return qr(e,t,s,null);if(r.data==="$!"){if(o=r.nextSibling&&r.nextSibling.dataset,o)var a=o.dgst;return o=a,i=Error(S(419)),o=al(i,o,void 0),qr(e,t,s,o)}if(a=(s&e.childLanes)!==0,Me||a){if(o=ye,o!==null){switch(s&-s){case 4:r=2;break;case 16:r=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:r=32;break;case 536870912:r=268435456;break;default:r=0}r=r&(o.suspendedLanes|s)?0:r,r!==0&&r!==i.retryLane&&(i.retryLane=r,jt(e,r),bt(o,e,r,-1))}return kc(),o=al(Error(S(421))),qr(e,t,s,o)}return r.data==="$?"?(t.flags|=128,t.child=e.child,t=Y0.bind(null,e),r._reactRetry=t,null):(e=i.treeContext,Ye=tn(r.nextSibling),Ge=t,ee=!0,vt=null,e!==null&&(lt[at++]=zt,lt[at++]=Bt,lt[at++]=$n,zt=e.id,Bt=e.overflow,$n=t),t=gc(t,o.children),t.flags|=4096,t)}function ud(e,t,n){e.lanes|=t;var o=e.alternate;o!==null&&(o.lanes|=t),oa(e.return,t,n)}function cl(e,t,n,o,r){var i=e.memoizedState;i===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:o,tail:n,tailMode:r}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=o,i.tail=n,i.tailMode=r)}function hp(e,t,n){var o=t.pendingProps,r=o.revealOrder,i=o.tail;if(Re(e,t,o.children,n),o=te.current,o&2)o=o&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&ud(e,n,t);else if(e.tag===19)ud(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}o&=1}if(Y(te,o),!(t.mode&1))t.memoizedState=null;else switch(r){case"forwards":for(n=t.child,r=null;n!==null;)e=n.alternate,e!==null&&Li(e)===null&&(r=n),n=n.sibling;n=r,n===null?(r=t.child,t.child=null):(r=n.sibling,n.sibling=null),cl(t,!1,r,n,i);break;case"backwards":for(n=null,r=t.child,t.child=null;r!==null;){if(e=r.alternate,e!==null&&Li(e)===null){t.child=r;break}e=r.sibling,r.sibling=n,n=r,r=e}cl(t,!0,n,null,i);break;case"together":cl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function di(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Ht(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),_n|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(S(153));if(t.child!==null){for(e=t.child,n=sn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=sn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function D0(e,t,n){switch(t.tag){case 3:dp(t),co();break;case 5:Nh(t);break;case 1:He(t.type)&&Ti(t);break;case 4:ac(t,t.stateNode.containerInfo);break;case 10:var o=t.type._context,r=t.memoizedProps.value;Y(_i,o._currentValue),o._currentValue=r;break;case 13:if(o=t.memoizedState,o!==null)return o.dehydrated!==null?(Y(te,te.current&1),t.flags|=128,null):n&t.child.childLanes?fp(e,t,n):(Y(te,te.current&1),e=Ht(e,t,n),e!==null?e.sibling:null);Y(te,te.current&1);break;case 19:if(o=(n&t.childLanes)!==0,e.flags&128){if(o)return hp(e,t,n);t.flags|=128}if(r=t.memoizedState,r!==null&&(r.rendering=null,r.tail=null,r.lastEffect=null),Y(te,te.current),o)break;return null;case 22:case 23:return t.lanes=0,cp(e,t,n)}return Ht(e,t,n)}var pp,da,mp,gp;pp=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};da=function(){};mp=function(e,t,n,o){var r=e.memoizedProps;if(r!==o){e=t.stateNode,xn(_t.current);var i=null;switch(n){case"input":r=Ol(e,r),o=Ol(e,o),i=[];break;case"select":r=oe({},r,{value:void 0}),o=oe({},o,{value:void 0}),i=[];break;case"textarea":r=Bl(e,r),o=Bl(e,o),i=[];break;default:typeof r.onClick!="function"&&typeof o.onClick=="function"&&(e.onclick=Si)}Ml(n,o);var s;n=null;for(c in r)if(!o.hasOwnProperty(c)&&r.hasOwnProperty(c)&&r[c]!=null)if(c==="style"){var a=r[c];for(s in a)a.hasOwnProperty(s)&&(n||(n={}),n[s]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(Zo.hasOwnProperty(c)?i||(i=[]):(i=i||[]).push(c,null));for(c in o){var l=o[c];if(a=r!=null?r[c]:void 0,o.hasOwnProperty(c)&&l!==a&&(l!=null||a!=null))if(c==="style")if(a){for(s in a)!a.hasOwnProperty(s)||l&&l.hasOwnProperty(s)||(n||(n={}),n[s]="");for(s in l)l.hasOwnProperty(s)&&a[s]!==l[s]&&(n||(n={}),n[s]=l[s])}else n||(i||(i=[]),i.push(c,n)),n=l;else c==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(i=i||[]).push(c,l)):c==="children"?typeof l!="string"&&typeof l!="number"||(i=i||[]).push(c,""+l):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(Zo.hasOwnProperty(c)?(l!=null&&c==="onScroll"&&X("scroll",e),i||a===l||(i=[])):(i=i||[]).push(c,l))}n&&(i=i||[]).push("style",n);var c=i;(t.updateQueue=c)&&(t.flags|=4)}};gp=function(e,t,n,o){n!==o&&(t.flags|=4)};function Ro(e,t){if(!ee)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var o=null;n!==null;)n.alternate!==null&&(o=n),n=n.sibling;o===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:o.sibling=null}}function $e(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,o=0;if(t)for(var r=e.child;r!==null;)n|=r.lanes|r.childLanes,o|=r.subtreeFlags&14680064,o|=r.flags&14680064,r.return=e,r=r.sibling;else for(r=e.child;r!==null;)n|=r.lanes|r.childLanes,o|=r.subtreeFlags,o|=r.flags,r.return=e,r=r.sibling;return e.subtreeFlags|=o,e.childLanes=n,t}function M0(e,t,n){var o=t.pendingProps;switch(tc(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return $e(t),null;case 1:return He(t.type)&&Ci(),$e(t),null;case 3:return o=t.stateNode,fo(),Z(je),Z(_e),uc(),o.pendingContext&&(o.context=o.pendingContext,o.pendingContext=null),(e===null||e.child===null)&&(Ur(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,vt!==null&&(ba(vt),vt=null))),da(e,t),$e(t),null;case 5:cc(t);var r=xn(ur.current);if(n=t.type,e!==null&&t.stateNode!=null)mp(e,t,n,o,r),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!o){if(t.stateNode===null)throw Error(S(166));return $e(t),null}if(e=xn(_t.current),Ur(t)){o=t.stateNode,n=t.type;var i=t.memoizedProps;switch(o[$t]=t,o[ar]=i,e=(t.mode&1)!==0,n){case"dialog":X("cancel",o),X("close",o);break;case"iframe":case"object":case"embed":X("load",o);break;case"video":case"audio":for(r=0;r<Mo.length;r++)X(Mo[r],o);break;case"source":X("error",o);break;case"img":case"image":case"link":X("error",o),X("load",o);break;case"details":X("toggle",o);break;case"input":bu(o,i),X("invalid",o);break;case"select":o._wrapperState={wasMultiple:!!i.multiple},X("invalid",o);break;case"textarea":xu(o,i),X("invalid",o)}Ml(n,i),r=null;for(var s in i)if(i.hasOwnProperty(s)){var a=i[s];s==="children"?typeof a=="string"?o.textContent!==a&&(i.suppressHydrationWarning!==!0&&Wr(o.textContent,a,e),r=["children",a]):typeof a=="number"&&o.textContent!==""+a&&(i.suppressHydrationWarning!==!0&&Wr(o.textContent,a,e),r=["children",""+a]):Zo.hasOwnProperty(s)&&a!=null&&s==="onScroll"&&X("scroll",o)}switch(n){case"input":Br(o),wu(o,i,!0);break;case"textarea":Br(o),ku(o);break;case"select":case"option":break;default:typeof i.onClick=="function"&&(o.onclick=Si)}o=r,t.updateQueue=o,o!==null&&(t.flags|=4)}else{s=r.nodeType===9?r:r.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Wf(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=s.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof o.is=="string"?e=s.createElement(n,{is:o.is}):(e=s.createElement(n),n==="select"&&(s=e,o.multiple?s.multiple=!0:o.size&&(s.size=o.size))):e=s.createElementNS(e,n),e[$t]=t,e[ar]=o,pp(e,t,!1,!1),t.stateNode=e;e:{switch(s=Nl(n,o),n){case"dialog":X("cancel",e),X("close",e),r=o;break;case"iframe":case"object":case"embed":X("load",e),r=o;break;case"video":case"audio":for(r=0;r<Mo.length;r++)X(Mo[r],e);r=o;break;case"source":X("error",e),r=o;break;case"img":case"image":case"link":X("error",e),X("load",e),r=o;break;case"details":X("toggle",e),r=o;break;case"input":bu(e,o),r=Ol(e,o),X("invalid",e);break;case"option":r=o;break;case"select":e._wrapperState={wasMultiple:!!o.multiple},r=oe({},o,{value:void 0}),X("invalid",e);break;case"textarea":xu(e,o),r=Bl(e,o),X("invalid",e);break;default:r=o}Ml(n,r),a=r;for(i in a)if(a.hasOwnProperty(i)){var l=a[i];i==="style"?qf(e,l):i==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&Uf(e,l)):i==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&Jo(e,l):typeof l=="number"&&Jo(e,""+l):i!=="suppressContentEditableWarning"&&i!=="suppressHydrationWarning"&&i!=="autoFocus"&&(Zo.hasOwnProperty(i)?l!=null&&i==="onScroll"&&X("scroll",e):l!=null&&ja(e,i,l,s))}switch(n){case"input":Br(e),wu(e,o,!1);break;case"textarea":Br(e),ku(e);break;case"option":o.value!=null&&e.setAttribute("value",""+an(o.value));break;case"select":e.multiple=!!o.multiple,i=o.value,i!=null?Jn(e,!!o.multiple,i,!1):o.defaultValue!=null&&Jn(e,!!o.multiple,o.defaultValue,!0);break;default:typeof r.onClick=="function"&&(e.onclick=Si)}switch(n){case"button":case"input":case"select":case"textarea":o=!!o.autoFocus;break e;case"img":o=!0;break e;default:o=!1}}o&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return $e(t),null;case 6:if(e&&t.stateNode!=null)gp(e,t,e.memoizedProps,o);else{if(typeof o!="string"&&t.stateNode===null)throw Error(S(166));if(n=xn(ur.current),xn(_t.current),Ur(t)){if(o=t.stateNode,n=t.memoizedProps,o[$t]=t,(i=o.nodeValue!==n)&&(e=Ge,e!==null))switch(e.tag){case 3:Wr(o.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Wr(o.nodeValue,n,(e.mode&1)!==0)}i&&(t.flags|=4)}else o=(n.nodeType===9?n:n.ownerDocument).createTextNode(o),o[$t]=t,t.stateNode=o}return $e(t),null;case 13:if(Z(te),o=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(ee&&Ye!==null&&t.mode&1&&!(t.flags&128))Ah(),co(),t.flags|=98560,i=!1;else if(i=Ur(t),o!==null&&o.dehydrated!==null){if(e===null){if(!i)throw Error(S(318));if(i=t.memoizedState,i=i!==null?i.dehydrated:null,!i)throw Error(S(317));i[$t]=t}else co(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;$e(t),i=!1}else vt!==null&&(ba(vt),vt=null),i=!0;if(!i)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(o=o!==null,o!==(e!==null&&e.memoizedState!==null)&&o&&(t.child.flags|=8192,t.mode&1&&(e===null||te.current&1?ge===0&&(ge=3):kc())),t.updateQueue!==null&&(t.flags|=4),$e(t),null);case 4:return fo(),da(e,t),e===null&&sr(t.stateNode.containerInfo),$e(t),null;case 10:return ic(t.type._context),$e(t),null;case 17:return He(t.type)&&Ci(),$e(t),null;case 19:if(Z(te),i=t.memoizedState,i===null)return $e(t),null;if(o=(t.flags&128)!==0,s=i.rendering,s===null)if(o)Ro(i,!1);else{if(ge!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(s=Li(e),s!==null){for(t.flags|=128,Ro(i,!1),o=s.updateQueue,o!==null&&(t.updateQueue=o,t.flags|=4),t.subtreeFlags=0,o=n,n=t.child;n!==null;)i=n,e=o,i.flags&=14680066,s=i.alternate,s===null?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=s.childLanes,i.lanes=s.lanes,i.child=s.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=s.memoizedProps,i.memoizedState=s.memoizedState,i.updateQueue=s.updateQueue,i.type=s.type,e=s.dependencies,i.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Y(te,te.current&1|2),t.child}e=e.sibling}i.tail!==null&&ae()>po&&(t.flags|=128,o=!0,Ro(i,!1),t.lanes=4194304)}else{if(!o)if(e=Li(s),e!==null){if(t.flags|=128,o=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Ro(i,!0),i.tail===null&&i.tailMode==="hidden"&&!s.alternate&&!ee)return $e(t),null}else 2*ae()-i.renderingStartTime>po&&n!==1073741824&&(t.flags|=128,o=!0,Ro(i,!1),t.lanes=4194304);i.isBackwards?(s.sibling=t.child,t.child=s):(n=i.last,n!==null?n.sibling=s:t.child=s,i.last=s)}return i.tail!==null?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=ae(),t.sibling=null,n=te.current,Y(te,o?n&1|2:n&1),t):($e(t),null);case 22:case 23:return xc(),o=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==o&&(t.flags|=8192),o&&t.mode&1?qe&1073741824&&($e(t),t.subtreeFlags&6&&(t.flags|=8192)):$e(t),null;case 24:return null;case 25:return null}throw Error(S(156,t.tag))}function N0(e,t){switch(tc(t),t.tag){case 1:return He(t.type)&&Ci(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return fo(),Z(je),Z(_e),uc(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return cc(t),null;case 13:if(Z(te),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(S(340));co()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Z(te),null;case 4:return fo(),null;case 10:return ic(t.type._context),null;case 22:case 23:return xc(),null;case 24:return null;default:return null}}var Kr=!1,Pe=!1,j0=typeof WeakSet=="function"?WeakSet:Set,O=null;function Gn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(o){re(e,t,o)}else n.current=null}function fa(e,t,n){try{n()}catch(o){re(e,t,o)}}var dd=!1;function H0(e,t){if(Yl=xi,e=xh(),Ja(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var o=n.getSelection&&n.getSelection();if(o&&o.rangeCount!==0){n=o.anchorNode;var r=o.anchorOffset,i=o.focusNode;o=o.focusOffset;try{n.nodeType,i.nodeType}catch(w){n=null;break e}var s=0,a=-1,l=-1,c=0,u=0,d=e,f=null;t:for(;;){for(var p;d!==n||r!==0&&d.nodeType!==3||(a=s+r),d!==i||o!==0&&d.nodeType!==3||(l=s+o),d.nodeType===3&&(s+=d.nodeValue.length),(p=d.firstChild)!==null;)f=d,d=p;for(;;){if(d===e)break t;if(f===n&&++c===r&&(a=s),f===i&&++u===o&&(l=s),(p=d.nextSibling)!==null)break;d=f,f=d.parentNode}d=p}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(Xl={focusedElem:e,selectionRange:n},xi=!1,O=t;O!==null;)if(t=O,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,O=e;else for(;O!==null;){t=O;try{var v=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(v!==null){var x=v.memoizedProps,R=v.memoizedState,m=t.stateNode,h=m.getSnapshotBeforeUpdate(t.elementType===t.type?x:mt(t.type,x),R);m.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var g=t.stateNode.containerInfo;g.nodeType===1?g.textContent="":g.nodeType===9&&g.documentElement&&g.removeChild(g.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(S(163))}}catch(w){re(t,t.return,w)}if(e=t.sibling,e!==null){e.return=t.return,O=e;break}O=t.return}return v=dd,dd=!1,v}function Ko(e,t,n){var o=t.updateQueue;if(o=o!==null?o.lastEffect:null,o!==null){var r=o=o.next;do{if((r.tag&e)===e){var i=r.destroy;r.destroy=void 0,i!==void 0&&fa(t,n,i)}r=r.next}while(r!==o)}}function is(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var o=n.create;n.destroy=o()}n=n.next}while(n!==t)}}function ha(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function vp(e){var t=e.alternate;t!==null&&(e.alternate=null,vp(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[$t],delete t[ar],delete t[Jl],delete t[E0],delete t[S0])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function yp(e){return e.tag===5||e.tag===3||e.tag===4}function fd(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||yp(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function pa(e,t,n){var o=e.tag;if(o===5||o===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Si));else if(o!==4&&(e=e.child,e!==null))for(pa(e,t,n),e=e.sibling;e!==null;)pa(e,t,n),e=e.sibling}function ma(e,t,n){var o=e.tag;if(o===5||o===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(o!==4&&(e=e.child,e!==null))for(ma(e,t,n),e=e.sibling;e!==null;)ma(e,t,n),e=e.sibling}var Ee=null,gt=!1;function Wt(e,t,n){for(n=n.child;n!==null;)bp(e,t,n),n=n.sibling}function bp(e,t,n){if(Pt&&typeof Pt.onCommitFiberUnmount=="function")try{Pt.onCommitFiberUnmount(Gi,n)}catch(a){}switch(n.tag){case 5:Pe||Gn(n,t);case 6:var o=Ee,r=gt;Ee=null,Wt(e,t,n),Ee=o,gt=r,Ee!==null&&(gt?(e=Ee,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Ee.removeChild(n.stateNode));break;case 18:Ee!==null&&(gt?(e=Ee,n=n.stateNode,e.nodeType===8?nl(e.parentNode,n):e.nodeType===1&&nl(e,n),or(e)):nl(Ee,n.stateNode));break;case 4:o=Ee,r=gt,Ee=n.stateNode.containerInfo,gt=!0,Wt(e,t,n),Ee=o,gt=r;break;case 0:case 11:case 14:case 15:if(!Pe&&(o=n.updateQueue,o!==null&&(o=o.lastEffect,o!==null))){r=o=o.next;do{var i=r,s=i.destroy;i=i.tag,s!==void 0&&(i&2||i&4)&&fa(n,t,s),r=r.next}while(r!==o)}Wt(e,t,n);break;case 1:if(!Pe&&(Gn(n,t),o=n.stateNode,typeof o.componentWillUnmount=="function"))try{o.props=n.memoizedProps,o.state=n.memoizedState,o.componentWillUnmount()}catch(a){re(n,t,a)}Wt(e,t,n);break;case 21:Wt(e,t,n);break;case 22:n.mode&1?(Pe=(o=Pe)||n.memoizedState!==null,Wt(e,t,n),Pe=o):Wt(e,t,n);break;default:Wt(e,t,n)}}function hd(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new j0),t.forEach(function(o){var r=X0.bind(null,e,o);n.has(o)||(n.add(o),o.then(r,r))})}}function pt(e,t){var n=t.deletions;if(n!==null)for(var o=0;o<n.length;o++){var r=n[o];try{var i=e,s=t,a=s;e:for(;a!==null;){switch(a.tag){case 5:Ee=a.stateNode,gt=!1;break e;case 3:Ee=a.stateNode.containerInfo,gt=!0;break e;case 4:Ee=a.stateNode.containerInfo,gt=!0;break e}a=a.return}if(Ee===null)throw Error(S(160));bp(i,s,r),Ee=null,gt=!1;var l=r.alternate;l!==null&&(l.return=null),r.return=null}catch(c){re(r,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)wp(t,e),t=t.sibling}function wp(e,t){var n=e.alternate,o=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(pt(t,e),St(e),o&4){try{Ko(3,e,e.return),is(3,e)}catch(x){re(e,e.return,x)}try{Ko(5,e,e.return)}catch(x){re(e,e.return,x)}}break;case 1:pt(t,e),St(e),o&512&&n!==null&&Gn(n,n.return);break;case 5:if(pt(t,e),St(e),o&512&&n!==null&&Gn(n,n.return),e.flags&32){var r=e.stateNode;try{Jo(r,"")}catch(x){re(e,e.return,x)}}if(o&4&&(r=e.stateNode,r!=null)){var i=e.memoizedProps,s=n!==null?n.memoizedProps:i,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&i.type==="radio"&&i.name!=null&&Vf(r,i),Nl(a,s);var c=Nl(a,i);for(s=0;s<l.length;s+=2){var u=l[s],d=l[s+1];u==="style"?qf(r,d):u==="dangerouslySetInnerHTML"?Uf(r,d):u==="children"?Jo(r,d):ja(r,u,d,c)}switch(a){case"input":Al(r,i);break;case"textarea":Ff(r,i);break;case"select":var f=r._wrapperState.wasMultiple;r._wrapperState.wasMultiple=!!i.multiple;var p=i.value;p!=null?Jn(r,!!i.multiple,p,!1):f!==!!i.multiple&&(i.defaultValue!=null?Jn(r,!!i.multiple,i.defaultValue,!0):Jn(r,!!i.multiple,i.multiple?[]:"",!1))}r[ar]=i}catch(x){re(e,e.return,x)}}break;case 6:if(pt(t,e),St(e),o&4){if(e.stateNode===null)throw Error(S(162));r=e.stateNode,i=e.memoizedProps;try{r.nodeValue=i}catch(x){re(e,e.return,x)}}break;case 3:if(pt(t,e),St(e),o&4&&n!==null&&n.memoizedState.isDehydrated)try{or(t.containerInfo)}catch(x){re(e,e.return,x)}break;case 4:pt(t,e),St(e);break;case 13:pt(t,e),St(e),r=e.child,r.flags&8192&&(i=r.memoizedState!==null,r.stateNode.isHidden=i,!i||r.alternate!==null&&r.alternate.memoizedState!==null||(bc=ae())),o&4&&hd(e);break;case 22:if(u=n!==null&&n.memoizedState!==null,e.mode&1?(Pe=(c=Pe)||u,pt(t,e),Pe=c):pt(t,e),St(e),o&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!u&&e.mode&1)for(O=e,u=e.child;u!==null;){for(d=O=u;O!==null;){switch(f=O,p=f.child,f.tag){case 0:case 11:case 14:case 15:Ko(4,f,f.return);break;case 1:Gn(f,f.return);var v=f.stateNode;if(typeof v.componentWillUnmount=="function"){o=f,n=f.return;try{t=o,v.props=t.memoizedProps,v.state=t.memoizedState,v.componentWillUnmount()}catch(x){re(o,n,x)}}break;case 5:Gn(f,f.return);break;case 22:if(f.memoizedState!==null){md(d);continue}}p!==null?(p.return=f,O=p):md(d)}u=u.sibling}e:for(u=null,d=e;;){if(d.tag===5){if(u===null){u=d;try{r=d.stateNode,c?(i=r.style,typeof i.setProperty=="function"?i.setProperty("display","none","important"):i.display="none"):(a=d.stateNode,l=d.memoizedProps.style,s=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=Qf("display",s))}catch(x){re(e,e.return,x)}}}else if(d.tag===6){if(u===null)try{d.stateNode.nodeValue=c?"":d.memoizedProps}catch(x){re(e,e.return,x)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;u===d&&(u=null),d=d.return}u===d&&(u=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:pt(t,e),St(e),o&4&&hd(e);break;case 21:break;default:pt(t,e),St(e)}}function St(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(yp(n)){var o=n;break e}n=n.return}throw Error(S(160))}switch(o.tag){case 5:var r=o.stateNode;o.flags&32&&(Jo(r,""),o.flags&=-33);var i=fd(e);ma(e,i,r);break;case 3:case 4:var s=o.stateNode.containerInfo,a=fd(e);pa(e,a,s);break;default:throw Error(S(161))}}catch(l){re(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function V0(e,t,n){O=e,xp(e)}function xp(e,t,n){for(var o=(e.mode&1)!==0;O!==null;){var r=O,i=r.child;if(r.tag===22&&o){var s=r.memoizedState!==null||Kr;if(!s){var a=r.alternate,l=a!==null&&a.memoizedState!==null||Pe;a=Kr;var c=Pe;if(Kr=s,(Pe=l)&&!c)for(O=r;O!==null;)s=O,l=s.child,s.tag===22&&s.memoizedState!==null?gd(r):l!==null?(l.return=s,O=l):gd(r);for(;i!==null;)O=i,xp(i),i=i.sibling;O=r,Kr=a,Pe=c}pd(e)}else r.subtreeFlags&8772&&i!==null?(i.return=r,O=i):pd(e)}}function pd(e){for(;O!==null;){var t=O;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Pe||is(5,t);break;case 1:var o=t.stateNode;if(t.flags&4&&!Pe)if(n===null)o.componentDidMount();else{var r=t.elementType===t.type?n.memoizedProps:mt(t.type,n.memoizedProps);o.componentDidUpdate(r,n.memoizedState,o.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;i!==null&&Zu(t,i,o);break;case 3:var s=t.updateQueue;if(s!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Zu(t,s,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var u=c.memoizedState;if(u!==null){var d=u.dehydrated;d!==null&&or(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(S(163))}Pe||t.flags&512&&ha(t)}catch(f){re(t,t.return,f)}}if(t===e){O=null;break}if(n=t.sibling,n!==null){n.return=t.return,O=n;break}O=t.return}}function md(e){for(;O!==null;){var t=O;if(t===e){O=null;break}var n=t.sibling;if(n!==null){n.return=t.return,O=n;break}O=t.return}}function gd(e){for(;O!==null;){var t=O;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{is(4,t)}catch(l){re(t,n,l)}break;case 1:var o=t.stateNode;if(typeof o.componentDidMount=="function"){var r=t.return;try{o.componentDidMount()}catch(l){re(t,r,l)}}var i=t.return;try{ha(t)}catch(l){re(t,i,l)}break;case 5:var s=t.return;try{ha(t)}catch(l){re(t,s,l)}}}catch(l){re(t,t.return,l)}if(t===e){O=null;break}var a=t.sibling;if(a!==null){a.return=t.return,O=a;break}O=t.return}}var F0=Math.ceil,zi=Vt.ReactCurrentDispatcher,vc=Vt.ReactCurrentOwner,dt=Vt.ReactCurrentBatchConfig,F=0,ye=null,ue=null,Se=0,qe=0,Zn=hn(0),ge=0,pr=null,_n=0,ss=0,yc=0,Yo=null,De=null,bc=0,po=1/0,Rt=null,Bi=!1,ga=null,on=null,Yr=!1,Xt=null,Di=0,Xo=0,va=null,fi=-1,hi=0;function Le(){return F&6?ae():fi!==-1?fi:fi=ae()}function rn(e){return e.mode&1?F&2&&Se!==0?Se&-Se:T0.transition!==null?(hi===0&&(hi=ih()),hi):(e=U,e!==0||(e=window.event,e=e===void 0?16:fh(e.type)),e):1}function bt(e,t,n,o){if(50<Xo)throw Xo=0,va=null,Error(S(185));kr(e,n,o),(!(F&2)||e!==ye)&&(e===ye&&(!(F&2)&&(ss|=n),ge===4&&Kt(e,Se)),Ve(e,o),n===1&&F===0&&!(t.mode&1)&&(po=ae()+500,ns&&pn()))}function Ve(e,t){var n=e.callbackNode;Tv(e,t);var o=wi(e,e===ye?Se:0);if(o===0)n!==null&&Cu(n),e.callbackNode=null,e.callbackPriority=0;else if(t=o&-o,e.callbackPriority!==t){if(n!=null&&Cu(n),t===1)e.tag===0?C0(vd.bind(null,e)):Rh(vd.bind(null,e)),x0(function(){!(F&6)&&pn()}),n=null;else{switch(sh(o)){case 1:n=Ua;break;case 4:n=oh;break;case 16:n=bi;break;case 536870912:n=rh;break;default:n=bi}n=_p(n,kp.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function kp(e,t){if(fi=-1,hi=0,F&6)throw Error(S(327));var n=e.callbackNode;if(ro()&&e.callbackNode!==n)return null;var o=wi(e,e===ye?Se:0);if(o===0)return null;if(o&30||o&e.expiredLanes||t)t=Mi(e,o);else{t=o;var r=F;F|=2;var i=Sp();(ye!==e||Se!==t)&&(Rt=null,po=ae()+500,Sn(e,t));do try{Q0();break}catch(a){Ep(e,a)}while(!0);rc(),zi.current=i,F=r,ue!==null?t=0:(ye=null,Se=0,t=ge)}if(t!==0){if(t===2&&(r=Wl(e),r!==0&&(o=r,t=ya(e,r))),t===1)throw n=pr,Sn(e,0),Kt(e,o),Ve(e,ae()),n;if(t===6)Kt(e,o);else{if(r=e.current.alternate,!(o&30)&&!W0(r)&&(t=Mi(e,o),t===2&&(i=Wl(e),i!==0&&(o=i,t=ya(e,i))),t===1))throw n=pr,Sn(e,0),Kt(e,o),Ve(e,ae()),n;switch(e.finishedWork=r,e.finishedLanes=o,t){case 0:case 1:throw Error(S(345));case 2:yn(e,De,Rt);break;case 3:if(Kt(e,o),(o&130023424)===o&&(t=bc+500-ae(),10<t)){if(wi(e,0)!==0)break;if(r=e.suspendedLanes,(r&o)!==o){Le(),e.pingedLanes|=e.suspendedLanes&r;break}e.timeoutHandle=Zl(yn.bind(null,e,De,Rt),t);break}yn(e,De,Rt);break;case 4:if(Kt(e,o),(o&4194240)===o)break;for(t=e.eventTimes,r=-1;0<o;){var s=31-yt(o);i=1<<s,s=t[s],s>r&&(r=s),o&=~i}if(o=r,o=ae()-o,o=(120>o?120:480>o?480:1080>o?1080:1920>o?1920:3e3>o?3e3:4320>o?4320:1960*F0(o/1960))-o,10<o){e.timeoutHandle=Zl(yn.bind(null,e,De,Rt),o);break}yn(e,De,Rt);break;case 5:yn(e,De,Rt);break;default:throw Error(S(329))}}}return Ve(e,ae()),e.callbackNode===n?kp.bind(null,e):null}function ya(e,t){var n=Yo;return e.current.memoizedState.isDehydrated&&(Sn(e,t).flags|=256),e=Mi(e,t),e!==2&&(t=De,De=n,t!==null&&ba(t)),e}function ba(e){De===null?De=e:De.push.apply(De,e)}function W0(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var o=0;o<n.length;o++){var r=n[o],i=r.getSnapshot;r=r.value;try{if(!xt(i(),r))return!1}catch(s){return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Kt(e,t){for(t&=~yc,t&=~ss,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-yt(t),o=1<<n;e[n]=-1,t&=~o}}function vd(e){if(F&6)throw Error(S(327));ro();var t=wi(e,0);if(!(t&1))return Ve(e,ae()),null;var n=Mi(e,t);if(e.tag!==0&&n===2){var o=Wl(e);o!==0&&(t=o,n=ya(e,o))}if(n===1)throw n=pr,Sn(e,0),Kt(e,t),Ve(e,ae()),n;if(n===6)throw Error(S(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,yn(e,De,Rt),Ve(e,ae()),null}function wc(e,t){var n=F;F|=1;try{return e(t)}finally{F=n,F===0&&(po=ae()+500,ns&&pn())}}function In(e){Xt!==null&&Xt.tag===0&&!(F&6)&&ro();var t=F;F|=1;var n=dt.transition,o=U;try{if(dt.transition=null,U=1,e)return e()}finally{U=o,dt.transition=n,F=t,!(F&6)&&pn()}}function xc(){qe=Zn.current,Z(Zn)}function Sn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,w0(n)),ue!==null)for(n=ue.return;n!==null;){var o=n;switch(tc(o),o.tag){case 1:o=o.type.childContextTypes,o!=null&&Ci();break;case 3:fo(),Z(je),Z(_e),uc();break;case 5:cc(o);break;case 4:fo();break;case 13:Z(te);break;case 19:Z(te);break;case 10:ic(o.type._context);break;case 22:case 23:xc()}n=n.return}if(ye=e,ue=e=sn(e.current,null),Se=qe=t,ge=0,pr=null,yc=ss=_n=0,De=Yo=null,wn!==null){for(t=0;t<wn.length;t++)if(n=wn[t],o=n.interleaved,o!==null){n.interleaved=null;var r=o.next,i=n.pending;if(i!==null){var s=i.next;i.next=r,o.next=s}n.pending=o}wn=null}return e}function Ep(e,t){do{var n=ue;try{if(rc(),ci.current=Ai,Oi){for(var o=ne.memoizedState;o!==null;){var r=o.queue;r!==null&&(r.pending=null),o=o.next}Oi=!1}if(Pn=0,ve=me=ne=null,qo=!1,dr=0,vc.current=null,n===null||n.return===null){ge=1,pr=t,ue=null;break}e:{var i=e,s=n.return,a=n,l=t;if(t=Se,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var c=l,u=a,d=u.tag;if(!(u.mode&1)&&(d===0||d===11||d===15)){var f=u.alternate;f?(u.updateQueue=f.updateQueue,u.memoizedState=f.memoizedState,u.lanes=f.lanes):(u.updateQueue=null,u.memoizedState=null)}var p=rd(s);if(p!==null){p.flags&=-257,id(p,s,a,i,t),p.mode&1&&od(i,c,t),t=p,l=c;var v=t.updateQueue;if(v===null){var x=new Set;x.add(l),t.updateQueue=x}else v.add(l);break e}else{if(!(t&1)){od(i,c,t),kc();break e}l=Error(S(426))}}else if(ee&&a.mode&1){var R=rd(s);if(R!==null){!(R.flags&65536)&&(R.flags|=256),id(R,s,a,i,t),nc(ho(l,a));break e}}i=l=ho(l,a),ge!==4&&(ge=2),Yo===null?Yo=[i]:Yo.push(i),i=s;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t;var m=sp(i,l,t);Gu(i,m);break e;case 1:a=l;var h=i.type,g=i.stateNode;if(!(i.flags&128)&&(typeof h.getDerivedStateFromError=="function"||g!==null&&typeof g.componentDidCatch=="function"&&(on===null||!on.has(g)))){i.flags|=65536,t&=-t,i.lanes|=t;var w=lp(i,a,t);Gu(i,w);break e}}i=i.return}while(i!==null)}Tp(n)}catch(T){t=T,ue===n&&n!==null&&(ue=n=n.return);continue}break}while(!0)}function Sp(){var e=zi.current;return zi.current=Ai,e===null?Ai:e}function kc(){(ge===0||ge===3||ge===2)&&(ge=4),ye===null||!(_n&268435455)&&!(ss&268435455)||Kt(ye,Se)}function Mi(e,t){var n=F;F|=2;var o=Sp();(ye!==e||Se!==t)&&(Rt=null,Sn(e,t));do try{U0();break}catch(r){Ep(e,r)}while(!0);if(rc(),F=n,zi.current=o,ue!==null)throw Error(S(261));return ye=null,Se=0,ge}function U0(){for(;ue!==null;)Cp(ue)}function Q0(){for(;ue!==null&&!vv();)Cp(ue)}function Cp(e){var t=Pp(e.alternate,e,qe);e.memoizedProps=e.pendingProps,t===null?Tp(e):ue=t,vc.current=null}function Tp(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=N0(n,t),n!==null){n.flags&=32767,ue=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{ge=6,ue=null;return}}else if(n=M0(n,t,qe),n!==null){ue=n;return}if(t=t.sibling,t!==null){ue=t;return}ue=t=e}while(t!==null);ge===0&&(ge=5)}function yn(e,t,n){var o=U,r=dt.transition;try{dt.transition=null,U=1,q0(e,t,n,o)}finally{dt.transition=r,U=o}return null}function q0(e,t,n,o){do ro();while(Xt!==null);if(F&6)throw Error(S(327));n=e.finishedWork;var r=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(S(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if($v(e,i),e===ye&&(ue=ye=null,Se=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||Yr||(Yr=!0,_p(bi,function(){return ro(),null})),i=(n.flags&15990)!==0,n.subtreeFlags&15990||i){i=dt.transition,dt.transition=null;var s=U;U=1;var a=F;F|=4,vc.current=null,H0(e,n),wp(n,e),h0(Xl),xi=!!Yl,Xl=Yl=null,e.current=n,V0(n),yv(),F=a,U=s,dt.transition=i}else e.current=n;if(Yr&&(Yr=!1,Xt=e,Di=r),i=e.pendingLanes,i===0&&(on=null),xv(n.stateNode),Ve(e,ae()),t!==null)for(o=e.onRecoverableError,n=0;n<t.length;n++)r=t[n],o(r.value,{componentStack:r.stack,digest:r.digest});if(Bi)throw Bi=!1,e=ga,ga=null,e;return Di&1&&e.tag!==0&&ro(),i=e.pendingLanes,i&1?e===va?Xo++:(Xo=0,va=e):Xo=0,pn(),null}function ro(){if(Xt!==null){var e=sh(Di),t=dt.transition,n=U;try{if(dt.transition=null,U=16>e?16:e,Xt===null)var o=!1;else{if(e=Xt,Xt=null,Di=0,F&6)throw Error(S(331));var r=F;for(F|=4,O=e.current;O!==null;){var i=O,s=i.child;if(O.flags&16){var a=i.deletions;if(a!==null){for(var l=0;l<a.length;l++){var c=a[l];for(O=c;O!==null;){var u=O;switch(u.tag){case 0:case 11:case 15:Ko(8,u,i)}var d=u.child;if(d!==null)d.return=u,O=d;else for(;O!==null;){u=O;var f=u.sibling,p=u.return;if(vp(u),u===c){O=null;break}if(f!==null){f.return=p,O=f;break}O=p}}}var v=i.alternate;if(v!==null){var x=v.child;if(x!==null){v.child=null;do{var R=x.sibling;x.sibling=null,x=R}while(x!==null)}}O=i}}if(i.subtreeFlags&2064&&s!==null)s.return=i,O=s;else e:for(;O!==null;){if(i=O,i.flags&2048)switch(i.tag){case 0:case 11:case 15:Ko(9,i,i.return)}var m=i.sibling;if(m!==null){m.return=i.return,O=m;break e}O=i.return}}var h=e.current;for(O=h;O!==null;){s=O;var g=s.child;if(s.subtreeFlags&2064&&g!==null)g.return=s,O=g;else e:for(s=h;O!==null;){if(a=O,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:is(9,a)}}catch(T){re(a,a.return,T)}if(a===s){O=null;break e}var w=a.sibling;if(w!==null){w.return=a.return,O=w;break e}O=a.return}}if(F=r,pn(),Pt&&typeof Pt.onPostCommitFiberRoot=="function")try{Pt.onPostCommitFiberRoot(Gi,e)}catch(T){}o=!0}return o}finally{U=n,dt.transition=t}}return!1}function yd(e,t,n){t=ho(n,t),t=sp(e,t,1),e=nn(e,t,1),t=Le(),e!==null&&(kr(e,1,t),Ve(e,t))}function re(e,t,n){if(e.tag===3)yd(e,e,n);else for(;t!==null;){if(t.tag===3){yd(t,e,n);break}else if(t.tag===1){var o=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof o.componentDidCatch=="function"&&(on===null||!on.has(o))){e=ho(n,e),e=lp(t,e,1),t=nn(t,e,1),e=Le(),t!==null&&(kr(t,1,e),Ve(t,e));break}}t=t.return}}function K0(e,t,n){var o=e.pingCache;o!==null&&o.delete(t),t=Le(),e.pingedLanes|=e.suspendedLanes&n,ye===e&&(Se&n)===n&&(ge===4||ge===3&&(Se&130023424)===Se&&500>ae()-bc?Sn(e,0):yc|=n),Ve(e,t)}function $p(e,t){t===0&&(e.mode&1?(t=Nr,Nr<<=1,!(Nr&130023424)&&(Nr=4194304)):t=1);var n=Le();e=jt(e,t),e!==null&&(kr(e,t,n),Ve(e,n))}function Y0(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),$p(e,n)}function X0(e,t){var n=0;switch(e.tag){case 13:var o=e.stateNode,r=e.memoizedState;r!==null&&(n=r.retryLane);break;case 19:o=e.stateNode;break;default:throw Error(S(314))}o!==null&&o.delete(t),$p(e,n)}var Pp;Pp=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||je.current)Me=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Me=!1,D0(e,t,n);Me=!!(e.flags&131072)}else Me=!1,ee&&t.flags&1048576&&Lh(t,Pi,t.index);switch(t.lanes=0,t.tag){case 2:var o=t.type;di(e,t),e=t.pendingProps;var r=ao(t,_e.current);oo(t,n),r=fc(null,t,o,e,r,n);var i=hc();return t.flags|=1,typeof r=="object"&&r!==null&&typeof r.render=="function"&&r.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,He(o)?(i=!0,Ti(t)):i=!1,t.memoizedState=r.state!==null&&r.state!==void 0?r.state:null,lc(t),r.updater=rs,t.stateNode=r,r._reactInternals=t,ia(t,o,e,n),t=aa(null,t,o,!0,i,n)):(t.tag=0,ee&&i&&ec(t),Re(null,t,r,n),t=t.child),t;case 16:o=t.elementType;e:{switch(di(e,t),e=t.pendingProps,r=o._init,o=r(o._payload),t.type=o,r=t.tag=Z0(o),e=mt(o,e),r){case 0:t=la(null,t,o,e,n);break e;case 1:t=ad(null,t,o,e,n);break e;case 11:t=sd(null,t,o,e,n);break e;case 14:t=ld(null,t,o,mt(o.type,e),n);break e}throw Error(S(306,o,""))}return t;case 0:return o=t.type,r=t.pendingProps,r=t.elementType===o?r:mt(o,r),la(e,t,o,r,n);case 1:return o=t.type,r=t.pendingProps,r=t.elementType===o?r:mt(o,r),ad(e,t,o,r,n);case 3:e:{if(dp(t),e===null)throw Error(S(387));o=t.pendingProps,i=t.memoizedState,r=i.element,Mh(e,t),Ri(t,o,null,n);var s=t.memoizedState;if(o=s.element,i.isDehydrated)if(i={element:o,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=i,t.memoizedState=i,t.flags&256){r=ho(Error(S(423)),t),t=cd(e,t,o,n,r);break e}else if(o!==r){r=ho(Error(S(424)),t),t=cd(e,t,o,n,r);break e}else for(Ye=tn(t.stateNode.containerInfo.firstChild),Ge=t,ee=!0,vt=null,n=Bh(t,null,o,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(co(),o===r){t=Ht(e,t,n);break e}Re(e,t,o,n)}t=t.child}return t;case 5:return Nh(t),e===null&&na(t),o=t.type,r=t.pendingProps,i=e!==null?e.memoizedProps:null,s=r.children,Gl(o,r)?s=null:i!==null&&Gl(o,i)&&(t.flags|=32),up(e,t),Re(e,t,s,n),t.child;case 6:return e===null&&na(t),null;case 13:return fp(e,t,n);case 4:return ac(t,t.stateNode.containerInfo),o=t.pendingProps,e===null?t.child=uo(t,null,o,n):Re(e,t,o,n),t.child;case 11:return o=t.type,r=t.pendingProps,r=t.elementType===o?r:mt(o,r),sd(e,t,o,r,n);case 7:return Re(e,t,t.pendingProps,n),t.child;case 8:return Re(e,t,t.pendingProps.children,n),t.child;case 12:return Re(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(o=t.type._context,r=t.pendingProps,i=t.memoizedProps,s=r.value,Y(_i,o._currentValue),o._currentValue=s,i!==null)if(xt(i.value,s)){if(i.children===r.children&&!je.current){t=Ht(e,t,n);break e}}else for(i=t.child,i!==null&&(i.return=t);i!==null;){var a=i.dependencies;if(a!==null){s=i.child;for(var l=a.firstContext;l!==null;){if(l.context===o){if(i.tag===1){l=Dt(-1,n&-n),l.tag=2;var c=i.updateQueue;if(c!==null){c=c.shared;var u=c.pending;u===null?l.next=l:(l.next=u.next,u.next=l),c.pending=l}}i.lanes|=n,l=i.alternate,l!==null&&(l.lanes|=n),oa(i.return,n,t),a.lanes|=n;break}l=l.next}}else if(i.tag===10)s=i.type===t.type?null:i.child;else if(i.tag===18){if(s=i.return,s===null)throw Error(S(341));s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),oa(s,n,t),s=i.sibling}else s=i.child;if(s!==null)s.return=i;else for(s=i;s!==null;){if(s===t){s=null;break}if(i=s.sibling,i!==null){i.return=s.return,s=i;break}s=s.return}i=s}Re(e,t,r.children,n),t=t.child}return t;case 9:return r=t.type,o=t.pendingProps.children,oo(t,n),r=ft(r),o=o(r),t.flags|=1,Re(e,t,o,n),t.child;case 14:return o=t.type,r=mt(o,t.pendingProps),r=mt(o.type,r),ld(e,t,o,r,n);case 15:return ap(e,t,t.type,t.pendingProps,n);case 17:return o=t.type,r=t.pendingProps,r=t.elementType===o?r:mt(o,r),di(e,t),t.tag=1,He(o)?(e=!0,Ti(t)):e=!1,oo(t,n),ip(t,o,r),ia(t,o,r,n),aa(null,t,o,!0,e,n);case 19:return hp(e,t,n);case 22:return cp(e,t,n)}throw Error(S(156,t.tag))};function _p(e,t){return nh(e,t)}function G0(e,t,n,o){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=o,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function ut(e,t,n,o){return new G0(e,t,n,o)}function Ec(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Z0(e){if(typeof e=="function")return Ec(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Va)return 11;if(e===Fa)return 14}return 2}function sn(e,t){var n=e.alternate;return n===null?(n=ut(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function pi(e,t,n,o,r,i){var s=2;if(o=e,typeof e=="function")Ec(e)&&(s=1);else if(typeof e=="string")s=5;else e:switch(e){case Vn:return Cn(n.children,r,i,t);case Ha:s=8,r|=8;break;case _l:return e=ut(12,n,t,r|2),e.elementType=_l,e.lanes=i,e;case Il:return e=ut(13,n,t,r),e.elementType=Il,e.lanes=i,e;case Rl:return e=ut(19,n,t,r),e.elementType=Rl,e.lanes=i,e;case Nf:return ls(n,r,i,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Df:s=10;break e;case Mf:s=9;break e;case Va:s=11;break e;case Fa:s=14;break e;case Ut:s=16,o=null;break e}throw Error(S(130,e==null?e:typeof e,""))}return t=ut(s,n,t,r),t.elementType=e,t.type=o,t.lanes=i,t}function Cn(e,t,n,o){return e=ut(7,e,o,t),e.lanes=n,e}function ls(e,t,n,o){return e=ut(22,e,o,t),e.elementType=Nf,e.lanes=n,e.stateNode={isHidden:!1},e}function ul(e,t,n){return e=ut(6,e,null,t),e.lanes=n,e}function dl(e,t,n){return t=ut(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function J0(e,t,n,o,r){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=Us(0),this.expirationTimes=Us(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Us(0),this.identifierPrefix=o,this.onRecoverableError=r,this.mutableSourceEagerHydrationData=null}function Sc(e,t,n,o,r,i,s,a,l){return e=new J0(e,t,n,a,l),t===1?(t=1,i===!0&&(t|=8)):t=0,i=ut(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:o,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},lc(i),e}function ey(e,t,n){var o=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:Hn,key:o==null?null:""+o,children:e,containerInfo:t,implementation:n}}function Ip(e){if(!e)return cn;e=e._reactInternals;e:{if(An(e)!==e||e.tag!==1)throw Error(S(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(He(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(S(171))}if(e.tag===1){var n=e.type;if(He(n))return Ih(e,n,t)}return t}function Rp(e,t,n,o,r,i,s,a,l){return e=Sc(n,o,!0,e,r,i,s,a,l),e.context=Ip(null),n=e.current,o=Le(),r=rn(n),i=Dt(o,r),i.callback=t!=null?t:null,nn(n,i,r),e.current.lanes=r,kr(e,r,o),Ve(e,o),e}function as(e,t,n,o){var r=t.current,i=Le(),s=rn(r);return n=Ip(n),t.context===null?t.context=n:t.pendingContext=n,t=Dt(i,s),t.payload={element:e},o=o===void 0?null:o,o!==null&&(t.callback=o),e=nn(r,t,s),e!==null&&(bt(e,r,s,i),ai(e,r,s)),s}function Ni(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function bd(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Cc(e,t){bd(e,t),(e=e.alternate)&&bd(e,t)}function ty(){return null}var Lp=typeof reportError=="function"?reportError:function(e){console.error(e)};function Tc(e){this._internalRoot=e}cs.prototype.render=Tc.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(S(409));as(e,t,null,null)};cs.prototype.unmount=Tc.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;In(function(){as(null,e,null,null)}),t[Nt]=null}};function cs(e){this._internalRoot=e}cs.prototype.unstable_scheduleHydration=function(e){if(e){var t=ch();e={blockedOn:null,target:e,priority:t};for(var n=0;n<qt.length&&t!==0&&t<qt[n].priority;n++);qt.splice(n,0,e),n===0&&dh(e)}};function $c(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function us(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function wd(){}function ny(e,t,n,o,r){if(r){if(typeof o=="function"){var i=o;o=function(){var c=Ni(s);i.call(c)}}var s=Rp(t,o,e,0,null,!1,!1,"",wd);return e._reactRootContainer=s,e[Nt]=s.current,sr(e.nodeType===8?e.parentNode:e),In(),s}for(;r=e.lastChild;)e.removeChild(r);if(typeof o=="function"){var a=o;o=function(){var c=Ni(l);a.call(c)}}var l=Sc(e,0,!1,null,null,!1,!1,"",wd);return e._reactRootContainer=l,e[Nt]=l.current,sr(e.nodeType===8?e.parentNode:e),In(function(){as(t,l,n,o)}),l}function ds(e,t,n,o,r){var i=n._reactRootContainer;if(i){var s=i;if(typeof r=="function"){var a=r;r=function(){var l=Ni(s);a.call(l)}}as(t,s,e,r)}else s=ny(n,t,e,r,o);return Ni(s)}lh=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=Do(t.pendingLanes);n!==0&&(Qa(t,n|1),Ve(t,ae()),!(F&6)&&(po=ae()+500,pn()))}break;case 13:In(function(){var o=jt(e,1);if(o!==null){var r=Le();bt(o,e,1,r)}}),Cc(e,1)}};qa=function(e){if(e.tag===13){var t=jt(e,134217728);if(t!==null){var n=Le();bt(t,e,134217728,n)}Cc(e,134217728)}};ah=function(e){if(e.tag===13){var t=rn(e),n=jt(e,t);if(n!==null){var o=Le();bt(n,e,t,o)}Cc(e,t)}};ch=function(){return U};uh=function(e,t){var n=U;try{return U=e,t()}finally{U=n}};Hl=function(e,t,n){switch(t){case"input":if(Al(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var o=n[t];if(o!==e&&o.form===e.form){var r=ts(o);if(!r)throw Error(S(90));Hf(o),Al(o,r)}}}break;case"textarea":Ff(e,n);break;case"select":t=n.value,t!=null&&Jn(e,!!n.multiple,t,!1)}};Xf=wc;Gf=In;var oy={usingClientEntryPoint:!1,Events:[Sr,Qn,ts,Kf,Yf,wc]},Lo={findFiberByHostInstance:bn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},ry={bundleType:Lo.bundleType,version:Lo.version,rendererPackageName:Lo.rendererPackageName,rendererConfig:Lo.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Vt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=eh(e),e===null?null:e.stateNode},findFiberByHostInstance:Lo.findFiberByHostInstance||ty,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Xr=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Xr.isDisabled&&Xr.supportsFiber)try{Gi=Xr.inject(ry),Pt=Xr}catch(e){}}Je.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=oy;Je.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!$c(t))throw Error(S(200));return ey(e,t,null,n)};Je.createRoot=function(e,t){if(!$c(e))throw Error(S(299));var n=!1,o="",r=Lp;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(o=t.identifierPrefix),t.onRecoverableError!==void 0&&(r=t.onRecoverableError)),t=Sc(e,1,!1,null,null,n,!1,o,r),e[Nt]=t.current,sr(e.nodeType===8?e.parentNode:e),new Tc(t)};Je.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(S(188)):(e=Object.keys(e).join(","),Error(S(268,e)));return e=eh(t),e=e===null?null:e.stateNode,e};Je.flushSync=function(e){return In(e)};Je.hydrate=function(e,t,n){if(!us(t))throw Error(S(200));return ds(null,e,t,!0,n)};Je.hydrateRoot=function(e,t,n){if(!$c(e))throw Error(S(405));var o=n!=null&&n.hydratedSources||null,r=!1,i="",s=Lp;if(n!=null&&(n.unstable_strictMode===!0&&(r=!0),n.identifierPrefix!==void 0&&(i=n.identifierPrefix),n.onRecoverableError!==void 0&&(s=n.onRecoverableError)),t=Rp(t,null,e,1,n!=null?n:null,r,!1,i,s),e[Nt]=t.current,sr(e),o)for(e=0;e<o.length;e++)n=o[e],r=n._getVersion,r=r(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,r]:t.mutableSourceEagerHydrationData.push(n,r);return new cs(t)};Je.render=function(e,t,n){if(!us(t))throw Error(S(200));return ds(null,e,t,!1,n)};Je.unmountComponentAtNode=function(e){if(!us(e))throw Error(S(40));return e._reactRootContainer?(In(function(){ds(null,null,e,!1,function(){e._reactRootContainer=null,e[Nt]=null})}),!0):!1};Je.unstable_batchedUpdates=wc;Je.unstable_renderSubtreeIntoContainer=function(e,t,n,o){if(!us(n))throw Error(S(200));if(e==null||e._reactInternals===void 0)throw Error(S(38));return ds(e,t,n,!1,o)};Je.version="18.3.1-next-f1338f8080-20240426";function Op(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(Op)}catch(e){console.error(e)}}Op(),Of.exports=Je;var Pc=Of.exports;const iy=wr(Pc);var Ap,xd=Pc;Ap=xd.createRoot,xd.hydrateRoot;function wa(e,t){return wa=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,o){return n.__proto__=o,n},wa(e,t)}function un(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,wa(e,t)}var zp={exports:{}},sy="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED",ly=sy,ay=ly;function Bp(){}function Dp(){}Dp.resetWarningCache=Bp;var cy=function(){function e(o,r,i,s,a,l){if(l!==ay){var c=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw c.name="Invariant Violation",c}}e.isRequired=e;function t(){return e}var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:Dp,resetWarningCache:Bp};return n.PropTypes=n,n};zp.exports=cy();var uy=zp.exports;const kd=wr(uy);function Fe(){return Fe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},Fe.apply(null,arguments)}function Gr(e){return e.charAt(0)==="/"}function fl(e,t){for(var n=t,o=n+1,r=e.length;o<r;n+=1,o+=1)e[n]=e[o];e.pop()}function dy(e,t){t===void 0&&(t="");var n=e&&e.split("/")||[],o=t&&t.split("/")||[],r=e&&Gr(e),i=t&&Gr(t),s=r||i;if(e&&Gr(e)?o=n:n.length&&(o.pop(),o=o.concat(n)),!o.length)return"/";var a;if(o.length){var l=o[o.length-1];a=l==="."||l===".."||l===""}else a=!1;for(var c=0,u=o.length;u>=0;u--){var d=o[u];d==="."?fl(o,u):d===".."?(fl(o,u),c++):c&&(fl(o,u),c--)}if(!s)for(;c--;c)o.unshift("..");s&&o[0]!==""&&(!o[0]||!Gr(o[0]))&&o.unshift("");var f=o.join("/");return a&&f.substr(-1)!=="/"&&(f+="/"),f}function Ed(e){return e.valueOf?e.valueOf():Object.prototype.valueOf.call(e)}function mi(e,t){if(e===t)return!0;if(e==null||t==null)return!1;if(Array.isArray(e))return Array.isArray(t)&&e.length===t.length&&e.every(function(r,i){return mi(r,t[i])});if(typeof e=="object"||typeof t=="object"){var n=Ed(e),o=Ed(t);return n!==e||o!==t?mi(n,o):Object.keys(Object.assign({},e,t)).every(function(r){return mi(e[r],t[r])})}return!1}var fy="Invariant failed";function zn(e,t){throw new Error(fy)}function Go(e){return e.charAt(0)==="/"?e:"/"+e}function Sd(e){return e.charAt(0)==="/"?e.substr(1):e}function hy(e,t){return e.toLowerCase().indexOf(t.toLowerCase())===0&&"/?#".indexOf(e.charAt(t.length))!==-1}function Mp(e,t){return hy(e,t)?e.substr(t.length):e}function Np(e){return e.charAt(e.length-1)==="/"?e.slice(0,-1):e}function py(e){var t=e||"/",n="",o="",r=t.indexOf("#");r!==-1&&(o=t.substr(r),t=t.substr(0,r));var i=t.indexOf("?");return i!==-1&&(n=t.substr(i),t=t.substr(0,i)),{pathname:t,search:n==="?"?"":n,hash:o==="#"?"":o}}function st(e){var t=e.pathname,n=e.search,o=e.hash,r=t||"/";return n&&n!=="?"&&(r+=n.charAt(0)==="?"?n:"?"+n),o&&o!=="#"&&(r+=o.charAt(0)==="#"?o:"#"+o),r}function Ne(e,t,n,o){var r;typeof e=="string"?(r=py(e),r.state=t):(r=Fe({},e),r.pathname===void 0&&(r.pathname=""),r.search?r.search.charAt(0)!=="?"&&(r.search="?"+r.search):r.search="",r.hash?r.hash.charAt(0)!=="#"&&(r.hash="#"+r.hash):r.hash="",t!==void 0&&r.state===void 0&&(r.state=t));try{r.pathname=decodeURI(r.pathname)}catch(i){throw i instanceof URIError?new URIError('Pathname "'+r.pathname+'" could not be decoded. This is likely caused by an invalid percent-encoding.'):i}return n&&(r.key=n),o?r.pathname?r.pathname.charAt(0)!=="/"&&(r.pathname=dy(r.pathname,o.pathname)):r.pathname=o.pathname:r.pathname||(r.pathname="/"),r}function my(e,t){return e.pathname===t.pathname&&e.search===t.search&&e.hash===t.hash&&e.key===t.key&&mi(e.state,t.state)}function _c(){var e=null;function t(s){return e=s,function(){e===s&&(e=null)}}function n(s,a,l,c){if(e!=null){var u=typeof e=="function"?e(s,a):e;typeof u=="string"?typeof l=="function"?l(u,c):c(!0):c(u!==!1)}else c(!0)}var o=[];function r(s){var a=!0;function l(){a&&s.apply(void 0,arguments)}return o.push(l),function(){a=!1,o=o.filter(function(c){return c!==l})}}function i(){for(var s=arguments.length,a=new Array(s),l=0;l<s;l++)a[l]=arguments[l];o.forEach(function(c){return c.apply(void 0,a)})}return{setPrompt:t,confirmTransitionTo:n,appendListener:r,notifyListeners:i}}var jp=!!(typeof window<"u"&&window.document&&window.document.createElement);function Hp(e,t){t(window.confirm(e))}function gy(){var e=window.navigator.userAgent;return(e.indexOf("Android 2.")!==-1||e.indexOf("Android 4.0")!==-1)&&e.indexOf("Mobile Safari")!==-1&&e.indexOf("Chrome")===-1&&e.indexOf("Windows Phone")===-1?!1:window.history&&"pushState"in window.history}function vy(){return window.navigator.userAgent.indexOf("Trident")===-1}function yy(){return window.navigator.userAgent.indexOf("Firefox")===-1}function by(e){return e.state===void 0&&navigator.userAgent.indexOf("CriOS")===-1}var Cd="popstate",Td="hashchange";function $d(){try{return window.history.state||{}}catch(e){return{}}}function wy(e){e===void 0&&(e={}),jp||zn();var t=window.history,n=gy(),o=!vy(),r=e,i=r.forceRefresh,s=i===void 0?!1:i,a=r.getUserConfirmation,l=a===void 0?Hp:a,c=r.keyLength,u=c===void 0?6:c,d=e.basename?Np(Go(e.basename)):"";function f(C){var E=C||{},z=E.key,L=E.state,W=window.location,H=W.pathname,ce=W.search,se=W.hash,he=H+ce+se;return d&&(he=Mp(he,d)),Ne(he,L,z)}function p(){return Math.random().toString(36).substr(2,u)}var v=_c();function x(C){Fe(_,C),_.length=t.length,v.notifyListeners(_.location,_.action)}function R(C){by(C)||g(f(C.state))}function m(){g(f($d()))}var h=!1;function g(C){if(h)h=!1,x();else{var E="POP";v.confirmTransitionTo(C,E,l,function(z){z?x({action:E,location:C}):w(C)})}}function w(C){var E=_.location,z=P.indexOf(E.key);z===-1&&(z=0);var L=P.indexOf(C.key);L===-1&&(L=0);var W=z-L;W&&(h=!0,B(W))}var T=f($d()),P=[T.key];function k(C){return d+st(C)}function $(C,E){var z="PUSH",L=Ne(C,E,p(),_.location);v.confirmTransitionTo(L,z,l,function(W){if(W){var H=k(L),ce=L.key,se=L.state;if(n)if(t.pushState({key:ce,state:se},null,H),s)window.location.href=H;else{var he=P.indexOf(_.location.key),It=P.slice(0,he+1);It.push(L.key),P=It,x({action:z,location:L})}else window.location.href=H}})}function I(C,E){var z="REPLACE",L=Ne(C,E,p(),_.location);v.confirmTransitionTo(L,z,l,function(W){if(W){var H=k(L),ce=L.key,se=L.state;if(n)if(t.replaceState({key:ce,state:se},null,H),s)window.location.replace(H);else{var he=P.indexOf(_.location.key);he!==-1&&(P[he]=L.key),x({action:z,location:L})}else window.location.replace(H)}})}function B(C){t.go(C)}function V(){B(-1)}function de(){B(1)}var fe=0;function ie(C){fe+=C,fe===1&&C===1?(window.addEventListener(Cd,R),o&&window.addEventListener(Td,m)):fe===0&&(window.removeEventListener(Cd,R),o&&window.removeEventListener(Td,m))}var K=!1;function J(C){C===void 0&&(C=!1);var E=v.setPrompt(C);return K||(ie(1),K=!0),function(){return K&&(K=!1,ie(-1)),E()}}function kt(C){var E=v.appendListener(C);return ie(1),function(){ie(-1),E()}}var _={length:t.length,action:"POP",location:T,createHref:k,push:$,replace:I,go:B,goBack:V,goForward:de,block:J,listen:kt};return _}var Pd="hashchange",xy={hashbang:{encodePath:function(t){return t.charAt(0)==="!"?t:"!/"+Sd(t)},decodePath:function(t){return t.charAt(0)==="!"?t.substr(1):t}},noslash:{encodePath:Sd,decodePath:Go},slash:{encodePath:Go,decodePath:Go}};function Vp(e){var t=e.indexOf("#");return t===-1?e:e.slice(0,t)}function Oo(){var e=window.location.href,t=e.indexOf("#");return t===-1?"":e.substring(t+1)}function ky(e){window.location.hash=e}function hl(e){window.location.replace(Vp(window.location.href)+"#"+e)}function Ey(e){e===void 0&&(e={}),jp||zn();var t=window.history;yy();var n=e,o=n.getUserConfirmation,r=o===void 0?Hp:o,i=n.hashType,s=i===void 0?"slash":i,a=e.basename?Np(Go(e.basename)):"",l=xy[s],c=l.encodePath,u=l.decodePath;function d(){var E=u(Oo());return a&&(E=Mp(E,a)),Ne(E)}var f=_c();function p(E){Fe(C,E),C.length=t.length,f.notifyListeners(C.location,C.action)}var v=!1,x=null;function R(E,z){return E.pathname===z.pathname&&E.search===z.search&&E.hash===z.hash}function m(){var E=Oo(),z=c(E);if(E!==z)hl(z);else{var L=d(),W=C.location;if(!v&&R(W,L)||x===st(L))return;x=null,h(L)}}function h(E){if(v)v=!1,p();else{var z="POP";f.confirmTransitionTo(E,z,r,function(L){L?p({action:z,location:E}):g(E)})}}function g(E){var z=C.location,L=k.lastIndexOf(st(z));L===-1&&(L=0);var W=k.lastIndexOf(st(E));W===-1&&(W=0);var H=L-W;H&&(v=!0,V(H))}var w=Oo(),T=c(w);w!==T&&hl(T);var P=d(),k=[st(P)];function $(E){var z=document.querySelector("base"),L="";return z&&z.getAttribute("href")&&(L=Vp(window.location.href)),L+"#"+c(a+st(E))}function I(E,z){var L="PUSH",W=Ne(E,void 0,void 0,C.location);f.confirmTransitionTo(W,L,r,function(H){if(H){var ce=st(W),se=c(a+ce),he=Oo()!==se;if(he){x=ce,ky(se);var It=k.lastIndexOf(st(C.location)),Rr=k.slice(0,It+1);Rr.push(ce),k=Rr,p({action:L,location:W})}else p()}})}function B(E,z){var L="REPLACE",W=Ne(E,void 0,void 0,C.location);f.confirmTransitionTo(W,L,r,function(H){if(H){var ce=st(W),se=c(a+ce),he=Oo()!==se;he&&(x=ce,hl(se));var It=k.indexOf(st(C.location));It!==-1&&(k[It]=ce),p({action:L,location:W})}})}function V(E){t.go(E)}function de(){V(-1)}function fe(){V(1)}var ie=0;function K(E){ie+=E,ie===1&&E===1?window.addEventListener(Pd,m):ie===0&&window.removeEventListener(Pd,m)}var J=!1;function kt(E){E===void 0&&(E=!1);var z=f.setPrompt(E);return J||(K(1),J=!0),function(){return J&&(J=!1,K(-1)),z()}}function _(E){var z=f.appendListener(E);return K(1),function(){K(-1),z()}}var C={length:t.length,action:"POP",location:P,createHref:$,push:I,replace:B,go:V,goBack:de,goForward:fe,block:kt,listen:_};return C}function _d(e,t,n){return Math.min(Math.max(e,t),n)}function Sy(e){e===void 0&&(e={});var t=e,n=t.getUserConfirmation,o=t.initialEntries,r=o===void 0?["/"]:o,i=t.initialIndex,s=i===void 0?0:i,a=t.keyLength,l=a===void 0?6:a,c=_c();function u($){Fe(k,$),k.length=k.entries.length,c.notifyListeners(k.location,k.action)}function d(){return Math.random().toString(36).substr(2,l)}var f=_d(s,0,r.length-1),p=r.map(function($){return typeof $=="string"?Ne($,void 0,d()):Ne($,void 0,$.key||d())}),v=st;function x($,I){var B="PUSH",V=Ne($,I,d(),k.location);c.confirmTransitionTo(V,B,n,function(de){if(de){var fe=k.index,ie=fe+1,K=k.entries.slice(0);K.length>ie?K.splice(ie,K.length-ie,V):K.push(V),u({action:B,location:V,index:ie,entries:K})}})}function R($,I){var B="REPLACE",V=Ne($,I,d(),k.location);c.confirmTransitionTo(V,B,n,function(de){de&&(k.entries[k.index]=V,u({action:B,location:V}))})}function m($){var I=_d(k.index+$,0,k.entries.length-1),B="POP",V=k.entries[I];c.confirmTransitionTo(V,B,n,function(de){de?u({action:B,location:V,index:I}):u()})}function h(){m(-1)}function g(){m(1)}function w($){var I=k.index+$;return I>=0&&I<k.entries.length}function T($){return $===void 0&&($=!1),c.setPrompt($)}function P($){return c.appendListener($)}var k={length:p.length,action:"POP",location:p[f],index:f,entries:p,createHref:v,push:x,replace:R,go:m,goBack:h,goForward:g,canGo:w,block:T,listen:P};return k}var Eo={exports:{}},Cy=Array.isArray||function(e){return Object.prototype.toString.call(e)=="[object Array]"},ji=Cy;Eo.exports=Up;Eo.exports.parse=Ic;Eo.exports.compile=Py;Eo.exports.tokensToFunction=Fp;Eo.exports.tokensToRegExp=Wp;var Ty=new RegExp(["(\\\\.)","([\\/.])?(?:(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?|(\\*))"].join("|"),"g");function Ic(e,t){for(var n=[],o=0,r=0,i="",s=t&&t.delimiter||"/",a;(a=Ty.exec(e))!=null;){var l=a[0],c=a[1],u=a.index;if(i+=e.slice(r,u),r=u+l.length,c){i+=c[1];continue}var d=e[r],f=a[2],p=a[3],v=a[4],x=a[5],R=a[6],m=a[7];i&&(n.push(i),i="");var h=f!=null&&d!=null&&d!==f,g=R==="+"||R==="*",w=R==="?"||R==="*",T=f||s,P=v||x,k=f||(typeof n[n.length-1]=="string"?n[n.length-1]:"");n.push({name:p||o++,prefix:f||"",delimiter:T,optional:w,repeat:g,partial:h,asterisk:!!m,pattern:P?Ry(P):m?".*":$y(T,k)})}return r<e.length&&(i+=e.substr(r)),i&&n.push(i),n}function $y(e,t){return!t||t.indexOf(e)>-1?"[^"+kn(e)+"]+?":kn(t)+"|(?:(?!"+kn(t)+")[^"+kn(e)+"])+?"}function Py(e,t){return Fp(Ic(e,t),t)}function _y(e){return encodeURI(e).replace(/[\/?#]/g,function(t){return"%"+t.charCodeAt(0).toString(16).toUpperCase()})}function Iy(e){return encodeURI(e).replace(/[?#]/g,function(t){return"%"+t.charCodeAt(0).toString(16).toUpperCase()})}function Fp(e,t){for(var n=new Array(e.length),o=0;o<e.length;o++)typeof e[o]=="object"&&(n[o]=new RegExp("^(?:"+e[o].pattern+")$",Lc(t)));return function(r,i){for(var s="",a=r||{},l=i||{},c=l.pretty?_y:encodeURIComponent,u=0;u<e.length;u++){var d=e[u];if(typeof d=="string"){s+=d;continue}var f=a[d.name],p;if(f==null)if(d.optional){d.partial&&(s+=d.prefix);continue}else throw new TypeError('Expected "'+d.name+'" to be defined');if(ji(f)){if(!d.repeat)throw new TypeError('Expected "'+d.name+'" to not repeat, but received `'+JSON.stringify(f)+"`");if(f.length===0){if(d.optional)continue;throw new TypeError('Expected "'+d.name+'" to not be empty')}for(var v=0;v<f.length;v++){if(p=c(f[v]),!n[u].test(p))throw new TypeError('Expected all "'+d.name+'" to match "'+d.pattern+'", but received `'+JSON.stringify(p)+"`");s+=(v===0?d.prefix:d.delimiter)+p}continue}if(p=d.asterisk?Iy(f):c(f),!n[u].test(p))throw new TypeError('Expected "'+d.name+'" to match "'+d.pattern+'", but received "'+p+'"');s+=d.prefix+p}return s}}function kn(e){return e.replace(/([.+*?=^!:${}()[\]|\/\\])/g,"\\$1")}function Ry(e){return e.replace(/([=!:$\/()])/g,"\\$1")}function Rc(e,t){return e.keys=t,e}function Lc(e){return e&&e.sensitive?"":"i"}function Ly(e,t){var n=e.source.match(/\((?!\?)/g);if(n)for(var o=0;o<n.length;o++)t.push({name:o,prefix:null,delimiter:null,optional:!1,repeat:!1,partial:!1,asterisk:!1,pattern:null});return Rc(e,t)}function Oy(e,t,n){for(var o=[],r=0;r<e.length;r++)o.push(Up(e[r],t,n).source);var i=new RegExp("(?:"+o.join("|")+")",Lc(n));return Rc(i,t)}function Ay(e,t,n){return Wp(Ic(e,n),t,n)}function Wp(e,t,n){ji(t)||(n=t||n,t=[]),n=n||{};for(var o=n.strict,r=n.end!==!1,i="",s=0;s<e.length;s++){var a=e[s];if(typeof a=="string")i+=kn(a);else{var l=kn(a.prefix),c="(?:"+a.pattern+")";t.push(a),a.repeat&&(c+="(?:"+l+c+")*"),a.optional?a.partial?c=l+"("+c+")?":c="(?:"+l+"("+c+"))?":c=l+"("+c+")",i+=c}}var u=kn(n.delimiter||"/"),d=i.slice(-u.length)===u;return o||(i=(d?i.slice(0,-u.length):i)+"(?:"+u+"(?=$))?"),r?i+="$":i+=o&&d?"":"(?="+u+"|$)",Rc(new RegExp("^"+i,Lc(n)),t)}function Up(e,t,n){return ji(t)||(n=t||n,t=[]),n=n||{},e instanceof RegExp?Ly(e,t):ji(e)?Oy(e,t,n):Ay(e,t,n)}var zy=Eo.exports;const Qp=wr(zy);var q={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xe=typeof Symbol=="function"&&Symbol.for,Oc=xe?Symbol.for("react.element"):60103,Ac=xe?Symbol.for("react.portal"):60106,fs=xe?Symbol.for("react.fragment"):60107,hs=xe?Symbol.for("react.strict_mode"):60108,ps=xe?Symbol.for("react.profiler"):60114,ms=xe?Symbol.for("react.provider"):60109,gs=xe?Symbol.for("react.context"):60110,zc=xe?Symbol.for("react.async_mode"):60111,vs=xe?Symbol.for("react.concurrent_mode"):60111,ys=xe?Symbol.for("react.forward_ref"):60112,bs=xe?Symbol.for("react.suspense"):60113,By=xe?Symbol.for("react.suspense_list"):60120,ws=xe?Symbol.for("react.memo"):60115,xs=xe?Symbol.for("react.lazy"):60116,Dy=xe?Symbol.for("react.block"):60121,My=xe?Symbol.for("react.fundamental"):60117,Ny=xe?Symbol.for("react.responder"):60118,jy=xe?Symbol.for("react.scope"):60119;function tt(e){if(typeof e=="object"&&e!==null){var t=e.$$typeof;switch(t){case Oc:switch(e=e.type,e){case zc:case vs:case fs:case ps:case hs:case bs:return e;default:switch(e=e&&e.$$typeof,e){case gs:case ys:case xs:case ws:case ms:return e;default:return t}}case Ac:return t}}}function qp(e){return tt(e)===vs}q.AsyncMode=zc;q.ConcurrentMode=vs;q.ContextConsumer=gs;q.ContextProvider=ms;q.Element=Oc;q.ForwardRef=ys;q.Fragment=fs;q.Lazy=xs;q.Memo=ws;q.Portal=Ac;q.Profiler=ps;q.StrictMode=hs;q.Suspense=bs;q.isAsyncMode=function(e){return qp(e)||tt(e)===zc};q.isConcurrentMode=qp;q.isContextConsumer=function(e){return tt(e)===gs};q.isContextProvider=function(e){return tt(e)===ms};q.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===Oc};q.isForwardRef=function(e){return tt(e)===ys};q.isFragment=function(e){return tt(e)===fs};q.isLazy=function(e){return tt(e)===xs};q.isMemo=function(e){return tt(e)===ws};q.isPortal=function(e){return tt(e)===Ac};q.isProfiler=function(e){return tt(e)===ps};q.isStrictMode=function(e){return tt(e)===hs};q.isSuspense=function(e){return tt(e)===bs};q.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===fs||e===vs||e===ps||e===hs||e===bs||e===By||typeof e=="object"&&e!==null&&(e.$$typeof===xs||e.$$typeof===ws||e.$$typeof===ms||e.$$typeof===gs||e.$$typeof===ys||e.$$typeof===My||e.$$typeof===Ny||e.$$typeof===jy||e.$$typeof===Dy)};q.typeOf=tt;function Kp(e,t){if(e==null)return{};var n={};for(var o in e)if({}.hasOwnProperty.call(e,o)){if(t.includes(o))continue;n[o]=e[o]}return n}var Yp={exports:{}},Q={};/** @license React v16.13.1
 * react-is.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ke=typeof Symbol=="function"&&Symbol.for,Bc=ke?Symbol.for("react.element"):60103,Dc=ke?Symbol.for("react.portal"):60106,ks=ke?Symbol.for("react.fragment"):60107,Es=ke?Symbol.for("react.strict_mode"):60108,Ss=ke?Symbol.for("react.profiler"):60114,Cs=ke?Symbol.for("react.provider"):60109,Ts=ke?Symbol.for("react.context"):60110,Mc=ke?Symbol.for("react.async_mode"):60111,$s=ke?Symbol.for("react.concurrent_mode"):60111,Ps=ke?Symbol.for("react.forward_ref"):60112,_s=ke?Symbol.for("react.suspense"):60113,Hy=ke?Symbol.for("react.suspense_list"):60120,Is=ke?Symbol.for("react.memo"):60115,Rs=ke?Symbol.for("react.lazy"):60116,Vy=ke?Symbol.for("react.block"):60121,Fy=ke?Symbol.for("react.fundamental"):60117,Wy=ke?Symbol.for("react.responder"):60118,Uy=ke?Symbol.for("react.scope"):60119;function nt(e){if(typeof e=="object"&&e!==null){var t=e.$$typeof;switch(t){case Bc:switch(e=e.type,e){case Mc:case $s:case ks:case Ss:case Es:case _s:return e;default:switch(e=e&&e.$$typeof,e){case Ts:case Ps:case Rs:case Is:case Cs:return e;default:return t}}case Dc:return t}}}function Xp(e){return nt(e)===$s}Q.AsyncMode=Mc;Q.ConcurrentMode=$s;Q.ContextConsumer=Ts;Q.ContextProvider=Cs;Q.Element=Bc;Q.ForwardRef=Ps;Q.Fragment=ks;Q.Lazy=Rs;Q.Memo=Is;Q.Portal=Dc;Q.Profiler=Ss;Q.StrictMode=Es;Q.Suspense=_s;Q.isAsyncMode=function(e){return Xp(e)||nt(e)===Mc};Q.isConcurrentMode=Xp;Q.isContextConsumer=function(e){return nt(e)===Ts};Q.isContextProvider=function(e){return nt(e)===Cs};Q.isElement=function(e){return typeof e=="object"&&e!==null&&e.$$typeof===Bc};Q.isForwardRef=function(e){return nt(e)===Ps};Q.isFragment=function(e){return nt(e)===ks};Q.isLazy=function(e){return nt(e)===Rs};Q.isMemo=function(e){return nt(e)===Is};Q.isPortal=function(e){return nt(e)===Dc};Q.isProfiler=function(e){return nt(e)===Ss};Q.isStrictMode=function(e){return nt(e)===Es};Q.isSuspense=function(e){return nt(e)===_s};Q.isValidElementType=function(e){return typeof e=="string"||typeof e=="function"||e===ks||e===$s||e===Ss||e===Es||e===_s||e===Hy||typeof e=="object"&&e!==null&&(e.$$typeof===Rs||e.$$typeof===Is||e.$$typeof===Cs||e.$$typeof===Ts||e.$$typeof===Ps||e.$$typeof===Fy||e.$$typeof===Wy||e.$$typeof===Uy||e.$$typeof===Vy)};Q.typeOf=nt;Yp.exports=Q;var Qy=Yp.exports,Nc=Qy,qy={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},Ky={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},Yy={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},Gp={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},jc={};jc[Nc.ForwardRef]=Yy;jc[Nc.Memo]=Gp;function Id(e){return Nc.isMemo(e)?Gp:jc[e.$$typeof]||qy}var Xy=Object.defineProperty,Gy=Object.getOwnPropertyNames,Rd=Object.getOwnPropertySymbols,Zy=Object.getOwnPropertyDescriptor,Jy=Object.getPrototypeOf,Ld=Object.prototype;function Zp(e,t,n){if(typeof t!="string"){if(Ld){var o=Jy(t);o&&o!==Ld&&Zp(e,o,n)}var r=Gy(t);Rd&&(r=r.concat(Rd(t)));for(var i=Id(e),s=Id(t),a=0;a<r.length;++a){var l=r[a];if(!Ky[l]&&!(n&&n[l])&&!(s&&s[l])&&!(i&&i[l])){var c=Zy(t,l);try{Xy(e,l,c)}catch(u){}}}}return e}var eb=Zp;const tb=wr(eb);var pl=**********,Od=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:{};function nb(){var e="__global_unique_id__";return Od[e]=(Od[e]||0)+1}function ob(e,t){return e===t?e!==0||1/e===1/t:e!==e&&t!==t}function rb(e){var t=[];return{on:function(o){t.push(o)},off:function(o){t=t.filter(function(r){return r!==o})},get:function(){return e},set:function(o,r){e=o,t.forEach(function(i){return i(e,r)})}}}function ib(e){return Array.isArray(e)?e[0]:e}function sb(e,t){var n,o,r="__create-react-context-"+nb()+"__",i=function(a){un(l,a);function l(){for(var u,d=arguments.length,f=new Array(d),p=0;p<d;p++)f[p]=arguments[p];return u=a.call.apply(a,[this].concat(f))||this,u.emitter=rb(u.props.value),u}var c=l.prototype;return c.getChildContext=function(){var d;return d={},d[r]=this.emitter,d},c.componentWillReceiveProps=function(d){if(this.props.value!==d.value){var f=this.props.value,p=d.value,v;ob(f,p)?v=0:(v=typeof t=="function"?t(f,p):pl,v|=0,v!==0&&this.emitter.set(d.value,v))}},c.render=function(){return this.props.children},l}(b.Component);i.childContextTypes=(n={},n[r]=kd.object.isRequired,n);var s=function(a){un(l,a);function l(){for(var u,d=arguments.length,f=new Array(d),p=0;p<d;p++)f[p]=arguments[p];return u=a.call.apply(a,[this].concat(f))||this,u.observedBits=void 0,u.state={value:u.getValue()},u.onUpdate=function(v,x){var R=u.observedBits|0;R&x&&u.setState({value:u.getValue()})},u}var c=l.prototype;return c.componentWillReceiveProps=function(d){var f=d.observedBits;this.observedBits=f==null?pl:f},c.componentDidMount=function(){this.context[r]&&this.context[r].on(this.onUpdate);var d=this.props.observedBits;this.observedBits=d==null?pl:d},c.componentWillUnmount=function(){this.context[r]&&this.context[r].off(this.onUpdate)},c.getValue=function(){return this.context[r]?this.context[r].get():e},c.render=function(){return ib(this.props.children)(this.state.value)},l}(b.Component);return s.contextTypes=(o={},o[r]=kd.object,o),{Provider:i,Consumer:s}}var lb=b.createContext||sb,Jp=function(t){var n=lb();return n.displayName=t,n},ab=Jp("Router-History"),mo=Jp("Router"),Tr=function(e){un(t,e),t.computeRootMatch=function(r){return{path:"/",url:"/",params:{},isExact:r==="/"}};function t(o){var r;return r=e.call(this,o)||this,r.state={location:o.history.location},r._isMounted=!1,r._pendingLocation=null,o.staticContext||(r.unlisten=o.history.listen(function(i){r._pendingLocation=i})),r}var n=t.prototype;return n.componentDidMount=function(){var r=this;this._isMounted=!0,this.unlisten&&this.unlisten(),this.props.staticContext||(this.unlisten=this.props.history.listen(function(i){r._isMounted&&r.setState({location:i})})),this._pendingLocation&&this.setState({location:this._pendingLocation})},n.componentWillUnmount=function(){this.unlisten&&(this.unlisten(),this._isMounted=!1,this._pendingLocation=null)},n.render=function(){return b.createElement(mo.Provider,{value:{history:this.props.history,location:this.state.location,match:t.computeRootMatch(this.state.location.pathname),staticContext:this.props.staticContext}},b.createElement(ab.Provider,{children:this.props.children||null,value:this.props.history}))},t}(b.Component);b.Component;var cb=function(e){un(t,e);function t(){return e.apply(this,arguments)||this}var n=t.prototype;return n.componentDidMount=function(){this.props.onMount&&this.props.onMount.call(this,this)},n.componentDidUpdate=function(r){this.props.onUpdate&&this.props.onUpdate.call(this,this,r)},n.componentWillUnmount=function(){this.props.onUnmount&&this.props.onUnmount.call(this,this)},n.render=function(){return null},t}(b.Component),ml={},ub=1e4,Ad=0;function db(e){if(ml[e])return ml[e];var t=Qp.compile(e);return Ad<ub&&(ml[e]=t,Ad++),t}function zd(e,t){return e===void 0&&(e="/"),t===void 0&&(t={}),e==="/"?e:db(e)(t,{pretty:!0})}function fb(e){var t=e.computedMatch,n=e.to,o=e.push,r=o===void 0?!1:o;return b.createElement(mo.Consumer,null,function(i){i||zn();var s=i.history,a=i.staticContext,l=r?s.push:s.replace,c=Ne(t?typeof n=="string"?zd(n,t.params):Fe({},n,{pathname:zd(n.pathname,t.params)}):n);return a?(l(c),null):b.createElement(cb,{onMount:function(){l(c)},onUpdate:function(d,f){var p=Ne(f.to);my(p,Fe({},c,{key:p.key}))||l(c)},to:n})})}var Bd={},hb=1e4,Dd=0;function pb(e,t){var n=""+t.end+t.strict+t.sensitive,o=Bd[n]||(Bd[n]={});if(o[e])return o[e];var r=[],i=Qp(e,r,t),s={regexp:i,keys:r};return Dd<hb&&(o[e]=s,Dd++),s}function Hc(e,t){t===void 0&&(t={}),(typeof t=="string"||Array.isArray(t))&&(t={path:t});var n=t,o=n.path,r=n.exact,i=r===void 0?!1:r,s=n.strict,a=s===void 0?!1:s,l=n.sensitive,c=l===void 0?!1:l,u=[].concat(o);return u.reduce(function(d,f){if(!f&&f!=="")return null;if(d)return d;var p=pb(f,{end:i,strict:a,sensitive:c}),v=p.regexp,x=p.keys,R=v.exec(e);if(!R)return null;var m=R[0],h=R.slice(1),g=e===m;return i&&!g?null:{path:f,url:f==="/"&&m===""?"/":m,isExact:g,params:x.reduce(function(w,T,P){return w[T.name]=h[P],w},{})}},null)}function mb(e){return b.Children.count(e)===0}var No=function(e){un(t,e);function t(){return e.apply(this,arguments)||this}var n=t.prototype;return n.render=function(){var r=this;return b.createElement(mo.Consumer,null,function(i){i||zn();var s=r.props.location||i.location,a=r.props.computedMatch?r.props.computedMatch:r.props.path?Hc(s.pathname,r.props):i.match,l=Fe({},i,{location:s,match:a}),c=r.props,u=c.children,d=c.component,f=c.render;return Array.isArray(u)&&mb(u)&&(u=null),b.createElement(mo.Provider,{value:l},l.match?u?typeof u=="function"?u(l):u:d?b.createElement(d,l):f?f(l):null:typeof u=="function"?u(l):null)})},t}(b.Component);function Vc(e){return e.charAt(0)==="/"?e:"/"+e}function gb(e,t){return e?Fe({},t,{pathname:Vc(e)+t.pathname}):t}function vb(e,t){if(!e)return t;var n=Vc(e);return t.pathname.indexOf(n)!==0?t:Fe({},t,{pathname:t.pathname.substr(n.length)})}function Md(e){return typeof e=="string"?e:st(e)}function gl(e){return function(){zn()}}function Nd(){}b.Component;b.Component;function yb(e){var t="withRouter("+(e.displayName||e.name)+")",n=function(r){var i=r.wrappedComponentRef,s=Kp(r,["wrappedComponentRef"]);return b.createElement(mo.Consumer,null,function(a){return a||zn(),b.createElement(e,Fe({},s,a,{ref:i}))})};return n.displayName=t,n.WrappedComponent=e,tb(n,e)}b.useContext;const bb="modulepreload",wb=function(e){return"/"+e},jd={},Tt=function(t,n,o){let r=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const i=document.querySelector("meta[property=csp-nonce]"),s=(i==null?void 0:i.nonce)||(i==null?void 0:i.getAttribute("nonce"));r=Promise.all(n.map(a=>{if(a=wb(a),a in jd)return;jd[a]=!0;const l=a.endsWith(".css"),c=l?'[rel="stylesheet"]':"";if(document.querySelector('link[href="'.concat(a,'"]').concat(c)))return;const u=document.createElement("link");if(u.rel=l?"stylesheet":bb,l||(u.as="script",u.crossOrigin=""),u.href=a,s&&u.setAttribute("nonce",s),document.head.appendChild(u),l)return new Promise((d,f)=>{u.addEventListener("load",d),u.addEventListener("error",()=>f(new Error("Unable to preload CSS for ".concat(a))))})}))}return r.then(()=>t()).catch(i=>{const s=new Event("vite:preloadError",{cancelable:!0});if(s.payload=i,window.dispatchEvent(s),!s.defaultPrevented)throw i})};var ct={allRenderFn:!1,cmpDidLoad:!0,cmpDidUnload:!1,cmpDidUpdate:!0,cmpDidRender:!0,cmpWillLoad:!0,cmpWillUpdate:!0,cmpWillRender:!0,connectedCallback:!0,disconnectedCallback:!0,element:!0,event:!0,hasRenderFn:!0,lifecycle:!0,hostListener:!0,hostListenerTargetWindow:!0,hostListenerTargetDocument:!0,hostListenerTargetBody:!0,hostListenerTargetParent:!1,hostListenerTarget:!0,member:!0,method:!0,mode:!0,observeAttribute:!0,prop:!0,propMutable:!0,reflect:!0,scoped:!0,shadowDom:!0,slot:!0,cssAnnotations:!0,state:!0,style:!0,formAssociated:!1,svg:!0,updatable:!0,vdomAttribute:!0,vdomXlink:!0,vdomClass:!0,vdomFunctional:!0,vdomKey:!0,vdomListener:!0,vdomRef:!0,vdomPropOrAttr:!0,vdomRender:!0,vdomStyle:!0,vdomText:!0,watchCallback:!0,taskQueue:!0,hotModuleReplacement:!1,isDebug:!1,isDev:!1,isTesting:!1,hydrateServerSide:!1,hydrateClientSide:!1,lifecycleDOMEvents:!1,lazyLoad:!1,profile:!1,slotRelocation:!0,appendChildSlotFix:!1,cloneNodeFix:!1,hydratedAttribute:!1,hydratedClass:!0,scriptDataOpts:!1,scopedSlotTextContentFix:!1,shadowDomShim:!1,slotChildNodesFix:!1,invisiblePrehydration:!0,propBoolean:!0,propNumber:!0,propString:!0,constructableCSS:!0,cmpShouldUpdate:!0,devTools:!1,shadowDelegatesFocus:!0,initializeNextTick:!1,asyncLoading:!1,asyncQueue:!1,transformTagName:!1,attachStyles:!0,experimentalSlotFixes:!1},xb=Object.defineProperty,kb=(e,t)=>{for(var n in t)xb(e,n,{get:t[n],enumerable:!0})},Eb={isDev:!1,isBrowser:!0,isServer:!1,isTesting:!1},em=new WeakMap,mn=e=>em.get(e),Sb=(e,t)=>{const n={$flags$:0,$hostElement$:e,$cmpMeta$:t,$instanceValues$:new Map};return em.set(e,n)},Hd=(e,t)=>t in e,$r=(e,t)=>(0,console.error)(e,t),Hi=new Map,tm=[],Cb="slot-fb{display:contents}slot-fb[hidden]{display:none}",Vd="http://www.w3.org/1999/xlink",Pr=typeof window<"u"?window:{},wt=Pr.document||{head:{}},Ue=Pr.HTMLElement||class{},we={$flags$:0,$resourcesUrl$:"",jmp:e=>e(),raf:e=>requestAnimationFrame(e),ael:(e,t,n,o)=>e.addEventListener(t,n,o),rel:(e,t,n,o)=>e.removeEventListener(t,n,o),ce:(e,t)=>new CustomEvent(e,t)},Tb=e=>{Object.assign(we,e)},$b=(()=>{let e=!1;try{wt.addEventListener("e",null,Object.defineProperty({},"passive",{get(){e=!0}}))}catch(t){}return e})(),Pb=e=>Promise.resolve(e),_b=(()=>{try{return new CSSStyleSheet,typeof new CSSStyleSheet().replaceSync=="function"}catch(e){}return!1})(),xa=!1,ka=[],nm=[],om=(e,t)=>n=>{e.push(n),xa||(xa=!0,t&&we.$flags$&4?Ib(Ea):we.raf(Ea))},Fd=e=>{for(let t=0;t<e.length;t++)try{e[t](performance.now())}catch(n){$r(n)}e.length=0},Ea=()=>{Fd(ka),Fd(nm),(xa=ka.length>0)&&we.raf(Ea)},Ib=e=>Pb().then(e),mr=om(ka,!1),dn=om(nm,!0),Rb=e=>{const t=new URL(e,we.$resourcesUrl$);return t.origin!==Pr.location.origin?t.href:t.pathname},Wd={},Lb="http://www.w3.org/2000/svg",Ob="http://www.w3.org/1999/xhtml",Ab=e=>e!=null,Fc=e=>(e=typeof e,e==="object"||e==="function");function zb(e){var t,n,o;return(o=(n=(t=e.head)==null?void 0:t.querySelector('meta[name="csp-nonce"]'))==null?void 0:n.getAttribute("content"))!=null?o:void 0}var Bb={};kb(Bb,{err:()=>rm,map:()=>Db,ok:()=>Sa,unwrap:()=>Mb,unwrapErr:()=>Nb});var Sa=e=>({isOk:!0,isErr:!1,value:e}),rm=e=>({isOk:!1,isErr:!0,value:e});function Db(e,t){if(e.isOk){const n=t(e.value);return n instanceof Promise?n.then(o=>Sa(o)):Sa(n)}if(e.isErr){const n=e.value;return rm(n)}throw"should never get here"}var Mb=e=>{if(e.isOk)return e.value;throw e.value},Nb=e=>{if(e.isErr)return e.value;throw e.value},Rn=(e,t="")=>()=>{},M=(e,t,...n)=>{let o=null,r=null,i=null,s=!1,a=!1;const l=[],c=d=>{for(let f=0;f<d.length;f++)o=d[f],Array.isArray(o)?c(o):o!=null&&typeof o!="boolean"&&((s=typeof e!="function"&&!Fc(o))&&(o=String(o)),s&&a?l[l.length-1].$text$+=o:l.push(s?Vi(null,o):o),a=s)};if(c(n),t){t.key&&(r=t.key),t.name&&(i=t.name);{const d=t.className||t.class;d&&(t.class=typeof d!="object"?d:Object.keys(d).filter(f=>d[f]).join(" "))}}if(typeof e=="function")return e(t===null?{}:t,l,Hb);const u=Vi(e,null);return u.$attrs$=t,l.length>0&&(u.$children$=l),u.$key$=r,u.$name$=i,u},Vi=(e,t)=>{const n={$flags$:0,$tag$:e,$text$:t,$elm$:null,$children$:null};return n.$attrs$=null,n.$key$=null,n.$name$=null,n},ot={},jb=e=>e&&e.$tag$===ot,Hb={forEach:(e,t)=>e.map(Ud).forEach(t),map:(e,t)=>e.map(Ud).map(t).map(Vb)},Ud=e=>({vattrs:e.$attrs$,vchildren:e.$children$,vkey:e.$key$,vname:e.$name$,vtag:e.$tag$,vtext:e.$text$}),Vb=e=>{if(typeof e.vtag=="function"){const n={...e.vattrs};return e.vkey&&(n.key=e.vkey),e.vname&&(n.name=e.vname),M(e.vtag,n,...e.vchildren||[])}const t=Vi(e.vtag,e.vtext);return t.$attrs$=e.vattrs,t.$children$=e.vchildren,t.$key$=e.vkey,t.$name$=e.vname,t},Fb=e=>tm.map(t=>t(e)).find(t=>!!t),Wb=e=>tm.push(e),Ub=e=>mn(e).$modeName$,Qb=(e,t)=>e!=null&&!Fc(e)?t&4?e==="false"?!1:e===""||!!e:t&2?parseFloat(e):t&1?String(e):e:e,qb=e=>e,be=(e,t,n)=>{const o=qb(e);return{emit:r=>Kb(o,t,{bubbles:!!(n&4),composed:!!(n&2),cancelable:!!(n&1),detail:r})}},Kb=(e,t,n)=>{const o=we.ce(t,n);return e.dispatchEvent(o),o},Qd=new WeakMap,Yb=(e,t,n)=>{let o=Hi.get(e);_b&&n?(o=o||new CSSStyleSheet,typeof o=="string"?o=t:o.replaceSync(t)):o=t,Hi.set(e,o)},Xb=(e,t,n)=>{var o;const r=im(t,n),i=Hi.get(r);if(e=e.nodeType===11?e:wt,i)if(typeof i=="string"){e=e.head||e;let s=Qd.get(e),a;if(s||Qd.set(e,s=new Set),!s.has(r)){{a=wt.createElement("style"),a.innerHTML=i;const l=(o=we.$nonce$)!=null?o:zb(wt);l!=null&&a.setAttribute("nonce",l),(!(t.$flags$&1)||t.$flags$&1&&e.nodeName!=="HEAD")&&e.insertBefore(a,e.querySelector("link"))}t.$flags$&4&&(a.innerHTML+=Cb),s&&s.add(r)}}else e.adoptedStyleSheets.includes(i)||(e.adoptedStyleSheets=[...e.adoptedStyleSheets,i]);return r},Gb=e=>{const t=e.$cmpMeta$,n=e.$hostElement$,o=t.$flags$,r=Rn("attachStyles",t.$tagName$),i=Xb(n.shadowRoot?n.shadowRoot:n.getRootNode(),t,e.$modeName$);o&10&&o&2&&(n["s-sc"]=i,n.classList.add(i+"-h"),o&2&&n.classList.add(i+"-s")),r()},im=(e,t)=>"sc-"+(t&&e.$flags$&32?e.$tagName$+"-"+t:e.$tagName$),qd=(e,t,n,o,r,i)=>{if(n!==o){let s=Hd(e,t),a=t.toLowerCase();if(t==="class"){const l=e.classList,c=Kd(n),u=Kd(o);l.remove(...c.filter(d=>d&&!u.includes(d))),l.add(...u.filter(d=>d&&!c.includes(d)))}else if(t==="style"){for(const l in n)(!o||o[l]==null)&&(l.includes("-")?e.style.removeProperty(l):e.style[l]="");for(const l in o)(!n||o[l]!==n[l])&&(l.includes("-")?e.style.setProperty(l,o[l]):e.style[l]=o[l])}else if(t!=="key")if(t==="ref")o&&o(e);else if(!e.__lookupSetter__(t)&&t[0]==="o"&&t[1]==="n"){if(t[2]==="-"?t=t.slice(3):Hd(Pr,a)?t=a.slice(2):t=a[2]+t.slice(3),n||o){const l=t.endsWith(sm);t=t.replace(Jb,""),n&&we.rel(e,t,n,l),o&&we.ael(e,t,o,l)}}else{const l=Fc(o);if((s||l&&o!==null)&&!r)try{if(e.tagName.includes("-"))e[t]=o;else{const u=o==null?"":o;t==="list"?s=!1:(n==null||e[t]!=u)&&(e[t]=u)}}catch(u){}let c=!1;a!==(a=a.replace(/^xlink\:?/,""))&&(t=a,c=!0),o==null||o===!1?(o!==!1||e.getAttribute(t)==="")&&(c?e.removeAttributeNS(Vd,t):e.removeAttribute(t)):(!s||i&4||r)&&!l&&(o=o===!0?"":o,c?e.setAttributeNS(Vd,t,o):e.setAttribute(t,o))}}},Zb=/\s/,Kd=e=>e?e.split(Zb):[],sm="Capture",Jb=new RegExp(sm+"$"),lm=(e,t,n)=>{const o=t.$elm$.nodeType===11&&t.$elm$.host?t.$elm$.host:t.$elm$,r=e&&e.$attrs$||Wd,i=t.$attrs$||Wd;for(const s of Yd(Object.keys(r)))s in i||qd(o,s,r[s],void 0,n,t.$flags$);for(const s of Yd(Object.keys(i)))qd(o,s,r[s],i[s],n,t.$flags$)};function Yd(e){return e.includes("ref")?[...e.filter(t=>t!=="ref"),"ref"]:e}var Mn,Ca,go,Fi=!1,Wi=!1,Wc=!1,Ke=!1,Ui=(e,t,n,o)=>{var r;const i=t.$children$[n];let s=0,a,l,c;if(Fi||(Wc=!0,i.$tag$==="slot"&&(Mn&&o.classList.add(Mn+"-s"),i.$flags$|=i.$children$?2:1)),i.$text$!==null)a=i.$elm$=wt.createTextNode(i.$text$);else if(i.$flags$&1)a=i.$elm$=wt.createTextNode("");else{if(Ke||(Ke=i.$tag$==="svg"),a=i.$elm$=wt.createElementNS(Ke?Lb:Ob,!Fi&&ct.slotRelocation&&i.$flags$&2?"slot-fb":i.$tag$),Ke&&i.$tag$==="foreignObject"&&(Ke=!1),lm(null,i,Ke),!!a.getRootNode().querySelector("body")&&ct.scoped&&Ab(Mn)&&a["s-si"]!==Mn&&a.classList.add(a["s-si"]=Mn),Qc(a,o),i.$children$)for(s=0;s<i.$children$.length;++s)l=Ui(e,i,s,a),l&&a.appendChild(l);i.$tag$==="svg"?Ke=!1:a.tagName==="foreignObject"&&(Ke=!0)}return a["s-hn"]=go,i.$flags$&3&&(a["s-sr"]=!0,a["s-cr"]=Ca,a["s-sn"]=i.$name$||"",a["s-rf"]=(r=i.$attrs$)==null?void 0:r.ref,c=e&&e.$children$&&e.$children$[n],c&&c.$tag$===i.$tag$&&e.$elm$&&gr(e.$elm$,!1)),a},gr=(e,t)=>{we.$flags$|=1;const n=Array.from(e.childNodes);if(e["s-sr"]&&ct.experimentalSlotFixes){let o=e;for(;o=o.nextSibling;)o&&o["s-sn"]===e["s-sn"]&&o["s-sh"]===go&&n.push(o)}for(let o=n.length-1;o>=0;o--){const r=n[o];r["s-hn"]!==go&&r["s-ol"]&&(ln(um(r),r,Uc(r)),r["s-ol"].remove(),r["s-ol"]=void 0,r["s-sh"]=void 0,Wc=!0),t&&gr(r,t)}we.$flags$&=-2},am=(e,t,n,o,r,i)=>{let s=e["s-cr"]&&e["s-cr"].parentNode||e,a;for(s.shadowRoot&&s.tagName===go&&(s=s.shadowRoot);r<=i;++r)o[r]&&(a=Ui(null,n,r,e),a&&(o[r].$elm$=a,ln(s,a,Uc(t))))},cm=(e,t,n)=>{for(let o=t;o<=n;++o){const r=e[o];if(r){const i=r.$elm$;hm(r),i&&(Wi=!0,i["s-ol"]?i["s-ol"].remove():gr(i,!0),i.remove())}}},e1=(e,t,n,o,r=!1)=>{let i=0,s=0,a=0,l=0,c=t.length-1,u=t[0],d=t[c],f=o.length-1,p=o[0],v=o[f],x,R;for(;i<=c&&s<=f;)if(u==null)u=t[++i];else if(d==null)d=t[--c];else if(p==null)p=o[++s];else if(v==null)v=o[--f];else if(Zr(u,p,r))Nn(u,p,r),u=t[++i],p=o[++s];else if(Zr(d,v,r))Nn(d,v,r),d=t[--c],v=o[--f];else if(Zr(u,v,r))(u.$tag$==="slot"||v.$tag$==="slot")&&gr(u.$elm$.parentNode,!1),Nn(u,v,r),ln(e,u.$elm$,d.$elm$.nextSibling),u=t[++i],v=o[--f];else if(Zr(d,p,r))(u.$tag$==="slot"||v.$tag$==="slot")&&gr(d.$elm$.parentNode,!1),Nn(d,p,r),ln(e,d.$elm$,u.$elm$),d=t[--c],p=o[++s];else{for(a=-1,l=i;l<=c;++l)if(t[l]&&t[l].$key$!==null&&t[l].$key$===p.$key$){a=l;break}a>=0?(R=t[a],R.$tag$!==p.$tag$?x=Ui(t&&t[s],n,a,e):(Nn(R,p,r),t[a]=void 0,x=R.$elm$),p=o[++s]):(x=Ui(t&&t[s],n,s,e),p=o[++s]),x&&ln(um(u.$elm$),x,Uc(u.$elm$))}i>c?am(e,o[f+1]==null?null:o[f+1].$elm$,n,o,s,f):s>f&&cm(t,i,c)},Zr=(e,t,n=!1)=>e.$tag$===t.$tag$?e.$tag$==="slot"?"$nodeId$"in e&&n&&e.$elm$.nodeType!==8?!1:e.$name$===t.$name$:n?!0:e.$key$===t.$key$:!1,Uc=e=>e&&e["s-ol"]||e,um=e=>(e["s-ol"]?e["s-ol"]:e).parentNode,Nn=(e,t,n=!1)=>{const o=t.$elm$=e.$elm$,r=e.$children$,i=t.$children$,s=t.$tag$,a=t.$text$;let l;a===null?(Ke=s==="svg"?!0:s==="foreignObject"?!1:Ke,s==="slot"&&!Fi||lm(e,t,Ke),r!==null&&i!==null?e1(o,r,t,i,n):i!==null?(e.$text$!==null&&(o.textContent=""),am(o,null,t,i,0,i.length-1)):!n&&ct.updatable&&r!==null&&cm(r,0,r.length-1),Ke&&s==="svg"&&(Ke=!1)):(l=o["s-cr"])?l.parentNode.textContent=a:e.$text$!==a&&(o.data=a)},dm=e=>{const t=e.childNodes;for(const n of t)if(n.nodeType===1){if(n["s-sr"]){const o=n["s-sn"];n.hidden=!1;for(const r of t)if(r!==n){if(r["s-hn"]!==n["s-hn"]||o!==""){if(r.nodeType===1&&(o===r.getAttribute("slot")||o===r["s-sn"])||r.nodeType===3&&o===r["s-sn"]){n.hidden=!0;break}}else if(r.nodeType===1||r.nodeType===3&&r.textContent.trim()!==""){n.hidden=!0;break}}}dm(n)}},Ot=[],fm=e=>{let t,n,o;for(const r of e.childNodes){if(r["s-sr"]&&(t=r["s-cr"])&&t.parentNode){n=t.parentNode.childNodes;const i=r["s-sn"];for(o=n.length-1;o>=0;o--)if(t=n[o],!t["s-cn"]&&!t["s-nr"]&&t["s-hn"]!==r["s-hn"]&&!ct.experimentalSlotFixes)if(Xd(t,i)){let s=Ot.find(a=>a.$nodeToRelocate$===t);Wi=!0,t["s-sn"]=t["s-sn"]||i,s?(s.$nodeToRelocate$["s-sh"]=r["s-hn"],s.$slotRefNode$=r):(t["s-sh"]=r["s-hn"],Ot.push({$slotRefNode$:r,$nodeToRelocate$:t})),t["s-sr"]&&Ot.map(a=>{Xd(a.$nodeToRelocate$,t["s-sn"])&&(s=Ot.find(l=>l.$nodeToRelocate$===t),s&&!a.$slotRefNode$&&(a.$slotRefNode$=s.$slotRefNode$))})}else Ot.some(s=>s.$nodeToRelocate$===t)||Ot.push({$nodeToRelocate$:t})}r.nodeType===1&&fm(r)}},Xd=(e,t)=>e.nodeType===1?e.getAttribute("slot")===null&&t===""||e.getAttribute("slot")===t:e["s-sn"]===t?!0:t==="",hm=e=>{e.$attrs$&&e.$attrs$.ref&&e.$attrs$.ref(null),e.$children$&&e.$children$.map(hm)},ln=(e,t,n)=>{const o=e==null?void 0:e.insertBefore(t,n);return Qc(t,e),o},pm=e=>{const t=[];return e&&t.push(...e["s-scs"]||[],e["s-si"],e["s-sc"],...pm(e.parentElement)),t},Qc=(e,t,n=!1)=>{var o;if(e&&t&&e.nodeType===1){const r=new Set(pm(t).filter(Boolean));if(r.size&&((o=e.classList)==null||o.add(...e["s-scs"]=[...r]),e["s-ol"]||n))for(const i of Array.from(e.childNodes))Qc(i,e,!0)}},t1=(e,t,n=!1)=>{var o,r,i,s,a;const l=e.$hostElement$,c=e.$cmpMeta$,u=e.$vnode$||Vi(null,null),d=jb(t)?t:M(null,null,t);if(go=l.tagName,c.$attrsToReflect$&&(d.$attrs$=d.$attrs$||{},c.$attrsToReflect$.map(([f,p])=>d.$attrs$[p]=l[f])),n&&d.$attrs$)for(const f of Object.keys(d.$attrs$))l.hasAttribute(f)&&!["key","ref","style","class"].includes(f)&&(d.$attrs$[f]=l[f]);d.$tag$=null,d.$flags$|=4,e.$vnode$=d,d.$elm$=u.$elm$=l.shadowRoot||l,Mn=l["s-sc"],Fi=(c.$flags$&1)!==0,Ca=l["s-cr"],Wi=!1,Nn(u,d,n);{if(we.$flags$|=1,Wc){fm(d.$elm$);for(const f of Ot){const p=f.$nodeToRelocate$;if(!p["s-ol"]){const v=wt.createTextNode("");v["s-nr"]=p,ln(p.parentNode,p["s-ol"]=v,p)}}for(const f of Ot){const p=f.$nodeToRelocate$,v=f.$slotRefNode$;if(v){const x=v.parentNode;let R=v.nextSibling;{let m=(o=p["s-ol"])==null?void 0:o.previousSibling;for(;m;){let h=(r=m["s-nr"])!=null?r:null;if(h&&h["s-sn"]===p["s-sn"]&&x===h.parentNode){for(h=h.nextSibling;h===p||h!=null&&h["s-sr"];)h=h==null?void 0:h.nextSibling;if(!h||!h["s-nr"]){R=h;break}}m=m.previousSibling}}(!R&&x!==p.parentNode||p.nextSibling!==R)&&p!==R&&(!p["s-hn"]&&p["s-ol"]&&(p["s-hn"]=p["s-ol"].parentNode.nodeName),ln(x,p,R),p.nodeType===1&&(p.hidden=(i=p["s-ih"])!=null?i:!1)),p&&typeof v["s-rf"]=="function"&&v["s-rf"](p)}else p.nodeType===1&&(n&&(p["s-ih"]=(s=p.hidden)!=null?s:!1),p.hidden=!0)}}Wi&&dm(d.$elm$),we.$flags$&=-2,Ot.length=0}if(ct.experimentalScopedSlotChanges&&c.$flags$&2)for(const f of d.$elm$.childNodes)f["s-hn"]!==go&&!f["s-sh"]&&(n&&f["s-ih"]==null&&(f["s-ih"]=(a=f.hidden)!=null?a:!1),f.hidden=!0);Ca=void 0},n1=(e,t)=>{},qc=(e,t)=>(e.$flags$|=16,n1(e,e.$ancestorComponent$),dn(()=>o1(e,t))),o1=(e,t)=>{const n=e.$hostElement$,o=Rn("scheduleUpdate",e.$cmpMeta$.$tagName$),r=n;if(!r)throw new Error("Can't render component <".concat(n.tagName.toLowerCase()," /> with invalid Stencil runtime! Make sure this imported component is compiled with a `externalRuntime: true` flag. For more information, please refer to https://stenciljs.com/docs/custom-elements#externalruntime"));let i;return t?i=io(r,"componentWillLoad"):i=io(r,"componentWillUpdate"),i=Gd(i,()=>io(r,"componentWillRender")),o(),Gd(i,()=>i1(e,r,t))},Gd=(e,t)=>r1(e)?e.then(t).catch(n=>{console.error(n),t()}):t(),r1=e=>e instanceof Promise||e&&e.then&&typeof e.then=="function",i1=async(e,t,n)=>{const o=e.$hostElement$,r=Rn("update",e.$cmpMeta$.$tagName$);o["s-rc"],n&&Gb(e);const i=Rn("render",e.$cmpMeta$.$tagName$);s1(e,t,o,n),i(),r(),l1(e)},Zd=null,s1=(e,t,n,o)=>{try{Zd=t,t=t.render&&t.render(),e.$flags$&=-17,e.$flags$|=2,(ct.hasRenderFn||ct.reflect)&&(ct.vdomRender||ct.reflect)&&(ct.hydrateServerSide||t1(e,t,o))}catch(l){$r(l,e.$hostElement$)}return Zd=null,null},l1=e=>{const t=e.$cmpMeta$.$tagName$,n=e.$hostElement$,o=Rn("postUpdate",t),r=n;e.$ancestorComponent$,io(r,"componentDidRender"),e.$flags$&64?(io(r,"componentDidUpdate"),o()):(e.$flags$|=64,io(r,"componentDidLoad"),o())},Ta=e=>{{const t=mn(e),n=t.$hostElement$.isConnected;return n&&(t.$flags$&18)===2&&qc(t,!1),n}},io=(e,t,n)=>{if(e&&e[t])try{return e[t](n)}catch(o){$r(o)}},a1=(e,t)=>mn(e).$instanceValues$.get(t),c1=(e,t,n,o)=>{const r=mn(e),i=e,s=r.$instanceValues$.get(t),a=r.$flags$,l=i;n=Qb(n,o.$members$[t][0]);const c=Number.isNaN(s)&&Number.isNaN(n);if(n!==s&&!c){r.$instanceValues$.set(t,n);{if(o.$watchers$&&a&128){const d=o.$watchers$[t];d&&d.map(f=>{try{l[f](n,s,t)}catch(p){$r(p,i)}})}if((a&18)===2){if(l.componentShouldUpdate&&l.componentShouldUpdate(n,s,t)===!1)return;qc(r,!1)}}}},u1=(e,t,n)=>{var o,r;const i=e.prototype;if(t.$members$||t.$watchers$||e.watchers){e.watchers&&!t.$watchers$&&(t.$watchers$=e.watchers);const s=Object.entries((o=t.$members$)!=null?o:{});s.map(([a,[l]])=>{(l&31||l&32)&&Object.defineProperty(i,a,{get(){return a1(this,a)},set(c){c1(this,a,c,t)},configurable:!0,enumerable:!0})});{const a=new Map;i.attributeChangedCallback=function(l,c,u){we.jmp(()=>{var d;const f=a.get(l);if(this.hasOwnProperty(f))u=this[f],delete this[f];else{if(i.hasOwnProperty(f)&&typeof this[f]=="number"&&this[f]==u)return;if(f==null){const p=mn(this),v=p==null?void 0:p.$flags$;if(v&&!(v&8)&&v&128&&u!==c){const R=this,m=(d=t.$watchers$)==null?void 0:d[l];m==null||m.forEach(h=>{R[h]!=null&&R[h].call(R,u,c,l)})}return}}this[f]=u===null&&typeof this[f]=="boolean"?!1:u})},e.observedAttributes=Array.from(new Set([...Object.keys((r=t.$watchers$)!=null?r:{}),...s.filter(([l,c])=>c[0]&15).map(([l,c])=>{var u;const d=c[1]||l;return a.set(d,l),c[0]&512&&((u=t.$attrsToReflect$)==null||u.push([l,d])),d})]))}}return e},d1=async(e,t,n,o)=>{let r;if(!(t.$flags$&32)){t.$flags$|=32,n.$lazyBundleId$;{r=e.constructor;const s=e.localName;customElements.whenDefined(s).then(()=>t.$flags$|=128)}if(r&&r.style){let s;typeof r.style=="string"?s=r.style:typeof r.style!="string"&&(t.$modeName$=Fb(e),t.$modeName$&&(s=r.style[t.$modeName$]));const a=im(n,t.$modeName$);if(!Hi.has(a)){const l=Rn("registerStyles",n.$tagName$);Yb(a,s,!!(n.$flags$&1)),l()}}}t.$ancestorComponent$,qc(t,!0)},Jd=e=>{},f1=e=>{if(!(we.$flags$&1)){const t=mn(e),n=t.$cmpMeta$,o=Rn("connectedCallback",n.$tagName$);t.$flags$&1?(mm(e,t,n.$listeners$),t!=null&&t.$lazyInstance$?Jd(t.$lazyInstance$):t!=null&&t.$onReadyPromise$&&t.$onReadyPromise$.then(()=>Jd(t.$lazyInstance$))):(t.$flags$|=1,n.$flags$&12&&h1(e),n.$members$&&Object.entries(n.$members$).map(([r,[i]])=>{if(i&31&&e.hasOwnProperty(r)){const s=e[r];delete e[r],e[r]=s}}),d1(e,t,n)),o()}},h1=e=>{const t=e["s-cr"]=wt.createComment("");t["s-cn"]=!0,ln(e,t,e.firstChild)},p1=async e=>{if(!(we.$flags$&1)){const t=mn(e);t.$rmListeners$&&(t.$rmListeners$.map(n=>n()),t.$rmListeners$=void 0)}},Qe=(e,t)=>{const n={$flags$:t[0],$tagName$:t[1]};n.$members$=t[2],n.$listeners$=t[3],n.$watchers$=e.$watchers$,n.$attrsToReflect$=[];const o=e.prototype.connectedCallback,r=e.prototype.disconnectedCallback;return Object.assign(e.prototype,{__registerHost(){Sb(this,n)},connectedCallback(){const i=mn(this);mm(this,i,n.$listeners$),f1(this),o&&o.call(this)},disconnectedCallback(){p1(this),r&&r.call(this)},__attachShadow(){if(!this.shadowRoot)this.attachShadow({mode:"open",delegatesFocus:!!(n.$flags$&16)});else if(this.shadowRoot.mode!=="open")throw new Error("Unable to re-use existing shadow root for ".concat(n.$tagName$,"! Mode is set to ").concat(this.shadowRoot.mode," but Stencil only supports open shadow roots."))}}),e.is=n.$tagName$,u1(e,n)},mm=(e,t,n,o)=>{n&&n.map(([r,i,s])=>{const a=g1(e,r),l=m1(t,s),c=v1(r);we.ael(a,i,l,c),(t.$rmListeners$=t.$rmListeners$||[]).push(()=>we.rel(a,i,l,c))})},m1=(e,t)=>n=>{var o;try{ct.lazyLoad||e.$hostElement$[t](n)}catch(r){$r(r)}},g1=(e,t)=>t&4?wt:t&8?Pr:t&16?wt.body:e,v1=e=>$b?{passive:(e&1)!==0,capture:(e&2)!==0}:(e&2)!==0;/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const pe=typeof window<"u"?window:void 0,vl=typeof document<"u"?document:void 0;/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */let yl;const y1=e=>{if(yl===void 0){const t=e.style.animationName!==void 0,n=e.style.webkitAnimationName!==void 0;yl=!t&&n?"-webkit-":""}return yl},bl=(e,t,n)=>{const o=t.startsWith("animation")?y1(e):"";e.style.setProperty(o+t,n)},Jr=(e=[],t)=>{if(t!==void 0){const n=Array.isArray(t)?t:[t];return[...e,...n]}return e},b1=e=>{let t,n,o,r,i,s,a=[],l=[],c=[],u=!1,d,f={},p=[],v=[],x={},R=0,m=!1,h=!1,g,w,T,P=!0,k=!1,$=!0,I,B=!1;const V=e,de=[],fe=[],ie=[],K=[],J=[],kt=[],_=[],C=[],E=[],z=[],L=[],W=typeof AnimationEffect=="function"||pe!==void 0&&typeof pe.AnimationEffect=="function",H=typeof Element=="function"&&typeof Element.prototype.animate=="function"&&W,ce=()=>L,se=y=>(J.forEach(A=>{A.destroy(y)}),he(y),K.length=0,J.length=0,a.length=0,Gm(),u=!1,$=!0,I),he=y=>{lu(),y&&Zm()},It=()=>{m=!1,h=!1,$=!0,g=void 0,w=void 0,T=void 0,R=0,k=!1,P=!0,B=!1},Rr=()=>R!==0&&!B,su=(y,A)=>{const N=A.findIndex(le=>le.c===y);N>-1&&A.splice(N,1)},Xm=(y,A)=>(ie.push({c:y,o:A}),I),zs=(y,A)=>((A!=null&&A.oneTimeCallback?fe:de).push({c:y,o:A}),I),Gm=()=>(de.length=0,fe.length=0,I),lu=()=>{H&&(L.forEach(y=>{y.cancel()}),L.length=0)},Zm=()=>{kt.forEach(y=>{y!=null&&y.parentNode&&y.parentNode.removeChild(y)}),kt.length=0},Jm=y=>(_.push(y),I),eg=y=>(C.push(y),I),tg=y=>(E.push(y),I),ng=y=>(z.push(y),I),og=y=>(l=Jr(l,y),I),rg=y=>(c=Jr(c,y),I),ig=(y={})=>(f=y,I),sg=(y=[])=>{for(const A of y)f[A]="";return I},lg=y=>(p=Jr(p,y),I),ag=y=>(v=Jr(v,y),I),cg=(y={})=>(x=y,I),ug=(y=[])=>{for(const A of y)x[A]="";return I},Bs=()=>i!==void 0?i:d?d.getFill():"both",Lr=()=>g!==void 0?g:s!==void 0?s:d?d.getDirection():"normal",Ds=()=>m?"linear":o!==void 0?o:d?d.getEasing():"linear",Bn=()=>h?0:w!==void 0?w:n!==void 0?n:d?d.getDuration():0,Ms=()=>r!==void 0?r:d?d.getIterations():1,Ns=()=>T!==void 0?T:t!==void 0?t:d?d.getDelay():0,dg=()=>a,fg=y=>(s=y,it(!0),I),hg=y=>(i=y,it(!0),I),pg=y=>(t=y,it(!0),I),mg=y=>(o=y,it(!0),I),gg=y=>(!H&&y===0&&(y=1),n=y,it(!0),I),vg=y=>(r=y,it(!0),I),yg=y=>(d=y,I),bg=y=>{if(y!=null)if(y.nodeType===1)K.push(y);else if(y.length>=0)for(let A=0;A<y.length;A++)K.push(y[A]);else console.error("Invalid addElement value");return I},wg=y=>{if(y!=null)if(Array.isArray(y))for(const A of y)A.parent(I),J.push(A);else y.parent(I),J.push(y);return I},xg=y=>{const A=a!==y;return a=y,A&&kg(a),I},kg=y=>{H&&ce().forEach(A=>{const N=A.effect;if(N.setKeyframes)N.setKeyframes(y);else{const le=new KeyframeEffect(N.target,y,N.getTiming());A.effect=le}})},Eg=()=>{_.forEach(le=>le()),C.forEach(le=>le());const y=l,A=c,N=f;K.forEach(le=>{const Ie=le.classList;y.forEach(Et=>Ie.add(Et)),A.forEach(Et=>Ie.remove(Et));for(const Et in N)N.hasOwnProperty(Et)&&bl(le,Et,N[Et])})},Sg=()=>{E.forEach(Ie=>Ie()),z.forEach(Ie=>Ie());const y=P?1:0,A=p,N=v,le=x;K.forEach(Ie=>{const Et=Ie.classList;A.forEach(gn=>Et.add(gn)),N.forEach(gn=>Et.remove(gn));for(const gn in le)le.hasOwnProperty(gn)&&bl(Ie,gn,le[gn])}),w=void 0,g=void 0,T=void 0,de.forEach(Ie=>Ie.c(y,I)),fe.forEach(Ie=>Ie.c(y,I)),fe.length=0,$=!0,P&&(k=!0),P=!0},Or=()=>{R!==0&&(R--,R===0&&(Sg(),d&&d.animationFinish()))},Cg=()=>{K.forEach(y=>{const A=y.animate(a,{id:V,delay:Ns(),duration:Bn(),easing:Ds(),iterations:Ms(),fill:Bs(),direction:Lr()});A.pause(),L.push(A)}),L.length>0&&(L[0].onfinish=()=>{Or()})},au=()=>{Eg(),a.length>0&&H&&Cg(),u=!0},So=y=>{y=Math.min(Math.max(y,0),.9999),H&&L.forEach(A=>{A.currentTime=A.effect.getComputedTiming().delay+Bn()*y,A.pause()})},cu=y=>{L.forEach(A=>{A.effect.updateTiming({delay:Ns(),duration:Bn(),easing:Ds(),iterations:Ms(),fill:Bs(),direction:Lr()})}),y!==void 0&&So(y)},it=(y=!1,A=!0,N)=>(y&&J.forEach(le=>{le.update(y,A,N)}),H&&cu(N),I),Tg=(y=!1,A)=>(J.forEach(N=>{N.progressStart(y,A)}),uu(),m=y,u||au(),it(!1,!0,A),I),$g=y=>(J.forEach(A=>{A.progressStep(y)}),So(y),I),Pg=(y,A,N)=>(m=!1,J.forEach(le=>{le.progressEnd(y,A,N)}),N!==void 0&&(w=N),k=!1,P=!0,y===0?(g=Lr()==="reverse"?"normal":"reverse",g==="reverse"&&(P=!1),H?(it(),So(1-A)):(T=(1-A)*Bn()*-1,it(!1,!1))):y===1&&(H?(it(),So(A)):(T=A*Bn()*-1,it(!1,!1))),y!==void 0&&!d&&du(),I),uu=()=>{u&&(H?L.forEach(y=>{y.pause()}):K.forEach(y=>{bl(y,"animation-play-state","paused")}),B=!0)},_g=()=>(J.forEach(y=>{y.pause()}),uu(),I),Ig=()=>{Or()},Rg=()=>{L.forEach(y=>{y.play()}),(a.length===0||K.length===0)&&Or()},Lg=()=>{H&&(So(0),cu())},du=y=>new Promise(A=>{y!=null&&y.sync&&(h=!0,zs(()=>h=!1,{oneTimeCallback:!0})),u||au(),k&&(Lg(),k=!1),$&&(R=J.length+1,$=!1);const N=()=>{su(le,fe),A()},le=()=>{su(N,ie),A()};zs(le,{oneTimeCallback:!0}),Xm(N,{oneTimeCallback:!0}),J.forEach(Ie=>{Ie.play()}),H?Rg():Ig(),B=!1}),Og=()=>{J.forEach(y=>{y.stop()}),u&&(lu(),u=!1),It(),ie.forEach(y=>y.c(0,I)),ie.length=0},fu=(y,A)=>{const N=a[0];return N!==void 0&&(N.offset===void 0||N.offset===0)?N[y]=A:a=[{offset:0,[y]:A},...a],I};return I={parentAnimation:d,elements:K,childAnimations:J,id:V,animationFinish:Or,from:fu,to:(y,A)=>{const N=a[a.length-1];return N!==void 0&&(N.offset===void 0||N.offset===1)?N[y]=A:a=[...a,{offset:1,[y]:A}],I},fromTo:(y,A,N)=>fu(y,A).to(y,N),parent:yg,play:du,pause:_g,stop:Og,destroy:se,keyframes:xg,addAnimation:wg,addElement:bg,update:it,fill:hg,direction:fg,iterations:vg,duration:gg,easing:mg,delay:pg,getWebAnimations:ce,getKeyframes:dg,getFill:Bs,getDirection:Lr,getDelay:Ns,getIterations:Ms,getEasing:Ds,getDuration:Bn,afterAddRead:tg,afterAddWrite:ng,afterClearStyles:ug,afterStyles:cg,afterRemoveClass:ag,afterAddClass:lg,beforeAddRead:Jm,beforeAddWrite:eg,beforeClearStyles:sg,beforeStyles:ig,beforeRemoveClass:rg,beforeAddClass:og,onFinish:zs,isRunning:Rr,progressStart:Tg,progressStep:$g,progressEnd:Pg}};/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */class w1{constructor(){this.m=new Map}reset(t){this.m=new Map(Object.entries(t))}get(t,n){const o=this.m.get(t);return o!==void 0?o:n}getBoolean(t,n=!1){const o=this.m.get(t);return o===void 0?n:typeof o=="string"?o==="true":!!o}getNumber(t,n){const o=parseFloat(this.m.get(t));return isNaN(o)?n!==void 0?n:NaN:o}set(t,n){this.m.set(t,n)}}const G=new w1,x1=e=>{try{const t=e.sessionStorage.getItem(gm);return t!==null?JSON.parse(t):{}}catch(t){return{}}},k1=(e,t)=>{try{e.sessionStorage.setItem(gm,JSON.stringify(t))}catch(n){return}},E1=e=>{const t={};return e.location.search.slice(1).split("&").map(n=>n.split("=")).map(([n,o])=>{try{return[decodeURIComponent(n),decodeURIComponent(o)]}catch(r){return["",""]}}).filter(([n])=>S1(n,ef)).map(([n,o])=>[n.slice(ef.length),o]).forEach(([n,o])=>{t[n]=o}),t},S1=(e,t)=>e.substr(0,t.length)===t,ef="ionic:",gm="ionic-persist-config",C1=e=>vm(e),Gt=(e,t)=>(typeof e=="string"&&(t=e,e=void 0),C1(e).includes(t)),vm=(e=window)=>{if(typeof e>"u")return[];e.Ionic=e.Ionic||{};let t=e.Ionic.platforms;return t==null&&(t=e.Ionic.platforms=T1(e),t.forEach(n=>e.document.documentElement.classList.add("plt-".concat(n)))),t},T1=e=>{const t=G.get("platform");return Object.keys(tf).filter(n=>{const o=t==null?void 0:t[n];return typeof o=="function"?o(e):tf[n](e)})},$1=e=>Ls(e)&&!bm(e),Kc=e=>!!(Ln(e,/iPad/i)||Ln(e,/Macintosh/i)&&Ls(e)),P1=e=>Ln(e,/iPhone/i),_1=e=>Ln(e,/iPhone|iPod/i)||Kc(e),ym=e=>Ln(e,/android|sink/i),I1=e=>ym(e)&&!Ln(e,/mobile/i),R1=e=>{const t=e.innerWidth,n=e.innerHeight,o=Math.min(t,n),r=Math.max(t,n);return o>390&&o<520&&r>620&&r<800},L1=e=>{const t=e.innerWidth,n=e.innerHeight,o=Math.min(t,n),r=Math.max(t,n);return Kc(e)||I1(e)||o>460&&o<820&&r>780&&r<1400},Ls=e=>B1(e,"(any-pointer:coarse)"),O1=e=>!Ls(e),bm=e=>wm(e)||xm(e),wm=e=>!!(e.cordova||e.phonegap||e.PhoneGap),xm=e=>{const t=e.Capacitor;return!!(t!=null&&t.isNative)},A1=e=>Ln(e,/electron/i),z1=e=>{var t;return!!(!((t=e.matchMedia)===null||t===void 0)&&t.call(e,"(display-mode: standalone)").matches||e.navigator.standalone)},Ln=(e,t)=>t.test(e.navigator.userAgent),B1=(e,t)=>{var n;return(n=e.matchMedia)===null||n===void 0?void 0:n.call(e,t).matches},tf={ipad:Kc,iphone:P1,ios:_1,android:ym,phablet:R1,tablet:L1,cordova:wm,capacitor:xm,electron:A1,pwa:z1,mobile:Ls,mobileweb:$1,desktop:O1,hybrid:bm};let jn;const We=e=>e&&Ub(e)||jn,D1=(e={})=>{if(typeof window>"u")return;const t=window.document,n=window,o=n.Ionic=n.Ionic||{},r={};e._ael&&(r.ael=e._ael),e._rel&&(r.rel=e._rel),e._ce&&(r.ce=e._ce),Tb(r);const i=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},x1(n)),{persistConfig:!1}),o.config),E1(n)),e);G.reset(i),G.getBoolean("persistConfig")&&k1(n,i),vm(n),o.config=G,o.mode=jn=G.get("mode",t.documentElement.getAttribute("mode")||(Gt(n,"ios")?"ios":"md")),G.set("mode",jn),t.documentElement.setAttribute("mode",jn),t.documentElement.classList.add(jn),G.getBoolean("_testing")&&G.set("animated",!1);const s=l=>{var c;return(c=l.tagName)===null||c===void 0?void 0:c.startsWith("ION-")},a=l=>["ios","md"].includes(l);Wb(l=>{for(;l;){const c=l.mode||l.getAttribute("mode");if(c){if(a(c))return c;s(l)&&console.warn('Invalid ionic mode: "'+c+'", expected: "ios" or "md"')}l=l.parentElement}return jn})};/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const km=(e,...t)=>console.warn("[Ionic Warning]: ".concat(e),...t),M1=(e,...t)=>console.error("<".concat(e.tagName.toLowerCase(),"> must be used inside ").concat(t.join(" or "),"."));/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const vr=(e,t)=>{e.componentOnReady?e.componentOnReady().then(n=>t(n)):Cm(()=>t(e))},$a=e=>e.componentOnReady!==void 0,Em=(e,t=[])=>{const n={};return t.forEach(o=>{e.hasAttribute(o)&&(e.getAttribute(o)!==null&&(n[o]=e.getAttribute(o)),e.removeAttribute(o))}),n},N1=["role","aria-activedescendant","aria-atomic","aria-autocomplete","aria-braillelabel","aria-brailleroledescription","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colindextext","aria-colspan","aria-controls","aria-current","aria-describedby","aria-description","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowindextext","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext"],Sm=(e,t)=>Em(e,N1),hE=(e,t,n,o)=>{var r;if(typeof window<"u"){const i=window,s=(r=i==null?void 0:i.Ionic)===null||r===void 0?void 0:r.config;if(s){const a=s.get("_ael");if(a)return a(e,t,n,o);if(s._ael)return s._ael(e,t,n,o)}}return e.addEventListener(t,n,o)},pE=(e,t,n,o)=>{var r;if(typeof window<"u"){const i=window,s=(r=i==null?void 0:i.Ionic)===null||r===void 0?void 0:r.config;if(s){const a=s.get("_rel");if(a)return a(e,t,n,o);if(s._rel)return s._rel(e,t,n,o)}}return e.removeEventListener(t,n,o)},Cm=e=>typeof __zone_symbol__requestAnimationFrame=="function"?__zone_symbol__requestAnimationFrame(e):typeof requestAnimationFrame=="function"?requestAnimationFrame(e):setTimeout(e),Tm=(e,t,n)=>Math.max(e,Math.min(t,n)),Be=(e,t)=>{if(!e){const n="ASSERT: "+t;console.error(n);debugger;throw new Error(n)}},mE=e=>{if(e){const t=e.changedTouches;if(t&&t.length>0){const n=t[0];return{x:n.clientX,y:n.clientY}}if(e.pageX!==void 0)return{x:e.pageX,y:e.pageY}}return{x:0,y:0}},$m=(e,t)=>{if(e!=null||(e={}),t!=null||(t={}),e===t)return!0;const n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(const o of n)if(!(o in t)||e[o]!==t[o])return!1;return!0},j1="ionViewWillEnter",H1="ionViewDidEnter",Pm="ionViewWillLeave",_m="ionViewDidLeave",wl="ionViewWillUnload",Ao=e=>{e.tabIndex=-1,e.focus()},ei=e=>e.offsetParent!==null,V1=()=>({saveViewFocus:n=>{if(G.get("focusManagerPriority",!1)){const r=document.activeElement;r!==null&&(n!=null&&n.contains(r))&&r.setAttribute(nf,"true")}},setViewFocus:n=>{const o=G.get("focusManagerPriority",!1);if(Array.isArray(o)&&!n.contains(document.activeElement)){const r=n.querySelector("[".concat(nf,"]"));if(r&&ei(r)){Ao(r);return}for(const i of o)switch(i){case"content":const s=n.querySelector('main, [role="main"]');if(s&&ei(s)){Ao(s);return}break;case"heading":const a=n.querySelector('h1, [role="heading"][aria-level="1"]');if(a&&ei(a)){Ao(a);return}break;case"banner":const l=n.querySelector('header, [role="banner"]');if(l&&ei(l)){Ao(l);return}break;default:km("Unrecognized focus manager priority value ".concat(i));break}Ao(n)}}}),nf="ion-last-focus",F1=()=>Tt(()=>import("./ios.transition-DC078KTL.js"),[]),W1=()=>Tt(()=>import("./md.transition-Do7vHepf.js"),[]),Im=V1(),Rm=e=>new Promise((t,n)=>{dn(()=>{U1(e),Q1(e).then(o=>{o.animation&&o.animation.destroy(),of(e),t(o)},o=>{of(e),n(o)})})}),U1=e=>{const t=e.enteringEl,n=e.leavingEl;Im.saveViewFocus(n),Z1(t,n,e.direction),e.showGoBack?t.classList.add("can-go-back"):t.classList.remove("can-go-back"),_a(t,!1),t.style.setProperty("pointer-events","none"),n&&(_a(n,!1),n.style.setProperty("pointer-events","none"))},Q1=async e=>{const t=await q1(e);return t&&Eb.isBrowser?K1(t,e):Y1(e)},of=e=>{const t=e.enteringEl,n=e.leavingEl;t.classList.remove("ion-page-invisible"),t.style.removeProperty("pointer-events"),n!==void 0&&(n.classList.remove("ion-page-invisible"),n.style.removeProperty("pointer-events")),Im.setViewFocus(t)},q1=async e=>!e.leavingEl||!e.animated||e.duration===0?void 0:e.animationBuilder?e.animationBuilder:e.mode==="ios"?(await F1()).iosTransitionAnimation:(await W1()).mdTransitionAnimation,K1=async(e,t)=>{await Lm(t,!0);const n=e(t.baseEl,t);Om(t.enteringEl,t.leavingEl);const o=await G1(n,t);return t.progressCallback&&t.progressCallback(void 0),o&&Am(t.enteringEl,t.leavingEl),{hasCompleted:o,animation:n}},Y1=async e=>{const t=e.enteringEl,n=e.leavingEl,o=G.get("focusManagerPriority",!1);return await Lm(e,o),Om(t,n),Am(t,n),{hasCompleted:!0}},Lm=async(e,t)=>{(e.deepWait!==void 0?e.deepWait:t)&&await Promise.all([Pa(e.enteringEl),Pa(e.leavingEl)]),await X1(e.viewIsReady,e.enteringEl)},X1=async(e,t)=>{e&&await e(t)},G1=(e,t)=>{const n=t.progressCallback,o=new Promise(r=>{e.onFinish(i=>r(i===1))});return n?(e.progressStart(!0),n(e)):e.play(),o},Om=(e,t)=>{At(t,Pm),At(e,j1)},Am=(e,t)=>{At(e,H1),At(t,_m)},At=(e,t)=>{if(e){const n=new CustomEvent(t,{bubbles:!1,cancelable:!1});e.dispatchEvent(n)}},Pa=async e=>{const t=e;if(t){if(t.componentOnReady!=null){if(await t.componentOnReady()!=null)return}else if(t.__registerHost!=null){await new Promise(o=>Cm(o));return}await Promise.all(Array.from(t.children).map(Pa))}},_a=(e,t)=>{t?(e.setAttribute("aria-hidden","true"),e.classList.add("ion-page-hidden")):(e.hidden=!1,e.removeAttribute("aria-hidden"),e.classList.remove("ion-page-hidden"))},Z1=(e,t,n)=>{e!==void 0&&(e.style.zIndex=n==="back"?"99":"101"),t!==void 0&&(t.style.zIndex="100")},gE=e=>{if(e.classList.contains("ion-page"))return e;const t=e.querySelector(":scope > .ion-page, :scope > ion-nav, :scope > ion-tabs");return t||e};/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const Qi=(e,t,n,o,r)=>ew(e[1],t[1],n[1],o[1],r).map(i=>J1(e[0],t[0],n[0],o[0],i)),J1=(e,t,n,o,r)=>{const i=3*t*Math.pow(r-1,2),s=-3*n*r+3*n+o*r,a=e*Math.pow(r-1,3);return r*(i+r*s)-a},ew=(e,t,n,o,r)=>(e-=r,t-=r,n-=r,o-=r,nw(o-3*n+3*t-e,3*n-6*t+3*e,3*t-3*e,e).filter(s=>s>=0&&s<=1)),tw=(e,t,n)=>{const o=t*t-4*e*n;return o<0?[]:[(-t+Math.sqrt(o))/(2*e),(-t-Math.sqrt(o))/(2*e)]},nw=(e,t,n,o)=>{if(e===0)return tw(t,n,o);t/=e,n/=e,o/=e;const r=(3*n-t*t)/3,i=(2*t*t*t-9*t*n+27*o)/27;if(r===0)return[Math.pow(-i,1/3)];if(i===0)return[Math.sqrt(-r),-Math.sqrt(-r)];const s=Math.pow(i/2,2)+Math.pow(r/3,3);if(s===0)return[Math.pow(i/2,1/2)-t/3];if(s>0)return[Math.pow(-(i/2)+Math.sqrt(s),1/3)-Math.pow(i/2+Math.sqrt(s),1/3)-t/3];const a=Math.sqrt(Math.pow(-(r/3),3)),l=Math.acos(-(i/(2*Math.sqrt(Math.pow(-(r/3),3))))),c=2*Math.pow(a,1/3);return[c*Math.cos(l/3)-t/3,c*Math.cos((l+2*Math.PI)/3)-t/3,c*Math.cos((l+4*Math.PI)/3)-t/3]};/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const Os=(e,t)=>t.closest(e)!==null,_r=(e,t)=>typeof e=="string"&&e.length>0?Object.assign({"ion-color":!0,["ion-color-".concat(e)]:!0},t):t;/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const qi=()=>G.get("experimentalCloseWatcher",!1)&&pe!==void 0&&"CloseWatcher"in pe,ow=()=>{document.addEventListener("backbutton",()=>{})},rw=()=>{const e=document;let t=!1;const n=()=>{if(t)return;let o=0,r=[];const i=new CustomEvent("ionBackButton",{bubbles:!1,detail:{register(l,c){r.push({priority:l,handler:c,id:o++})}}});e.dispatchEvent(i);const s=async l=>{try{if(l!=null&&l.handler){const c=l.handler(a);c!=null&&await c}}catch(c){console.error(c)}},a=()=>{if(r.length>0){let l={priority:Number.MIN_SAFE_INTEGER,handler:()=>{},id:-1};r.forEach(c=>{c.priority>=l.priority&&(l=c)}),t=!0,r=r.filter(c=>c.id!==l.id),s(l).then(()=>t=!1)}};a()};if(qi()){let o;const r=()=>{o==null||o.destroy(),o=new pe.CloseWatcher,o.onclose=()=>{n(),r()}};r()}else e.addEventListener("backbutton",n)},iw=100,sw=99,lw=Object.freeze(Object.defineProperty({__proto__:null,MENU_BACK_BUTTON_PRIORITY:sw,OVERLAY_BACK_BUTTON_PRIORITY:iw,blockHardwareBackButton:ow,shouldUseCloseWatcher:qi,startHardwareBackButton:rw},Symbol.toStringTag,{value:"Module"}));/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const Yc=async(e,t,n,o,r,i)=>{var s;if(e)return e.attachViewToDom(t,n,r,o);if(typeof n!="string"&&!(n instanceof HTMLElement))throw new Error("framework delegate is missing");const a=typeof n=="string"?(s=t.ownerDocument)===null||s===void 0?void 0:s.createElement(n):n;return o&&o.forEach(l=>a.classList.add(l)),r&&Object.assign(a,r),t.appendChild(a),await new Promise(l=>vr(a,l)),a},aw=(e,t)=>{if(t){if(e){const n=t.parentElement;return e.removeViewFromDom(n,t)}t.remove()}return Promise.resolve()};function Oe(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var r=0,o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]]);return n}/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const cw=":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:strict;pointer-events:none}:host(.unbounded){contain:layout size style}.ripple-effect{border-radius:50%;position:absolute;background-color:currentColor;color:inherit;contain:strict;opacity:0;-webkit-animation:225ms rippleAnimation forwards, 75ms fadeInAnimation forwards;animation:225ms rippleAnimation forwards, 75ms fadeInAnimation forwards;will-change:transform, opacity;pointer-events:none}.fade-out{-webkit-transform:translate(var(--translate-end)) scale(var(--final-scale, 1));transform:translate(var(--translate-end)) scale(var(--final-scale, 1));-webkit-animation:150ms fadeOutAnimation forwards;animation:150ms fadeOutAnimation forwards}@-webkit-keyframes rippleAnimation{from{-webkit-animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);-webkit-transform:scale(1);transform:scale(1)}to{-webkit-transform:translate(var(--translate-end)) scale(var(--final-scale, 1));transform:translate(var(--translate-end)) scale(var(--final-scale, 1))}}@keyframes rippleAnimation{from{-webkit-animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);-webkit-transform:scale(1);transform:scale(1)}to{-webkit-transform:translate(var(--translate-end)) scale(var(--final-scale, 1));transform:translate(var(--translate-end)) scale(var(--final-scale, 1))}}@-webkit-keyframes fadeInAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0}to{opacity:0.16}}@keyframes fadeInAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0}to{opacity:0.16}}@-webkit-keyframes fadeOutAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0.16}to{opacity:0}}@keyframes fadeOutAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0.16}to{opacity:0}}",uw=cw,dw=Qe(class extends Ue{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.type="bounded"}async addRipple(t,n){return new Promise(o=>{mr(()=>{const r=this.el.getBoundingClientRect(),i=r.width,s=r.height,a=Math.sqrt(i*i+s*s),l=Math.max(s,i),c=this.unbounded?l:a+hw,u=Math.floor(l*pw),d=c/u;let f=t-r.left,p=n-r.top;this.unbounded&&(f=i*.5,p=s*.5);const v=f-u*.5,x=p-u*.5,R=i*.5-f,m=s*.5-p;dn(()=>{const h=document.createElement("div");h.classList.add("ripple-effect");const g=h.style;g.top=x+"px",g.left=v+"px",g.width=g.height=u+"px",g.setProperty("--final-scale","".concat(d)),g.setProperty("--translate-end","".concat(R,"px, ").concat(m,"px")),(this.el.shadowRoot||this.el).appendChild(h),setTimeout(()=>{o(()=>{fw(h)})},325)})})})}get unbounded(){return this.type==="unbounded"}render(){const t=We(this);return M(ot,{key:"7ae559bda5d2c1856a45bfa150c2cb4f83373f8e",role:"presentation",class:{[t]:!0,unbounded:this.unbounded}})}get el(){return this}static get style(){return uw}},[1,"ion-ripple-effect",{type:[1],addRipple:[64]}]),fw=e=>{e.classList.add("fade-out"),setTimeout(()=>{e.remove()},200)},hw=10,pw=.5;function mw(){if(typeof customElements>"u")return;["ion-ripple-effect"].forEach(t=>{switch(t){case"ion-ripple-effect":customElements.get(t)||customElements.define(t,dw);break}})}/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const ti=typeof window<"u"?window:void 0;ti&&(ti.CSS&&ti.CSS.supports&&ti.CSS.supports("--a: 0"));/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const gw=e=>e&&e.dir!==""?e.dir.toLowerCase()==="rtl":(document==null?void 0:document.dir.toLowerCase())==="rtl";/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const vw=':host{--background:var(--ion-background-color, #fff);--color:var(--ion-text-color, #000);--padding-top:0px;--padding-bottom:0px;--padding-start:0px;--padding-end:0px;--keyboard-offset:0px;--offset-top:0px;--offset-bottom:0px;--overflow:auto;display:block;position:relative;-ms-flex:1;flex:1;width:100%;height:100%;margin:0 !important;padding:0 !important;font-family:var(--ion-font-family, inherit);contain:size style}:host(.ion-color) .inner-scroll{background:var(--ion-color-base);color:var(--ion-color-contrast)}#background-content{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);position:absolute;background:var(--background)}.inner-scroll{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:calc(var(--padding-top) + var(--offset-top));padding-bottom:calc(var(--padding-bottom) + var(--keyboard-offset) + var(--offset-bottom));position:absolute;color:var(--color);-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;-ms-touch-action:pan-x pan-y pinch-zoom;touch-action:pan-x pan-y pinch-zoom}.scroll-y,.scroll-x{-webkit-overflow-scrolling:touch;z-index:0;will-change:scroll-position}.scroll-y{overflow-y:var(--overflow);overscroll-behavior-y:contain}.scroll-x{overflow-x:var(--overflow);overscroll-behavior-x:contain}.overscroll::before,.overscroll::after{position:absolute;width:1px;height:1px;content:""}.overscroll::before{bottom:-1px}.overscroll::after{top:-1px}:host(.content-sizing){display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;min-height:0;contain:none}:host(.content-sizing) .inner-scroll{position:relative;top:0;bottom:0;margin-top:calc(var(--offset-top) * -1);margin-bottom:calc(var(--offset-bottom) * -1)}.transition-effect{display:none;position:absolute;width:100%;height:100vh;opacity:0;pointer-events:none}:host(.content-ltr) .transition-effect{left:-100%;}:host(.content-rtl) .transition-effect{right:-100%;}.transition-cover{position:absolute;right:0;width:100%;height:100%;background:black;opacity:0.1}.transition-shadow{display:block;position:absolute;width:100%;height:100%;-webkit-box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03);box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03)}:host(.content-ltr) .transition-shadow{right:0;}:host(.content-rtl) .transition-shadow{left:0;-webkit-transform:scaleX(-1);transform:scaleX(-1)}::slotted([slot=fixed]){position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0)}',yw=vw,bw=Qe(class extends Ue{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionScrollStart=be(this,"ionScrollStart",7),this.ionScroll=be(this,"ionScroll",7),this.ionScrollEnd=be(this,"ionScrollEnd",7),this.watchDog=null,this.isScrolling=!1,this.lastScroll=0,this.queued=!1,this.cTop=-1,this.cBottom=-1,this.isMainContent=!0,this.resizeTimeout=null,this.inheritedAttributes={},this.tabsElement=null,this.detail={scrollTop:0,scrollLeft:0,type:"scroll",event:void 0,startX:0,startY:0,startTime:0,currentX:0,currentY:0,velocityX:0,velocityY:0,deltaX:0,deltaY:0,currentTime:0,data:void 0,isScrolling:!0},this.color=void 0,this.fullscreen=!1,this.fixedSlotPlacement="after",this.forceOverscroll=void 0,this.scrollX=!1,this.scrollY=!0,this.scrollEvents=!1}componentWillLoad(){this.inheritedAttributes=Sm(this.el)}connectedCallback(){if(this.isMainContent=this.el.closest("ion-menu, ion-popover, ion-modal")===null,$a(this.el)){const t=this.tabsElement=this.el.closest("ion-tabs");t!==null&&(this.tabsLoadCallback=()=>this.resize(),t.addEventListener("ionTabBarLoaded",this.tabsLoadCallback))}}disconnectedCallback(){if(this.onScrollEnd(),$a(this.el)){const{tabsElement:t,tabsLoadCallback:n}=this;t!==null&&n!==void 0&&t.removeEventListener("ionTabBarLoaded",n),this.tabsElement=null,this.tabsLoadCallback=void 0}}onResize(){this.resizeTimeout&&(clearTimeout(this.resizeTimeout),this.resizeTimeout=null),this.resizeTimeout=setTimeout(()=>{this.el.offsetParent!==null&&this.resize()},100)}shouldForceOverscroll(){const{forceOverscroll:t}=this,n=We(this);return t===void 0?n==="ios"&&Gt("ios"):t}resize(){this.fullscreen?mr(()=>this.readDimensions()):(this.cTop!==0||this.cBottom!==0)&&(this.cTop=this.cBottom=0,Ta(this))}readDimensions(){const t=xw(this.el),n=Math.max(this.el.offsetTop,0),o=Math.max(t.offsetHeight-n-this.el.offsetHeight,0);(n!==this.cTop||o!==this.cBottom)&&(this.cTop=n,this.cBottom=o,Ta(this))}onScroll(t){const n=Date.now(),o=!this.isScrolling;this.lastScroll=n,o&&this.onScrollStart(),!this.queued&&this.scrollEvents&&(this.queued=!0,mr(r=>{this.queued=!1,this.detail.event=t,kw(this.detail,this.scrollEl,r,o),this.ionScroll.emit(this.detail)}))}async getScrollElement(){return this.scrollEl||await new Promise(t=>vr(this.el,t)),Promise.resolve(this.scrollEl)}async getBackgroundElement(){return this.backgroundContentEl||await new Promise(t=>vr(this.el,t)),Promise.resolve(this.backgroundContentEl)}scrollToTop(t=0){return this.scrollToPoint(void 0,0,t)}async scrollToBottom(t=0){const n=await this.getScrollElement(),o=n.scrollHeight-n.clientHeight;return this.scrollToPoint(void 0,o,t)}async scrollByPoint(t,n,o){const r=await this.getScrollElement();return this.scrollToPoint(t+r.scrollLeft,n+r.scrollTop,o)}async scrollToPoint(t,n,o=0){const r=await this.getScrollElement();if(o<32){n!=null&&(r.scrollTop=n),t!=null&&(r.scrollLeft=t);return}let i,s=0;const a=new Promise(p=>i=p),l=r.scrollTop,c=r.scrollLeft,u=n!=null?n-l:0,d=t!=null?t-c:0,f=p=>{const v=Math.min(1,(p-s)/o)-1,x=Math.pow(v,3)+1;u!==0&&(r.scrollTop=Math.floor(x*u+l)),d!==0&&(r.scrollLeft=Math.floor(x*d+c)),x<1?requestAnimationFrame(f):i()};return requestAnimationFrame(p=>{s=p,f(p)}),a}onScrollStart(){this.isScrolling=!0,this.ionScrollStart.emit({isScrolling:!0}),this.watchDog&&clearInterval(this.watchDog),this.watchDog=setInterval(()=>{this.lastScroll<Date.now()-120&&this.onScrollEnd()},100)}onScrollEnd(){this.watchDog&&clearInterval(this.watchDog),this.watchDog=null,this.isScrolling&&(this.isScrolling=!1,this.ionScrollEnd.emit({isScrolling:!1}))}render(){const{fixedSlotPlacement:t,inheritedAttributes:n,isMainContent:o,scrollX:r,scrollY:i,el:s}=this,a=gw(s)?"rtl":"ltr",l=We(this),c=this.shouldForceOverscroll(),u=l==="ios";return this.resize(),M(ot,Object.assign({key:"f2a24aa66dbf5c76f9d4b06f708eb73cadc239df",role:o?"main":void 0,class:_r(this.color,{[l]:!0,"content-sizing":Os("ion-popover",this.el),overscroll:c,["content-".concat(a)]:!0}),style:{"--offset-top":"".concat(this.cTop,"px"),"--offset-bottom":"".concat(this.cBottom,"px")}},n),M("div",{key:"6480ca7648b278abb36477b3838bccbcd4995e2a",ref:d=>this.backgroundContentEl=d,id:"background-content",part:"background"}),t==="before"?M("slot",{name:"fixed"}):null,M("div",{key:"29a23b663f5f0215bb000820c01e1814c0d55985",class:{"inner-scroll":!0,"scroll-x":r,"scroll-y":i,overscroll:(r||i)&&c},ref:d=>this.scrollEl=d,onScroll:this.scrollEvents?d=>this.onScroll(d):void 0,part:"scroll"},M("slot",{key:"0fe1bd05609a4b88ae2ce9addf5d5dc5dc1806f0"})),u?M("div",{class:"transition-effect"},M("div",{class:"transition-cover"}),M("div",{class:"transition-shadow"})):null,t==="after"?M("slot",{name:"fixed"}):null)}get el(){return this}static get style(){return yw}},[1,"ion-content",{color:[513],fullscreen:[4],fixedSlotPlacement:[1,"fixed-slot-placement"],forceOverscroll:[1028,"force-overscroll"],scrollX:[4,"scroll-x"],scrollY:[4,"scroll-y"],scrollEvents:[4,"scroll-events"],getScrollElement:[64],getBackgroundElement:[64],scrollToTop:[64],scrollToBottom:[64],scrollByPoint:[64],scrollToPoint:[64]},[[9,"resize","onResize"]]]),ww=e=>{var t;return e.parentElement?e.parentElement:!((t=e.parentNode)===null||t===void 0)&&t.host?e.parentNode.host:null},xw=e=>{const t=e.closest("ion-tabs");if(t)return t;const n=e.closest("ion-app, ion-page, .ion-page, page-inner, .popover-content");return n||ww(e)},kw=(e,t,n,o)=>{const r=e.currentX,i=e.currentY,s=e.currentTime,a=t.scrollLeft,l=t.scrollTop,c=n-s;if(o&&(e.startTime=n,e.startX=a,e.startY=l,e.velocityX=e.velocityY=0),e.currentTime=n,e.currentX=e.scrollLeft=a,e.currentY=e.scrollTop=l,e.deltaX=a-e.startX,e.deltaY=l-e.startY,c>0&&c<100){const u=(a-r)/c,d=(l-i)/c;e.velocityX=u*.7+e.velocityX*.3,e.velocityY=d*.7+e.velocityY*.3}};function Ew(){if(typeof customElements>"u")return;["ion-content"].forEach(t=>{switch(t){case"ion-content":customElements.get(t)||customElements.define(t,bw);break}})}/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const Sw=Ew;/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const Cw=()=>{if(pe!==void 0)return pe.Capacitor};/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const Tw=()=>{let e;return{lock:async()=>{const n=e;let o;return e=new Promise(r=>o=r),n!==void 0&&await n,o}}};/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const $w="ION-CONTENT",zm="ion-content",Bm=".ion-content-scroll-host",Dm="".concat(zm,", ").concat(Bm),Xc=e=>e.tagName===$w,rf=async e=>Xc(e)?(await new Promise(t=>vr(e,t)),e.getScrollElement()):e,sf=e=>{const t=e.querySelector(Bm);return t||e.querySelector(Dm)},bE=e=>e.closest(Dm),wE=(e,t)=>Xc(e)?e.scrollToTop(t):Promise.resolve(e.scrollTo({top:0,left:0,behavior:"smooth"})),xE=(e,t,n,o)=>Xc(e)?e.scrollByPoint(t,n,o):Promise.resolve(e.scrollBy({top:n,left:t,behavior:o>0?"smooth":"auto"})),lf=e=>M1(e,zm);/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */var Ia;(function(e){e.Unimplemented="UNIMPLEMENTED",e.Unavailable="UNAVAILABLE"})(Ia||(Ia={}));var Ra;(function(e){e.Body="body",e.Ionic="ionic",e.Native="native",e.None="none"})(Ra||(Ra={}));const Pw={getEngine(){const e=Cw();if(e!=null&&e.isPluginAvailable("Keyboard"))return e.Plugins.Keyboard},getResizeMode(){const e=this.getEngine();return e!=null&&e.getResizeMode?e.getResizeMode().catch(t=>{if(t.code!==Ia.Unimplemented)throw t}):Promise.resolve(void 0)}};/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const Mm=e=>{if(vl===void 0||e===Ra.None||e===void 0)return null;const t=vl.querySelector("ion-app");return t!=null?t:vl.body},af=e=>{const t=Mm(e);return t===null?0:t.clientHeight},_w=async e=>{let t,n,o,r;const i=async()=>{const u=await Pw.getResizeMode(),d=u===void 0?void 0:u.mode;t=()=>{r===void 0&&(r=af(d)),o=!0,s(o,d)},n=()=>{o=!1,s(o,d)},pe==null||pe.addEventListener("keyboardWillShow",t),pe==null||pe.addEventListener("keyboardWillHide",n)},s=(u,d)=>{e&&e(u,a(d))},a=u=>{if(r===0||r===af(u))return;const d=Mm(u);if(d!==null)return new Promise(f=>{const p=()=>{d.clientHeight===r&&(v.disconnect(),f())},v=new ResizeObserver(p);v.observe(d)})},l=()=>{pe==null||pe.removeEventListener("keyboardWillShow",t),pe==null||pe.removeEventListener("keyboardWillHide",n),t=n=void 0},c=()=>o;return await i(),{init:i,destroy:l,isKeyboardVisible:c}};/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const Iw="all 0.2s ease-in-out",cf=e=>{const t=document.querySelector("".concat(e,".ion-cloned-element"));if(t!==null)return t;const n=document.createElement(e);return n.classList.add("ion-cloned-element"),n.style.setProperty("display","none"),document.body.appendChild(n),n},uf=e=>{if(!e)return;const t=e.querySelectorAll("ion-toolbar");return{el:e,toolbars:Array.from(t).map(n=>{const o=n.querySelector("ion-title");return{el:n,background:n.shadowRoot.querySelector(".toolbar-background"),ionTitleEl:o,innerTitleEl:o?o.shadowRoot.querySelector(".toolbar-title"):null,ionButtonsEl:Array.from(n.querySelectorAll("ion-buttons"))}})}},Rw=(e,t,n)=>{mr(()=>{const o=e.scrollTop,r=Tm(1,1+-o/500,1.1);n.querySelector("ion-refresher.refresher-native")===null&&dn(()=>{Aw(t.toolbars,r)})})},Gc=(e,t)=>{e.collapse!=="fade"&&(t===void 0?e.style.removeProperty("--opacity-scale"):e.style.setProperty("--opacity-scale",t.toString()))},Lw=(e,t,n)=>{if(!e[0].isIntersecting)return;const o=e[0].intersectionRatio>.9||n<=0?0:(1-e[0].intersectionRatio)*100/75;Gc(t.el,o===1?void 0:o)},Ow=(e,t,n,o)=>{dn(()=>{const r=o.scrollTop;Lw(e,t,r);const i=e[0],s=i.intersectionRect,a=s.width*s.height,l=i.rootBounds.width*i.rootBounds.height,c=a===0&&l===0,u=Math.abs(s.left-i.boundingClientRect.left),d=Math.abs(s.right-i.boundingClientRect.right),f=a>0&&(u>=5||d>=5);c||f||(i.isIntersecting?(jo(t,!1),jo(n)):(s.x===0&&s.y===0||s.width!==0&&s.height!==0)&&r>0&&(jo(t),jo(n,!1),Gc(t.el)))})},jo=(e,t=!0)=>{const n=e.el,r=e.toolbars.map(i=>i.ionTitleEl);t?(n.classList.remove("header-collapse-condense-inactive"),r.forEach(i=>{i&&i.removeAttribute("aria-hidden")})):(n.classList.add("header-collapse-condense-inactive"),r.forEach(i=>{i&&i.setAttribute("aria-hidden","true")}))},Aw=(e=[],t=1,n=!1)=>{e.forEach(o=>{const r=o.ionTitleEl,i=o.innerTitleEl;!r||r.size!=="large"||(i.style.transition=n?Iw:"",i.style.transform="scale3d(".concat(t,", ").concat(t,", 1)"))})},df=(e,t,n)=>{mr(()=>{const o=e.scrollTop,r=t.clientHeight,i=n?n.clientHeight:0;if(n!==null&&o<i){t.style.setProperty("--opacity-scale","0"),e.style.setProperty("clip-path","inset(".concat(r,"px 0px 0px 0px)"));return}const s=o-i,l=Tm(0,s/10,1);dn(()=>{e.style.removeProperty("clip-path"),t.style.setProperty("--opacity-scale",l.toString())})})},zw="ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-ios ion-toolbar:last-of-type{--border-width:0 0 0.55px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.header-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}.header-translucent-ios ion-toolbar{--opacity:.8}.header-collapse-condense-inactive .header-background{-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px)}}.header-ios.ion-no-border ion-toolbar:last-of-type{--border-width:0}.header-collapse-fade ion-toolbar{--opacity-scale:inherit}.header-collapse-condense{z-index:9}.header-collapse-condense ion-toolbar{position:-webkit-sticky;position:sticky;top:0}.header-collapse-condense ion-toolbar:first-of-type{padding-top:0px;z-index:1}.header-collapse-condense ion-toolbar{--background:var(--ion-background-color, #fff);z-index:0}.header-collapse-condense ion-toolbar:last-of-type{--border-width:0px}.header-collapse-condense ion-toolbar ion-searchbar{padding-top:0px;padding-bottom:13px}.header-collapse-main{--opacity-scale:1}.header-collapse-main ion-toolbar{--opacity-scale:inherit}.header-collapse-main ion-toolbar.in-toolbar ion-title,.header-collapse-main ion-toolbar.in-toolbar ion-buttons{-webkit-transition:all 0.2s ease-in-out;transition:all 0.2s ease-in-out}.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-buttons.buttons-collapse{opacity:0;pointer-events:none}.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-buttons.buttons-collapse{visibility:hidden}ion-header.header-ios:not(.header-collapse-main):has(~ion-content ion-header.header-ios[collapse=condense],~ion-content ion-header.header-ios.header-collapse-condense){opacity:0}",Bw=zw,Dw="ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-md{-webkit-box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12)}.header-collapse-condense{display:none}.header-md.ion-no-border{-webkit-box-shadow:none;box-shadow:none}",Mw=Dw,Nw=Qe(class extends Ue{constructor(){super(),this.__registerHost(),this.inheritedAttributes={},this.setupFadeHeader=async(t,n)=>{const o=this.scrollEl=await rf(t);this.contentScrollCallback=()=>{df(this.scrollEl,this.el,n)},o.addEventListener("scroll",this.contentScrollCallback),df(this.scrollEl,this.el,n)},this.collapse=void 0,this.translucent=!1}componentWillLoad(){this.inheritedAttributes=Sm(this.el)}componentDidLoad(){this.checkCollapsibleHeader()}componentDidUpdate(){this.checkCollapsibleHeader()}disconnectedCallback(){this.destroyCollapsibleHeader()}async checkCollapsibleHeader(){if(We(this)!=="ios")return;const{collapse:n}=this,o=n==="condense",r=n==="fade";if(this.destroyCollapsibleHeader(),o){const i=this.el.closest("ion-app,ion-page,.ion-page,page-inner"),s=i?sf(i):null;dn(()=>{const a=cf("ion-title");a.size="large",cf("ion-back-button")}),await this.setupCondenseHeader(s,i)}else if(r){const i=this.el.closest("ion-app,ion-page,.ion-page,page-inner"),s=i?sf(i):null;if(!s){lf(this.el);return}const a=s.querySelector('ion-header[collapse="condense"]');await this.setupFadeHeader(s,a)}}destroyCollapsibleHeader(){this.intersectionObserver&&(this.intersectionObserver.disconnect(),this.intersectionObserver=void 0),this.scrollEl&&this.contentScrollCallback&&(this.scrollEl.removeEventListener("scroll",this.contentScrollCallback),this.contentScrollCallback=void 0),this.collapsibleMainHeader&&(this.collapsibleMainHeader.classList.remove("header-collapse-main"),this.collapsibleMainHeader=void 0)}async setupCondenseHeader(t,n){if(!t||!n){lf(this.el);return}if(typeof IntersectionObserver>"u")return;this.scrollEl=await rf(t);const o=n.querySelectorAll("ion-header");if(this.collapsibleMainHeader=Array.from(o).find(a=>a.collapse!=="condense"),!this.collapsibleMainHeader)return;const r=uf(this.collapsibleMainHeader),i=uf(this.el);if(!r||!i)return;jo(r,!1),Gc(r.el,0);const s=a=>{Ow(a,r,i,this.scrollEl)};this.intersectionObserver=new IntersectionObserver(s,{root:t,threshold:[.25,.3,.4,.5,.6,.7,.8,.9,1]}),this.intersectionObserver.observe(i.toolbars[i.toolbars.length-1].el),this.contentScrollCallback=()=>{Rw(this.scrollEl,i,t)},this.scrollEl.addEventListener("scroll",this.contentScrollCallback),dn(()=>{this.collapsibleMainHeader!==void 0&&this.collapsibleMainHeader.classList.add("header-collapse-main")})}render(){const{translucent:t,inheritedAttributes:n}=this,o=We(this),r=this.collapse||"none",i=Os("ion-menu",this.el)?"none":"banner";return M(ot,Object.assign({key:"b6cc27f0b08afc9fcc889683525da765d80ba672",role:i,class:{[o]:!0,["header-".concat(o)]:!0,"header-translucent":this.translucent,["header-collapse-".concat(r)]:!0,["header-translucent-".concat(o)]:this.translucent}},n),o==="ios"&&t&&M("div",{key:"395766d4dcee3398bc91960db21f922095292f14",class:"header-background"}),M("slot",{key:"09a67ece27b258ff1248805d43d92a49b2c6859a"}))}get el(){return this}static get style(){return{ios:Bw,md:Mw}}},[36,"ion-header",{collapse:[1],translucent:[4]}]);function jw(){if(typeof customElements>"u")return;["ion-header"].forEach(t=>{switch(t){case"ion-header":customElements.get(t)||customElements.define(t,Nw);break}})}/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const Hw=jw;/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const Vw=".item.sc-ion-label-ios-h,.item .sc-ion-label-ios-h{--color:initial;display:block;color:var(--color);font-family:var(--ion-font-family, inherit);font-size:inherit;text-overflow:ellipsis;-webkit-box-sizing:border-box;box-sizing:border-box}.ion-color.sc-ion-label-ios-h{color:var(--ion-color-base)}.ion-text-nowrap.sc-ion-label-ios-h{overflow:hidden}.item-interactive-disabled.sc-ion-label-ios-h:not(.item-multiple-inputs),.item-interactive-disabled:not(.item-multiple-inputs) .sc-ion-label-ios-h{cursor:default;opacity:0.3;pointer-events:none}.item-input.sc-ion-label-ios-h,.item-input .sc-ion-label-ios-h{-ms-flex:initial;flex:initial;max-width:200px;pointer-events:none}.item-textarea.sc-ion-label-ios-h,.item-textarea .sc-ion-label-ios-h{-ms-flex-item-align:baseline;align-self:baseline}.item-skeleton-text.sc-ion-label-ios-h,.item-skeleton-text .sc-ion-label-ios-h{overflow:hidden}.label-fixed.sc-ion-label-ios-h{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.label-stacked.sc-ion-label-ios-h,.label-floating.sc-ion-label-ios-h{margin-bottom:0;-ms-flex-item-align:stretch;align-self:stretch;width:auto;max-width:100%}.label-no-animate.label-floating.sc-ion-label-ios-h{-webkit-transition:none;transition:none}.sc-ion-label-ios-s h1,.sc-ion-label-ios-s h2,.sc-ion-label-ios-s h3,.sc-ion-label-ios-s h4,.sc-ion-label-ios-s h5,.sc-ion-label-ios-s h6{text-overflow:inherit;overflow:inherit}.ion-text-wrap.sc-ion-label-ios-h{font-size:0.875rem;line-height:1.5}.label-stacked.sc-ion-label-ios-h{margin-bottom:4px;font-size:0.875rem}.label-floating.sc-ion-label-ios-h{margin-bottom:0;-webkit-transform:translate(0, 29px);transform:translate(0, 29px);-webkit-transform-origin:left top;transform-origin:left top;-webkit-transition:-webkit-transform 150ms ease-in-out;transition:-webkit-transform 150ms ease-in-out;transition:transform 150ms ease-in-out;transition:transform 150ms ease-in-out, -webkit-transform 150ms ease-in-out}[dir=rtl].sc-ion-label-ios-h -no-combinator.label-floating.sc-ion-label-ios-h,[dir=rtl] .sc-ion-label-ios-h -no-combinator.label-floating.sc-ion-label-ios-h,[dir=rtl].label-floating.sc-ion-label-ios-h,[dir=rtl] .label-floating.sc-ion-label-ios-h{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.label-floating.sc-ion-label-ios-h:dir(rtl){-webkit-transform-origin:right top;transform-origin:right top}}.item-textarea.label-floating.sc-ion-label-ios-h,.item-textarea .label-floating.sc-ion-label-ios-h{-webkit-transform:translate(0, 28px);transform:translate(0, 28px)}.item-has-focus.label-floating.sc-ion-label-ios-h,.item-has-focus .label-floating.sc-ion-label-ios-h,.item-has-placeholder.sc-ion-label-ios-h:not(.item-input).label-floating,.item-has-placeholder:not(.item-input) .label-floating.sc-ion-label-ios-h,.item-has-value.label-floating.sc-ion-label-ios-h,.item-has-value .label-floating.sc-ion-label-ios-h{-webkit-transform:scale(0.82);transform:scale(0.82)}.sc-ion-label-ios-s h1{margin-left:0;margin-right:0;margin-top:3px;margin-bottom:2px;font-size:1.375rem;font-weight:normal}.sc-ion-label-ios-s h2{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:1.0625rem;font-weight:normal}.sc-ion-label-ios-s h3,.sc-ion-label-ios-s h4,.sc-ion-label-ios-s h5,.sc-ion-label-ios-s h6{margin-left:0;margin-right:0;margin-top:0;margin-bottom:3px;font-size:0.875rem;font-weight:normal;line-height:normal}.sc-ion-label-ios-s p{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:0.875rem;line-height:normal;text-overflow:inherit;overflow:inherit}.sc-ion-label-ios-s>p{color:var(--ion-color-step-400, var(--ion-text-color-step-600, #999999))}.sc-ion-label-ios-h.in-item-color.sc-ion-label-ios-s>p{color:inherit}.sc-ion-label-ios-s h2:last-child,.sc-ion-label-ios-s h3:last-child,.sc-ion-label-ios-s h4:last-child,.sc-ion-label-ios-s h5:last-child,.sc-ion-label-ios-s h6:last-child,.sc-ion-label-ios-s p:last-child{margin-bottom:0}",Fw=Vw,Ww=".item.sc-ion-label-md-h,.item .sc-ion-label-md-h{--color:initial;display:block;color:var(--color);font-family:var(--ion-font-family, inherit);font-size:inherit;text-overflow:ellipsis;-webkit-box-sizing:border-box;box-sizing:border-box}.ion-color.sc-ion-label-md-h{color:var(--ion-color-base)}.ion-text-nowrap.sc-ion-label-md-h{overflow:hidden}.item-interactive-disabled.sc-ion-label-md-h:not(.item-multiple-inputs),.item-interactive-disabled:not(.item-multiple-inputs) .sc-ion-label-md-h{cursor:default;opacity:0.3;pointer-events:none}.item-input.sc-ion-label-md-h,.item-input .sc-ion-label-md-h{-ms-flex:initial;flex:initial;max-width:200px;pointer-events:none}.item-textarea.sc-ion-label-md-h,.item-textarea .sc-ion-label-md-h{-ms-flex-item-align:baseline;align-self:baseline}.item-skeleton-text.sc-ion-label-md-h,.item-skeleton-text .sc-ion-label-md-h{overflow:hidden}.label-fixed.sc-ion-label-md-h{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.label-stacked.sc-ion-label-md-h,.label-floating.sc-ion-label-md-h{margin-bottom:0;-ms-flex-item-align:stretch;align-self:stretch;width:auto;max-width:100%}.label-no-animate.label-floating.sc-ion-label-md-h{-webkit-transition:none;transition:none}.sc-ion-label-md-s h1,.sc-ion-label-md-s h2,.sc-ion-label-md-s h3,.sc-ion-label-md-s h4,.sc-ion-label-md-s h5,.sc-ion-label-md-s h6{text-overflow:inherit;overflow:inherit}.ion-text-wrap.sc-ion-label-md-h{line-height:1.5}.label-stacked.sc-ion-label-md-h,.label-floating.sc-ion-label-md-h{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-transform-origin:top left;transform-origin:top left}.label-stacked.label-rtl.sc-ion-label-md-h,.label-floating.label-rtl.sc-ion-label-md-h{-webkit-transform-origin:top right;transform-origin:top right}.label-stacked.sc-ion-label-md-h{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1)}.label-floating.sc-ion-label-md-h{-webkit-transform:translateY(96%);transform:translateY(96%);-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1)}.ion-focused.label-floating.sc-ion-label-md-h,.ion-focused .label-floating.sc-ion-label-md-h,.item-has-focus.label-floating.sc-ion-label-md-h,.item-has-focus .label-floating.sc-ion-label-md-h,.item-has-placeholder.sc-ion-label-md-h:not(.item-input).label-floating,.item-has-placeholder:not(.item-input) .label-floating.sc-ion-label-md-h,.item-has-value.label-floating.sc-ion-label-md-h,.item-has-value .label-floating.sc-ion-label-md-h{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75)}.ion-focused.label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused .label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused.label-floating.sc-ion-label-md-h:not(.ion-color),.ion-focused .label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus.label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus .label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus.label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus .label-floating.sc-ion-label-md-h:not(.ion-color){color:var(--ion-color-primary, #0054e9)}.ion-focused.ion-color.label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused.ion-color .label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused.ion-color.label-floating.sc-ion-label-md-h:not(.ion-color),.ion-focused.ion-color .label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color.label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color .label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color.label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color .label-floating.sc-ion-label-md-h:not(.ion-color){color:var(--ion-color-contrast)}.ion-invalid.ion-touched.label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-invalid.ion-touched .label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-invalid.ion-touched.label-floating.sc-ion-label-md-h:not(.ion-color),.ion-invalid.ion-touched .label-floating.sc-ion-label-md-h:not(.ion-color){color:var(--highlight-color-invalid)}.sc-ion-label-md-s h1{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:1.5rem;font-weight:normal}.sc-ion-label-md-s h2{margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px;font-size:1rem;font-weight:normal}.sc-ion-label-md-s h3,.sc-ion-label-md-s h4,.sc-ion-label-md-s h5,.sc-ion-label-md-s h6{margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px;font-size:0.875rem;font-weight:normal;line-height:normal}.sc-ion-label-md-s p{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:0.875rem;line-height:1.25rem;text-overflow:inherit;overflow:inherit}.sc-ion-label-md-s>p{color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}.sc-ion-label-md-h.in-item-color.sc-ion-label-md-s>p{color:inherit}",Uw=Ww,Qw=Qe(class extends Ue{constructor(){super(),this.__registerHost(),this.ionColor=be(this,"ionColor",7),this.ionStyle=be(this,"ionStyle",7),this.inRange=!1,this.color=void 0,this.position=void 0,this.noAnimate=!1}componentWillLoad(){this.inRange=!!this.el.closest("ion-range"),this.noAnimate=this.position==="floating",this.emitStyle(),this.emitColor()}componentDidLoad(){this.noAnimate&&setTimeout(()=>{this.noAnimate=!1},1e3)}colorChanged(){this.emitColor()}positionChanged(){this.emitStyle()}emitColor(){const{color:t}=this;this.ionColor.emit({"item-label-color":t!==void 0,["ion-color-".concat(t)]:t!==void 0})}emitStyle(){const{inRange:t,position:n}=this;t||this.ionStyle.emit({label:!0,["label-".concat(n)]:n!==void 0})}render(){const t=this.position,n=We(this);return M(ot,{key:"6353a70565ef6fbbbf4042b000e536c61bcf99a9",class:_r(this.color,{[n]:!0,"in-item-color":Os("ion-item.ion-color",this.el),["label-".concat(t)]:t!==void 0,"label-no-animate":this.noAnimate,"label-rtl":document.dir==="rtl"})},M("slot",{key:"6ef9c2758c0168442aa84941af0a6cec1ef1ec21"}))}get el(){return this}static get watchers(){return{color:["colorChanged"],position:["positionChanged"]}}static get style(){return{ios:Fw,md:Uw}}},[38,"ion-label",{color:[513],position:[1],noAnimate:[32]},void 0,{color:["colorChanged"],position:["positionChanged"]}]);function qw(){if(typeof customElements>"u")return;["ion-label"].forEach(t=>{switch(t){case"ion-label":customElements.get(t)||customElements.define(t,Qw);break}})}/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const Kw=qw,Nm=1,jm=2,Ki=3;class La{constructor(t,n){this.component=t,this.params=n,this.state=Nm}async init(t){if(this.state=jm,!this.element){const n=this.component;this.element=await Yc(this.delegate,t,n,["ion-page","ion-page-invisible"],this.params)}}_destroy(){Be(this.state!==Ki,"view state must be ATTACHED");const t=this.element;t&&(this.delegate?this.delegate.removeViewFromDom(t.parentElement,t):t.remove()),this.nav=void 0,this.state=Ki}}const ff=(e,t,n)=>!e||e.component!==t?!1:$m(e.params,n),hf=(e,t)=>e?e instanceof La?e:new La(e,t):null,Yw=e=>e.map(t=>t instanceof La?t:"component"in t?hf(t.component,t.componentProps===null?void 0:t.componentProps):hf(t,void 0)).filter(t=>t!==null),Xw=":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;z-index:0}",Gw=Xw,Zw=Qe(class extends Ue{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionNavWillLoad=be(this,"ionNavWillLoad",7),this.ionNavWillChange=be(this,"ionNavWillChange",3),this.ionNavDidChange=be(this,"ionNavDidChange",3),this.transInstr=[],this.gestureOrAnimationInProgress=!1,this.useRouter=!1,this.isTransitioning=!1,this.destroyed=!1,this.views=[],this.didLoad=!1,this.delegate=void 0,this.swipeGesture=void 0,this.animated=!0,this.animation=void 0,this.rootParams=void 0,this.root=void 0}swipeGestureChanged(){this.gesture&&this.gesture.enable(this.swipeGesture===!0)}rootChanged(){this.root!==void 0&&this.didLoad!==!1&&(this.useRouter||this.root!==void 0&&this.setRoot(this.root,this.rootParams))}componentWillLoad(){if(this.useRouter=document.querySelector("ion-router")!==null&&this.el.closest("[no-router]")===null,this.swipeGesture===void 0){const t=We(this);this.swipeGesture=G.getBoolean("swipeBackEnabled",t==="ios")}this.ionNavWillLoad.emit()}async componentDidLoad(){this.didLoad=!0,this.rootChanged(),this.gesture=(await Tt(()=>import("./swipe-back-BjopVS_2.js"),[])).createSwipeBackGesture(this.el,this.canStart.bind(this),this.onStart.bind(this),this.onMove.bind(this),this.onEnd.bind(this)),this.swipeGestureChanged()}connectedCallback(){this.destroyed=!1}disconnectedCallback(){for(const t of this.views)At(t.element,wl),t._destroy();this.gesture&&(this.gesture.destroy(),this.gesture=void 0),this.transInstr.length=0,this.views.length=0,this.destroyed=!0}push(t,n,o,r){return this.insert(-1,t,n,o,r)}insert(t,n,o,r,i){return this.insertPages(t,[{component:n,componentProps:o}],r,i)}insertPages(t,n,o,r){return this.queueTrns({insertStart:t,insertViews:n,opts:o},r)}pop(t,n){return this.removeIndex(-1,1,t,n)}popTo(t,n,o){const r={removeStart:-1,removeCount:-1,opts:n};return typeof t=="object"&&t.component?(r.removeView=t,r.removeStart=1):typeof t=="number"&&(r.removeStart=t+1),this.queueTrns(r,o)}popToRoot(t,n){return this.removeIndex(1,-1,t,n)}removeIndex(t,n=1,o,r){return this.queueTrns({removeStart:t,removeCount:n,opts:o},r)}setRoot(t,n,o,r){return this.setPages([{component:t,componentProps:n}],o,r)}setPages(t,n,o){return n!=null||(n={}),n.animated!==!0&&(n.animated=!1),this.queueTrns({insertStart:0,insertViews:t,removeStart:0,removeCount:-1,opts:n},o)}setRouteId(t,n,o,r){const i=this.getActiveSync();if(ff(i,t,n))return Promise.resolve({changed:!1,element:i.element});let s;const a=new Promise(u=>s=u);let l;const c={updateURL:!1,viewIsReady:u=>{let d;const f=new Promise(p=>d=p);return s({changed:!0,element:u,markVisible:async()=>{d(),await l}}),f}};if(o==="root")l=this.setRoot(t,n,c);else{const u=this.views.find(d=>ff(d,t,n));u?l=this.popTo(u,Object.assign(Object.assign({},c),{direction:"back",animationBuilder:r})):o==="forward"?l=this.push(t,n,Object.assign(Object.assign({},c),{animationBuilder:r})):o==="back"&&(l=this.setRoot(t,n,Object.assign(Object.assign({},c),{direction:"back",animated:!0,animationBuilder:r})))}return a}async getRouteId(){const t=this.getActiveSync();if(t)return{id:t.element.tagName,params:t.params,element:t.element}}async getActive(){return this.getActiveSync()}async getByIndex(t){return this.views[t]}async canGoBack(t){return this.canGoBackSync(t)}async getPrevious(t){return this.getPreviousSync(t)}async getLength(){return Promise.resolve(this.views.length)}getActiveSync(){return this.views[this.views.length-1]}canGoBackSync(t=this.getActiveSync()){return!!(t&&this.getPreviousSync(t))}getPreviousSync(t=this.getActiveSync()){if(!t)return;const n=this.views,o=n.indexOf(t);return o>0?n[o-1]:void 0}async queueTrns(t,n){var o,r;if(this.isTransitioning&&(!((o=t.opts)===null||o===void 0)&&o.skipIfBusy))return!1;const i=new Promise((s,a)=>{t.resolve=s,t.reject=a});if(t.done=n,t.opts&&t.opts.updateURL!==!1&&this.useRouter){const s=document.querySelector("ion-router");if(s){const a=await s.canTransition();if(a===!1)return!1;if(typeof a=="string")return s.push(a,t.opts.direction||"back"),!1}}return((r=t.insertViews)===null||r===void 0?void 0:r.length)===0&&(t.insertViews=void 0),this.transInstr.push(t),this.nextTrns(),i}success(t,n){if(this.destroyed){this.fireError("nav controller was destroyed",n);return}if(n.done&&n.done(t.hasCompleted,t.requiresTransition,t.enteringView,t.leavingView,t.direction),n.resolve(t.hasCompleted),n.opts.updateURL!==!1&&this.useRouter){const o=document.querySelector("ion-router");if(o){const r=t.direction==="back"?"back":"forward";o.navChanged(r)}}}failed(t,n){if(this.destroyed){this.fireError("nav controller was destroyed",n);return}this.transInstr.length=0,this.fireError(t,n)}fireError(t,n){n.done&&n.done(!1,!1,t),n.reject&&!this.destroyed?n.reject(t):n.resolve(!1)}nextTrns(){if(this.isTransitioning)return!1;const t=this.transInstr.shift();return t?(this.runTransition(t),!0):!1}async runTransition(t){try{this.ionNavWillChange.emit(),this.isTransitioning=!0,this.prepareTI(t);const n=this.getActiveSync(),o=this.getEnteringView(t,n);if(!n&&!o)throw new Error("no views in the stack to be removed");o&&o.state===Nm&&await o.init(this.el),this.postViewInit(o,n,t);const r=(t.enteringRequiresTransition||t.leavingRequiresTransition)&&o!==n;r&&t.opts&&n&&(t.opts.direction==="back"&&(t.opts.animationBuilder=t.opts.animationBuilder||(o==null?void 0:o.animationBuilder)),n.animationBuilder=t.opts.animationBuilder);let i;r?i=await this.transition(o,n,t):i={hasCompleted:!0,requiresTransition:!1},this.success(i,t),this.ionNavDidChange.emit()}catch(n){this.failed(n,t)}this.isTransitioning=!1,this.nextTrns()}prepareTI(t){var n,o,r;const i=this.views.length;if((n=t.opts)!==null&&n!==void 0||(t.opts={}),(o=(r=t.opts).delegate)!==null&&o!==void 0||(r.delegate=this.delegate),t.removeView!==void 0){Be(t.removeStart!==void 0,"removeView needs removeStart"),Be(t.removeCount!==void 0,"removeView needs removeCount");const l=this.views.indexOf(t.removeView);if(l<0)throw new Error("removeView was not found");t.removeStart+=l}t.removeStart!==void 0&&(t.removeStart<0&&(t.removeStart=i-1),t.removeCount<0&&(t.removeCount=i-t.removeStart),t.leavingRequiresTransition=t.removeCount>0&&t.removeStart+t.removeCount===i),t.insertViews&&((t.insertStart<0||t.insertStart>i)&&(t.insertStart=i),t.enteringRequiresTransition=t.insertStart===i);const s=t.insertViews;if(!s)return;Be(s.length>0,"length can not be zero");const a=Yw(s);if(a.length===0)throw new Error("invalid views to insert");for(const l of a){l.delegate=t.opts.delegate;const c=l.nav;if(c&&c!==this)throw new Error("inserted view was already inserted");if(l.state===Ki)throw new Error("inserted view was already destroyed")}t.insertViews=a}getEnteringView(t,n){const o=t.insertViews;if(o!==void 0)return o[o.length-1];const r=t.removeStart;if(r!==void 0){const i=this.views,s=r+t.removeCount;for(let a=i.length-1;a>=0;a--){const l=i[a];if((a<r||a>=s)&&l!==n)return l}}}postViewInit(t,n,o){var r,i,s;Be(n||t,"Both leavingView and enteringView are null"),Be(o.resolve,"resolve must be valid"),Be(o.reject,"reject must be valid");const a=o.opts,{insertViews:l,removeStart:c,removeCount:u}=o;let d;if(c!==void 0&&u!==void 0){Be(c>=0,"removeStart can not be negative"),Be(u>=0,"removeCount can not be negative"),d=[];for(let p=c;p<c+u;p++){const v=this.views[p];v!==void 0&&v!==t&&v!==n&&d.push(v)}(r=a.direction)!==null&&r!==void 0||(a.direction="back")}const f=this.views.length+((i=l==null?void 0:l.length)!==null&&i!==void 0?i:0)-(u!=null?u:0);if(Be(f>=0,"final balance can not be negative"),f===0)throw console.warn("You can't remove all the pages in the navigation stack. nav.pop() is probably called too many times.",this,this.el),new Error("navigation stack needs at least one root page");if(l){let p=o.insertStart;for(const v of l)this.insertViewAt(v,p),p++;o.enteringRequiresTransition&&((s=a.direction)!==null&&s!==void 0||(a.direction="forward"))}if(d&&d.length>0){for(const p of d)At(p.element,Pm),At(p.element,_m),At(p.element,wl);for(const p of d)this.destroyView(p)}}async transition(t,n,o){const r=o.opts,i=r.progressAnimation?d=>{d!==void 0&&!this.gestureOrAnimationInProgress?(this.gestureOrAnimationInProgress=!0,d.onFinish(()=>{this.gestureOrAnimationInProgress=!1},{oneTimeCallback:!0}),d.progressEnd(0,0,0)):this.sbAni=d}:void 0,s=We(this),a=t.element,l=n&&n.element,c=Object.assign(Object.assign({mode:s,showGoBack:this.canGoBackSync(t),baseEl:this.el,progressCallback:i,animated:this.animated&&G.getBoolean("animated",!0),enteringEl:a,leavingEl:l},r),{animationBuilder:r.animationBuilder||this.animation||G.get("navAnimation")}),{hasCompleted:u}=await Rm(c);return this.transitionFinish(u,t,n,r)}transitionFinish(t,n,o,r){const i=t?n:o;return i&&this.unmountInactiveViews(i),{hasCompleted:t,requiresTransition:!0,enteringView:n,leavingView:o,direction:r.direction}}insertViewAt(t,n){const o=this.views,r=o.indexOf(t);r>-1?(Be(t.nav===this,"view is not part of the nav"),o.splice(r,1),o.splice(n,0,t)):(Be(!t.nav,"nav is used"),t.nav=this,o.splice(n,0,t))}removeView(t){Be(t.state===jm||t.state===Ki,"view state should be loaded or destroyed");const n=this.views,o=n.indexOf(t);Be(o>-1,"view must be part of the stack"),o>=0&&n.splice(o,1)}destroyView(t){t._destroy(),this.removeView(t)}unmountInactiveViews(t){if(this.destroyed)return;const n=this.views,o=n.indexOf(t);for(let r=n.length-1;r>=0;r--){const i=n[r],s=i.element;s&&(r>o?(At(s,wl),this.destroyView(i)):r<o&&_a(s,!0))}}canStart(){return!this.gestureOrAnimationInProgress&&!!this.swipeGesture&&!this.isTransitioning&&this.transInstr.length===0&&this.canGoBackSync()}onStart(){this.gestureOrAnimationInProgress=!0,this.pop({direction:"back",progressAnimation:!0})}onMove(t){this.sbAni&&this.sbAni.progressStep(t)}onEnd(t,n,o){if(this.sbAni){this.sbAni.onFinish(()=>{this.gestureOrAnimationInProgress=!1},{oneTimeCallback:!0});let r=t?-.001:.001;t?r+=Qi([0,0],[.32,.72],[0,1],[1,1],n)[0]:(this.sbAni.easing("cubic-bezier(1, 0, 0.68, 0.28)"),r+=Qi([0,0],[1,0],[.68,.28],[1,1],n)[0]),this.sbAni.progressEnd(t?1:0,r,o)}else this.gestureOrAnimationInProgress=!1}render(){return M("slot",{key:"dfe98cb6604a2015a49f41beffebdd2da957dfff"})}get el(){return this}static get watchers(){return{swipeGesture:["swipeGestureChanged"],root:["rootChanged"]}}static get style(){return Gw}},[1,"ion-nav",{delegate:[16],swipeGesture:[1028,"swipe-gesture"],animated:[4],animation:[16],rootParams:[16],root:[1],push:[64],insert:[64],insertPages:[64],pop:[64],popTo:[64],popToRoot:[64],removeIndex:[64],setRoot:[64],setPages:[64],setRouteId:[64],getRouteId:[64],getActive:[64],getByIndex:[64],canGoBack:[64],getPrevious:[64],getLength:[64]},void 0,{swipeGesture:["swipeGestureChanged"],root:["rootChanged"]}]);function Jw(){if(typeof customElements>"u")return;["ion-nav"].forEach(t=>{switch(t){case"ion-nav":customElements.get(t)||customElements.define(t,Zw);break}})}const ex=Jw;/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const tx=":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{top:0;-webkit-padding-start:90px;padding-inline-start:90px;-webkit-padding-end:90px;padding-inline-end:90px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);position:absolute;width:100%;height:100%;-webkit-transform:translateZ(0);transform:translateZ(0);font-size:min(1.0625rem, 20.4px);font-weight:600;text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}:host{inset-inline-start:0}:host(.title-small){-webkit-padding-start:9px;padding-inline-start:9px;-webkit-padding-end:9px;padding-inline-end:9px;padding-top:6px;padding-bottom:16px;position:relative;font-size:min(0.8125rem, 23.4px);font-weight:normal}:host(.title-large){-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:2px;padding-bottom:4px;-webkit-transform-origin:left center;transform-origin:left center;position:static;-ms-flex-align:end;align-items:flex-end;min-width:100%;font-size:min(2.125rem, 61.2px);font-weight:700;text-align:start}:host(.title-large.title-rtl){-webkit-transform-origin:right center;transform-origin:right center}:host(.title-large.ion-cloned-element){--color:var(--ion-text-color, #000);font-family:var(--ion-font-family)}:host(.title-large) .toolbar-title{-webkit-transform-origin:inherit;transform-origin:inherit;width:auto}:host-context([dir=rtl]):host(.title-large) .toolbar-title,:host-context([dir=rtl]).title-large .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}@supports selector(:dir(rtl)){:host(.title-large:dir(rtl)) .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}}",nx=tx,ox=":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:0;padding-bottom:0;font-size:1.25rem;font-weight:500;letter-spacing:0.0125em}:host(.title-small){width:100%;height:100%;font-size:0.9375rem;font-weight:normal}",rx=ox,ix=Qe(class extends Ue{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionStyle=be(this,"ionStyle",7),this.color=void 0,this.size=void 0}sizeChanged(){this.emitStyle()}connectedCallback(){this.emitStyle()}emitStyle(){const t=this.getSize();this.ionStyle.emit({["title-".concat(t)]:!0})}getSize(){return this.size!==void 0?this.size:"default"}render(){const t=We(this),n=this.getSize();return M(ot,{key:"7293d2ecd6262feb0d8d769effbb208230baed89",class:_r(this.color,{[t]:!0,["title-".concat(n)]:!0,"title-rtl":document.dir==="rtl"})},M("div",{key:"086ec3a361ebdf6506846a8704b457cda3a6f897",class:"toolbar-title"},M("slot",{key:"59add7eb92b82d6832a8f0894f897c51fdf4f214"})))}get el(){return this}static get watchers(){return{size:["sizeChanged"]}}static get style(){return{ios:nx,md:rx}}},[33,"ion-title",{color:[513],size:[1]},void 0,{size:["sizeChanged"]}]);function sx(){if(typeof customElements>"u")return;["ion-title"].forEach(t=>{switch(t){case"ion-title":customElements.get(t)||customElements.define(t,ix);break}})}/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const lx=":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-color-step-50, var(--ion-background-color-step-50, #f7f7f7)));--color:var(--ion-toolbar-color, var(--ion-text-color, #000));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.2)))));--padding-top:3px;--padding-bottom:3px;--padding-start:4px;--padding-end:4px;--min-height:44px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:4;order:4;min-width:0}:host(.toolbar-segment) .toolbar-content{display:-ms-inline-flexbox;display:inline-flex}:host(.toolbar-searchbar) .toolbar-container{padding-top:0;padding-bottom:0}:host(.toolbar-searchbar) ::slotted(*){-ms-flex-item-align:start;align-self:start}:host(.toolbar-searchbar) ::slotted(ion-chip){margin-top:3px}::slotted(ion-buttons){min-height:38px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:3;order:3}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}:host(.toolbar-title-large) .toolbar-container{-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:start;align-items:flex-start}:host(.toolbar-title-large) .toolbar-content ion-title{-ms-flex:1;flex:1;-ms-flex-order:8;order:8;min-width:100%}",ax=lx,cx=":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-background-color, #fff));--color:var(--ion-toolbar-color, var(--ion-text-color, #424242));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, #c1c4cd))));--padding-top:0;--padding-bottom:0;--padding-start:0;--padding-end:0;--min-height:56px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:3;order:3;min-width:0;max-width:100%}::slotted(.buttons-first-slot){-webkit-margin-start:4px;margin-inline-start:4px}::slotted(.buttons-last-slot){-webkit-margin-end:4px;margin-inline-end:4px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:4;order:4}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}",ux=cx,dx=Qe(class extends Ue{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.childrenStyles=new Map,this.color=void 0}componentWillLoad(){const t=Array.from(this.el.querySelectorAll("ion-buttons")),n=t.find(i=>i.slot==="start");n&&n.classList.add("buttons-first-slot");const o=t.reverse(),r=o.find(i=>i.slot==="end")||o.find(i=>i.slot==="primary")||o.find(i=>i.slot==="secondary");r&&r.classList.add("buttons-last-slot")}childrenStyle(t){t.stopPropagation();const n=t.target.tagName,o=t.detail,r={},i=this.childrenStyles.get(n)||{};let s=!1;Object.keys(o).forEach(a=>{const l="toolbar-".concat(a),c=o[a];c!==i[l]&&(s=!0),c&&(r[l]=!0)}),s&&(this.childrenStyles.set(n,r),Ta(this))}render(){const t=We(this),n={};return this.childrenStyles.forEach(o=>{Object.assign(n,o)}),M(ot,{key:"462538a5ecd01baf3cde116c9f029aeda26c81be",class:Object.assign(Object.assign({},n),_r(this.color,{[t]:!0,"in-toolbar":Os("ion-toolbar",this.el)}))},M("div",{key:"c0b4415d3b2472de643a9be7cb3b13b3b628621b",class:"toolbar-background"}),M("div",{key:"0ccb8a2dbeaa28d8f9bed87629c0c097446690c2",class:"toolbar-container"},M("slot",{key:"3e726dac359e923df21d80301651f16063a3de20",name:"start"}),M("slot",{key:"cd799330b56a7f8833bc61bb2807aafb21846f71",name:"secondary"}),M("div",{key:"395282e6ac8c53576922dcdb5f08c25d34638c86",class:"toolbar-content"},M("slot",{key:"a437c60e4ba5aae65e55169ae82a6f379868ec1d"})),M("slot",{key:"711af9b9d321a7b31ede924c9bdcad767aa9a1ca",name:"primary"}),M("slot",{key:"ecc02edeaf80a837890bcb08d5096df1e22a0b9a",name:"end"})))}get el(){return this}static get style(){return{ios:ax,md:ux}}},[33,"ion-toolbar",{color:[513]},[[0,"ionStyle","childrenStyle"]]]);function fx(){if(typeof customElements>"u")return;["ion-toolbar"].forEach(t=>{switch(t){case"ion-toolbar":customElements.get(t)||customElements.define(t,dx);break}})}/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const hx=":host(.tab-hidden){display:none !important}",px=hx,mx=Qe(class extends Ue{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.loaded=!1,this.active=!1,this.delegate=void 0,this.tab=void 0,this.component=void 0}async componentWillLoad(){this.active&&await this.setActive()}async setActive(){await this.prepareLazyLoaded(),this.active=!0}changeActive(t){t&&this.prepareLazyLoaded()}prepareLazyLoaded(){if(!this.loaded&&this.component!=null){this.loaded=!0;try{return Yc(this.delegate,this.el,this.component,["ion-page"])}catch(t){console.error(t)}}return Promise.resolve(void 0)}render(){const{tab:t,active:n,component:o}=this;return M(ot,{key:"cb75d0877979b3b8df8f7e1952bfa9677da1eaa5",role:"tabpanel","aria-hidden":n?null:"true","aria-labelledby":"tab-button-".concat(t),class:{"ion-page":o===void 0,"tab-hidden":!n}},M("slot",{key:"37fbb7b7a6b03eb93b1dacd2dc1025b78eb2aa6b"}))}get el(){return this}static get watchers(){return{active:["changeActive"]}}static get style(){return px}},[1,"ion-tab",{active:[1028],delegate:[16],tab:[1],component:[1],setActive:[64]},void 0,{active:["changeActive"]}]);function gx(){if(typeof customElements>"u")return;["ion-tab"].forEach(t=>{switch(t){case"ion-tab":customElements.get(t)||customElements.define(t,mx);break}})}const vx=gx;/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const yx=sx;/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const bx=fx,wx="html.plt-mobile ion-app{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}html.plt-mobile ion-app [contenteditable]{-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text}ion-app.force-statusbar-padding{--ion-safe-area-top:20px}",xx=wx,kx=Qe(class extends Ue{constructor(){super(),this.__registerHost()}componentDidLoad(){Sx(async()=>{const t=Gt(window,"hybrid");if(G.getBoolean("_testing")||Tt(()=>import("./index9-BivZ7fF_.js"),[]).then(r=>r.startTapClick(G)),G.getBoolean("statusTap",t)&&Tt(()=>import("./status-tap-DIwVyzEH.js"),[]).then(r=>r.startStatusTap()),G.getBoolean("inputShims",Ex())){const r=Gt(window,"ios")?"ios":"android";Tt(()=>import("./input-shims-zuSN-ko7.js"),[]).then(i=>i.startInputShims(G,r))}const n=await Tt(()=>Promise.resolve().then(()=>lw),void 0),o=t||qi();G.getBoolean("hardwareBackButton",o)?n.startHardwareBackButton():(qi()&&km("experimentalCloseWatcher was set to `true`, but hardwareBackButton was set to `false`. Both config options must be `true` for the Close Watcher API to be used."),n.blockHardwareBackButton()),typeof window<"u"&&Tt(()=>import("./keyboard2-BrDiOKjm.js"),[]).then(r=>r.startKeyboardAssist(window)),Tt(()=>import("./focus-visible-supuXXMI.js"),[]).then(r=>this.focusVisible=r.startFocusVisible())})}async setFocus(t){this.focusVisible&&this.focusVisible.setFocus(t)}render(){const t=We(this);return M(ot,{key:"96715520fd05d6f0e6fa26a8ba78792cfccd4c0a",class:{[t]:!0,"ion-page":!0,"force-statusbar-padding":G.getBoolean("_forceStatusbarPadding")}})}get el(){return this}static get style(){return xx}},[0,"ion-app",{setFocus:[64]}]),Ex=()=>!!(Gt(window,"ios")&&Gt(window,"mobile")||Gt(window,"android")&&Gt(window,"mobileweb")),Sx=e=>{"requestIdleCallback"in window?window.requestIdleCallback(e):setTimeout(e,32)};function Cx(){if(typeof customElements>"u")return;["ion-app"].forEach(t=>{switch(t){case"ion-app":customElements.get(t)||customElements.define(t,kx);break}})}const Tx=Cx,$x=":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;z-index:0}",Px=$x,_x=Qe(class extends Ue{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionNavWillLoad=be(this,"ionNavWillLoad",7),this.ionNavWillChange=be(this,"ionNavWillChange",3),this.ionNavDidChange=be(this,"ionNavDidChange",3),this.lockController=Tw(),this.gestureOrAnimationInProgress=!1,this.mode=We(this),this.delegate=void 0,this.animated=!0,this.animation=void 0,this.swipeHandler=void 0}swipeHandlerChanged(){this.gesture&&this.gesture.enable(this.swipeHandler!==void 0)}async connectedCallback(){const t=()=>{this.gestureOrAnimationInProgress=!0,this.swipeHandler&&this.swipeHandler.onStart()};this.gesture=(await Tt(()=>import("./swipe-back-BjopVS_2.js"),[])).createSwipeBackGesture(this.el,()=>!this.gestureOrAnimationInProgress&&!!this.swipeHandler&&this.swipeHandler.canStart(),()=>t(),n=>{var o;return(o=this.ani)===null||o===void 0?void 0:o.progressStep(n)},(n,o,r)=>{if(this.ani){this.ani.onFinish(()=>{this.gestureOrAnimationInProgress=!1,this.swipeHandler&&this.swipeHandler.onEnd(n)},{oneTimeCallback:!0});let i=n?-.001:.001;n?i+=Qi([0,0],[.32,.72],[0,1],[1,1],o)[0]:(this.ani.easing("cubic-bezier(1, 0, 0.68, 0.28)"),i+=Qi([0,0],[1,0],[.68,.28],[1,1],o)[0]),this.ani.progressEnd(n?1:0,i,r)}else this.gestureOrAnimationInProgress=!1}),this.swipeHandlerChanged()}componentWillLoad(){this.ionNavWillLoad.emit()}disconnectedCallback(){this.gesture&&(this.gesture.destroy(),this.gesture=void 0)}async commit(t,n,o){const r=await this.lockController.lock();let i=!1;try{i=await this.transition(t,n,o)}catch(s){console.error(s)}return r(),i}async setRouteId(t,n,o,r){return{changed:await this.setRoot(t,n,{duration:o==="root"?0:void 0,direction:o==="back"?"back":"forward",animationBuilder:r}),element:this.activeEl}}async getRouteId(){const t=this.activeEl;return t?{id:t.tagName,element:t,params:this.activeParams}:void 0}async setRoot(t,n,o){if(this.activeComponent===t&&$m(n,this.activeParams))return!1;const r=this.activeEl,i=await Yc(this.delegate,this.el,t,["ion-page","ion-page-invisible"],n);return this.activeComponent=t,this.activeEl=i,this.activeParams=n,await this.commit(i,r,o),await aw(this.delegate,r),!0}async transition(t,n,o={}){if(n===t)return!1;this.ionNavWillChange.emit();const{el:r,mode:i}=this,s=this.animated&&G.getBoolean("animated",!0),a=o.animationBuilder||this.animation||G.get("navAnimation");return await Rm(Object.assign(Object.assign({mode:i,animated:s,enteringEl:t,leavingEl:n,baseEl:r,deepWait:$a(r),progressCallback:o.progressAnimation?l=>{l!==void 0&&!this.gestureOrAnimationInProgress?(this.gestureOrAnimationInProgress=!0,l.onFinish(()=>{this.gestureOrAnimationInProgress=!1,this.swipeHandler&&this.swipeHandler.onEnd(!1)},{oneTimeCallback:!0}),l.progressEnd(0,0,0)):this.ani=l}:void 0},o),{animationBuilder:a})),this.ionNavDidChange.emit(),!0}render(){return M("slot",{key:"a70341f58d19df55de1dad00e3464388e446aa2a"})}get el(){return this}static get watchers(){return{swipeHandler:["swipeHandlerChanged"]}}static get style(){return Px}},[1,"ion-router-outlet",{mode:[1025],delegate:[16],animated:[4],animation:[16],swipeHandler:[16],commit:[64],setRouteId:[64],getRouteId:[64]},void 0,{swipeHandler:["swipeHandlerChanged"]}]);function Ix(){if(typeof customElements>"u")return;["ion-router-outlet"].forEach(t=>{switch(t){case"ion-router-outlet":customElements.get(t)||customElements.define(t,_x);break}})}const Rx=Ix;/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const Lx=":host{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:auto;padding-right:var(--ion-safe-area-right);padding-bottom:var(--ion-safe-area-bottom, 0);padding-left:var(--ion-safe-area-left);border-top:var(--border);background:var(--background);color:var(--color);text-align:center;contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:10;-webkit-box-sizing:content-box !important;box-sizing:content-box !important}:host(.ion-color) ::slotted(ion-tab-button){--background-focused:var(--ion-color-shade);--color-selected:var(--ion-color-contrast)}:host(.ion-color) ::slotted(.tab-selected){color:var(--ion-color-contrast)}:host(.ion-color),:host(.ion-color) ::slotted(ion-tab-button){color:rgba(var(--ion-color-contrast-rgb), 0.7)}:host(.ion-color),:host(.ion-color) ::slotted(ion-tab-button){background:var(--ion-color-base)}:host(.ion-color) ::slotted(ion-tab-button.ion-focused),:host(.tab-bar-translucent) ::slotted(ion-tab-button.ion-focused){background:var(--background-focused)}:host(.tab-bar-translucent) ::slotted(ion-tab-button){background:transparent}:host([slot=top]){padding-top:var(--ion-safe-area-top, 0);padding-bottom:0;border-top:0;border-bottom:var(--border)}:host(.tab-bar-hidden){display:none !important}:host{--background:var(--ion-tab-bar-background, var(--ion-color-step-50, var(--ion-background-color-step-50, #f7f7f7)));--background-focused:var(--ion-tab-bar-background-focused, #e0e0e0);--border:0.55px solid var(--ion-tab-bar-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.2)))));--color:var(--ion-tab-bar-color, var(--ion-color-step-600, var(--ion-text-color-step-400, #666666)));--color-selected:var(--ion-tab-bar-color-selected, var(--ion-color-primary, #0054e9));height:50px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){:host(.tab-bar-translucent){--background:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(210%) blur(20px);backdrop-filter:saturate(210%) blur(20px)}:host(.ion-color.tab-bar-translucent){background:rgba(var(--ion-color-base-rgb), 0.8)}:host(.tab-bar-translucent) ::slotted(ion-tab-button.ion-focused){background:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.6)}}",Ox=Lx,Ax=":host{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:auto;padding-right:var(--ion-safe-area-right);padding-bottom:var(--ion-safe-area-bottom, 0);padding-left:var(--ion-safe-area-left);border-top:var(--border);background:var(--background);color:var(--color);text-align:center;contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:10;-webkit-box-sizing:content-box !important;box-sizing:content-box !important}:host(.ion-color) ::slotted(ion-tab-button){--background-focused:var(--ion-color-shade);--color-selected:var(--ion-color-contrast)}:host(.ion-color) ::slotted(.tab-selected){color:var(--ion-color-contrast)}:host(.ion-color),:host(.ion-color) ::slotted(ion-tab-button){color:rgba(var(--ion-color-contrast-rgb), 0.7)}:host(.ion-color),:host(.ion-color) ::slotted(ion-tab-button){background:var(--ion-color-base)}:host(.ion-color) ::slotted(ion-tab-button.ion-focused),:host(.tab-bar-translucent) ::slotted(ion-tab-button.ion-focused){background:var(--background-focused)}:host(.tab-bar-translucent) ::slotted(ion-tab-button){background:transparent}:host([slot=top]){padding-top:var(--ion-safe-area-top, 0);padding-bottom:0;border-top:0;border-bottom:var(--border)}:host(.tab-bar-hidden){display:none !important}:host{--background:var(--ion-tab-bar-background, var(--ion-background-color, #fff));--background-focused:var(--ion-tab-bar-background-focused, #e0e0e0);--border:1px solid var(--ion-tab-bar-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.07)))));--color:var(--ion-tab-bar-color, var(--ion-color-step-650, var(--ion-text-color-step-350, #595959)));--color-selected:var(--ion-tab-bar-color-selected, var(--ion-color-primary, #0054e9));height:56px}",zx=Ax,Bx=Qe(class extends Ue{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionTabBarChanged=be(this,"ionTabBarChanged",7),this.ionTabBarLoaded=be(this,"ionTabBarLoaded",7),this.keyboardCtrl=null,this.keyboardVisible=!1,this.color=void 0,this.selectedTab=void 0,this.translucent=!1}selectedTabChanged(){this.selectedTab!==void 0&&this.ionTabBarChanged.emit({tab:this.selectedTab})}componentWillLoad(){this.selectedTabChanged()}async connectedCallback(){this.keyboardCtrl=await _w(async(t,n)=>{t===!1&&n!==void 0&&await n,this.keyboardVisible=t})}disconnectedCallback(){this.keyboardCtrl&&this.keyboardCtrl.destroy()}componentDidLoad(){this.ionTabBarLoaded.emit()}render(){const{color:t,translucent:n,keyboardVisible:o}=this,r=We(this),i=o&&this.el.getAttribute("slot")!=="top";return M(ot,{key:"a87fd2ea5df053705a37ea7ffec043e75c4a9907",role:"tablist","aria-hidden":i?"true":null,class:_r(t,{[r]:!0,"tab-bar-translucent":n,"tab-bar-hidden":i})},M("slot",{key:"81a6223299b6cab29d7ddced590e9152e2b3ded0"}))}get el(){return this}static get watchers(){return{selectedTab:["selectedTabChanged"]}}static get style(){return{ios:Ox,md:zx}}},[33,"ion-tab-bar",{color:[513],selectedTab:[1,"selected-tab"],translucent:[4],keyboardVisible:[32]},void 0,{selectedTab:["selectedTabChanged"]}]);function Dx(){if(typeof customElements>"u")return;["ion-tab-bar"].forEach(t=>{switch(t){case"ion-tab-bar":customElements.get(t)||customElements.define(t,Bx);break}})}const Mx=Dx;/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const Nx=':host{--ripple-color:var(--color-selected);--background-focused-opacity:1;-ms-flex:1;flex:1;-ms-flex-direction:column;flex-direction:column;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;height:100%;outline:none;background:var(--background);color:var(--color)}.button-native{border-radius:inherit;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;height:100%;border:0;outline:none;background:transparent;text-decoration:none;cursor:pointer;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-user-drag:none}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:"";opacity:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:inherit;flex-flow:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;height:100%;z-index:1}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}@media (any-hover: hover){a:hover{color:var(--color-selected)}}:host(.tab-selected){color:var(--color-selected)}:host(.tab-hidden){display:none !important}:host(.tab-disabled){pointer-events:none;opacity:0.4}::slotted(ion-label),::slotted(ion-icon){display:block;-ms-flex-item-align:center;align-self:center;max-width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box}::slotted(ion-label){-ms-flex-order:0;order:0}::slotted(ion-icon){-ms-flex-order:-1;order:-1;height:1em}:host(.tab-has-label-only) ::slotted(ion-label){white-space:normal}::slotted(ion-badge){-webkit-box-sizing:border-box;box-sizing:border-box;position:absolute;z-index:1}:host(.tab-layout-icon-start){-ms-flex-direction:row;flex-direction:row}:host(.tab-layout-icon-end){-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.tab-layout-icon-bottom){-ms-flex-direction:column-reverse;flex-direction:column-reverse}:host(.tab-layout-icon-hide) ::slotted(ion-icon){display:none}:host(.tab-layout-label-hide) ::slotted(ion-label){display:none}ion-ripple-effect{color:var(--ripple-color)}:host{--padding-top:0;--padding-end:2px;--padding-bottom:0;--padding-start:2px;max-width:240px;font-size:10px}::slotted(ion-badge){-webkit-padding-start:6px;padding-inline-start:6px;-webkit-padding-end:6px;padding-inline-end:6px;padding-top:1px;padding-bottom:1px;top:4px;height:auto;font-size:12px;line-height:16px}::slotted(ion-badge){inset-inline-start:calc(50% + 6px)}::slotted(ion-icon){margin-top:2px;margin-bottom:2px;font-size:24px}::slotted(ion-icon::before){vertical-align:top}::slotted(ion-label){margin-top:0;margin-bottom:1px;min-height:11px;font-weight:500}:host(.tab-has-label-only) ::slotted(ion-label){margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px;font-size:12px;font-size:14px;line-height:1.1}:host(.tab-layout-icon-end) ::slotted(ion-label),:host(.tab-layout-icon-start) ::slotted(ion-label),:host(.tab-layout-icon-hide) ::slotted(ion-label){margin-top:2px;margin-bottom:2px;font-size:14px;line-height:1.1}:host(.tab-layout-icon-end) ::slotted(ion-icon),:host(.tab-layout-icon-start) ::slotted(ion-icon){min-width:24px;height:26px;margin-top:2px;margin-bottom:1px;font-size:24px}:host(.tab-layout-icon-bottom) ::slotted(ion-badge){inset-inline-start:calc(50% + 12px)}:host(.tab-layout-icon-bottom) ::slotted(ion-icon){margin-top:0;margin-bottom:1px}:host(.tab-layout-icon-bottom) ::slotted(ion-label){margin-top:4px}:host(.tab-layout-icon-start) ::slotted(ion-badge),:host(.tab-layout-icon-end) ::slotted(ion-badge){top:10px}:host(.tab-layout-icon-start) ::slotted(ion-badge),:host(.tab-layout-icon-end) ::slotted(ion-badge){inset-inline-start:calc(50% + 35px)}:host(.tab-layout-icon-hide) ::slotted(ion-badge),:host(.tab-has-label-only) ::slotted(ion-badge){top:10px}:host(.tab-layout-icon-hide) ::slotted(ion-badge),:host(.tab-has-label-only) ::slotted(ion-badge){inset-inline-start:calc(50% + 30px)}:host(.tab-layout-label-hide) ::slotted(ion-badge),:host(.tab-has-icon-only) ::slotted(ion-badge){top:10px}:host(.tab-layout-label-hide) ::slotted(ion-icon){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}:host(.tab-layout-label-hide) ::slotted(ion-icon),:host(.tab-has-icon-only) ::slotted(ion-icon){font-size:30px}',jx=Nx,Hx=':host{--ripple-color:var(--color-selected);--background-focused-opacity:1;-ms-flex:1;flex:1;-ms-flex-direction:column;flex-direction:column;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;height:100%;outline:none;background:var(--background);color:var(--color)}.button-native{border-radius:inherit;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;height:100%;border:0;outline:none;background:transparent;text-decoration:none;cursor:pointer;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-user-drag:none}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:"";opacity:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:inherit;flex-flow:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;height:100%;z-index:1}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}@media (any-hover: hover){a:hover{color:var(--color-selected)}}:host(.tab-selected){color:var(--color-selected)}:host(.tab-hidden){display:none !important}:host(.tab-disabled){pointer-events:none;opacity:0.4}::slotted(ion-label),::slotted(ion-icon){display:block;-ms-flex-item-align:center;align-self:center;max-width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box}::slotted(ion-label){-ms-flex-order:0;order:0}::slotted(ion-icon){-ms-flex-order:-1;order:-1;height:1em}:host(.tab-has-label-only) ::slotted(ion-label){white-space:normal}::slotted(ion-badge){-webkit-box-sizing:border-box;box-sizing:border-box;position:absolute;z-index:1}:host(.tab-layout-icon-start){-ms-flex-direction:row;flex-direction:row}:host(.tab-layout-icon-end){-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.tab-layout-icon-bottom){-ms-flex-direction:column-reverse;flex-direction:column-reverse}:host(.tab-layout-icon-hide) ::slotted(ion-icon){display:none}:host(.tab-layout-label-hide) ::slotted(ion-label){display:none}ion-ripple-effect{color:var(--ripple-color)}:host{--padding-top:0;--padding-end:12px;--padding-bottom:0;--padding-start:12px;max-width:168px;font-size:12px;font-weight:normal;letter-spacing:0.03em}::slotted(ion-label){margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px;text-transform:none}::slotted(ion-icon){margin-left:0;margin-right:0;margin-top:16px;margin-bottom:16px;-webkit-transform-origin:center center;transform-origin:center center;font-size:22px}:host-context([dir=rtl]) ::slotted(ion-icon){-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}[dir=rtl] ::slotted(ion-icon){-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}@supports selector(:dir(rtl)){::slotted(ion-icon):dir(rtl){-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}}::slotted(ion-badge){border-radius:8px;-webkit-padding-start:2px;padding-inline-start:2px;-webkit-padding-end:2px;padding-inline-end:2px;padding-top:3px;padding-bottom:2px;top:8px;min-width:12px;font-size:8px;font-weight:normal}::slotted(ion-badge){inset-inline-start:calc(50% + 6px)}::slotted(ion-badge:empty){display:block;min-width:8px;height:8px}:host(.tab-layout-icon-top) ::slotted(ion-icon){margin-top:6px;margin-bottom:2px}:host(.tab-layout-icon-top) ::slotted(ion-label){margin-top:0;margin-bottom:6px}:host(.tab-layout-icon-bottom) ::slotted(ion-badge){top:8px}:host(.tab-layout-icon-bottom) ::slotted(ion-badge){inset-inline-start:70%}:host(.tab-layout-icon-bottom) ::slotted(ion-icon){margin-top:0;margin-bottom:6px}:host(.tab-layout-icon-bottom) ::slotted(ion-label){margin-top:6px;margin-bottom:0}:host(.tab-layout-icon-start) ::slotted(ion-badge),:host(.tab-layout-icon-end) ::slotted(ion-badge){top:16px}:host(.tab-layout-icon-start) ::slotted(ion-badge),:host(.tab-layout-icon-end) ::slotted(ion-badge){inset-inline-start:80%}:host(.tab-layout-icon-start) ::slotted(ion-icon){-webkit-margin-end:6px;margin-inline-end:6px}:host(.tab-layout-icon-end) ::slotted(ion-icon){-webkit-margin-start:6px;margin-inline-start:6px}:host(.tab-layout-icon-hide) ::slotted(ion-badge),:host(.tab-has-label-only) ::slotted(ion-badge){top:16px}:host(.tab-layout-icon-hide) ::slotted(ion-badge),:host(.tab-has-label-only) ::slotted(ion-badge){inset-inline-start:70%}:host(.tab-layout-icon-hide) ::slotted(ion-label),:host(.tab-has-label-only) ::slotted(ion-label){margin-top:0;margin-bottom:0}:host(.tab-layout-label-hide) ::slotted(ion-badge),:host(.tab-has-icon-only) ::slotted(ion-badge){top:16px}:host(.tab-layout-label-hide) ::slotted(ion-icon),:host(.tab-has-icon-only) ::slotted(ion-icon){margin-top:0;margin-bottom:0;font-size:24px}',Vx=Hx,Fx=Qe(class extends Ue{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionTabButtonClick=be(this,"ionTabButtonClick",7),this.inheritedAttributes={},this.onKeyUp=t=>{(t.key==="Enter"||t.key===" ")&&this.selectTab(t)},this.onClick=t=>{this.selectTab(t)},this.disabled=!1,this.download=void 0,this.href=void 0,this.rel=void 0,this.layout=void 0,this.selected=!1,this.tab=void 0,this.target=void 0}onTabBarChanged(t){const n=t.target,o=this.el.parentElement;(t.composedPath().includes(o)||n!=null&&n.contains(this.el))&&(this.selected=this.tab===t.detail.tab)}componentWillLoad(){this.inheritedAttributes=Object.assign({},Em(this.el,["aria-label"])),this.layout===void 0&&(this.layout=G.get("tabButtonLayout","icon-top"))}selectTab(t){this.tab!==void 0&&(this.disabled||this.ionTabButtonClick.emit({tab:this.tab,href:this.href,selected:this.selected}),t.preventDefault())}get hasLabel(){return!!this.el.querySelector("ion-label")}get hasIcon(){return!!this.el.querySelector("ion-icon")}render(){const{disabled:t,hasIcon:n,hasLabel:o,href:r,rel:i,target:s,layout:a,selected:l,tab:c,inheritedAttributes:u}=this,d=We(this),f={download:this.download,href:r,rel:i,target:s};return M(ot,{key:"5976c45943ea7ea8e7c1a85fc9996de421439f08",onClick:this.onClick,onKeyup:this.onKeyUp,id:c!==void 0?"tab-button-".concat(c):null,class:{[d]:!0,"tab-selected":l,"tab-disabled":t,"tab-has-label":o,"tab-has-icon":n,"tab-has-label-only":o&&!n,"tab-has-icon-only":n&&!o,["tab-layout-".concat(a)]:!0,"ion-activatable":!0,"ion-selectable":!0,"ion-focusable":!0}},M("a",Object.assign({key:"1db09d861b67ff292018fb4b0dc7b85bd4677eb8"},f,{class:"button-native",part:"native",role:"tab","aria-selected":l?"true":null,"aria-disabled":t?"true":null,tabindex:t?"-1":void 0},u),M("span",{key:"4381eafcb27e8c7bb0d86d4f115ceb0caf03b9b4",class:"button-inner"},M("slot",{key:"1981135f6fbb88376c1bd923c55c70fe8b5c5159"})),d==="md"&&M("ion-ripple-effect",{key:"0509bc7155d055d1ed710600e9cf4df135881491",type:"unbounded"})))}get el(){return this}static get style(){return{ios:jx,md:Vx}}},[33,"ion-tab-button",{disabled:[4],download:[1],href:[1],rel:[1],layout:[1025],selected:[1028],tab:[1],target:[1]},[[8,"ionTabBarChanged","onTabBarChanged"]]]);function Wx(){if(typeof customElements>"u")return;["ion-tab-button","ion-ripple-effect"].forEach(t=>{switch(t){case"ion-tab-button":customElements.get(t)||customElements.define(t,Fx);break;case"ion-ripple-effect":customElements.get(t)||mw();break}})}const Ux=Wx;/*!
 * (C) Ionic http://ionicframework.com - MIT License
 */const Qx=":host{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:100%;height:100%;contain:layout size style;z-index:0}.tabs-inner{position:relative;-ms-flex:1;flex:1;contain:layout size style}",qx=Qx,Kx=Qe(class extends Ue{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionNavWillLoad=be(this,"ionNavWillLoad",7),this.ionTabsWillChange=be(this,"ionTabsWillChange",3),this.ionTabsDidChange=be(this,"ionTabsDidChange",3),this.transitioning=!1,this.onTabClicked=t=>{const{href:n,tab:o}=t.detail;if(this.useRouter&&n!==void 0){const r=document.querySelector("ion-router");r&&r.push(n)}else this.select(o)},this.selectedTab=void 0,this.useRouter=!1}async componentWillLoad(){if(this.useRouter||(this.useRouter=(!!this.el.querySelector("ion-router-outlet")||!!document.querySelector("ion-router"))&&!this.el.closest("[no-router]")),!this.useRouter){const t=this.tabs;t.length>0&&await this.select(t[0])}this.ionNavWillLoad.emit()}componentWillRender(){const t=this.el.querySelector("ion-tab-bar");if(t){const n=this.selectedTab?this.selectedTab.tab:void 0;t.selectedTab=n}}async select(t){const n=xl(this.tabs,t);return this.shouldSwitch(n)?(await this.setActive(n),await this.notifyRouter(),this.tabSwitch(),!0):!1}async getTab(t){return xl(this.tabs,t)}getSelected(){return Promise.resolve(this.selectedTab?this.selectedTab.tab:void 0)}async setRouteId(t){const n=xl(this.tabs,t);return this.shouldSwitch(n)?(await this.setActive(n),{changed:!0,element:this.selectedTab,markVisible:()=>this.tabSwitch()}):{changed:!1,element:this.selectedTab}}async getRouteId(){var t;const n=(t=this.selectedTab)===null||t===void 0?void 0:t.tab;return n!==void 0?{id:n,element:this.selectedTab}:void 0}setActive(t){return this.transitioning?Promise.reject("transitioning already happening"):(this.transitioning=!0,this.leavingTab=this.selectedTab,this.selectedTab=t,this.ionTabsWillChange.emit({tab:t.tab}),t.active=!0,Promise.resolve())}tabSwitch(){const t=this.selectedTab,n=this.leavingTab;this.leavingTab=void 0,this.transitioning=!1,t&&n!==t&&(n&&(n.active=!1),this.ionTabsDidChange.emit({tab:t.tab}))}notifyRouter(){if(this.useRouter){const t=document.querySelector("ion-router");if(t)return t.navChanged("forward")}return Promise.resolve(!1)}shouldSwitch(t){const n=this.selectedTab;return t!==void 0&&t!==n&&!this.transitioning}get tabs(){return Array.from(this.el.querySelectorAll("ion-tab"))}render(){return M(ot,{key:"e01ccf6bfaccad094515be50e407399c733fc226",onIonTabButtonClick:this.onTabClicked},M("slot",{key:"38d2d01dbfd8a08f01e6f0e27274b21d75424e37",name:"top"}),M("div",{key:"7e894f0f423e2d43e1c68daff5f9f6c442fad237",class:"tabs-inner"},M("slot",{key:"df16be529a0370a26d0adf850530b31607507c23"})),M("slot",{key:"44642e1cb24c3281c43db75fd69a32fe0defe40a",name:"bottom"}))}get el(){return this}static get style(){return qx}},[1,"ion-tabs",{useRouter:[1028,"use-router"],selectedTab:[32],select:[64],getTab:[64],getSelected:[64],setRouteId:[64],getRouteId:[64]}]),xl=(e,t)=>{const n=typeof t=="string"?e.find(o=>o.tab===t):t;return n||console.error('tab with id: "'.concat(n,'" does not exist')),n};function Yx(){if(typeof customElements>"u")return;["ion-tabs"].forEach(t=>{switch(t){case"ion-tabs":customElements.get(t)||customElements.define(t,Kx);break}})}const Xx=Yx;let kl;const Gx=()=>{if(typeof window>"u")return new Map;if(!kl){const e=window;e.Ionicons=e.Ionicons||{},kl=e.Ionicons.map=e.Ionicons.map||new Map}return kl},Zx=e=>{let t=El(e.src);return t||(t=Hm(e.name,e.icon,e.mode,e.ios,e.md),t?Jx(t,e):e.icon&&(t=El(e.icon),t||(t=El(e.icon[e.mode]),t))?t:null)},Jx=(e,t)=>{const n=Gx().get(e);if(n)return n;try{return Rb("svg/".concat(e,".svg"))}catch(o){console.warn('[Ionicons Warning]: Could not load icon with name "'.concat(e,'". Ensure that the icon is registered using addIcons or that the icon SVG data is passed directly to the icon component.'),t)}},Hm=(e,t,n,o,r)=>(n=(n&&ni(n))==="ios"?"ios":"md",o&&n==="ios"?e=ni(o):r&&n==="md"?e=ni(r):(!e&&t&&!Vm(t)&&(e=t),Yi(e)&&(e=ni(e))),!Yi(e)||e.trim()===""||e.replace(/[a-z]|-|\d/gi,"")!==""?null:e),El=e=>Yi(e)&&(e=e.trim(),Vm(e))?e:null,Vm=e=>e.length>0&&/(\/|\.)/.test(e),Yi=e=>typeof e=="string",ni=e=>e.toLowerCase(),ek=(e,t=[])=>{const n={};return t.forEach(o=>{e.hasAttribute(o)&&(e.getAttribute(o)!==null&&(n[o]=e.getAttribute(o)),e.removeAttribute(o))}),n},tk=e=>e&&e.dir!==""?e.dir.toLowerCase()==="rtl":(document==null?void 0:document.dir.toLowerCase())==="rtl",nk=e=>{const t=document.createElement("div");t.innerHTML=e;for(let o=t.childNodes.length-1;o>=0;o--)t.childNodes[o].nodeName.toLowerCase()!=="svg"&&t.removeChild(t.childNodes[o]);const n=t.firstElementChild;if(n&&n.nodeName.toLowerCase()==="svg"){const o=n.getAttribute("class")||"";if(n.setAttribute("class",(o+" s-ion-icon").trim()),Fm(n))return t.innerHTML}return""},Fm=e=>{if(e.nodeType===1){if(e.nodeName.toLowerCase()==="script")return!1;for(let t=0;t<e.attributes.length;t++){const n=e.attributes[t].name;if(Yi(n)&&n.toLowerCase().indexOf("on")===0)return!1}for(let t=0;t<e.childNodes.length;t++)if(!Fm(e.childNodes[t]))return!1}return!0},ok=e=>e.startsWith("data:image/svg+xml"),rk=e=>e.indexOf(";utf8,")!==-1,En=new Map,pf=new Map;let Sl;const ik=(e,t)=>{let n=pf.get(e);if(!n)if(typeof fetch<"u"&&typeof document<"u")if(ok(e)&&rk(e)){Sl||(Sl=new DOMParser);const r=Sl.parseFromString(e,"text/html").querySelector("svg");return r&&En.set(e,r.outerHTML),Promise.resolve()}else n=fetch(e).then(o=>{if(o.ok)return o.text().then(r=>{r&&t!==!1&&(r=nk(r)),En.set(e,r||"")});En.set(e,"")}),pf.set(e,n);else return En.set(e,""),Promise.resolve();return n},sk=":host{display:inline-block;width:1em;height:1em;contain:strict;fill:currentColor;-webkit-box-sizing:content-box !important;box-sizing:content-box !important}:host .ionicon{stroke:currentColor}.ionicon-fill-none{fill:none}.ionicon-stroke-width{stroke-width:32px;stroke-width:var(--ionicon-stroke-width, 32px)}.icon-inner,.ionicon,svg{display:block;height:100%;width:100%}@supports (background: -webkit-named-image(i)){:host(.icon-rtl) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}}@supports not selector(:dir(rtl)) and selector(:host-context([dir='rtl'])){:host(.icon-rtl) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}}:host(.flip-rtl):host-context([dir='rtl']) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}@supports selector(:dir(rtl)){:host(.flip-rtl:dir(rtl)) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}:host(.flip-rtl:dir(ltr)) .icon-inner{-webkit-transform:scaleX(1);transform:scaleX(1)}}:host(.icon-small){font-size:1.125rem !important}:host(.icon-large){font-size:2rem !important}:host(.ion-color){color:var(--ion-color-base) !important}:host(.ion-color-primary){--ion-color-base:var(--ion-color-primary, #3880ff)}:host(.ion-color-secondary){--ion-color-base:var(--ion-color-secondary, #0cd1e8)}:host(.ion-color-tertiary){--ion-color-base:var(--ion-color-tertiary, #f4a942)}:host(.ion-color-success){--ion-color-base:var(--ion-color-success, #10dc60)}:host(.ion-color-warning){--ion-color-base:var(--ion-color-warning, #ffce00)}:host(.ion-color-danger){--ion-color-base:var(--ion-color-danger, #f14141)}:host(.ion-color-light){--ion-color-base:var(--ion-color-light, #f4f5f8)}:host(.ion-color-medium){--ion-color-base:var(--ion-color-medium, #989aa2)}:host(.ion-color-dark){--ion-color-base:var(--ion-color-dark, #222428)}",lk=Qe(class extends Ue{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.iconName=null,this.inheritedAttributes={},this.didLoadIcon=!1,this.svgContent=void 0,this.isVisible=!1,this.mode=ak(),this.color=void 0,this.ios=void 0,this.md=void 0,this.flipRtl=void 0,this.name=void 0,this.src=void 0,this.icon=void 0,this.size=void 0,this.lazy=!1,this.sanitize=!0}componentWillLoad(){this.inheritedAttributes=ek(this.el,["aria-label"])}connectedCallback(){this.waitUntilVisible(this.el,"50px",()=>{this.isVisible=!0,this.loadIcon()})}componentDidLoad(){this.didLoadIcon||this.loadIcon()}disconnectedCallback(){this.io&&(this.io.disconnect(),this.io=void 0)}waitUntilVisible(t,n,o){if(this.lazy&&typeof window<"u"&&window.IntersectionObserver){const r=this.io=new window.IntersectionObserver(i=>{i[0].isIntersecting&&(r.disconnect(),this.io=void 0,o())},{rootMargin:n});r.observe(t)}else o()}loadIcon(){if(this.isVisible){const t=Zx(this);t&&(En.has(t)?this.svgContent=En.get(t):ik(t,this.sanitize).then(()=>this.svgContent=En.get(t)),this.didLoadIcon=!0)}this.iconName=Hm(this.name,this.icon,this.mode,this.ios,this.md)}render(){const{flipRtl:t,iconName:n,inheritedAttributes:o,el:r}=this,i=this.mode||"md",s=n?(n.includes("arrow")||n.includes("chevron"))&&t!==!1:!1,a=t||s;return M(ot,Object.assign({role:"img",class:Object.assign(Object.assign({[i]:!0},ck(this.color)),{["icon-".concat(this.size)]:!!this.size,"flip-rtl":a,"icon-rtl":a&&tk(r)})},o),this.svgContent?M("div",{class:"icon-inner",innerHTML:this.svgContent}):M("div",{class:"icon-inner"}))}static get assetsDirs(){return["svg"]}get el(){return this}static get watchers(){return{name:["loadIcon"],src:["loadIcon"],icon:["loadIcon"],ios:["loadIcon"],md:["loadIcon"]}}static get style(){return sk}},[1,"ion-icon",{mode:[1025],color:[1],ios:[1],md:[1],flipRtl:[4,"flip-rtl"],name:[513],src:[1],icon:[8],size:[1],lazy:[4],sanitize:[4],svgContent:[32],isVisible:[32]}]),ak=()=>typeof document<"u"&&document.documentElement.getAttribute("mode")||"md",ck=e=>e?{"ion-color":!0,["ion-color-".concat(e)]:!0}:null;function uk(){if(typeof customElements>"u")return;["ion-icon"].forEach(t=>{switch(t){case"ion-icon":customElements.get(t)||customElements.define(t,lk);break}})}const dk=uk,Zc=b.createContext({onIonViewWillEnter:()=>{},ionViewWillEnter:()=>{},onIonViewDidEnter:()=>{},ionViewDidEnter:()=>{},onIonViewWillLeave:()=>{},ionViewWillLeave:()=>{},onIonViewDidLeave:()=>{},ionViewDidLeave:()=>{},cleanupIonViewWillEnter:()=>{},cleanupIonViewDidEnter:()=>{},cleanupIonViewWillLeave:()=>{},cleanupIonViewDidLeave:()=>{}}),fk=class{constructor(){this.ionViewWillEnterCallbacks=[],this.ionViewDidEnterCallbacks=[],this.ionViewWillLeaveCallbacks=[],this.ionViewDidLeaveCallbacks=[],this.ionViewWillEnterDestructorCallbacks=[],this.ionViewDidEnterDestructorCallbacks=[],this.ionViewWillLeaveDestructorCallbacks=[],this.ionViewDidLeaveDestructorCallbacks=[]}onIonViewWillEnter(e){if(e.id){const t=this.ionViewWillEnterCallbacks.findIndex(n=>n.id===e.id);t>-1?this.ionViewWillEnterCallbacks[t]=e:this.ionViewWillEnterCallbacks.push(e)}else this.ionViewWillEnterCallbacks.push(e)}teardownCallback(e,t){const n=t.filter(o=>o.id===e.id);n.length!==0&&(n.forEach(o=>{o&&typeof o.destructor=="function"&&o.destructor()}),t=t.filter(o=>o.id!==e.id))}cleanupIonViewWillEnter(e){this.teardownCallback(e,this.ionViewWillEnterDestructorCallbacks)}cleanupIonViewDidEnter(e){this.teardownCallback(e,this.ionViewDidEnterDestructorCallbacks)}cleanupIonViewWillLeave(e){this.teardownCallback(e,this.ionViewWillLeaveDestructorCallbacks)}cleanupIonViewDidLeave(e){this.teardownCallback(e,this.ionViewDidLeaveDestructorCallbacks)}ionViewWillEnter(){this.ionViewWillEnterCallbacks.forEach(e=>{const t=e();e.id&&this.ionViewWillEnterDestructorCallbacks.push({id:e.id,destructor:t})})}onIonViewDidEnter(e){if(e.id){const t=this.ionViewDidEnterCallbacks.findIndex(n=>n.id===e.id);t>-1?this.ionViewDidEnterCallbacks[t]=e:this.ionViewDidEnterCallbacks.push(e)}else this.ionViewDidEnterCallbacks.push(e)}ionViewDidEnter(){this.ionViewDidEnterCallbacks.forEach(e=>{const t=e();e.id&&this.ionViewDidEnterDestructorCallbacks.push({id:e.id,destructor:t})})}onIonViewWillLeave(e){if(e.id){const t=this.ionViewWillLeaveCallbacks.findIndex(n=>n.id===e.id);t>-1?this.ionViewWillLeaveCallbacks[t]=e:this.ionViewWillLeaveCallbacks.push(e)}else this.ionViewWillLeaveCallbacks.push(e)}ionViewWillLeave(){this.ionViewWillLeaveCallbacks.forEach(e=>{const t=e();e.id&&this.ionViewWillLeaveDestructorCallbacks.push({id:e.id,destructor:t})})}onIonViewDidLeave(e){if(e.id){const t=this.ionViewDidLeaveCallbacks.findIndex(n=>n.id===e.id);t>-1?this.ionViewDidLeaveCallbacks[t]=e:this.ionViewDidLeaveCallbacks.push(e)}else this.ionViewDidLeaveCallbacks.push(e)}ionViewDidLeave(){this.ionViewDidLeaveCallbacks.forEach(e=>{const t=e();e.id&&this.ionViewDidLeaveDestructorCallbacks.push({id:e.id,destructor:t})}),this.componentCanBeDestroyed()}onComponentCanBeDestroyed(e){this.componentCanBeDestroyedCallback=e}componentCanBeDestroyed(){this.componentCanBeDestroyedCallback&&this.componentCanBeDestroyedCallback()}},Ft=b.createContext({getIonRedirect:()=>{},getIonRoute:()=>{},getPageManager:()=>{},getStackManager:()=>{},goBack:e=>{typeof window<"u"&&(typeof e=="string"?window.location.pathname=e:window.history.back())},navigate:e=>{typeof window<"u"&&(window.location.pathname=e)},hasIonicRouter:()=>!1,routeInfo:void 0,setCurrentTab:()=>{},changeTab:(e,t)=>{typeof window<"u"&&(window.location.pathname=t)},resetTab:(e,t)=>{typeof window<"u"&&(window.location.pathname=t)}}),hk=e=>e.toLowerCase().split("-").map(t=>t.charAt(0).toUpperCase()+t.slice(1)).join(""),Wm=e=>e.replace(/([A-Z])/g,t=>"-".concat(t[0].toLowerCase())),pk=(e,t,n={})=>{if(e instanceof Element){const o=mk(e.classList,t,n);o!==""&&(e.className=o),Object.keys(t).forEach(r=>{if(!(r==="children"||r==="style"||r==="ref"||r==="class"||r==="className"||r==="forwardedRef"))if(r.indexOf("on")===0&&r[2]===r[2].toUpperCase()){const i=r.substring(2),s=i[0].toLowerCase()+i.substring(1);Um(s)||vk(e,s,t[r])}else e[r]=t[r],typeof t[r]==="string"&&e.setAttribute(Wm(r),t[r])})}},mk=(e,t,n)=>{const o=t.className||t.class,r=n.className||n.class,i=Cl(e),s=Cl(o?o.split(" "):[]),a=Cl(r?r.split(" "):[]),l=[];return i.forEach(c=>{s.has(c)?(l.push(c),s.delete(c)):a.has(c)||l.push(c)}),s.forEach(c=>l.push(c)),l.join(" ")},gk=e=>{switch(e){case"doubleclick":return"dblclick"}return e};/**
 * Checks if an event is supported in the current execution environment.
 * @license Modernizr 3.0.0pre (Custom Build) | MIT
 */const Um=e=>{if(typeof document>"u")return!0;{const t="on"+gk(e);let n=t in document;if(!n){const o=document.createElement("div");o.setAttribute(t,"return;"),n=typeof o[t]=="function"}return n}},vk=(e,t,n)=>{const o=e.__events||(e.__events={}),r=o[t];r&&e.removeEventListener(t,r),e.addEventListener(t,o[t]=function(s){n&&n.call(this,s)})},Cl=e=>{const t=new Map;return e.forEach(n=>t.set(n,n)),t},yk=(e,t)=>{typeof e=="function"?e(t):e!=null&&(e.current=t)},Qm=(...e)=>t=>{e.forEach(n=>{yk(n,t)})},bk=(e,t)=>{const n=(o,r)=>b.createElement(e,Object.assign({},o,{forwardedRef:r}));return n.displayName=t,b.forwardRef(n)},rt=(e,t,n,o)=>{o!==void 0&&o();const r=hk(e),i=class extends b.Component{constructor(s){super(s),this.setComponentElRef=a=>{this.componentEl=a}}componentDidMount(){this.componentDidUpdate(this.props)}componentDidUpdate(s){pk(this.componentEl,this.props,s)}render(){const s=this.props,{children:a,forwardedRef:l,style:c,className:u,ref:d}=s,f=Oe(s,["children","forwardedRef","style","className","ref"]);let p=Object.keys(f).reduce((x,R)=>{const m=f[R];if(R.indexOf("on")===0&&R[2]===R[2].toUpperCase()){const h=R.substring(2).toLowerCase();typeof document<"u"&&Um(h)&&(x[R]=m)}else{const h=typeof m;(h==="string"||h==="boolean"||h==="number")&&(x[Wm(R)]=m)}return x},{});const v=Object.assign(Object.assign({},p),{ref:Qm(l,this.setComponentElRef),style:c});return Xe.createElement(e,v,a)}static get displayName(){return r}};return bk(i,r)},Jc=rt("ion-content",void 0,void 0,Sw),vo=rt("ion-header",void 0,void 0,Hw),Tl=rt("ion-label",void 0,void 0,Kw),wk=rt("ion-tab",void 0,void 0,vx),yo=rt("ion-title",void 0,void 0,yx),bo=rt("ion-toolbar",void 0,void 0,bx),Ir=(e,t)=>{const n=(o,r)=>b.createElement(e,Object.assign({},o,{forwardedRef:r}));return n.displayName=t,b.forwardRef(n)},eu=()=>{if(typeof window<"u"){const e=window.Ionic;if(e&&e.config)return e.config}return null},xk=b.createContext({addOverlay:()=>{},removeOverlay:()=>{}}),kk=({onAddOverlay:e,onRemoveOverlay:t})=>{const[n,o]=Xe.useState({}),r=Xe.useRef({});Xe.useEffect(()=>{e(i),t(s)},[]);const i=(l,c,u)=>{const d=Object.assign({},r.current);d[l]={component:c,containerElement:u},r.current=d,o(d)},s=l=>{const c=Object.assign({},r.current);delete c[l],r.current=c,o(c)},a=Object.keys(n);return b.createElement(b.Fragment,null,a.map(l=>{const c=n[l];return iy.createPortal(c.component,c.containerElement,"overlay-".concat(l))}))},Ek=rt("ion-tab-button",void 0,void 0,Ux),Sk=rt("ion-tab-bar",void 0,void 0,Mx),mf=rt("ion-tabs",void 0,void 0,Xx),Oa=rt("ion-router-outlet",void 0,void 0,Rx),Ck=rt("ion-app",void 0,void 0,Tx),Tk=rt("ion-icon",void 0,void 0,dk),$k=class extends b.Component{constructor(e){super(e),this.ionContext={addOverlay:(t,n,o)=>{this.addOverlayCallback&&this.addOverlayCallback(t,n,o)},removeOverlay:t=>{this.removeOverlayCallback&&this.removeOverlayCallback(t)}}}render(){return b.createElement(xk.Provider,{value:this.ionContext},b.createElement(Ck,Object.assign({},this.props),this.props.children),b.createElement(kk,{onAddOverlay:e=>{this.addOverlayCallback=e},onRemoveOverlay:e=>{this.removeOverlayCallback=e}}))}static get displayName(){return"IonApp"}},tu=b.createContext({registerIonPage:()=>{},isInOutlet:()=>!1});class nu extends b.PureComponent{constructor(t){super(t),this.ionPageElementRef=b.createRef(),this.stableMergedRefs=Qm(this.ionPageElementRef,this.props.forwardedRef),this.ionViewWillEnterHandler=this.ionViewWillEnterHandler.bind(this),this.ionViewDidEnterHandler=this.ionViewDidEnterHandler.bind(this),this.ionViewWillLeaveHandler=this.ionViewWillLeaveHandler.bind(this),this.ionViewDidLeaveHandler=this.ionViewDidLeaveHandler.bind(this)}componentDidMount(){this.ionPageElementRef.current&&(this.context.isInOutlet()&&this.ionPageElementRef.current.classList.add("ion-page-invisible"),this.context.registerIonPage(this.ionPageElementRef.current,this.props.routeInfo),this.ionPageElementRef.current.addEventListener("ionViewWillEnter",this.ionViewWillEnterHandler),this.ionPageElementRef.current.addEventListener("ionViewDidEnter",this.ionViewDidEnterHandler),this.ionPageElementRef.current.addEventListener("ionViewWillLeave",this.ionViewWillLeaveHandler),this.ionPageElementRef.current.addEventListener("ionViewDidLeave",this.ionViewDidLeaveHandler))}componentWillUnmount(){this.ionPageElementRef.current&&(this.ionPageElementRef.current.removeEventListener("ionViewWillEnter",this.ionViewWillEnterHandler),this.ionPageElementRef.current.removeEventListener("ionViewDidEnter",this.ionViewDidEnterHandler),this.ionPageElementRef.current.removeEventListener("ionViewWillLeave",this.ionViewWillLeaveHandler))}ionViewWillEnterHandler(){this.ionLifeCycleContext.ionViewWillEnter()}ionViewDidEnterHandler(){this.ionLifeCycleContext.ionViewDidEnter()}ionViewWillLeaveHandler(){this.ionLifeCycleContext.ionViewWillLeave()}ionViewDidLeaveHandler(){this.ionLifeCycleContext.ionViewDidLeave()}render(){const t=this.props,{className:n,children:o,routeInfo:r,forwardedRef:i}=t,s=Oe(t,["className","children","routeInfo","forwardedRef"]);return b.createElement(Zc.Consumer,null,a=>(this.ionLifeCycleContext=a,b.createElement("div",Object.assign({className:n?"".concat(n," ion-page"):"ion-page",ref:this.stableMergedRefs},s),o)))}static get contextType(){return tu}}class Pk extends b.Component{constructor(t){super(t)}render(){const t=this.props,{className:n,children:o,forwardedRef:r}=t,i=Oe(t,["className","children","forwardedRef"]);return this.context.hasIonicRouter()?b.createElement(nu,Object.assign({className:n?"".concat(n):"",routeInfo:this.context.routeInfo,forwardedRef:r},i),o):b.createElement("div",Object.assign({className:n?"ion-page ".concat(n):"ion-page",ref:r},i),o)}static get displayName(){return"IonPage"}static get contextType(){return Ft}}const ou=Ir(Pk,"IonPage"),gf={main:0},yr=(e="main")=>{var t;const n=((t=gf[e])!==null&&t!==void 0?t:0)+1;return gf[e]=n,n.toString()},_k=(e,t)=>{const n=new WeakMap,o="react-delegate-".concat(yr());let r=0;return{attachViewToDom:async(a,l,c,u)=>{const d=document.createElement("div");u&&d.classList.add(...u),a.appendChild(d);const f=l(c),p="".concat(o,"-").concat(r++),v=Pc.createPortal(f,d,p);return n.set(d,v),e(v),Promise.resolve(d)},removeViewFromDom:(a,l)=>{const c=n.get(l);return c&&t(c),l.remove(),Promise.resolve()}}},Ik=rt("ion-nav",void 0,void 0,ex),Rk=e=>{var{children:t,forwardedRef:n}=e,o=Oe(e,["children","forwardedRef"]);const[r,i]=Xe.useState([]),s=c=>i(u=>[...u,c]),a=c=>i(u=>u.filter(d=>d!==c)),l=Xe.useMemo(()=>_k(s,a),[]);return b.createElement(Ik,Object.assign({delegate:l,ref:n},o),r)};Ir(Rk,"IonNav");const qm=b.createContext({activeTab:void 0,selectTab:()=>!1,hasRouterOutlet:!1,tabBarProps:{ref:b.createRef()}}),Lk=typeof HTMLElement<"u"?HTMLElement:class{};class Ok extends b.Component{constructor(t){super(t),this.outletIsReady=!1,this.ionViewWillEnterHandler=this.ionViewWillEnterHandler.bind(this),this.ionViewDidEnterHandler=this.ionViewDidEnterHandler.bind(this),this.ionViewWillLeaveHandler=this.ionViewWillLeaveHandler.bind(this),this.ionViewDidLeaveHandler=this.ionViewDidLeaveHandler.bind(this)}componentDidMount(){this.ionRouterOutlet&&(this.outletIsReady||vr(this.ionRouterOutlet,()=>{this.outletIsReady=!0,this.context.registerIonPage(this.ionRouterOutlet,this.props.routeInfo)}),this.ionRouterOutlet.addEventListener("ionViewWillEnter",this.ionViewWillEnterHandler),this.ionRouterOutlet.addEventListener("ionViewDidEnter",this.ionViewDidEnterHandler),this.ionRouterOutlet.addEventListener("ionViewWillLeave",this.ionViewWillLeaveHandler),this.ionRouterOutlet.addEventListener("ionViewDidLeave",this.ionViewDidLeaveHandler))}componentWillUnmount(){this.ionRouterOutlet&&(this.ionRouterOutlet.removeEventListener("ionViewWillEnter",this.ionViewWillEnterHandler),this.ionRouterOutlet.removeEventListener("ionViewDidEnter",this.ionViewDidEnterHandler),this.ionRouterOutlet.removeEventListener("ionViewWillLeave",this.ionViewWillLeaveHandler),this.ionRouterOutlet.removeEventListener("ionViewDidLeave",this.ionViewDidLeaveHandler))}ionViewWillEnterHandler(){this.ionLifeCycleContext.ionViewWillEnter()}ionViewDidEnterHandler(){this.ionLifeCycleContext.ionViewDidEnter()}ionViewWillLeaveHandler(){this.ionLifeCycleContext.ionViewWillLeave()}ionViewDidLeaveHandler(){this.ionLifeCycleContext.ionViewDidLeave()}render(){const t=this.props,{StackManager:n,children:o,routeInfo:r}=t,i=Oe(t,["StackManager","children","routeInfo"]);return b.createElement(Zc.Consumer,null,s=>(this.ionLifeCycleContext=s,b.createElement(n,{routeInfo:r},b.createElement(Oa,Object.assign({setRef:a=>this.ionRouterOutlet=a},i),o))))}static get contextType(){return tu}}class Ak extends b.Component{constructor(t){super(t)}render(){const t=this.context.getStackManager(),n=this.props,{children:o,forwardedRef:r}=n,i=Oe(n,["children","forwardedRef"]);return this.context.hasIonicRouter()?i.ionPage?b.createElement(Ok,Object.assign({StackManager:t,routeInfo:this.context.routeInfo},i),o):b.createElement(t,{routeInfo:this.context.routeInfo},b.createElement(Oa,Object.assign({},i,{forwardedRef:r}),o)):b.createElement(Oa,Object.assign({ref:r},this.props),this.props.children)}static get contextType(){return Ft}}const Ho=Ir(Ak,"IonRouterOutlet");class zk extends Lk{constructor(){super()}}typeof window<"u"&&window.customElements&&(window.customElements.get("ion-tabs")||window.customElements.define("ion-tabs",zk));const Bk=class extends b.Component{constructor(e){super(e),this.routerOutletRef=b.createRef(),this.tabBarRef=b.createRef(),this.ionTabContextState={activeTab:void 0,selectTab:()=>!1,hasRouterOutlet:!1,tabBarProps:{ref:this.tabBarRef}}}componentDidMount(){this.tabBarRef.current&&(this.ionTabContextState.activeTab=this.tabBarRef.current.state.activeTab,this.tabBarRef.current.setActiveTabOnContext=e=>{this.ionTabContextState.activeTab=e},this.ionTabContextState.selectTab=this.tabBarRef.current.selectTab)}renderTabsInner(e,t){return b.createElement(mf,Object.assign({},this.props),b.Children.map(e,n=>b.isValidElement(n)&&(n.type===Ho||n.type.isRouterOutlet||n.type===Xe.Fragment&&n.props.children[0].type===Ho)?t:n))}render(){let e,t=!1;const n=this.props,{className:o,onIonTabsDidChange:r,onIonTabsWillChange:i}=n,s=Oe(n,["className","onIonTabsDidChange","onIonTabsWillChange"]),a=typeof this.props.children=="function"?this.props.children(this.ionTabContextState):this.props.children;if(b.Children.forEach(a,l=>{if(l==null||typeof l!="object"||!l.hasOwnProperty("type"))return;l.type===Ho||l.type.isRouterOutlet?e=b.cloneElement(l):l.type===Xe.Fragment&&l.props.children[0].type===Ho?e=b.cloneElement(l.props.children[0]):l.type===wk&&(t=!0),this.ionTabContextState.hasRouterOutlet=!!e;let c=Object.assign({},this.ionTabContextState.tabBarProps);r!==void 0&&(c=Object.assign(Object.assign({},c),{onIonTabsDidChange:r})),i!==void 0&&(c=Object.assign(Object.assign({},c),{onIonTabsWillChange:i})),this.ionTabContextState.tabBarProps=c}),!e&&!t)throw new Error("IonTabs must contain an IonRouterOutlet or an IonTab");if(e&&t)throw new Error("IonTabs cannot contain an IonRouterOutlet and an IonTab at the same time");return t?b.createElement(mf,Object.assign({},this.props)):b.createElement(qm.Provider,{value:this.ionTabContextState},this.context.hasIonicRouter()?b.createElement(nu,Object.assign({className:o?"".concat(o):"",routeInfo:this.context.routeInfo},s),this.renderTabsInner(a,e)):this.renderTabsInner(a,e))}static get contextType(){return Ft}},so=class extends b.Component{constructor(e){super(e),this.handleIonTabButtonClick=this.handleIonTabButtonClick.bind(this)}handleIonTabButtonClick(){this.props.onClick&&this.props.onClick(new CustomEvent("ionTabButtonClick",{detail:{tab:this.props.tab,href:this.props.href,routeOptions:this.props.routerOptions}}))}render(){const e=this.props,t=Oe(e,["onClick"]);return b.createElement(Ek,Object.assign({onIonTabButtonClick:this.handleIonTabButtonClick},t))}static get displayName(){return"IonTabButton"}};class Dk extends b.PureComponent{constructor(t){super(t),this.setActiveTabOnContext=o=>{};const n={};b.Children.forEach(t.children,o=>{var r,i,s,a;o!=null&&typeof o=="object"&&o.props&&(o.type===so||o.type.isTabButton)&&(n[o.props.tab]={originalHref:o.props.href,currentHref:o.props.href,originalRouteOptions:o.props.href===((r=t.routeInfo)===null||r===void 0?void 0:r.pathname)?(i=t.routeInfo)===null||i===void 0?void 0:i.routeOptions:void 0,currentRouteOptions:o.props.href===((s=t.routeInfo)===null||s===void 0?void 0:s.pathname)?(a=t.routeInfo)===null||a===void 0?void 0:a.routeOptions:void 0})}),this.state={tabs:n},this.onTabButtonClick=this.onTabButtonClick.bind(this),this.renderTabButton=this.renderTabButton.bind(this),this.setActiveTabOnContext=this.setActiveTabOnContext.bind(this),this.selectTab=this.selectTab.bind(this)}componentDidMount(){const t=this.state.tabs,o=Object.keys(t).find(r=>{const i=t[r].originalHref;return this.props.routeInfo.pathname.startsWith(i)});o&&this.setState({activeTab:o})}componentDidUpdate(){this.state.activeTab&&this.setActiveTabOnContext(this.state.activeTab)}selectTab(t){const n=this.state.tabs[t];return n?(this.onTabButtonClick(new CustomEvent("ionTabButtonClick",{detail:{href:n.currentHref,tab:t,selected:t===this.state.activeTab,routeOptions:void 0}})),!0):!1}static getDerivedStateFromProps(t,n){var o,r,i;const s=Object.assign({},n.tabs),l=Object.keys(n.tabs).find(u=>{const d=n.tabs[u].originalHref;return t.routeInfo.pathname.startsWith(d)});b.Children.forEach(t.children,u=>{if(u!=null&&typeof u=="object"&&u.props&&(u.type===so||u.type.isTabButton)){const d=s[u.props.tab];(!d||d.originalHref!==u.props.href)&&(s[u.props.tab]={originalHref:u.props.href,currentHref:u.props.href,originalRouteOptions:u.props.routeOptions,currentRouteOptions:u.props.routeOptions})}});const{activeTab:c}=n;if(l&&c){const u=n.tabs[c].currentHref,d=n.tabs[c].currentRouteOptions;(l!==c||u!==((o=t.routeInfo)===null||o===void 0?void 0:o.pathname)||d!==((r=t.routeInfo)===null||r===void 0?void 0:r.routeOptions))&&(s[l]={originalHref:s[l].originalHref,currentHref:t.routeInfo.pathname+(t.routeInfo.search||""),originalRouteOptions:s[l].originalRouteOptions,currentRouteOptions:(i=t.routeInfo)===null||i===void 0?void 0:i.routeOptions},t.routeInfo.routeAction==="pop"&&l!==c&&(s[c]={originalHref:s[c].originalHref,currentHref:s[c].originalHref,originalRouteOptions:s[c].originalRouteOptions,currentRouteOptions:s[c].currentRouteOptions}))}return l&&t.onSetCurrentTab(l,t.routeInfo),{activeTab:l,tabs:s}}onTabButtonClick(t,n){var o;const r=this.state.tabs[t.detail.tab],i=r.originalHref,s=(o=this.props.tabsContext)===null||o===void 0?void 0:o.hasRouterOutlet,a=s?t.detail.href:"",{activeTab:l}=this.state;n&&n(t),l===t.detail.tab?i!==a&&this.context.resetTab(t.detail.tab,i,r.originalRouteOptions):(this.props.onIonTabsWillChange&&this.props.onIonTabsWillChange(new CustomEvent("ionTabWillChange",{detail:{tab:t.detail.tab}})),this.props.onIonTabsDidChange&&this.props.onIonTabsDidChange(new CustomEvent("ionTabDidChange",{detail:{tab:t.detail.tab}})),s&&(this.setActiveTabOnContext(t.detail.tab),this.context.changeTab(t.detail.tab,a,t.detail.routeOptions)))}renderTabButton(t){return n=>{var o,r;if(n!=null&&n.props&&(n.type===so||n.type.isTabButton)){const i=n.props.tab===t?(o=this.props.routeInfo)===null||o===void 0?void 0:o.pathname:this.state.tabs[n.props.tab].currentHref,s=n.props.tab===t?(r=this.props.routeInfo)===null||r===void 0?void 0:r.routeOptions:this.state.tabs[n.props.tab].currentRouteOptions;return b.cloneElement(n,{href:i,routeOptions:s,onClick:a=>this.onTabButtonClick(a,n.props.onClick)})}return null}}render(){const{activeTab:t}=this.state;return b.createElement(Sk,Object.assign({},this.props,{selectedTab:t}),b.Children.map(this.props.children,this.renderTabButton(t)))}static get contextType(){return Ft}}const Mk=b.memo(e=>{var{forwardedRef:t}=e,n=Oe(e,["forwardedRef"]);const o=Xe.useContext(Ft),r=Xe.useContext(qm),i=t||r.tabBarProps.ref,s=Object.assign(Object.assign({},r.tabBarProps),{ref:i});return b.createElement(Dk,Object.assign({ref:i},n,{routeInfo:n.routeInfo||o.routeInfo||{pathname:window.location.pathname},onSetCurrentTab:o.setCurrentTab,tabsContext:Object.assign(Object.assign({},r),{tabBarProps:s})}),n.children)}),Nk=Ir(Mk,"IonTabBar");class jk extends b.PureComponent{constructor(t){super(t),this.props.name&&console.warn('In Ionic React, you import icons from "ionicons/icons" and set the icon you imported to the "icon" property. Setting the "name" property has no effect.')}render(){var t,n;const o=this.props,{icon:r,ios:i,md:s,mode:a}=o,l=Oe(o,["icon","ios","md","mode"]);let c;const u=eu(),d=a||(u==null?void 0:u.get("mode"));return i||s?d==="ios"?c=(t=i!=null?i:s)!==null&&t!==void 0?t:r:c=(n=s!=null?s:i)!==null&&n!==void 0?n:r:c=r,b.createElement(Tk,Object.assign({ref:this.props.forwardedRef,icon:c},l),this.props.children)}static get contextType(){return Ft}}const $l=Ir(jk,"IonIcon");class ru extends b.PureComponent{render(){const t=this.context.getIonRoute();return!this.context.hasIonicRouter()||!ru?(console.error("You either do not have an Ionic Router package, or your router does not support using <IonRoute>"),null):b.createElement(t,Object.assign({},this.props))}static get contextType(){return Ft}}class Km extends b.PureComponent{render(){const t=this.context.getIonRedirect();return!this.context.hasIonicRouter()||!Km?(console.error("You either do not have an Ionic Router package, or your router does not support using <IonRedirect>"),null):b.createElement(t,Object.assign({},this.props))}static get contextType(){return Ft}}const Hk=b.createContext({routeInfo:void 0,push:()=>{throw new Error("An Ionic Router is required for IonRouterContext")},back:()=>{throw new Error("An Ionic Router is required for IonRouterContext")},canGoBack:()=>{throw new Error("An Ionic Router is required for IonRouterContext")},nativeBack:()=>{throw new Error("An Ionic Router is required for IonRouterContext")}});class AE extends b.PureComponent{constructor(t){super(t),this.nodes=new Map,this.animation=b1(t.id)}setupAnimation(t){const n=this.animation;this.nodes.size>0&&n.addElement(Array.from(this.nodes.values())),vf(n,t),yf(n,t)}componentDidMount(){const t=this.props;this.setupAnimation(t)}componentDidUpdate(t){const n=this.animation,o=this.props;vf(n,o,t),Vk(n,o,t),yf(n,o,t)}render(){const{children:t}=this.props;return b.createElement(b.Fragment,null,b.Children.map(t,(n,o)=>b.cloneElement(n,{ref:r=>this.nodes.set(o,r)})))}}const vf=(e,t={},n={})=>{const o=["children","progressStart","progressStep","progressEnd","pause","stop","destroy","play","from","to","fromTo","onFinish"];for(const l in t)t.hasOwnProperty(l)&&!o.includes(l)&&t[l]!==n[l]&&e[l](t[l]);const r=t.from;r&&r!==n.from&&(Array.isArray(r)?r:[r]).forEach(c=>e.from(c.property,c.value));const i=t.to;i&&i!==n.to&&(Array.isArray(i)?i:[i]).forEach(c=>e.to(c.property,c.value));const s=t.fromTo;s&&s!==n.fromTo&&(Array.isArray(s)?s:[s]).forEach(c=>e.fromTo(c.property,c.fromValue,c.toValue));const a=t.onFinish;a&&a!==n.onFinish&&(Array.isArray(a)?a:[a]).forEach(c=>e.onFinish(c.callback,c.opts))},Vk=(e,t={},n={})=>{var o,r,i,s,a;const{progressStart:l,progressStep:c,progressEnd:u}=t;l&&(((o=n.progressStart)===null||o===void 0?void 0:o.forceLinearEasing)!==(l==null?void 0:l.forceLinearEasing)||((r=n.progressStart)===null||r===void 0?void 0:r.step)!==(l==null?void 0:l.step))&&e.progressStart(l.forceLinearEasing,l.step),c&&((i=n.progressStep)===null||i===void 0?void 0:i.step)!==(c==null?void 0:c.step)&&e.progressStep(c.step),u&&(((s=n.progressEnd)===null||s===void 0?void 0:s.playTo)!==(u==null?void 0:u.playTo)||((a=n.progressEnd)===null||a===void 0?void 0:a.step)!==(u==null?void 0:u.step)||(n==null?void 0:n.dur)!==(u==null?void 0:u.dur))&&e.progressEnd(u.playTo,u.step,u.dur)},yf=(e,t={},n={})=>{!n.play&&t.play&&e.play(),!n.pause&&t.pause&&e.pause(),!n.stop&&t.stop&&e.stop(),!n.destroy&&t.destroy&&e.destroy()},Fk=(e={})=>{typeof document<"u"&&document.documentElement.classList.add("ion-ce"),D1(Object.assign({},e))},Ym=b.createContext({addViewItem:()=>{},canGoBack:()=>{},clearOutlet:()=>{},createViewItem:()=>{},findViewItemByPathname:()=>{},findLeavingViewItemByRouteInfo:()=>{},findViewItemByRouteInfo:()=>{},getChildrenToRender:()=>{},goBack:()=>{},unMountViewItem:()=>{}});class bf extends b.Component{constructor(t){super(t),this.ionLifeCycleContext=new fk,this._isMounted=!1,this.ionLifeCycleContext.onComponentCanBeDestroyed(()=>{this.props.mount||this._isMounted&&this.setState({show:!1},()=>this.props.removeView())}),this.state={show:!0}}componentDidMount(){this._isMounted=!0}componentWillUnmount(){this._isMounted=!1}render(){const{show:t}=this.state;return b.createElement(Zc.Provider,{value:this.ionLifeCycleContext},t&&this.props.children)}}class Wk{constructor(){this.locationHistory=[],this.tabHistory={}}add(t){t.routeAction==="push"||t.routeAction==null?this._add(t):t.routeAction==="pop"?this._pop(t):t.routeAction==="replace"&&this._replace(t),t.routeDirection==="root"&&(this._clear(),this._add(t))}clearTabStack(t){const n=this._getRouteInfosByKey(t);n&&(n.forEach(o=>{this.locationHistory=this.locationHistory.filter(r=>r.id!==o.id)}),this.tabHistory[t]=[])}update(t){const n=this.locationHistory.findIndex(r=>r.id===t.id);n>-1&&this.locationHistory.splice(n,1,t);const o=this.tabHistory[t.tab||""];if(o){const r=o.findIndex(i=>i.id===t.id);r>-1?o.splice(r,1,t):o.push(t)}else t.tab&&(this.tabHistory[t.tab]=[t])}_add(t){const n=this._getRouteInfosByKey(t.tab);n&&(this._areRoutesEqual(n[n.length-1],t)&&n.pop(),n.push(t)),this.locationHistory.push(t)}_areRoutesEqual(t,n){return!t||!n?!1:t.pathname===n.pathname&&t.search===n.search}_pop(t){const n=this._getRouteInfosByKey(t.tab);n&&(n.pop(),n.pop(),n.push(t)),this.locationHistory.pop(),this.locationHistory.pop(),this.locationHistory.push(t)}_replace(t){const n=this._getRouteInfosByKey(t.tab);n&&n.pop(),this.locationHistory.pop(),this._add(t)}_clear(){Object.keys(this.tabHistory).forEach(n=>this.tabHistory[n]=[]),this.locationHistory=[]}_getRouteInfosByKey(t){let n;return t&&(n=this.tabHistory[t],n||(n=this.tabHistory[t]=[])),n}getFirstRouteInfoForTab(t){const n=this._getRouteInfosByKey(t);if(n)return n[0]}getCurrentRouteInfoForTab(t){const n=this._getRouteInfosByKey(t);if(n)return n[n.length-1]}findLastLocation(t){const n=this._getRouteInfosByKey(t.tab);if(n)for(let o=n.length-2;o>=0;o--){const r=n[o];if(r&&r.pathname===t.pushedByRoute)return r}for(let o=this.locationHistory.length-2;o>=0;o--){const r=this.locationHistory[o];if(r&&r.pathname===t.pushedByRoute)return r}}previous(){return this.locationHistory[this.locationHistory.length-2]||this.locationHistory[this.locationHistory.length-1]}current(){return this.locationHistory[this.locationHistory.length-1]}canGoBack(){return this.locationHistory.length>1}}class Uk extends b.PureComponent{constructor(t){super(t),this.ionRouterContextValue={push:(n,o,r,i,s)=>{this.navigate(n,o,r,s,i)},back:n=>{this.goBack(void 0,n)},canGoBack:()=>this.props.locationHistory.canGoBack(),nativeBack:()=>this.props.onNativeBack(),routeInfo:this.props.routeInfo},this.state={goBack:this.goBack.bind(this),hasIonicRouter:()=>!0,navigate:this.navigate.bind(this),getIonRedirect:this.getIonRedirect.bind(this),getIonRoute:this.getIonRoute.bind(this),getStackManager:this.getStackManager.bind(this),getPageManager:this.getPageManager.bind(this),routeInfo:this.props.routeInfo,setCurrentTab:this.props.onSetCurrentTab,changeTab:this.props.onChangeTab,resetTab:this.props.onResetTab}}componentDidMount(){typeof document<"u"&&(this.handleHardwareBackButton=this.handleHardwareBackButton.bind(this),document.addEventListener("ionBackButton",this.handleHardwareBackButton))}componentWillUnmount(){typeof document<"u"&&document.removeEventListener("ionBackButton",this.handleHardwareBackButton)}handleHardwareBackButton(t){t.detail.register(0,n=>{this.nativeGoBack(),n()})}goBack(t,n){this.props.onNavigateBack(t,n)}nativeGoBack(){this.props.onNativeBack()}navigate(t,n="forward",o="push",r,i,s){this.props.onNavigate(t,o,n,r,i,s)}getPageManager(){return nu}getIonRedirect(){return this.props.ionRedirect}getIonRoute(){return this.props.ionRoute}getStackManager(){return this.props.stackManager}render(){return b.createElement(Ft.Provider,{value:Object.assign(Object.assign({},this.state),{routeInfo:this.props.routeInfo})},b.createElement(Hk.Provider,{value:Object.assign(Object.assign({},this.ionRouterContextValue),{routeInfo:this.props.routeInfo})},this.props.children))}}class Qk{constructor(){this.viewStacks={},this.add=this.add.bind(this),this.clear=this.clear.bind(this),this.getViewItemsForOutlet=this.getViewItemsForOutlet.bind(this),this.remove=this.remove.bind(this)}add(t){const{outletId:n}=t;this.viewStacks[n]?this.viewStacks[n].push(t):this.viewStacks[n]=[t]}clear(t){return setTimeout(()=>{delete this.viewStacks[t]},500)}getViewItemsForOutlet(t){return this.viewStacks[t]||[]}remove(t){const{outletId:n}=t,o=this.viewStacks[n];if(o){const r=o.find(i=>i.id===t.id);r&&(r.mount=!1,this.viewStacks[n]=o.filter(i=>i.id!==r.id))}}getStackIds(){return Object.keys(this.viewStacks)}getAllViewItems(){const t=this.getStackIds(),n=[];return t.forEach(o=>{n.push(...this.viewStacks[o])}),n}}class qk extends b.PureComponent{render(){return b.createElement(No,Object.assign({path:this.props.path,exact:this.props.exact,render:this.props.render},this.props.computedMatch!==void 0?{computedMatch:this.props.computedMatch}:{}))}}const br=({pathname:e,componentProps:t})=>{const{exact:n,component:o}=t,r=t.path||t.from,s=Hc(e,{exact:n,path:r,component:o});return s||!1};class Kk extends Qk{constructor(){super(),this.createViewItem=this.createViewItem.bind(this),this.findViewItemByRouteInfo=this.findViewItemByRouteInfo.bind(this),this.findLeavingViewItemByRouteInfo=this.findLeavingViewItemByRouteInfo.bind(this),this.getChildrenToRender=this.getChildrenToRender.bind(this),this.findViewItemByPathname=this.findViewItemByPathname.bind(this)}createViewItem(t,n,o,r){const i={id:yr("viewItem"),outletId:t,ionPageElement:r,reactElement:n,mount:!0,ionRoute:!1};return n.type===ru&&(i.ionRoute=!0,i.disableIonPageManagement=n.props.disableIonPageManagement),i.routeData={match:br({pathname:o.pathname,componentProps:n.props}),childProps:n.props},i}getChildrenToRender(t,n,o){const r=this.getViewItemsForOutlet(t);return b.Children.forEach(n.props.children,s=>{const a=r.find(l=>wf(s,l.routeData.childProps.path||l.routeData.childProps.from));a&&(a.reactElement=s)}),r.map(s=>{let a;if(s.ionRoute&&!s.disableIonPageManagement)a=b.createElement(bf,{key:"view-".concat(s.id),mount:s.mount,removeView:()=>this.remove(s)},b.cloneElement(s.reactElement,{computedMatch:s.routeData.match}));else{const l=wf(s.reactElement,o.pathname);a=b.createElement(bf,{key:"view-".concat(s.id),mount:s.mount,removeView:()=>this.remove(s)},b.cloneElement(s.reactElement,{computedMatch:s.routeData.match})),!l&&s.routeData.match&&(s.routeData.match=void 0,s.mount=!1)}return a})}findViewItemByRouteInfo(t,n,o){const{viewItem:r,match:i}=this.findViewItemByPath(t.pathname,n);return(o===void 0||o===!0)&&r&&i&&(r.routeData.match=i),r}findLeavingViewItemByRouteInfo(t,n,o=!0){const{viewItem:r}=this.findViewItemByPath(t.lastPathname,n,o);return r}findViewItemByPathname(t,n){const{viewItem:o}=this.findViewItemByPath(t,n);return o}findViewItemByPath(t,n,o){let r,i,s;if(n)s=this.getViewItemsForOutlet(n),s.some(a),r||s.some(l);else{const c=this.getAllViewItems();c.some(a),r||c.some(l)}return{viewItem:r,match:i};function a(c){var u,d;if(o&&!c.ionRoute)return!1;if(i=br({pathname:t,componentProps:c.routeData.childProps}),i){const f=i.path.includes(":");if(!f||f&&i.url===((d=(u=c.routeData)===null||u===void 0?void 0:u.match)===null||d===void 0?void 0:d.url))return r=c,!0}return!1}function l(c){return!c.routeData.childProps.path&&!c.routeData.childProps.from?(i={path:t,url:t,isExact:!0,params:{}},r=c,!0):!1}}}function wf(e,t){return br({pathname:t,componentProps:e.props})}function Yk(e){let t;if(typeof e=="string"?t=e:t=e.outerHTML,document){const n=document.createElement("div");n.innerHTML=t,n.style.zIndex="";const o=n.getElementsByTagName("ion-back-button");return o[0]&&o[0].remove(),n.firstChild}}const xf=e=>!e.classList.contains("ion-page-invisible")&&!e.classList.contains("ion-page-hidden");class Xk extends b.PureComponent{constructor(t){super(t),this.stackContextValue={registerIonPage:this.registerIonPage.bind(this),isInOutlet:()=>!0},this.pendingPageTransition=!1,this.registerIonPage=this.registerIonPage.bind(this),this.transitionPage=this.transitionPage.bind(this),this.handlePageTransition=this.handlePageTransition.bind(this),this.id=yr("routerOutlet"),this.prevProps=void 0,this.skipTransition=!1}componentDidMount(){this.clearOutletTimeout&&clearTimeout(this.clearOutletTimeout),this.routerOutletElement&&(this.setupRouterOutlet(this.routerOutletElement),this.handlePageTransition(this.props.routeInfo))}componentDidUpdate(t){const{pathname:n}=this.props.routeInfo,{pathname:o}=t.routeInfo;n!==o?(this.prevProps=t,this.handlePageTransition(this.props.routeInfo)):this.pendingPageTransition&&(this.handlePageTransition(this.props.routeInfo),this.pendingPageTransition=!1)}componentWillUnmount(){this.clearOutletTimeout=this.context.clearOutlet(this.id)}async handlePageTransition(t){var n,o;if(!this.routerOutletElement||!this.routerOutletElement.commit)this.pendingPageTransition=!0;else{let r=this.context.findViewItemByRouteInfo(t,this.id),i=this.context.findLeavingViewItemByRouteInfo(t,this.id);!i&&t.prevRouteLastPathname&&(i=this.context.findViewItemByPathname(t.prevRouteLastPathname,this.id)),i&&(t.routeAction==="replace"?i.mount=!1:t.routeAction==="push"&&t.routeDirection==="forward"?!((n=t.routeOptions)===null||n===void 0)&&n.unmount&&(i.mount=!1):t.routeDirection!=="none"&&r!==i&&(i.mount=!1));const s=Gk((o=this.ionRouterOutlet)===null||o===void 0?void 0:o.props.children,t);if(r?r.reactElement=s:s&&(r=this.context.createViewItem(this.id,s,t),this.context.addViewItem(r)),r&&r.ionPageElement){if(r===i&&r.routeData.match.url!==t.pathname||(!i&&this.props.routeInfo.prevRouteLastPathname&&(i=this.context.findViewItemByPathname(this.props.routeInfo.prevRouteLastPathname,this.id)),xf(r.ionPageElement)&&i!==void 0&&!xf(i.ionPageElement)))return;this.transitionPage(t,r,i)}else i&&!s&&!r&&i.ionPageElement&&(i.ionPageElement.classList.add("ion-page-hidden"),i.ionPageElement.setAttribute("aria-hidden","true"));this.forceUpdate()}}registerIonPage(t,n){const o=this.context.findViewItemByRouteInfo(n,this.id);if(o){const r=o.ionPageElement;if(o.ionPageElement=t,o.ionRoute=!0,r===t)return}this.handlePageTransition(n)}async setupRouterOutlet(t){const n=()=>{const i=eu();if(!(i&&i.get("swipeBackEnabled",t.mode==="ios")))return!1;const{routeInfo:a}=this.props,l=this.prevProps&&this.prevProps.routeInfo.pathname===a.pushedByRoute?this.prevProps.routeInfo:{pathname:a.pushedByRoute||""},c=this.context.findViewItemByRouteInfo(l,this.id,!1);return!!c&&c.mount&&c.routeData.match.path!==a.pathname},o=async()=>{const{routeInfo:i}=this.props,s=this.prevProps&&this.prevProps.routeInfo.pathname===i.pushedByRoute?this.prevProps.routeInfo:{pathname:i.pushedByRoute||""},a=this.context.findViewItemByRouteInfo(s,this.id,!1),l=this.context.findViewItemByRouteInfo(i,this.id,!1);return a&&l&&await this.transitionPage(i,a,l,"back",!0),Promise.resolve()},r=i=>{if(i)this.skipTransition=!0,this.context.goBack();else{const{routeInfo:s}=this.props,a=this.prevProps&&this.prevProps.routeInfo.pathname===s.pushedByRoute?this.prevProps.routeInfo:{pathname:s.pushedByRoute||""},l=this.context.findViewItemByRouteInfo(a,this.id,!1),c=this.context.findViewItemByRouteInfo(s,this.id,!1);if(l!==c&&(l==null?void 0:l.ionPageElement)!==void 0){const{ionPageElement:u}=l;u.setAttribute("aria-hidden","true"),u.classList.add("ion-page-hidden")}}};t.swipeHandler={canStart:n,onStart:o,onEnd:r}}async transitionPage(t,n,o,r,i=!1){const s=async(u,d)=>{const f=this.skipTransition;f?this.skipTransition=!1:(u.classList.add("ion-page"),u.classList.add("ion-page-invisible")),await a.commit(u,d,{duration:f||c===void 0?0:void 0,direction:c,showGoBack:!!t.pushedByRoute,progressAnimation:i,animationBuilder:t.routeAnimation})},a=this.routerOutletElement,l=t.routeDirection==="none"||t.routeDirection==="root"?void 0:t.routeDirection,c=r!=null?r:l;if(n&&n.ionPageElement&&this.routerOutletElement)if(o&&o.ionPageElement&&n===o)if(Zk(o.reactElement,t.pathname,!0)){const d=Yk(o.ionPageElement.outerHTML);d&&(this.routerOutletElement.appendChild(d),await s(n.ionPageElement,d),this.routerOutletElement.removeChild(d))}else await s(n.ionPageElement,void 0);else await s(n.ionPageElement,o==null?void 0:o.ionPageElement),o&&o.ionPageElement&&!i&&(o.ionPageElement.classList.add("ion-page-hidden"),o.ionPageElement.setAttribute("aria-hidden","true"))}render(){const{children:t}=this.props,n=b.Children.only(t);this.ionRouterOutlet=n;const o=this.context.getChildrenToRender(this.id,this.ionRouterOutlet,this.props.routeInfo,()=>{this.forceUpdate()});return b.createElement(tu.Provider,{value:this.stackContextValue},b.cloneElement(n,{ref:r=>{n.props.setRef&&n.props.setRef(r),n.props.forwardedRef&&(n.props.forwardedRef.current=r),this.routerOutletElement=r;const{ref:i}=n;typeof i=="function"&&i(r)}},o))}static get contextType(){return Ym}}function Gk(e,t){let n;return b.Children.forEach(e,o=>{br({pathname:t.pathname,componentProps:o.props})&&(n=o)}),n||(b.Children.forEach(e,o=>{o.props.path||o.props.from||(n=o)}),n)}function Zk(e,t,n){return br({pathname:t,componentProps:Object.assign(Object.assign({},e.props),{exact:n})})}class Jk extends b.PureComponent{constructor(t){super(t),this.exitViewFromOtherOutletHandlers=[],this.locationHistory=new Wk,this.viewStack=new Kk,this.routeMangerContextState={canGoBack:()=>this.locationHistory.canGoBack(),clearOutlet:this.viewStack.clear,findViewItemByPathname:this.viewStack.findViewItemByPathname,getChildrenToRender:this.viewStack.getChildrenToRender,goBack:()=>this.handleNavigateBack(),createViewItem:this.viewStack.createViewItem,findViewItemByRouteInfo:this.viewStack.findViewItemByRouteInfo,findLeavingViewItemByRouteInfo:this.viewStack.findLeavingViewItemByRouteInfo,addViewItem:this.viewStack.add,unMountViewItem:this.viewStack.remove};const n={id:yr("routeInfo"),pathname:this.props.location.pathname,search:this.props.location.search};this.locationHistory.add(n),this.handleChangeTab=this.handleChangeTab.bind(this),this.handleResetTab=this.handleResetTab.bind(this),this.handleNativeBack=this.handleNativeBack.bind(this),this.handleNavigate=this.handleNavigate.bind(this),this.handleNavigateBack=this.handleNavigateBack.bind(this),this.props.registerHistoryListener(this.handleHistoryChange.bind(this)),this.handleSetCurrentTab=this.handleSetCurrentTab.bind(this),this.state={routeInfo:n}}handleChangeTab(t,n,o){if(!n)return;const r=this.locationHistory.getCurrentRouteInfoForTab(t),[i,s]=n.split("?");r?(this.incomingRouteParams=Object.assign(Object.assign({},r),{routeAction:"push",routeDirection:"none"}),r.pathname===i?(this.incomingRouteParams.routeOptions=o,this.props.history.push(r.pathname+(r.search||""))):(this.incomingRouteParams.pathname=i,this.incomingRouteParams.search=s?"?"+s:void 0,this.incomingRouteParams.routeOptions=o,this.props.history.push(i+(s?"?"+s:"")))):this.handleNavigate(i,"push","none",void 0,o,t)}handleHistoryChange(t,n){var o,r,i;let s;if(this.incomingRouteParams?this.incomingRouteParams.routeAction==="replace"?s=this.locationHistory.previous():s=this.locationHistory.current():s=this.locationHistory.current(),s.pathname+s.search!==t.pathname){if(!this.incomingRouteParams){if(n==="REPLACE"&&(this.incomingRouteParams={routeAction:"replace",routeDirection:"none",tab:this.currentTab}),n==="POP"){const c=this.locationHistory.current();if(c&&c.pushedByRoute){const u=this.locationHistory.findLastLocation(c);this.incomingRouteParams=Object.assign(Object.assign({},u),{routeAction:"pop",routeDirection:"back"})}else this.incomingRouteParams={routeAction:"pop",routeDirection:"none",tab:this.currentTab}}this.incomingRouteParams||(this.incomingRouteParams={routeAction:"push",routeDirection:((o=t.state)===null||o===void 0?void 0:o.direction)||"forward",routeOptions:(r=t.state)===null||r===void 0?void 0:r.routerOptions,tab:this.currentTab})}let l;if(!((i=this.incomingRouteParams)===null||i===void 0)&&i.id)l=Object.assign(Object.assign({},this.incomingRouteParams),{lastPathname:s.pathname}),this.locationHistory.add(l);else{const c=this.incomingRouteParams.routeAction==="push"&&this.incomingRouteParams.routeDirection==="forward";if(l=Object.assign(Object.assign({id:yr("routeInfo")},this.incomingRouteParams),{lastPathname:s.pathname,pathname:t.pathname,search:t.search,params:this.props.match.params,prevRouteLastPathname:s.lastPathname}),c)l.tab=s.tab,l.pushedByRoute=s.pathname;else if(l.routeAction==="pop"){const u=this.locationHistory.findLastLocation(l);l.pushedByRoute=u==null?void 0:u.pushedByRoute}else if(l.routeAction==="push"&&l.tab!==s.tab){const u=this.locationHistory.getCurrentRouteInfoForTab(l.tab);l.pushedByRoute=u==null?void 0:u.pushedByRoute}else if(l.routeAction==="replace"){const u=this.locationHistory.current(),d=u==null?void 0:u.pushedByRoute,f=d!==void 0&&d!==l.pathname?d:l.pushedByRoute;l.lastPathname=(u==null?void 0:u.pathname)||l.lastPathname,l.prevRouteLastPathname=u==null?void 0:u.lastPathname,l.pushedByRoute=f,l.routeDirection=l.routeDirection||(u==null?void 0:u.routeDirection),l.routeAnimation=l.routeAnimation||(u==null?void 0:u.routeAnimation)}this.locationHistory.add(l)}this.setState({routeInfo:l})}this.incomingRouteParams=void 0}handleNativeBack(){const t=this.props.history;(t.goBack||t.back)()}handleNavigate(t,n,o,r,i,s){this.incomingRouteParams=Object.assign(this.incomingRouteParams||{},{routeAction:n,routeDirection:o,routeOptions:i,routeAnimation:r,tab:s}),n==="push"?this.props.history.push(t):this.props.history.replace(t)}handleNavigateBack(t="/",n){const o=eu();t=t||o&&o.get("backButtonDefaultHref");const r=this.locationHistory.current();if(r&&r.pushedByRoute){const i=this.locationHistory.findLastLocation(r);if(i){const s=n||r.routeAnimation;if(this.incomingRouteParams=Object.assign(Object.assign({},i),{routeAction:"pop",routeDirection:"back",routeAnimation:s}),r.lastPathname===r.pushedByRoute||i.pathname===r.pushedByRoute&&r.tab===""&&i.tab===""){const a=this.props.history;(a.goBack||a.back)()}else this.handleNavigate(i.pathname+(i.search||""),"pop","back",s)}else this.handleNavigate(t,"pop","back",n)}else this.handleNavigate(t,"pop","back",n)}handleResetTab(t,n,o){const r=this.locationHistory.getFirstRouteInfoForTab(t);if(r){const i=Object.assign({},r);i.pathname=n,i.routeOptions=o,this.incomingRouteParams=Object.assign(Object.assign({},i),{routeAction:"pop",routeDirection:"back"}),this.props.history.push(i.pathname+(i.search||""))}}handleSetCurrentTab(t){this.currentTab=t;const n=Object.assign({},this.locationHistory.current());n.tab!==t&&(n.tab=t,this.locationHistory.update(n))}render(){return b.createElement(Ym.Provider,{value:this.routeMangerContextState},b.createElement(Uk,{ionRoute:qk,ionRedirect:{},stackManager:Xk,routeInfo:this.state.routeInfo,onNativeBack:this.handleNativeBack,onNavigateBack:this.handleNavigateBack,onNavigate:this.handleNavigate,onSetCurrentTab:this.handleSetCurrentTab,onChangeTab:this.handleChangeTab,onResetTab:this.handleResetTab,locationHistory:this.locationHistory},this.props.children))}}const As=yb(Jk);As.displayName="IonRouter";class eE extends b.Component{constructor(t){super(t);const{history:n}=t,o=Oe(t,["history"]);this.history=n||wy(o),this.history.listen(this.handleHistoryChange.bind(this)),this.registerHistoryListener=this.registerHistoryListener.bind(this)}handleHistoryChange(t,n){const o=t.location||t,r=t.action||n;this.historyListenHandler&&this.historyListenHandler(o,r)}registerHistoryListener(t){this.historyListenHandler=t}render(){const t=this.props,{children:n}=t,o=Oe(t,["children"]);return b.createElement(Tr,Object.assign({history:this.history},o),b.createElement(As,{registerHistoryListener:this.registerHistoryListener},n))}}class zE extends b.Component{constructor(t){super(t),this.history=t.history,this.history.listen(this.handleHistoryChange.bind(this)),this.registerHistoryListener=this.registerHistoryListener.bind(this)}handleHistoryChange(t,n){const o=t.location||t,r=t.action||n;this.historyListenHandler&&this.historyListenHandler(o,r)}registerHistoryListener(t){this.historyListenHandler=t}render(){const t=this.props,{children:n}=t,o=Oe(t,["children"]);return b.createElement(Tr,Object.assign({},o),b.createElement(As,{registerHistoryListener:this.registerHistoryListener},n))}}class BE extends b.Component{constructor(t){super(t);const{history:n}=t,o=Oe(t,["history"]);this.history=n||Ey(o),this.history.listen(this.handleHistoryChange.bind(this)),this.registerHistoryListener=this.registerHistoryListener.bind(this)}handleHistoryChange(t,n){const o=t.location||t,r=t.action||n;this.historyListenHandler&&this.historyListenHandler(o,r)}registerHistoryListener(t){this.historyListenHandler=t}render(){const t=this.props,{children:n}=t,o=Oe(t,["children"]);return b.createElement(Tr,Object.assign({history:this.history},o),b.createElement(As,{registerHistoryListener:this.registerHistoryListener},n))}}const tE="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M256 464c-114.69 0-208-93.31-208-208S141.31 48 256 48s208 93.31 208 208-93.31 208-208 208z'/></svg>",nE="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M416 464H96a48.05 48.05 0 01-48-48V96a48.05 48.05 0 0148-48h320a48.05 48.05 0 0148 48v320a48.05 48.05 0 01-48 48z'/></svg>",oE="data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M464 464H48a16 16 0 01-14.07-23.62l208-384a16 16 0 0128.14 0l208 384A16 16 0 01464 464z'/></svg>",iu=({name:e})=>D.jsxs("div",{className:"container",children:[D.jsx("strong",{children:e}),D.jsxs("p",{children:["Explore ",D.jsx("a",{target:"_blank",rel:"noopener noreferrer",href:"https://ionicframework.com/docs/components",children:"UI Components"})]})]}),rE=()=>D.jsxs(ou,{children:[D.jsx(vo,{children:D.jsx(bo,{children:D.jsx(yo,{children:"Tab 1"})})}),D.jsxs(Jc,{fullscreen:!0,children:[D.jsx(vo,{collapse:"condense",children:D.jsx(bo,{children:D.jsx(yo,{size:"large",children:"Tab 1"})})}),D.jsx(iu,{name:"Tab 1 page"})]})]}),iE=()=>D.jsxs(ou,{children:[D.jsx(vo,{children:D.jsx(bo,{children:D.jsx(yo,{children:"Tab 2"})})}),D.jsxs(Jc,{fullscreen:!0,children:[D.jsx(vo,{collapse:"condense",children:D.jsx(bo,{children:D.jsx(yo,{size:"large",children:"Tab 2"})})}),D.jsx(iu,{name:"Tab 2 page"})]})]}),sE=()=>D.jsxs(ou,{children:[D.jsx(vo,{children:D.jsx(bo,{children:D.jsx(yo,{children:"Tab 3"})})}),D.jsxs(Jc,{fullscreen:!0,children:[D.jsx(vo,{collapse:"condense",children:D.jsx(bo,{children:D.jsx(yo,{size:"large",children:"Tab 3"})})}),D.jsx(iu,{name:"Tab 3 page"})]})]});Fk();const lE=()=>D.jsx($k,{children:D.jsx(eE,{children:D.jsxs(Bk,{children:[D.jsxs(Ho,{children:[D.jsx(No,{exact:!0,path:"/tab1",children:D.jsx(rE,{})}),D.jsx(No,{exact:!0,path:"/tab2",children:D.jsx(iE,{})}),D.jsx(No,{path:"/tab3",children:D.jsx(sE,{})}),D.jsx(No,{exact:!0,path:"/",children:D.jsx(fb,{to:"/tab1"})})]}),D.jsxs(Nk,{slot:"bottom",children:[D.jsxs(so,{tab:"tab1",href:"/tab1",children:[D.jsx($l,{"aria-hidden":"true",icon:oE}),D.jsx(Tl,{children:"Tab 1"})]}),D.jsxs(so,{tab:"tab2",href:"/tab2",children:[D.jsx($l,{"aria-hidden":"true",icon:tE}),D.jsx(Tl,{children:"Tab 2"})]}),D.jsxs(so,{tab:"tab3",href:"/tab3",children:[D.jsx($l,{"aria-hidden":"true",icon:nE}),D.jsx(Tl,{children:"Tab 3"})]})]})]})})}),aE=document.getElementById("root"),cE=Ap(aE);cE.render(D.jsx(b.StrictMode,{children:D.jsx(lE,{})}));export{Pw as K,fE as __vite_legacy_guard,vr as a,hE as b,Tm as c,vl as d,pE as e,bE as f,Ra as g,pe as h,gw as i,Cm as j,rf as k,xE as l,b1 as m,gE as n,mE as p,mr as r,wE as s,dn as w};
