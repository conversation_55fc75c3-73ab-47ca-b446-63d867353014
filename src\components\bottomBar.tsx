import { App<PERSON><PERSON>, <PERSON>conButton, Avatar, createStyles, makeStyles, Theme, SwipeableDrawer } from '@material-ui/core'
import { HomeOutlined, ScheduleOutlined, DeviceHubOutlined, RepeatOutlined } from '@material-ui/icons'
import React, { useState } from 'react'
import { useHistory, withRouter, useLocation } from 'react-router-dom'
// import { ReactComponent as RoutineIcon } from '../assets/routines.svg';
import { Fab } from '@material-ui/core';
import { AddAlarmOutlined } from '@material-ui/icons';
import { useSpring, config, animated } from 'react-spring';
// import AddRoutines from './routines/AddRoutines';
const BottomBar = (props: any) => {
    const location = useLocation();
    const history = useHistory();
    const appBarHeight = '60px';
    const [drawerOpen, setDrawerOpen] = useState<boolean>(false);
    const spring = useSpring({
        from: { transform: 'scale(0)' },
        to: { transform: 'scale(1)' },
        config: config.wobbly,
        // delay:800
    })
    const useStyles = makeStyles((theme: Theme) =>
        createStyles({
            appBar: {
                top: 'auto',
                bottom: 0,
                background: "#ffffff",
                height: appBarHeight,
            },
        }),
    );
    const classes = useStyles();
    return <>
        {/* <SwipeableDrawer
            anchor={'bottom'}
            open={drawerOpen}
            onClose={() => setDrawerOpen(false)}
            onOpen={() => console.log('drawer closed')}
        >
             <AddRoutines />
        </SwipeableDrawer> 
    */}
        <AppBar position="fixed" className={classes.appBar}>
            <div style={{ display: "flex", height: appBarHeight, justifyContent: "space-evenly", alignItems: "center" }}>
                <IconButton edge="start" color="inherit" aria-label="open drawer" onClick={() => {
                    history.push('/')
                }}>
                    <HomeOutlined color={'primary'} style={{ fontSize: "30px" }} />
                </IconButton>
                {/* TODO: [HOM-208] Save constants to enumeration or config */}


                {location.pathname === '/routines' ?
                    <animated.div style={spring}>
                        <Fab onClick={() => setDrawerOpen(true)} style={{ marginBottom: location.pathname === '/routines' ? "50px" : "none" }} color="primary" aria-label="add">
                            <AddAlarmOutlined style={{ color: '#ffffff' }} />
                        </Fab>
                    </animated.div>

                    :
                    <IconButton
                        color="inherit"
                        onClick={() => {
                            history.push('/routines');
                            setDrawerOpen(false);
                        }}>
                        {/* <RoutineIcon style={{ fontSize: "30px", height: "30px", width: "30px", color: "#cccccc" }} /> */}
                    </IconButton>
                }
                <IconButton edge="end" color="inherit" onClick={() => {
                    history.push('/applianceSetup')
                }}>
                    <DeviceHubOutlined style={{ fontSize: "30px", color: "#cccccc" }} />
                </IconButton>
                <IconButton edge="end" color="inherit" onClick={() => {
                    history.push('/profile')
                }}>
                    <Avatar style={{ fontSize: "20px" }}>S</Avatar>
                </IconButton>
            </div>
        </AppBar>

    </>
}

export default BottomBar