System.register([],(function(e,t){"use strict";return{execute:function(){var n=document.createElement("style");function o(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}n.textContent='.container{text-align:center;position:absolute;left:0;right:0;top:50%;transform:translateY(-50%)}.container strong{font-size:20px;line-height:26px}.container p{font-size:16px;line-height:22px;color:#8c8c8c;margin:0}.container a{text-decoration:none}:root{--ion-color-primary: #0054e9;--ion-color-primary-rgb: 0, 84, 233;--ion-color-primary-contrast: #fff;--ion-color-primary-contrast-rgb: 255, 255, 255;--ion-color-primary-shade: #004acd;--ion-color-primary-tint: #1a65eb;--ion-color-secondary: #0163aa;--ion-color-secondary-rgb: 1, 99, 170;--ion-color-secondary-contrast: #fff;--ion-color-secondary-contrast-rgb: 255, 255, 255;--ion-color-secondary-shade: #015796;--ion-color-secondary-tint: #1a73b3;--ion-color-tertiary: #6030ff;--ion-color-tertiary-rgb: 96, 48, 255;--ion-color-tertiary-contrast: #fff;--ion-color-tertiary-contrast-rgb: 255, 255, 255;--ion-color-tertiary-shade: #542ae0;--ion-color-tertiary-tint: #7045ff;--ion-color-success: #2dd55b;--ion-color-success-rgb: 45, 213, 91;--ion-color-success-contrast: #000;--ion-color-success-contrast-rgb: 0, 0, 0;--ion-color-success-shade: #28bb50;--ion-color-success-tint: #42d96b;--ion-color-warning: #ffc409;--ion-color-warning-rgb: 255, 196, 9;--ion-color-warning-contrast: #000;--ion-color-warning-contrast-rgb: 0, 0, 0;--ion-color-warning-shade: #e0ac08;--ion-color-warning-tint: #ffca22;--ion-color-danger: #c5000f;--ion-color-danger-rgb: 197, 0, 15;--ion-color-danger-contrast: #fff;--ion-color-danger-contrast-rgb: 255, 255, 255;--ion-color-danger-shade: #ad000d;--ion-color-danger-tint: #cb1a27;--ion-color-light: #f4f5f8;--ion-color-light-rgb: 244, 245, 248;--ion-color-light-contrast: #000;--ion-color-light-contrast-rgb: 0, 0, 0;--ion-color-light-shade: #d7d8da;--ion-color-light-tint: #f5f6f9;--ion-color-medium: #636469;--ion-color-medium-rgb: 99, 100, 105;--ion-color-medium-contrast: #fff;--ion-color-medium-contrast-rgb: 255, 255, 255;--ion-color-medium-shade: #57585c;--ion-color-medium-tint: #737478;--ion-color-dark: #222428;--ion-color-dark-rgb: 34, 36, 40;--ion-color-dark-contrast: #fff;--ion-color-dark-contrast-rgb: 255, 255, 255;--ion-color-dark-shade: #1e2023;--ion-color-dark-tint: #383a3e}html.ios{--ion-default-font: -apple-system, BlinkMacSystemFont, "Helvetica Neue", "Roboto", sans-serif}html.md{--ion-default-font: "Roboto", "Helvetica Neue", sans-serif}html{--ion-dynamic-font: -apple-system-body;--ion-font-family: var(--ion-default-font)}body{background:var(--ion-background-color);color:var(--ion-text-color)}body.backdrop-no-scroll{overflow:hidden}html.ios ion-modal.modal-card ion-header ion-toolbar:first-of-type,html.ios ion-modal.modal-sheet ion-header ion-toolbar:first-of-type,html.ios ion-modal ion-footer ion-toolbar:first-of-type{padding-top:6px}html.ios ion-modal.modal-card ion-header ion-toolbar:last-of-type,html.ios ion-modal.modal-sheet ion-header ion-toolbar:last-of-type{padding-bottom:6px}html.ios ion-modal ion-toolbar{padding-right:calc(var(--ion-safe-area-right) + 8px);padding-left:calc(var(--ion-safe-area-left) + 8px)}@media screen and (min-width: 768px){html.ios ion-modal.modal-card:first-of-type{--backdrop-opacity: .18}}ion-modal.modal-default.show-modal~ion-modal.modal-default{--backdrop-opacity: 0;--box-shadow: none}html.ios ion-modal.modal-card .ion-page{border-top-left-radius:var(--border-radius)}.ion-color-primary{--ion-color-base: var(--ion-color-primary, #0054e9) !important;--ion-color-base-rgb: var(--ion-color-primary-rgb, 0, 84, 233) !important;--ion-color-contrast: var(--ion-color-primary-contrast, #fff) !important;--ion-color-contrast-rgb: var(--ion-color-primary-contrast-rgb, 255, 255, 255) !important;--ion-color-shade: var(--ion-color-primary-shade, #004acd) !important;--ion-color-tint: var(--ion-color-primary-tint, #1a65eb) !important}.ion-color-secondary{--ion-color-base: var(--ion-color-secondary, #0163aa) !important;--ion-color-base-rgb: var(--ion-color-secondary-rgb, 1, 99, 170) !important;--ion-color-contrast: var(--ion-color-secondary-contrast, #fff) !important;--ion-color-contrast-rgb: var(--ion-color-secondary-contrast-rgb, 255, 255, 255) !important;--ion-color-shade: var(--ion-color-secondary-shade, #015796) !important;--ion-color-tint: var(--ion-color-secondary-tint, #1a73b3) !important}.ion-color-tertiary{--ion-color-base: var(--ion-color-tertiary, #6030ff) !important;--ion-color-base-rgb: var(--ion-color-tertiary-rgb, 96, 48, 255) !important;--ion-color-contrast: var(--ion-color-tertiary-contrast, #fff) !important;--ion-color-contrast-rgb: var(--ion-color-tertiary-contrast-rgb, 255, 255, 255) !important;--ion-color-shade: var(--ion-color-tertiary-shade, #542ae0) !important;--ion-color-tint: var(--ion-color-tertiary-tint, #7045ff) !important}.ion-color-success{--ion-color-base: var(--ion-color-success, #2dd55b) !important;--ion-color-base-rgb: var(--ion-color-success-rgb, 45, 213, 91) !important;--ion-color-contrast: var(--ion-color-success-contrast, #000) !important;--ion-color-contrast-rgb: var(--ion-color-success-contrast-rgb, 0, 0, 0) !important;--ion-color-shade: var(--ion-color-success-shade, #28bb50) !important;--ion-color-tint: var(--ion-color-success-tint, #42d96b) !important}.ion-color-warning{--ion-color-base: var(--ion-color-warning, #ffc409) !important;--ion-color-base-rgb: var(--ion-color-warning-rgb, 255, 196, 9) !important;--ion-color-contrast: var(--ion-color-warning-contrast, #000) !important;--ion-color-contrast-rgb: var(--ion-color-warning-contrast-rgb, 0, 0, 0) !important;--ion-color-shade: var(--ion-color-warning-shade, #e0ac08) !important;--ion-color-tint: var(--ion-color-warning-tint, #ffca22) !important}.ion-color-danger{--ion-color-base: var(--ion-color-danger, #c5000f) !important;--ion-color-base-rgb: var(--ion-color-danger-rgb, 197, 0, 15) !important;--ion-color-contrast: var(--ion-color-danger-contrast, #fff) !important;--ion-color-contrast-rgb: var(--ion-color-danger-contrast-rgb, 255, 255, 255) !important;--ion-color-shade: var(--ion-color-danger-shade, #ad000d) !important;--ion-color-tint: var(--ion-color-danger-tint, #cb1a27) !important}.ion-color-light{--ion-color-base: var(--ion-color-light, #f4f5f8) !important;--ion-color-base-rgb: var(--ion-color-light-rgb, 244, 245, 248) !important;--ion-color-contrast: var(--ion-color-light-contrast, #000) !important;--ion-color-contrast-rgb: var(--ion-color-light-contrast-rgb, 0, 0, 0) !important;--ion-color-shade: var(--ion-color-light-shade, #d7d8da) !important;--ion-color-tint: var(--ion-color-light-tint, #f5f6f9) !important}.ion-color-medium{--ion-color-base: var(--ion-color-medium, #636469) !important;--ion-color-base-rgb: var(--ion-color-medium-rgb, 99, 100, 105) !important;--ion-color-contrast: var(--ion-color-medium-contrast, #fff) !important;--ion-color-contrast-rgb: var(--ion-color-medium-contrast-rgb, 255, 255, 255) !important;--ion-color-shade: var(--ion-color-medium-shade, #57585c) !important;--ion-color-tint: var(--ion-color-medium-tint, #737478) !important}.ion-color-dark{--ion-color-base: var(--ion-color-dark, #222428) !important;--ion-color-base-rgb: var(--ion-color-dark-rgb, 34, 36, 40) !important;--ion-color-contrast: var(--ion-color-dark-contrast, #fff) !important;--ion-color-contrast-rgb: var(--ion-color-dark-contrast-rgb, 255, 255, 255) !important;--ion-color-shade: var(--ion-color-dark-shade, #1e2023) !important;--ion-color-tint: var(--ion-color-dark-tint, #383a3e) !important}.ion-page{left:0;right:0;top:0;bottom:0;display:flex;position:absolute;flex-direction:column;justify-content:space-between;contain:layout size style;z-index:0}ion-modal>.ion-page{position:relative;contain:layout style;height:100%}.split-pane-visible>.ion-page.split-pane-main{position:relative}ion-route,ion-route-redirect,ion-router,ion-select-option,ion-nav-controller,ion-menu-controller,ion-action-sheet-controller,ion-alert-controller,ion-loading-controller,ion-modal-controller,ion-picker-controller,ion-popover-controller,ion-toast-controller,.ion-page-hidden{display:none!important}.ion-page-invisible{opacity:0}.can-go-back>ion-header ion-back-button{display:block}html.plt-ios.plt-hybrid,html.plt-ios.plt-pwa{--ion-statusbar-padding: 20px}@supports (padding-top: 20px){html{--ion-safe-area-top: var(--ion-statusbar-padding)}}@supports (padding-top: env(safe-area-inset-top)){html{--ion-safe-area-top: env(safe-area-inset-top);--ion-safe-area-bottom: env(safe-area-inset-bottom);--ion-safe-area-left: env(safe-area-inset-left);--ion-safe-area-right: env(safe-area-inset-right)}}ion-card.ion-color .ion-inherit-color,ion-card-header.ion-color .ion-inherit-color{color:inherit}.menu-content{transform:translateZ(0)}.menu-content-open{cursor:pointer;touch-action:manipulation;pointer-events:none;overflow-y:hidden}.menu-content-open ion-content{--overflow: hidden}.menu-content-open .ion-content-scroll-host{overflow:hidden}.ios .menu-content-reveal{box-shadow:-8px 0 42px rgba(0,0,0,.08)}[dir=rtl].ios .menu-content-reveal{box-shadow:8px 0 42px rgba(0,0,0,.08)}.md .menu-content-reveal,.md .menu-content-push{box-shadow:4px 0 16px rgba(0,0,0,.18)}ion-accordion-group.accordion-group-expand-inset>ion-accordion:first-of-type{border-top-left-radius:8px;border-top-right-radius:8px}ion-accordion-group.accordion-group-expand-inset>ion-accordion:last-of-type{border-bottom-left-radius:8px;border-bottom-right-radius:8px}ion-accordion-group>ion-accordion:last-of-type ion-item[slot=header]{--border-width: 0px}ion-accordion.accordion-animated>[slot=header] .ion-accordion-toggle-icon{transition:.3s transform cubic-bezier(.25,.8,.5,1)}@media (prefers-reduced-motion: reduce){ion-accordion .ion-accordion-toggle-icon{transition:none!important}}ion-accordion.accordion-expanding>[slot=header] .ion-accordion-toggle-icon,ion-accordion.accordion-expanded>[slot=header] .ion-accordion-toggle-icon{transform:rotate(180deg)}ion-accordion-group.accordion-group-expand-inset.md>ion-accordion.accordion-previous ion-item[slot=header]{--border-width: 0px;--inner-border-width: 0px}ion-accordion-group.accordion-group-expand-inset.md>ion-accordion.accordion-expanding:first-of-type,ion-accordion-group.accordion-group-expand-inset.md>ion-accordion.accordion-expanded:first-of-type{margin-top:0}ion-input input::-webkit-date-and-time-value{text-align:start}.ion-datetime-button-overlay{--width: fit-content;--height: fit-content}.ion-datetime-button-overlay ion-datetime.datetime-grid{width:320px;min-height:320px}[ion-last-focus],header[tabindex="-1"]:focus,[role=banner][tabindex="-1"]:focus,main[tabindex="-1"]:focus,[role=main][tabindex="-1"]:focus,h1[tabindex="-1"]:focus,[role=heading][aria-level="1"][tabindex="-1"]:focus{outline:none}.popover-viewport:has(>ion-content){overflow:hidden}@supports not selector(:has(> ion-content)){.popover-viewport{overflow:hidden}}audio,canvas,progress,video{vertical-align:baseline}audio:not([controls]){display:none;height:0}b,strong{font-weight:700}img{max-width:100%}hr{height:1px;border-width:0;box-sizing:content-box}pre{overflow:auto}code,kbd,pre,samp{font-family:monospace,monospace;font-size:1em}label,input,select,textarea{font-family:inherit;line-height:normal}textarea{overflow:auto;height:auto;font:inherit;color:inherit}textarea::placeholder{padding-left:2px}form,input,optgroup,select{margin:0;font:inherit;color:inherit}html input[type=button],input[type=reset],input[type=submit]{cursor:pointer;-webkit-appearance:button}a,a div,a span,a ion-icon,a ion-label,button,button div,button span,button ion-icon,button ion-label,.ion-tappable,[tappable],[tappable] div,[tappable] span,[tappable] ion-icon,[tappable] ion-label,input,textarea{touch-action:manipulation}a ion-label,button ion-label{pointer-events:none}button{padding:0;border:0;border-radius:0;font-family:inherit;font-style:inherit;font-variant:inherit;line-height:1;text-transform:none;cursor:pointer;-webkit-appearance:button}[tappable]{cursor:pointer}a[disabled],button[disabled],html input[disabled]{cursor:default}button::-moz-focus-inner,input::-moz-focus-inner{padding:0;border:0}input[type=number]::-webkit-inner-spin-button,input[type=number]::-webkit-outer-spin-button{height:auto}input[type=search]::-webkit-search-cancel-button,input[type=search]::-webkit-search-decoration{-webkit-appearance:none}table{border-collapse:collapse;border-spacing:0}td,th{padding:0}*{box-sizing:border-box;-webkit-tap-highlight-color:rgba(0,0,0,0);-webkit-tap-highlight-color:transparent;-webkit-touch-callout:none}html{width:100%;height:100%;-webkit-text-size-adjust:100%;text-size-adjust:100%}html:not(.hydrated) body{display:none}html.ion-ce body{display:block}html.plt-pwa{height:100vh}body{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;margin:0;padding:0;position:fixed;width:100%;max-width:100%;height:100%;max-height:100%;transform:translateZ(0);text-rendering:optimizeLegibility;overflow:hidden;touch-action:manipulation;-webkit-user-drag:none;-ms-content-zooming:none;word-wrap:break-word;overscroll-behavior-y:none;-webkit-text-size-adjust:none;text-size-adjust:none}html{font-family:var(--ion-font-family)}@supports (-webkit-touch-callout: none){html{font:var(--ion-dynamic-font, 16px var(--ion-font-family))}}a{background-color:transparent;color:var(--ion-color-primary, #0054e9)}h1,h2,h3,h4,h5,h6{margin-top:16px;margin-bottom:10px;font-weight:500;line-height:1.2}h1{margin-top:20px;font-size:1.625rem}h2{margin-top:18px;font-size:1.5rem}h3{font-size:1.375rem}h4{font-size:1.25rem}h5{font-size:1.125rem}h6{font-size:1rem}small{font-size:75%}sub,sup{position:relative;font-size:75%;line-height:0;vertical-align:baseline}sup{top:-.5em}sub{bottom:-.25em}.ion-no-padding{--padding-start: 0;--padding-end: 0;--padding-top: 0;--padding-bottom: 0;padding:0}.ion-padding{--padding-start: var(--ion-padding, 16px);--padding-end: var(--ion-padding, 16px);--padding-top: var(--ion-padding, 16px);--padding-bottom: var(--ion-padding, 16px);-webkit-padding-start:var(--ion-padding, 16px);padding-inline-start:var(--ion-padding, 16px);-webkit-padding-end:var(--ion-padding, 16px);padding-inline-end:var(--ion-padding, 16px);padding-top:var(--ion-padding, 16px);padding-bottom:var(--ion-padding, 16px)}.ion-padding-top{--padding-top: var(--ion-padding, 16px);padding-top:var(--ion-padding, 16px)}.ion-padding-start{--padding-start: var(--ion-padding, 16px);-webkit-padding-start:var(--ion-padding, 16px);padding-inline-start:var(--ion-padding, 16px)}.ion-padding-end{--padding-end: var(--ion-padding, 16px);-webkit-padding-end:var(--ion-padding, 16px);padding-inline-end:var(--ion-padding, 16px)}.ion-padding-bottom{--padding-bottom: var(--ion-padding, 16px);padding-bottom:var(--ion-padding, 16px)}.ion-padding-vertical{--padding-top: var(--ion-padding, 16px);--padding-bottom: var(--ion-padding, 16px);padding-top:var(--ion-padding, 16px);padding-bottom:var(--ion-padding, 16px)}.ion-padding-horizontal{--padding-start: var(--ion-padding, 16px);--padding-end: var(--ion-padding, 16px);-webkit-padding-start:var(--ion-padding, 16px);padding-inline-start:var(--ion-padding, 16px);-webkit-padding-end:var(--ion-padding, 16px);padding-inline-end:var(--ion-padding, 16px)}.ion-no-margin{--margin-start: 0;--margin-end: 0;--margin-top: 0;--margin-bottom: 0;margin:0}.ion-margin{--margin-start: var(--ion-margin, 16px);--margin-end: var(--ion-margin, 16px);--margin-top: var(--ion-margin, 16px);--margin-bottom: var(--ion-margin, 16px);-webkit-margin-start:var(--ion-margin, 16px);margin-inline-start:var(--ion-margin, 16px);-webkit-margin-end:var(--ion-margin, 16px);margin-inline-end:var(--ion-margin, 16px);margin-top:var(--ion-margin, 16px);margin-bottom:var(--ion-margin, 16px)}.ion-margin-top{--margin-top: var(--ion-margin, 16px);margin-top:var(--ion-margin, 16px)}.ion-margin-start{--margin-start: var(--ion-margin, 16px);-webkit-margin-start:var(--ion-margin, 16px);margin-inline-start:var(--ion-margin, 16px)}.ion-margin-end{--margin-end: var(--ion-margin, 16px);-webkit-margin-end:var(--ion-margin, 16px);margin-inline-end:var(--ion-margin, 16px)}.ion-margin-bottom{--margin-bottom: var(--ion-margin, 16px);margin-bottom:var(--ion-margin, 16px)}.ion-margin-vertical{--margin-top: var(--ion-margin, 16px);--margin-bottom: var(--ion-margin, 16px);margin-top:var(--ion-margin, 16px);margin-bottom:var(--ion-margin, 16px)}.ion-margin-horizontal{--margin-start: var(--ion-margin, 16px);--margin-end: var(--ion-margin, 16px);-webkit-margin-start:var(--ion-margin, 16px);margin-inline-start:var(--ion-margin, 16px);-webkit-margin-end:var(--ion-margin, 16px);margin-inline-end:var(--ion-margin, 16px)}.ion-float-left{float:left!important}.ion-float-right{float:right!important}.ion-float-start{float:left!important}:host-context([dir=rtl]) .ion-float-start{float:right!important}[dir=rtl] .ion-float-start{float:right!important}@supports selector(:dir(rtl)){.ion-float-start:dir(rtl){float:right!important}}.ion-float-end{float:right!important}:host-context([dir=rtl]) .ion-float-end{float:left!important}[dir=rtl] .ion-float-end{float:left!important}@supports selector(:dir(rtl)){.ion-float-end:dir(rtl){float:left!important}}@media (min-width: 576px){.ion-float-sm-left{float:left!important}.ion-float-sm-right{float:right!important}.ion-float-sm-start{float:left!important}:host-context([dir=rtl]) .ion-float-sm-start{float:right!important}[dir=rtl] .ion-float-sm-start{float:right!important}@supports selector(:dir(rtl)){.ion-float-sm-start:dir(rtl){float:right!important}}.ion-float-sm-end{float:right!important}:host-context([dir=rtl]) .ion-float-sm-end{float:left!important}[dir=rtl] .ion-float-sm-end{float:left!important}@supports selector(:dir(rtl)){.ion-float-sm-end:dir(rtl){float:left!important}}}@media (min-width: 768px){.ion-float-md-left{float:left!important}.ion-float-md-right{float:right!important}.ion-float-md-start{float:left!important}:host-context([dir=rtl]) .ion-float-md-start{float:right!important}[dir=rtl] .ion-float-md-start{float:right!important}@supports selector(:dir(rtl)){.ion-float-md-start:dir(rtl){float:right!important}}.ion-float-md-end{float:right!important}:host-context([dir=rtl]) .ion-float-md-end{float:left!important}[dir=rtl] .ion-float-md-end{float:left!important}@supports selector(:dir(rtl)){.ion-float-md-end:dir(rtl){float:left!important}}}@media (min-width: 992px){.ion-float-lg-left{float:left!important}.ion-float-lg-right{float:right!important}.ion-float-lg-start{float:left!important}:host-context([dir=rtl]) .ion-float-lg-start{float:right!important}[dir=rtl] .ion-float-lg-start{float:right!important}@supports selector(:dir(rtl)){.ion-float-lg-start:dir(rtl){float:right!important}}.ion-float-lg-end{float:right!important}:host-context([dir=rtl]) .ion-float-lg-end{float:left!important}[dir=rtl] .ion-float-lg-end{float:left!important}@supports selector(:dir(rtl)){.ion-float-lg-end:dir(rtl){float:left!important}}}@media (min-width: 1200px){.ion-float-xl-left{float:left!important}.ion-float-xl-right{float:right!important}.ion-float-xl-start{float:left!important}:host-context([dir=rtl]) .ion-float-xl-start{float:right!important}[dir=rtl] .ion-float-xl-start{float:right!important}@supports selector(:dir(rtl)){.ion-float-xl-start:dir(rtl){float:right!important}}.ion-float-xl-end{float:right!important}:host-context([dir=rtl]) .ion-float-xl-end{float:left!important}[dir=rtl] .ion-float-xl-end{float:left!important}@supports selector(:dir(rtl)){.ion-float-xl-end:dir(rtl){float:left!important}}}.ion-text-center{text-align:center!important}.ion-text-justify{text-align:justify!important}.ion-text-start{text-align:start!important}.ion-text-end{text-align:end!important}.ion-text-left{text-align:left!important}.ion-text-right{text-align:right!important}.ion-text-nowrap{white-space:nowrap!important}.ion-text-wrap{white-space:normal!important}@media (min-width: 576px){.ion-text-sm-center{text-align:center!important}.ion-text-sm-justify{text-align:justify!important}.ion-text-sm-start{text-align:start!important}.ion-text-sm-end{text-align:end!important}.ion-text-sm-left{text-align:left!important}.ion-text-sm-right{text-align:right!important}.ion-text-sm-nowrap{white-space:nowrap!important}.ion-text-sm-wrap{white-space:normal!important}}@media (min-width: 768px){.ion-text-md-center{text-align:center!important}.ion-text-md-justify{text-align:justify!important}.ion-text-md-start{text-align:start!important}.ion-text-md-end{text-align:end!important}.ion-text-md-left{text-align:left!important}.ion-text-md-right{text-align:right!important}.ion-text-md-nowrap{white-space:nowrap!important}.ion-text-md-wrap{white-space:normal!important}}@media (min-width: 992px){.ion-text-lg-center{text-align:center!important}.ion-text-lg-justify{text-align:justify!important}.ion-text-lg-start{text-align:start!important}.ion-text-lg-end{text-align:end!important}.ion-text-lg-left{text-align:left!important}.ion-text-lg-right{text-align:right!important}.ion-text-lg-nowrap{white-space:nowrap!important}.ion-text-lg-wrap{white-space:normal!important}}@media (min-width: 1200px){.ion-text-xl-center{text-align:center!important}.ion-text-xl-justify{text-align:justify!important}.ion-text-xl-start{text-align:start!important}.ion-text-xl-end{text-align:end!important}.ion-text-xl-left{text-align:left!important}.ion-text-xl-right{text-align:right!important}.ion-text-xl-nowrap{white-space:nowrap!important}.ion-text-xl-wrap{white-space:normal!important}}.ion-text-uppercase{text-transform:uppercase!important}.ion-text-lowercase{text-transform:lowercase!important}.ion-text-capitalize{text-transform:capitalize!important}@media (min-width: 576px){.ion-text-sm-uppercase{text-transform:uppercase!important}.ion-text-sm-lowercase{text-transform:lowercase!important}.ion-text-sm-capitalize{text-transform:capitalize!important}}@media (min-width: 768px){.ion-text-md-uppercase{text-transform:uppercase!important}.ion-text-md-lowercase{text-transform:lowercase!important}.ion-text-md-capitalize{text-transform:capitalize!important}}@media (min-width: 992px){.ion-text-lg-uppercase{text-transform:uppercase!important}.ion-text-lg-lowercase{text-transform:lowercase!important}.ion-text-lg-capitalize{text-transform:capitalize!important}}@media (min-width: 1200px){.ion-text-xl-uppercase{text-transform:uppercase!important}.ion-text-xl-lowercase{text-transform:lowercase!important}.ion-text-xl-capitalize{text-transform:capitalize!important}}.ion-align-self-start{align-self:flex-start!important}.ion-align-self-end{align-self:flex-end!important}.ion-align-self-center{align-self:center!important}.ion-align-self-stretch{align-self:stretch!important}.ion-align-self-baseline{align-self:baseline!important}.ion-align-self-auto{align-self:auto!important}.ion-wrap{flex-wrap:wrap!important}.ion-nowrap{flex-wrap:nowrap!important}.ion-wrap-reverse{flex-wrap:wrap-reverse!important}.ion-justify-content-start{justify-content:flex-start!important}.ion-justify-content-center{justify-content:center!important}.ion-justify-content-end{justify-content:flex-end!important}.ion-justify-content-around{justify-content:space-around!important}.ion-justify-content-between{justify-content:space-between!important}.ion-justify-content-evenly{justify-content:space-evenly!important}.ion-align-items-start{align-items:flex-start!important}.ion-align-items-center{align-items:center!important}.ion-align-items-end{align-items:flex-end!important}.ion-align-items-stretch{align-items:stretch!important}.ion-align-items-baseline{align-items:baseline!important}.ion-hide,.ion-hide-up,.ion-hide-down{display:none!important}@media (min-width: 576px){.ion-hide-sm-up{display:none!important}}@media (max-width: 575.98px){.ion-hide-sm-down{display:none!important}}@media (min-width: 768px){.ion-hide-md-up{display:none!important}}@media (max-width: 767.98px){.ion-hide-md-down{display:none!important}}@media (min-width: 992px){.ion-hide-lg-up{display:none!important}}@media (max-width: 991.98px){.ion-hide-lg-down{display:none!important}}@media (min-width: 1200px){.ion-hide-xl-up{display:none!important}}@media (max-width: 1199.98px){.ion-hide-xl-down{display:none!important}}@media (prefers-color-scheme: dark){:root{--ion-color-primary: #4d8dff;--ion-color-primary-rgb: 77, 141, 255;--ion-color-primary-contrast: #000;--ion-color-primary-contrast-rgb: 0, 0, 0;--ion-color-primary-shade: #447ce0;--ion-color-primary-tint: #5f98ff;--ion-color-secondary: #46b1ff;--ion-color-secondary-rgb: 70, 177, 255;--ion-color-secondary-contrast: #000;--ion-color-secondary-contrast-rgb: 0, 0, 0;--ion-color-secondary-shade: #3e9ce0;--ion-color-secondary-tint: #59b9ff;--ion-color-tertiary: #8482fb;--ion-color-tertiary-rgb: 132, 130, 251;--ion-color-tertiary-contrast: #000;--ion-color-tertiary-contrast-rgb: 0, 0, 0;--ion-color-tertiary-shade: #7472dd;--ion-color-tertiary-tint: #908ffb;--ion-color-success: #2dd55b;--ion-color-success-rgb: 45, 213, 91;--ion-color-success-contrast: #000;--ion-color-success-contrast-rgb: 0, 0, 0;--ion-color-success-shade: #28bb50;--ion-color-success-tint: #42d96b;--ion-color-warning: #ffce31;--ion-color-warning-rgb: 255, 206, 49;--ion-color-warning-contrast: #000;--ion-color-warning-contrast-rgb: 0, 0, 0;--ion-color-warning-shade: #e0b52b;--ion-color-warning-tint: #ffd346;--ion-color-danger: #f24c58;--ion-color-danger-rgb: 242, 76, 88;--ion-color-danger-contrast: #000;--ion-color-danger-contrast-rgb: 0, 0, 0;--ion-color-danger-shade: #d5434d;--ion-color-danger-tint: #f35e69;--ion-color-light: #222428;--ion-color-light-rgb: 34, 36, 40;--ion-color-light-contrast: #fff;--ion-color-light-contrast-rgb: 255, 255, 255;--ion-color-light-shade: #1e2023;--ion-color-light-tint: #383a3e;--ion-color-medium: #989aa2;--ion-color-medium-rgb: 152, 154, 162;--ion-color-medium-contrast: #000;--ion-color-medium-contrast-rgb: 0, 0, 0;--ion-color-medium-shade: #86888f;--ion-color-medium-tint: #a2a4ab;--ion-color-dark: #f4f5f8;--ion-color-dark-rgb: 244, 245, 248;--ion-color-dark-contrast: #000;--ion-color-dark-contrast-rgb: 0, 0, 0;--ion-color-dark-shade: #d7d8da;--ion-color-dark-tint: #f5f6f9}:root.ios{--ion-background-color: #000000;--ion-background-color-rgb: 0, 0, 0;--ion-text-color: #ffffff;--ion-text-color-rgb: 255, 255, 255;--ion-background-color-step-50: #0d0d0d;--ion-background-color-step-100: #1a1a1a;--ion-background-color-step-150: #262626;--ion-background-color-step-200: #333333;--ion-background-color-step-250: #404040;--ion-background-color-step-300: #4d4d4d;--ion-background-color-step-350: #595959;--ion-background-color-step-400: #666666;--ion-background-color-step-450: #737373;--ion-background-color-step-500: #808080;--ion-background-color-step-550: #8c8c8c;--ion-background-color-step-600: #999999;--ion-background-color-step-650: #a6a6a6;--ion-background-color-step-700: #b3b3b3;--ion-background-color-step-750: #bfbfbf;--ion-background-color-step-800: #cccccc;--ion-background-color-step-850: #d9d9d9;--ion-background-color-step-900: #e6e6e6;--ion-background-color-step-950: #f2f2f2;--ion-text-color-step-50: #f2f2f2;--ion-text-color-step-100: #e6e6e6;--ion-text-color-step-150: #d9d9d9;--ion-text-color-step-200: #cccccc;--ion-text-color-step-250: #bfbfbf;--ion-text-color-step-300: #b3b3b3;--ion-text-color-step-350: #a6a6a6;--ion-text-color-step-400: #999999;--ion-text-color-step-450: #8c8c8c;--ion-text-color-step-500: #808080;--ion-text-color-step-550: #737373;--ion-text-color-step-600: #666666;--ion-text-color-step-650: #595959;--ion-text-color-step-700: #4d4d4d;--ion-text-color-step-750: #404040;--ion-text-color-step-800: #333333;--ion-text-color-step-850: #262626;--ion-text-color-step-900: #1a1a1a;--ion-text-color-step-950: #0d0d0d;--ion-item-background: #000000;--ion-card-background: #1c1c1d}:root.ios ion-modal{--ion-background-color: var(--ion-color-step-100, var(--ion-background-color-step-100));--ion-toolbar-background: var(--ion-color-step-150, var(--ion-background-color-step-150));--ion-toolbar-border-color: var(--ion-color-step-250, var(--ion-background-color-step-250))}:root.md{--ion-background-color: #121212;--ion-background-color-rgb: 18, 18, 18;--ion-text-color: #ffffff;--ion-text-color-rgb: 255, 255, 255;--ion-background-color-step-50: #1e1e1e;--ion-background-color-step-100: #2a2a2a;--ion-background-color-step-150: #363636;--ion-background-color-step-200: #414141;--ion-background-color-step-250: #4d4d4d;--ion-background-color-step-300: #595959;--ion-background-color-step-350: #656565;--ion-background-color-step-400: #717171;--ion-background-color-step-450: #7d7d7d;--ion-background-color-step-500: #898989;--ion-background-color-step-550: #949494;--ion-background-color-step-600: #a0a0a0;--ion-background-color-step-650: #acacac;--ion-background-color-step-700: #b8b8b8;--ion-background-color-step-750: #c4c4c4;--ion-background-color-step-800: #d0d0d0;--ion-background-color-step-850: #dbdbdb;--ion-background-color-step-900: #e7e7e7;--ion-background-color-step-950: #f3f3f3;--ion-text-color-step-50: #f3f3f3;--ion-text-color-step-100: #e7e7e7;--ion-text-color-step-150: #dbdbdb;--ion-text-color-step-200: #d0d0d0;--ion-text-color-step-250: #c4c4c4;--ion-text-color-step-300: #b8b8b8;--ion-text-color-step-350: #acacac;--ion-text-color-step-400: #a0a0a0;--ion-text-color-step-450: #949494;--ion-text-color-step-500: #898989;--ion-text-color-step-550: #7d7d7d;--ion-text-color-step-600: #717171;--ion-text-color-step-650: #656565;--ion-text-color-step-700: #595959;--ion-text-color-step-750: #4d4d4d;--ion-text-color-step-800: #414141;--ion-text-color-step-850: #363636;--ion-text-color-step-900: #2a2a2a;--ion-text-color-step-950: #1e1e1e;--ion-item-background: #1e1e1e;--ion-toolbar-background: #1f1f1f;--ion-tab-bar-background: #1f1f1f;--ion-card-background: #1e1e1e}}\n',document.head.appendChild(n);var r={exports:{}},i={},a={exports:{}},l={},s=Symbol.for("react.element"),c=Symbol.for("react.portal"),u=Symbol.for("react.fragment"),d=Symbol.for("react.strict_mode"),f=Symbol.for("react.profiler"),p=Symbol.for("react.provider"),h=Symbol.for("react.context"),m=Symbol.for("react.forward_ref"),g=Symbol.for("react.suspense"),b=Symbol.for("react.memo"),v=Symbol.for("react.lazy"),y=Symbol.iterator,w={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},x=Object.assign,k={};function E(e,t,n){this.props=e,this.context=t,this.refs=k,this.updater=n||w}function S(){}function C(e,t,n){this.props=e,this.context=t,this.refs=k,this.updater=n||w}E.prototype.isReactComponent={},E.prototype.setState=function(e,t){if("object"!=typeof e&&"function"!=typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},E.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},S.prototype=E.prototype;var $=C.prototype=new S;$.constructor=C,x($,E.prototype),$.isPureReactComponent=!0;var T=Array.isArray,P=Object.prototype.hasOwnProperty,R={current:null},I={key:!0,ref:!0,__self:!0,__source:!0};function O(e,t,n){var o,r={},i=null,a=null;if(null!=t)for(o in void 0!==t.ref&&(a=t.ref),void 0!==t.key&&(i=""+t.key),t)P.call(t,o)&&!I.hasOwnProperty(o)&&(r[o]=t[o]);var l=arguments.length-2;if(1===l)r.children=n;else if(1<l){for(var c=Array(l),u=0;u<l;u++)c[u]=arguments[u+2];r.children=c}if(e&&e.defaultProps)for(o in l=e.defaultProps)void 0===r[o]&&(r[o]=l[o]);return{$$typeof:s,type:e,key:i,ref:a,props:r,_owner:R.current}}function L(e){return"object"==typeof e&&null!==e&&e.$$typeof===s}var _=/\/+/g;function z(e,t){return"object"==typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function N(e,t,n,o,r){var i=typeof e;"undefined"!==i&&"boolean"!==i||(e=null);var a=!1;if(null===e)a=!0;else switch(i){case"string":case"number":a=!0;break;case"object":switch(e.$$typeof){case s:case c:a=!0}}if(a)return r=r(a=e),e=""===o?"."+z(a,0):o,T(r)?(n="",null!=e&&(n=e.replace(_,"$&/")+"/"),N(r,t,n,"",(function(e){return e}))):null!=r&&(L(r)&&(r=function(e,t){return{$$typeof:s,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(r,n+(!r.key||a&&a.key===r.key?"":(""+r.key).replace(_,"$&/")+"/")+e)),t.push(r)),1;if(a=0,o=""===o?".":o+":",T(e))for(var l=0;l<e.length;l++){var u=o+z(i=e[l],l);a+=N(i,t,n,u,r)}else if(u=function(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=y&&e[y]||e["@@iterator"])?e:null}(e),"function"==typeof u)for(e=u.call(e),l=0;!(i=e.next()).done;)a+=N(i=i.value,t,n,u=o+z(i,l++),r);else if("object"===i)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return a}function M(e,t,n){if(null==e)return e;var o=[],r=0;return N(e,o,"","",(function(e){return t.call(n,e,r++)})),o}function j(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var D={current:null},A={transition:null},B={ReactCurrentDispatcher:D,ReactCurrentBatchConfig:A,ReactCurrentOwner:R};function V(){throw Error("act(...) is not supported in production builds of React.")}l.Children={map:M,forEach:function(e,t,n){M(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return M(e,(function(){t++})),t},toArray:function(e){return M(e,(function(e){return e}))||[]},only:function(e){if(!L(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},l.Component=E,l.Fragment=u,l.Profiler=f,l.PureComponent=C,l.StrictMode=d,l.Suspense=g,l.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=B,l.act=V,l.cloneElement=function(e,t,n){if(null==e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var o=x({},e.props),r=e.key,i=e.ref,a=e._owner;if(null!=t){if(void 0!==t.ref&&(i=t.ref,a=R.current),void 0!==t.key&&(r=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(c in t)P.call(t,c)&&!I.hasOwnProperty(c)&&(o[c]=void 0===t[c]&&void 0!==l?l[c]:t[c])}var c=arguments.length-2;if(1===c)o.children=n;else if(1<c){l=Array(c);for(var u=0;u<c;u++)l[u]=arguments[u+2];o.children=l}return{$$typeof:s,type:e.type,key:r,ref:i,props:o,_owner:a}},l.createContext=function(e){return(e={$$typeof:h,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:p,_context:e},e.Consumer=e},l.createElement=O,l.createFactory=function(e){var t=O.bind(null,e);return t.type=e,t},l.createRef=function(){return{current:null}},l.forwardRef=function(e){return{$$typeof:m,render:e}},l.isValidElement=L,l.lazy=function(e){return{$$typeof:v,_payload:{_status:-1,_result:e},_init:j}},l.memo=function(e,t){return{$$typeof:b,type:e,compare:void 0===t?null:t}},l.startTransition=function(e){var t=A.transition;A.transition={};try{e()}finally{A.transition=t}},l.unstable_act=V,l.useCallback=function(e,t){return D.current.useCallback(e,t)},l.useContext=function(e){return D.current.useContext(e)},l.useDebugValue=function(){},l.useDeferredValue=function(e){return D.current.useDeferredValue(e)},l.useEffect=function(e,t){return D.current.useEffect(e,t)},l.useId=function(){return D.current.useId()},l.useImperativeHandle=function(e,t,n){return D.current.useImperativeHandle(e,t,n)},l.useInsertionEffect=function(e,t){return D.current.useInsertionEffect(e,t)},l.useLayoutEffect=function(e,t){return D.current.useLayoutEffect(e,t)},l.useMemo=function(e,t){return D.current.useMemo(e,t)},l.useReducer=function(e,t,n){return D.current.useReducer(e,t,n)},l.useRef=function(e){return D.current.useRef(e)},l.useState=function(e){return D.current.useState(e)},l.useSyncExternalStore=function(e,t,n){return D.current.useSyncExternalStore(e,t,n)},l.useTransition=function(){return D.current.useTransition()},l.version="18.3.1",a.exports=l;var H=a.exports;const F=o(H);
/**
             * @license React
             * react-jsx-runtime.production.min.js
             *
             * Copyright (c) Facebook, Inc. and its affiliates.
             *
             * This source code is licensed under the MIT license found in the
             * LICENSE file in the root directory of this source tree.
             */var W=H,U=Symbol.for("react.element"),q=Symbol.for("react.fragment"),Q=Object.prototype.hasOwnProperty,K=W.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Y={key:!0,ref:!0,__self:!0,__source:!0};function X(e,t,n){var o,r={},i=null,a=null;for(o in void 0!==n&&(i=""+n),void 0!==t.key&&(i=""+t.key),void 0!==t.ref&&(a=t.ref),t)Q.call(t,o)&&!Y.hasOwnProperty(o)&&(r[o]=t[o]);if(e&&e.defaultProps)for(o in t=e.defaultProps)void 0===r[o]&&(r[o]=t[o]);return{$$typeof:U,type:e,key:i,ref:a,props:r,_owner:K.current}}i.Fragment=q,i.jsx=X,i.jsxs=X,r.exports=i;var G=r.exports,Z={exports:{}},J={},ee={exports:{}},te={};
/**
             * @license React
             * scheduler.production.min.js
             *
             * Copyright (c) Facebook, Inc. and its affiliates.
             *
             * This source code is licensed under the MIT license found in the
             * LICENSE file in the root directory of this source tree.
             */
!function(e){function t(e,t){var n=e.length;e.push(t);e:for(;0<n;){var o=n-1>>>1,i=e[o];if(!(0<r(i,t)))break e;e[o]=t,e[n]=i,n=o}}function n(e){return 0===e.length?null:e[0]}function o(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var o=0,i=e.length,a=i>>>1;o<a;){var l=2*(o+1)-1,s=e[l],c=l+1,u=e[c];if(0>r(s,n))c<i&&0>r(u,s)?(e[o]=u,e[c]=n,o=c):(e[o]=s,e[l]=n,o=l);else{if(!(c<i&&0>r(u,n)))break e;e[o]=u,e[c]=n,o=c}}}return t}function r(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"==typeof performance&&"function"==typeof performance.now){var i=performance;e.unstable_now=function(){return i.now()}}else{var a=Date,l=a.now();e.unstable_now=function(){return a.now()-l}}var s=[],c=[],u=1,d=null,f=3,p=!1,h=!1,m=!1,g="function"==typeof setTimeout?setTimeout:null,b="function"==typeof clearTimeout?clearTimeout:null,v="undefined"!=typeof setImmediate?setImmediate:null;function y(e){for(var r=n(c);null!==r;){if(null===r.callback)o(c);else{if(!(r.startTime<=e))break;o(c),r.sortIndex=r.expirationTime,t(s,r)}r=n(c)}}function w(e){if(m=!1,y(e),!h)if(null!==n(s))h=!0,L(x);else{var t=n(c);null!==t&&_(w,t.startTime-e)}}function x(t,r){h=!1,m&&(m=!1,b(C),C=-1),p=!0;var i=f;try{for(y(r),d=n(s);null!==d&&(!(d.expirationTime>r)||t&&!P());){var a=d.callback;if("function"==typeof a){d.callback=null,f=d.priorityLevel;var l=a(d.expirationTime<=r);r=e.unstable_now(),"function"==typeof l?d.callback=l:d===n(s)&&o(s),y(r)}else o(s);d=n(s)}if(null!==d)var u=!0;else{var g=n(c);null!==g&&_(w,g.startTime-r),u=!1}return u}finally{d=null,f=i,p=!1}}"undefined"!=typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var k,E=!1,S=null,C=-1,$=5,T=-1;function P(){return!(e.unstable_now()-T<$)}function R(){if(null!==S){var t=e.unstable_now();T=t;var n=!0;try{n=S(!0,t)}finally{n?k():(E=!1,S=null)}}else E=!1}if("function"==typeof v)k=function(){v(R)};else if("undefined"!=typeof MessageChannel){var I=new MessageChannel,O=I.port2;I.port1.onmessage=R,k=function(){O.postMessage(null)}}else k=function(){g(R,0)};function L(e){S=e,E||(E=!0,k())}function _(t,n){C=g((function(){t(e.unstable_now())}),n)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(e){e.callback=null},e.unstable_continueExecution=function(){h||p||(h=!0,L(x))},e.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):$=0<e?Math.floor(1e3/e):5},e.unstable_getCurrentPriorityLevel=function(){return f},e.unstable_getFirstCallbackNode=function(){return n(s)},e.unstable_next=function(e){switch(f){case 1:case 2:case 3:var t=3;break;default:t=f}var n=f;f=t;try{return e()}finally{f=n}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=f;f=e;try{return t()}finally{f=n}},e.unstable_scheduleCallback=function(o,r,i){var a=e.unstable_now();switch(i="object"==typeof i&&null!==i&&"number"==typeof(i=i.delay)&&0<i?a+i:a,o){case 1:var l=-1;break;case 2:l=250;break;case 5:l=**********;break;case 4:l=1e4;break;default:l=5e3}return o={id:u++,callback:r,priorityLevel:o,startTime:i,expirationTime:l=i+l,sortIndex:-1},i>a?(o.sortIndex=i,t(c,o),null===n(s)&&o===n(c)&&(m?(b(C),C=-1):m=!0,_(w,i-a))):(o.sortIndex=l,t(s,o),h||p||(h=!0,L(x))),o},e.unstable_shouldYield=P,e.unstable_wrapCallback=function(e){var t=f;return function(){var n=f;f=t;try{return e.apply(this,arguments)}finally{f=n}}}}(te),ee.exports=te;var ne=ee.exports,oe=H,re=ne;
/**
             * @license React
             * react-dom.production.min.js
             *
             * Copyright (c) Facebook, Inc. and its affiliates.
             *
             * This source code is licensed under the MIT license found in the
             * LICENSE file in the root directory of this source tree.
             */function ie(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var ae=new Set,le={};function se(e,t){ce(e,t),ce(e+"Capture",t)}function ce(e,t){for(le[e]=t,e=0;e<t.length;e++)ae.add(t[e])}var ue=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement),de=Object.prototype.hasOwnProperty,fe=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,pe={},he={};function me(e,t,n,o,r,i,a){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=o,this.attributeNamespace=r,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=i,this.removeEmptyString=a}var ge={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){ge[e]=new me(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];ge[t]=new me(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){ge[e]=new me(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){ge[e]=new me(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){ge[e]=new me(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){ge[e]=new me(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){ge[e]=new me(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){ge[e]=new me(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){ge[e]=new me(e,5,!1,e.toLowerCase(),null,!1,!1)}));var be=/[\-:]([a-z])/g;function ve(e){return e[1].toUpperCase()}function ye(e,t,n,o){var r=ge.hasOwnProperty(t)?ge[t]:null;(null!==r?0!==r.type:o||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,o){if(null==t||function(e,t,n,o){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!o&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,o))return!0;if(o)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,r,o)&&(n=null),o||null===r?function(e){return!!de.call(he,e)||!de.call(pe,e)&&(fe.test(e)?he[e]=!0:(pe[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):r.mustUseProperty?e[r.propertyName]=null===n?3!==r.type&&"":n:(t=r.attributeName,o=r.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(r=r.type)||4===r&&!0===n?"":""+n,o?e.setAttributeNS(o,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(be,ve);ge[t]=new me(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(be,ve);ge[t]=new me(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(be,ve);ge[t]=new me(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){ge[e]=new me(e,1,!1,e.toLowerCase(),null,!1,!1)})),ge.xlinkHref=new me("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){ge[e]=new me(e,1,!1,e.toLowerCase(),null,!0,!0)}));var we=oe.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,xe=Symbol.for("react.element"),ke=Symbol.for("react.portal"),Ee=Symbol.for("react.fragment"),Se=Symbol.for("react.strict_mode"),Ce=Symbol.for("react.profiler"),$e=Symbol.for("react.provider"),Te=Symbol.for("react.context"),Pe=Symbol.for("react.forward_ref"),Re=Symbol.for("react.suspense"),Ie=Symbol.for("react.suspense_list"),Oe=Symbol.for("react.memo"),Le=Symbol.for("react.lazy"),_e=Symbol.for("react.offscreen"),ze=Symbol.iterator;function Ne(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=ze&&e[ze]||e["@@iterator"])?e:null}var Me,je=Object.assign;function De(e){if(void 0===Me)try{throw Error()}catch(wf){var t=wf.stack.trim().match(/\n( *(at )?)/);Me=t&&t[1]||""}return"\n"+Me+e}var Ae=!1;function Be(e,t){if(!e||Ae)return"";Ae=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(Tf){var o=Tf}Reflect.construct(e,[],t)}else{try{t.call()}catch(Tf){o=Tf}e.call(t.prototype)}else{try{throw Error()}catch(Tf){o=Tf}e()}}catch(Tf){if(Tf&&o&&"string"==typeof Tf.stack){for(var r=Tf.stack.split("\n"),i=o.stack.split("\n"),a=r.length-1,l=i.length-1;1<=a&&0<=l&&r[a]!==i[l];)l--;for(;1<=a&&0<=l;a--,l--)if(r[a]!==i[l]){if(1!==a||1!==l)do{if(a--,0>--l||r[a]!==i[l]){var s="\n"+r[a].replace(" at new "," at ");return e.displayName&&s.includes("<anonymous>")&&(s=s.replace("<anonymous>",e.displayName)),s}}while(1<=a&&0<=l);break}}}finally{Ae=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?De(e):""}function Ve(e){switch(e.tag){case 5:return De(e.type);case 16:return De("Lazy");case 13:return De("Suspense");case 19:return De("SuspenseList");case 0:case 2:case 15:return e=Be(e.type,!1);case 11:return e=Be(e.type.render,!1);case 1:return e=Be(e.type,!0);default:return""}}function He(e){if(null==e)return null;if("function"==typeof e)return e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case Ee:return"Fragment";case ke:return"Portal";case Ce:return"Profiler";case Se:return"StrictMode";case Re:return"Suspense";case Ie:return"SuspenseList"}if("object"==typeof e)switch(e.$$typeof){case Te:return(e.displayName||"Context")+".Consumer";case $e:return(e._context.displayName||"Context")+".Provider";case Pe:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case Oe:return null!==(t=e.displayName||null)?t:He(e.type)||"Memo";case Le:t=e._payload,e=e._init;try{return He(e(t))}catch(wf){}}return null}function Fe(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return He(t);case 8:return t===Se?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"==typeof t)return t.displayName||t.name||null;if("string"==typeof t)return t}return null}function We(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function Ue(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function qe(e){e._valueTracker||(e._valueTracker=function(e){var t=Ue(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),o=""+e[t];if(!e.hasOwnProperty(t)&&void 0!==n&&"function"==typeof n.get&&"function"==typeof n.set){var r=n.get,i=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return r.call(this)},set:function(e){o=""+e,i.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return o},setValue:function(e){o=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function Qe(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),o="";return e&&(o=Ue(e)?e.checked?"true":"false":e.value),(e=o)!==n&&(t.setValue(e),!0)}function Ke(e){if(void 0===(e=e||("undefined"!=typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(yf){return e.body}}function Ye(e,t){var n=t.checked;return je({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function Xe(e,t){var n=null==t.defaultValue?"":t.defaultValue,o=null!=t.checked?t.checked:t.defaultChecked;n=We(null!=t.value?t.value:n),e._wrapperState={initialChecked:o,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function Ge(e,t){null!=(t=t.checked)&&ye(e,"checked",t,!1)}function Ze(e,t){Ge(e,t);var n=We(t.value),o=t.type;if(null!=n)"number"===o?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===o||"reset"===o)return void e.removeAttribute("value");t.hasOwnProperty("value")?et(e,t.type,n):t.hasOwnProperty("defaultValue")&&et(e,t.type,We(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Je(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var o=t.type;if(!("submit"!==o&&"reset"!==o||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function et(e,t,n){"number"===t&&Ke(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var tt=Array.isArray;function nt(e,t,n,o){if(e=e.options,t){t={};for(var r=0;r<n.length;r++)t["$"+n[r]]=!0;for(n=0;n<e.length;n++)r=t.hasOwnProperty("$"+e[n].value),e[n].selected!==r&&(e[n].selected=r),r&&o&&(e[n].defaultSelected=!0)}else{for(n=""+We(n),t=null,r=0;r<e.length;r++){if(e[r].value===n)return e[r].selected=!0,void(o&&(e[r].defaultSelected=!0));null!==t||e[r].disabled||(t=e[r])}null!==t&&(t.selected=!0)}}function ot(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(ie(91));return je({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function rt(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(ie(92));if(tt(n)){if(1<n.length)throw Error(ie(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:We(n)}}function it(e,t){var n=We(t.value),o=We(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=o&&(e.defaultValue=""+o)}function at(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function lt(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function st(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?lt(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ct,ut,dt=(ut=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ct=ct||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ct.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!=typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,o){MSApp.execUnsafeLocalFunction((function(){return ut(e,t)}))}:ut);function ft(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var pt={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},ht=["Webkit","ms","Moz","O"];function mt(e,t,n){return null==t||"boolean"==typeof t||""===t?"":n||"number"!=typeof t||0===t||pt.hasOwnProperty(e)&&pt[e]?(""+t).trim():t+"px"}function gt(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var o=0===n.indexOf("--"),r=mt(n,t[n],o);"float"===n&&(n="cssFloat"),o?e.setProperty(n,r):e[n]=r}}Object.keys(pt).forEach((function(e){ht.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),pt[t]=pt[e]}))}));var bt=je({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function vt(e,t){if(t){if(bt[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(ie(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(ie(60));if("object"!=typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(ie(61))}if(null!=t.style&&"object"!=typeof t.style)throw Error(ie(62))}}function yt(e,t){if(-1===e.indexOf("-"))return"string"==typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var wt=null;function xt(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var kt=null,Et=null,St=null;function Ct(e){if(e=wi(e)){if("function"!=typeof kt)throw Error(ie(280));var t=e.stateNode;t&&(t=ki(t),kt(e.stateNode,e.type,t))}}function $t(e){Et?St?St.push(e):St=[e]:Et=e}function Tt(){if(Et){var e=Et,t=St;if(St=Et=null,Ct(e),t)for(e=0;e<t.length;e++)Ct(t[e])}}function Pt(e,t){return e(t)}function Rt(){}var It=!1;function Ot(e,t,n){if(It)return e(t,n);It=!0;try{return Pt(e,t,n)}finally{It=!1,(null!==Et||null!==St)&&(Rt(),Tt())}}function Lt(e,t){var n=e.stateNode;if(null===n)return null;var o=ki(n);if(null===o)return null;n=o[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(o=!o.disabled)||(o=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!o;break e;default:e=!1}if(e)return null;if(n&&"function"!=typeof n)throw Error(ie(231,t,typeof n));return n}var _t=!1;if(ue)try{var zt={};Object.defineProperty(zt,"passive",{get:function(){_t=!0}}),window.addEventListener("test",zt,zt),window.removeEventListener("test",zt,zt)}catch(ut){_t=!1}function Nt(e,t,n,o,r,i,a,l,s){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(Pf){this.onError(Pf)}}var Mt=!1,jt=null,Dt=!1,At=null,Bt={onError:function(e){Mt=!0,jt=e}};function Vt(e,t,n,o,r,i,a,l,s){Mt=!1,jt=null,Nt.apply(Bt,arguments)}function Ht(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{!!(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function Ft(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&null!==(e=e.alternate)&&(t=e.memoizedState),null!==t)return t.dehydrated}return null}function Wt(e){if(Ht(e)!==e)throw Error(ie(188))}function Ut(e){return e=function(e){var t=e.alternate;if(!t){if(null===(t=Ht(e)))throw Error(ie(188));return t!==e?null:e}for(var n=e,o=t;;){var r=n.return;if(null===r)break;var i=r.alternate;if(null===i){if(null!==(o=r.return)){n=o;continue}break}if(r.child===i.child){for(i=r.child;i;){if(i===n)return Wt(r),e;if(i===o)return Wt(r),t;i=i.sibling}throw Error(ie(188))}if(n.return!==o.return)n=r,o=i;else{for(var a=!1,l=r.child;l;){if(l===n){a=!0,n=r,o=i;break}if(l===o){a=!0,o=r,n=i;break}l=l.sibling}if(!a){for(l=i.child;l;){if(l===n){a=!0,n=i,o=r;break}if(l===o){a=!0,o=i,n=r;break}l=l.sibling}if(!a)throw Error(ie(189))}}if(n.alternate!==o)throw Error(ie(190))}if(3!==n.tag)throw Error(ie(188));return n.stateNode.current===n?e:t}(e),null!==e?qt(e):null}function qt(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=qt(e);if(null!==t)return t;e=e.sibling}return null}var Qt=re.unstable_scheduleCallback,Kt=re.unstable_cancelCallback,Yt=re.unstable_shouldYield,Xt=re.unstable_requestPaint,Gt=re.unstable_now,Zt=re.unstable_getCurrentPriorityLevel,Jt=re.unstable_ImmediatePriority,en=re.unstable_UserBlockingPriority,tn=re.unstable_NormalPriority,nn=re.unstable_LowPriority,on=re.unstable_IdlePriority,rn=null,an=null,ln=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(sn(e)/cn|0)|0},sn=Math.log,cn=Math.LN2,un=64,dn=4194304;function fn(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function pn(e,t){var n=e.pendingLanes;if(0===n)return 0;var o=0,r=e.suspendedLanes,i=e.pingedLanes,a=268435455&n;if(0!==a){var l=a&~r;0!==l?o=fn(l):0!=(i&=a)&&(o=fn(i))}else 0!=(a=n&~r)?o=fn(a):0!==i&&(o=fn(i));if(0===o)return 0;if(0!==t&&t!==o&&!(t&r)&&((r=o&-o)>=(i=t&-t)||16===r&&4194240&i))return t;if(4&o&&(o|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=o;0<t;)r=1<<(n=31-ln(t)),o|=e[n],t&=~r;return o}function hn(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function mn(e){return 0!=(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function gn(){var e=un;return!(4194240&(un<<=1))&&(un=64),e}function bn(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function vn(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-ln(t)]=n}function yn(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var o=31-ln(n),r=1<<o;r&t|e[o]&t&&(e[o]|=t),n&=~r}}var wn=0;function xn(e){return 1<(e&=-e)?4<e?268435455&e?16:536870912:4:1}var kn,En,Sn,Cn,$n,Tn=!1,Pn=[],Rn=null,In=null,On=null,Ln=new Map,_n=new Map,zn=[],Nn="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Mn(e,t){switch(e){case"focusin":case"focusout":Rn=null;break;case"dragenter":case"dragleave":In=null;break;case"mouseover":case"mouseout":On=null;break;case"pointerover":case"pointerout":Ln.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":_n.delete(t.pointerId)}}function jn(e,t,n,o,r,i){return null===e||e.nativeEvent!==i?(e={blockedOn:t,domEventName:n,eventSystemFlags:o,nativeEvent:i,targetContainers:[r]},null!==t&&null!==(t=wi(t))&&En(t),e):(e.eventSystemFlags|=o,t=e.targetContainers,null!==r&&-1===t.indexOf(r)&&t.push(r),e)}function Dn(e){var t=yi(e.target);if(null!==t){var n=Ht(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=Ft(n)))return e.blockedOn=t,void $n(e.priority,(function(){Sn(n)}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function An(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Xn(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=wi(n))&&En(t),e.blockedOn=n,!1;var o=new(n=e.nativeEvent).constructor(n.type,n);wt=o,n.target.dispatchEvent(o),wt=null,t.shift()}return!0}function Bn(e,t,n){An(e)&&n.delete(t)}function Vn(){Tn=!1,null!==Rn&&An(Rn)&&(Rn=null),null!==In&&An(In)&&(In=null),null!==On&&An(On)&&(On=null),Ln.forEach(Bn),_n.forEach(Bn)}function Hn(e,t){e.blockedOn===t&&(e.blockedOn=null,Tn||(Tn=!0,re.unstable_scheduleCallback(re.unstable_NormalPriority,Vn)))}function Fn(e){function t(t){return Hn(t,e)}if(0<Pn.length){Hn(Pn[0],e);for(var n=1;n<Pn.length;n++){var o=Pn[n];o.blockedOn===e&&(o.blockedOn=null)}}for(null!==Rn&&Hn(Rn,e),null!==In&&Hn(In,e),null!==On&&Hn(On,e),Ln.forEach(t),_n.forEach(t),n=0;n<zn.length;n++)(o=zn[n]).blockedOn===e&&(o.blockedOn=null);for(;0<zn.length&&null===(n=zn[0]).blockedOn;)Dn(n),null===n.blockedOn&&zn.shift()}var Wn=we.ReactCurrentBatchConfig,Un=!0;function qn(e,t,n,o){var r=wn,i=Wn.transition;Wn.transition=null;try{wn=1,Kn(e,t,n,o)}finally{wn=r,Wn.transition=i}}function Qn(e,t,n,o){var r=wn,i=Wn.transition;Wn.transition=null;try{wn=4,Kn(e,t,n,o)}finally{wn=r,Wn.transition=i}}function Kn(e,t,n,o){if(Un){var r=Xn(e,t,n,o);if(null===r)Ur(e,t,o,Yn,n),Mn(e,o);else if(function(e,t,n,o,r){switch(t){case"focusin":return Rn=jn(Rn,e,t,n,o,r),!0;case"dragenter":return In=jn(In,e,t,n,o,r),!0;case"mouseover":return On=jn(On,e,t,n,o,r),!0;case"pointerover":var i=r.pointerId;return Ln.set(i,jn(Ln.get(i)||null,e,t,n,o,r)),!0;case"gotpointercapture":return i=r.pointerId,_n.set(i,jn(_n.get(i)||null,e,t,n,o,r)),!0}return!1}(r,e,t,n,o))o.stopPropagation();else if(Mn(e,o),4&t&&-1<Nn.indexOf(e)){for(;null!==r;){var i=wi(r);if(null!==i&&kn(i),null===(i=Xn(e,t,n,o))&&Ur(e,t,o,Yn,n),i===r)break;r=i}null!==r&&o.stopPropagation()}else Ur(e,t,o,null,n)}}var Yn=null;function Xn(e,t,n,o){if(Yn=null,null!==(e=yi(e=xt(o))))if(null===(t=Ht(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=Ft(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Yn=e,null}function Gn(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Zt()){case Jt:return 1;case en:return 4;case tn:case nn:return 16;case on:return 536870912;default:return 16}default:return 16}}var Zn=null,Jn=null,eo=null;function to(){if(eo)return eo;var e,t,n=Jn,o=n.length,r="value"in Zn?Zn.value:Zn.textContent,i=r.length;for(e=0;e<o&&n[e]===r[e];e++);var a=o-e;for(t=1;t<=a&&n[o-t]===r[i-t];t++);return eo=r.slice(e,1<t?1-t:void 0)}function no(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function oo(){return!0}function ro(){return!1}function io(e){function t(t,n,o,r,i){for(var a in this._reactName=t,this._targetInst=o,this.type=n,this.nativeEvent=r,this.target=i,this.currentTarget=null,e)e.hasOwnProperty(a)&&(t=e[a],this[a]=t?t(r):r[a]);return this.isDefaultPrevented=(null!=r.defaultPrevented?r.defaultPrevented:!1===r.returnValue)?oo:ro,this.isPropagationStopped=ro,this}return je(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!=typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=oo)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!=typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=oo)},persist:function(){},isPersistent:oo}),t}var ao,lo,so,co={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},uo=io(co),fo=je({},co,{view:0,detail:0}),po=io(fo),ho=je({},fo,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:$o,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==so&&(so&&"mousemove"===e.type?(ao=e.screenX-so.screenX,lo=e.screenY-so.screenY):lo=ao=0,so=e),ao)},movementY:function(e){return"movementY"in e?e.movementY:lo}}),mo=io(ho),go=io(je({},ho,{dataTransfer:0})),bo=io(je({},fo,{relatedTarget:0})),vo=io(je({},co,{animationName:0,elapsedTime:0,pseudoElement:0})),yo=je({},co,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),wo=io(yo),xo=io(je({},co,{data:0})),ko={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Eo={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},So={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Co(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=So[e])&&!!t[e]}function $o(){return Co}var To=je({},fo,{key:function(e){if(e.key){var t=ko[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=no(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?Eo[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:$o,charCode:function(e){return"keypress"===e.type?no(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?no(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),Po=io(To),Ro=io(je({},ho,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Io=io(je({},fo,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:$o})),Oo=io(je({},co,{propertyName:0,elapsedTime:0,pseudoElement:0})),Lo=je({},ho,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),_o=io(Lo),zo=[9,13,27,32],No=ue&&"CompositionEvent"in window,Mo=null;ue&&"documentMode"in document&&(Mo=document.documentMode);var jo=ue&&"TextEvent"in window&&!Mo,Do=ue&&(!No||Mo&&8<Mo&&11>=Mo),Ao=String.fromCharCode(32),Bo=!1;function Vo(e,t){switch(e){case"keyup":return-1!==zo.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ho(e){return"object"==typeof(e=e.detail)&&"data"in e?e.data:null}var Fo=!1,Wo={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Uo(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Wo[e.type]:"textarea"===t}function qo(e,t,n,o){$t(o),0<(t=Qr(t,"onChange")).length&&(n=new uo("onChange","change",null,n,o),e.push({event:n,listeners:t}))}var Qo=null,Ko=null;function Yo(e){Ar(e,0)}function Xo(e){if(Qe(xi(e)))return e}function Go(e,t){if("change"===e)return t}var Zo=!1;if(ue){var Jo;if(ue){var er="oninput"in document;if(!er){var tr=document.createElement("div");tr.setAttribute("oninput","return;"),er="function"==typeof tr.oninput}Jo=er}else Jo=!1;Zo=Jo&&(!document.documentMode||9<document.documentMode)}function nr(){Qo&&(Qo.detachEvent("onpropertychange",or),Ko=Qo=null)}function or(e){if("value"===e.propertyName&&Xo(Ko)){var t=[];qo(t,Ko,e,xt(e)),Ot(Yo,t)}}function rr(e,t,n){"focusin"===e?(nr(),Ko=n,(Qo=t).attachEvent("onpropertychange",or)):"focusout"===e&&nr()}function ir(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Xo(Ko)}function ar(e,t){if("click"===e)return Xo(t)}function lr(e,t){if("input"===e||"change"===e)return Xo(t)}var sr="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t};function cr(e,t){if(sr(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),o=Object.keys(t);if(n.length!==o.length)return!1;for(o=0;o<n.length;o++){var r=n[o];if(!de.call(t,r)||!sr(e[r],t[r]))return!1}return!0}function ur(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function dr(e,t){var n,o=ur(e);for(e=0;o;){if(3===o.nodeType){if(n=e+o.textContent.length,e<=t&&n>=t)return{node:o,offset:t-e};e=n}e:{for(;o;){if(o.nextSibling){o=o.nextSibling;break e}o=o.parentNode}o=void 0}o=ur(o)}}function fr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?fr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function pr(){for(var e=window,t=Ke();t instanceof e.HTMLIFrameElement;){try{var n="string"==typeof t.contentWindow.location.href}catch(xf){n=!1}if(!n)break;t=Ke((e=t.contentWindow).document)}return t}function hr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function mr(e){var t=pr(),n=e.focusedElem,o=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&fr(n.ownerDocument.documentElement,n)){if(null!==o&&hr(n))if(t=o.start,void 0===(e=o.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var r=n.textContent.length,i=Math.min(o.start,r);o=void 0===o.end?i:Math.min(o.end,r),!e.extend&&i>o&&(r=o,o=i,i=r),r=dr(n,i);var a=dr(n,o);r&&a&&(1!==e.rangeCount||e.anchorNode!==r.node||e.anchorOffset!==r.offset||e.focusNode!==a.node||e.focusOffset!==a.offset)&&((t=t.createRange()).setStart(r.node,r.offset),e.removeAllRanges(),i>o?(e.addRange(t),e.extend(a.node,a.offset)):(t.setEnd(a.node,a.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"==typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var gr=ue&&"documentMode"in document&&11>=document.documentMode,br=null,vr=null,yr=null,wr=!1;function xr(e,t,n){var o=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;wr||null==br||br!==Ke(o)||(o="selectionStart"in(o=br)&&hr(o)?{start:o.selectionStart,end:o.selectionEnd}:{anchorNode:(o=(o.ownerDocument&&o.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:o.anchorOffset,focusNode:o.focusNode,focusOffset:o.focusOffset},yr&&cr(yr,o)||(yr=o,0<(o=Qr(vr,"onSelect")).length&&(t=new uo("onSelect","select",null,t,n),e.push({event:t,listeners:o}),t.target=br)))}function kr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Er={animationend:kr("Animation","AnimationEnd"),animationiteration:kr("Animation","AnimationIteration"),animationstart:kr("Animation","AnimationStart"),transitionend:kr("Transition","TransitionEnd")},Sr={},Cr={};function $r(e){if(Sr[e])return Sr[e];if(!Er[e])return e;var t,n=Er[e];for(t in n)if(n.hasOwnProperty(t)&&t in Cr)return Sr[e]=n[t];return e}ue&&(Cr=document.createElement("div").style,"AnimationEvent"in window||(delete Er.animationend.animation,delete Er.animationiteration.animation,delete Er.animationstart.animation),"TransitionEvent"in window||delete Er.transitionend.transition);var Tr=$r("animationend"),Pr=$r("animationiteration"),Rr=$r("animationstart"),Ir=$r("transitionend"),Or=new Map,Lr="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function _r(e,t){Or.set(e,t),se(t,[e])}for(var zr=0;zr<Lr.length;zr++){var Nr=Lr[zr];_r(Nr.toLowerCase(),"on"+(Nr[0].toUpperCase()+Nr.slice(1)))}_r(Tr,"onAnimationEnd"),_r(Pr,"onAnimationIteration"),_r(Rr,"onAnimationStart"),_r("dblclick","onDoubleClick"),_r("focusin","onFocus"),_r("focusout","onBlur"),_r(Ir,"onTransitionEnd"),ce("onMouseEnter",["mouseout","mouseover"]),ce("onMouseLeave",["mouseout","mouseover"]),ce("onPointerEnter",["pointerout","pointerover"]),ce("onPointerLeave",["pointerout","pointerover"]),se("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),se("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),se("onBeforeInput",["compositionend","keypress","textInput","paste"]),se("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),se("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),se("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Mr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),jr=new Set("cancel close invalid load scroll toggle".split(" ").concat(Mr));function Dr(e,t,n){var o=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,o,r,i,a,l,s){if(Vt.apply(this,arguments),Mt){if(!Mt)throw Error(ie(198));var c=jt;Mt=!1,jt=null,Dt||(Dt=!0,At=c)}}(o,t,void 0,e),e.currentTarget=null}function Ar(e,t){t=!!(4&t);for(var n=0;n<e.length;n++){var o=e[n],r=o.event;o=o.listeners;e:{var i=void 0;if(t)for(var a=o.length-1;0<=a;a--){var l=o[a],s=l.instance,c=l.currentTarget;if(l=l.listener,s!==i&&r.isPropagationStopped())break e;Dr(r,l,c),i=s}else for(a=0;a<o.length;a++){if(s=(l=o[a]).instance,c=l.currentTarget,l=l.listener,s!==i&&r.isPropagationStopped())break e;Dr(r,l,c),i=s}}}if(Dt)throw e=At,Dt=!1,At=null,e}function Br(e,t){var n=t[gi];void 0===n&&(n=t[gi]=new Set);var o=e+"__bubble";n.has(o)||(Wr(t,e,2,!1),n.add(o))}function Vr(e,t,n){var o=0;t&&(o|=4),Wr(n,e,o,t)}var Hr="_reactListening"+Math.random().toString(36).slice(2);function Fr(e){if(!e[Hr]){e[Hr]=!0,ae.forEach((function(t){"selectionchange"!==t&&(jr.has(t)||Vr(t,!1,e),Vr(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Hr]||(t[Hr]=!0,Vr("selectionchange",!1,t))}}function Wr(e,t,n,o){switch(Gn(t)){case 1:var r=qn;break;case 4:r=Qn;break;default:r=Kn}n=r.bind(null,t,n,e),r=void 0,!_t||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(r=!0),o?void 0!==r?e.addEventListener(t,n,{capture:!0,passive:r}):e.addEventListener(t,n,!0):void 0!==r?e.addEventListener(t,n,{passive:r}):e.addEventListener(t,n,!1)}function Ur(e,t,n,o,r){var i=o;if(!(1&t||2&t||null===o))e:for(;;){if(null===o)return;var a=o.tag;if(3===a||4===a){var l=o.stateNode.containerInfo;if(l===r||8===l.nodeType&&l.parentNode===r)break;if(4===a)for(a=o.return;null!==a;){var s=a.tag;if((3===s||4===s)&&((s=a.stateNode.containerInfo)===r||8===s.nodeType&&s.parentNode===r))return;a=a.return}for(;null!==l;){if(null===(a=yi(l)))return;if(5===(s=a.tag)||6===s){o=i=a;continue e}l=l.parentNode}}o=o.return}Ot((function(){var o=i,r=xt(n),a=[];e:{var l=Or.get(e);if(void 0!==l){var s=uo,c=e;switch(e){case"keypress":if(0===no(n))break e;case"keydown":case"keyup":s=Po;break;case"focusin":c="focus",s=bo;break;case"focusout":c="blur",s=bo;break;case"beforeblur":case"afterblur":s=bo;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":s=mo;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":s=go;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":s=Io;break;case Tr:case Pr:case Rr:s=vo;break;case Ir:s=Oo;break;case"scroll":s=po;break;case"wheel":s=_o;break;case"copy":case"cut":case"paste":s=wo;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":s=Ro}var u=!!(4&t),d=!u&&"scroll"===e,f=u?null!==l?l+"Capture":null:l;u=[];for(var p,h=o;null!==h;){var m=(p=h).stateNode;if(5===p.tag&&null!==m&&(p=m,null!==f&&null!=(m=Lt(h,f))&&u.push(qr(h,m,p))),d)break;h=h.return}0<u.length&&(l=new s(l,c,null,n,r),a.push({event:l,listeners:u}))}}if(!(7&t)){if(s="mouseout"===e||"pointerout"===e,(!(l="mouseover"===e||"pointerover"===e)||n===wt||!(c=n.relatedTarget||n.fromElement)||!yi(c)&&!c[mi])&&(s||l)&&(l=r.window===r?r:(l=r.ownerDocument)?l.defaultView||l.parentWindow:window,s?(s=o,null!==(c=(c=n.relatedTarget||n.toElement)?yi(c):null)&&(c!==(d=Ht(c))||5!==c.tag&&6!==c.tag)&&(c=null)):(s=null,c=o),s!==c)){if(u=mo,m="onMouseLeave",f="onMouseEnter",h="mouse","pointerout"!==e&&"pointerover"!==e||(u=Ro,m="onPointerLeave",f="onPointerEnter",h="pointer"),d=null==s?l:xi(s),p=null==c?l:xi(c),(l=new u(m,h+"leave",s,n,r)).target=d,l.relatedTarget=p,m=null,yi(r)===o&&((u=new u(f,h+"enter",c,n,r)).target=p,u.relatedTarget=d,m=u),d=m,s&&c)e:{for(f=c,h=0,p=u=s;p;p=Kr(p))h++;for(p=0,m=f;m;m=Kr(m))p++;for(;0<h-p;)u=Kr(u),h--;for(;0<p-h;)f=Kr(f),p--;for(;h--;){if(u===f||null!==f&&u===f.alternate)break e;u=Kr(u),f=Kr(f)}u=null}else u=null;null!==s&&Yr(a,l,s,u,!1),null!==c&&null!==d&&Yr(a,d,c,u,!0)}if("select"===(s=(l=o?xi(o):window).nodeName&&l.nodeName.toLowerCase())||"input"===s&&"file"===l.type)var g=Go;else if(Uo(l))if(Zo)g=lr;else{g=ir;var b=rr}else(s=l.nodeName)&&"input"===s.toLowerCase()&&("checkbox"===l.type||"radio"===l.type)&&(g=ar);switch(g&&(g=g(e,o))?qo(a,g,n,r):(b&&b(e,l,o),"focusout"===e&&(b=l._wrapperState)&&b.controlled&&"number"===l.type&&et(l,"number",l.value)),b=o?xi(o):window,e){case"focusin":(Uo(b)||"true"===b.contentEditable)&&(br=b,vr=o,yr=null);break;case"focusout":yr=vr=br=null;break;case"mousedown":wr=!0;break;case"contextmenu":case"mouseup":case"dragend":wr=!1,xr(a,n,r);break;case"selectionchange":if(gr)break;case"keydown":case"keyup":xr(a,n,r)}var v;if(No)e:{switch(e){case"compositionstart":var y="onCompositionStart";break e;case"compositionend":y="onCompositionEnd";break e;case"compositionupdate":y="onCompositionUpdate";break e}y=void 0}else Fo?Vo(e,n)&&(y="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(y="onCompositionStart");y&&(Do&&"ko"!==n.locale&&(Fo||"onCompositionStart"!==y?"onCompositionEnd"===y&&Fo&&(v=to()):(Jn="value"in(Zn=r)?Zn.value:Zn.textContent,Fo=!0)),0<(b=Qr(o,y)).length&&(y=new xo(y,e,null,n,r),a.push({event:y,listeners:b}),(v||null!==(v=Ho(n)))&&(y.data=v))),(v=jo?function(e,t){switch(e){case"compositionend":return Ho(t);case"keypress":return 32!==t.which?null:(Bo=!0,Ao);case"textInput":return(e=t.data)===Ao&&Bo?null:e;default:return null}}(e,n):function(e,t){if(Fo)return"compositionend"===e||!No&&Vo(e,t)?(e=to(),eo=Jn=Zn=null,Fo=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Do&&"ko"!==t.locale?null:t.data}}(e,n))&&0<(o=Qr(o,"onBeforeInput")).length&&(r=new xo("onBeforeInput","beforeinput",null,n,r),a.push({event:r,listeners:o}),r.data=v)}Ar(a,t)}))}function qr(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Qr(e,t){for(var n=t+"Capture",o=[];null!==e;){var r=e,i=r.stateNode;5===r.tag&&null!==i&&(r=i,null!=(i=Lt(e,n))&&o.unshift(qr(e,i,r)),null!=(i=Lt(e,t))&&o.push(qr(e,i,r))),e=e.return}return o}function Kr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Yr(e,t,n,o,r){for(var i=t._reactName,a=[];null!==n&&n!==o;){var l=n,s=l.alternate,c=l.stateNode;if(null!==s&&s===o)break;5===l.tag&&null!==c&&(l=c,r?null!=(s=Lt(n,i))&&a.unshift(qr(n,s,l)):r||null!=(s=Lt(n,i))&&a.push(qr(n,s,l))),n=n.return}0!==a.length&&e.push({event:t,listeners:a})}var Xr=/\r\n?/g,Gr=/\u0000|\uFFFD/g;function Zr(e){return("string"==typeof e?e:""+e).replace(Xr,"\n").replace(Gr,"")}function Jr(e,t,n){if(t=Zr(t),Zr(e)!==t&&n)throw Error(ie(425))}function ei(){}var ti=null,ni=null;function oi(e,t){return"textarea"===e||"noscript"===e||"string"==typeof t.children||"number"==typeof t.children||"object"==typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ri="function"==typeof setTimeout?setTimeout:void 0,ii="function"==typeof clearTimeout?clearTimeout:void 0,ai="function"==typeof Promise?Promise:void 0,li="function"==typeof queueMicrotask?queueMicrotask:void 0!==ai?function(e){return ai.resolve(null).then(e).catch(si)}:ri;function si(e){setTimeout((function(){throw e}))}function ci(e,t){var n=t,o=0;do{var r=n.nextSibling;if(e.removeChild(n),r&&8===r.nodeType)if("/$"===(n=r.data)){if(0===o)return e.removeChild(r),void Fn(t);o--}else"$"!==n&&"$?"!==n&&"$!"!==n||o++;n=r}while(n);Fn(t)}function ui(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function di(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var fi=Math.random().toString(36).slice(2),pi="__reactFiber$"+fi,hi="__reactProps$"+fi,mi="__reactContainer$"+fi,gi="__reactEvents$"+fi,bi="__reactListeners$"+fi,vi="__reactHandles$"+fi;function yi(e){var t=e[pi];if(t)return t;for(var n=e.parentNode;n;){if(t=n[mi]||n[pi]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=di(e);null!==e;){if(n=e[pi])return n;e=di(e)}return t}n=(e=n).parentNode}return null}function wi(e){return!(e=e[pi]||e[mi])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function xi(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(ie(33))}function ki(e){return e[hi]||null}var Ei=[],Si=-1;function Ci(e){return{current:e}}function $i(e){0>Si||(e.current=Ei[Si],Ei[Si]=null,Si--)}function Ti(e,t){Si++,Ei[Si]=e.current,e.current=t}var Pi={},Ri=Ci(Pi),Ii=Ci(!1),Oi=Pi;function Li(e,t){var n=e.type.contextTypes;if(!n)return Pi;var o=e.stateNode;if(o&&o.__reactInternalMemoizedUnmaskedChildContext===t)return o.__reactInternalMemoizedMaskedChildContext;var r,i={};for(r in n)i[r]=t[r];return o&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function _i(e){return null!=(e=e.childContextTypes)}function zi(){$i(Ii),$i(Ri)}function Ni(e,t,n){if(Ri.current!==Pi)throw Error(ie(168));Ti(Ri,t),Ti(Ii,n)}function Mi(e,t,n){var o=e.stateNode;if(t=t.childContextTypes,"function"!=typeof o.getChildContext)return n;for(var r in o=o.getChildContext())if(!(r in t))throw Error(ie(108,Fe(e)||"Unknown",r));return je({},n,o)}function ji(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Pi,Oi=Ri.current,Ti(Ri,e),Ti(Ii,Ii.current),!0}function Di(e,t,n){var o=e.stateNode;if(!o)throw Error(ie(169));n?(e=Mi(e,t,Oi),o.__reactInternalMemoizedMergedChildContext=e,$i(Ii),$i(Ri),Ti(Ri,e)):$i(Ii),Ti(Ii,n)}var Ai=null,Bi=!1,Vi=!1;function Hi(e){null===Ai?Ai=[e]:Ai.push(e)}function Fi(){if(!Vi&&null!==Ai){Vi=!0;var e=0,t=wn;try{var n=Ai;for(wn=1;e<n.length;e++){var o=n[e];do{o=o(!0)}while(null!==o)}Ai=null,Bi=!1}catch(kf){throw null!==Ai&&(Ai=Ai.slice(e+1)),Qt(Jt,Fi),kf}finally{wn=t,Vi=!1}}return null}var Wi=[],Ui=0,qi=null,Qi=0,Ki=[],Yi=0,Xi=null,Gi=1,Zi="";function Ji(e,t){Wi[Ui++]=Qi,Wi[Ui++]=qi,qi=e,Qi=t}function ea(e,t,n){Ki[Yi++]=Gi,Ki[Yi++]=Zi,Ki[Yi++]=Xi,Xi=e;var o=Gi;e=Zi;var r=32-ln(o)-1;o&=~(1<<r),n+=1;var i=32-ln(t)+r;if(30<i){var a=r-r%5;i=(o&(1<<a)-1).toString(32),o>>=a,r-=a,Gi=1<<32-ln(t)+r|n<<r|o,Zi=i+e}else Gi=1<<i|n<<r|o,Zi=e}function ta(e){null!==e.return&&(Ji(e,1),ea(e,1,0))}function na(e){for(;e===qi;)qi=Wi[--Ui],Wi[Ui]=null,Qi=Wi[--Ui],Wi[Ui]=null;for(;e===Xi;)Xi=Ki[--Yi],Ki[Yi]=null,Zi=Ki[--Yi],Ki[Yi]=null,Gi=Ki[--Yi],Ki[Yi]=null}var oa=null,ra=null,ia=!1,aa=null;function la(e,t){var n=Ou(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function sa(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,oa=e,ra=ui(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,oa=e,ra=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Xi?{id:Gi,overflow:Zi}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Ou(18,null,null,0)).stateNode=t,n.return=e,e.child=n,oa=e,ra=null,!0);default:return!1}}function ca(e){return!(!(1&e.mode)||128&e.flags)}function ua(e){if(ia){var t=ra;if(t){var n=t;if(!sa(e,t)){if(ca(e))throw Error(ie(418));t=ui(n.nextSibling);var o=oa;t&&sa(e,t)?la(o,n):(e.flags=-4097&e.flags|2,ia=!1,oa=e)}}else{if(ca(e))throw Error(ie(418));e.flags=-4097&e.flags|2,ia=!1,oa=e}}}function da(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;oa=e}function fa(e){if(e!==oa)return!1;if(!ia)return da(e),ia=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!oi(e.type,e.memoizedProps)),t&&(t=ra)){if(ca(e))throw pa(),Error(ie(418));for(;t;)la(e,t),t=ui(t.nextSibling)}if(da(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(ie(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){ra=ui(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}ra=null}}else ra=oa?ui(e.stateNode.nextSibling):null;return!0}function pa(){for(var e=ra;e;)e=ui(e.nextSibling)}function ha(){ra=oa=null,ia=!1}function ma(e){null===aa?aa=[e]:aa.push(e)}var ga=we.ReactCurrentBatchConfig;function ba(e,t,n){if(null!==(e=n.ref)&&"function"!=typeof e&&"object"!=typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(ie(309));var o=n.stateNode}if(!o)throw Error(ie(147,e));var r=o,i=""+e;return null!==t&&null!==t.ref&&"function"==typeof t.ref&&t.ref._stringRef===i?t.ref:(t=function(e){var t=r.refs;null===e?delete t[i]:t[i]=e},t._stringRef=i,t)}if("string"!=typeof e)throw Error(ie(284));if(!n._owner)throw Error(ie(290,e))}return e}function va(e,t){throw e=Object.prototype.toString.call(t),Error(ie(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ya(e){return(0,e._init)(e._payload)}function wa(e){function t(t,n){if(e){var o=t.deletions;null===o?(t.deletions=[n],t.flags|=16):o.push(n)}}function n(n,o){if(!e)return null;for(;null!==o;)t(n,o),o=o.sibling;return null}function o(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function r(e,t){return(e=_u(e,t)).index=0,e.sibling=null,e}function i(t,n,o){return t.index=o,e?null!==(o=t.alternate)?(o=o.index)<n?(t.flags|=2,n):o:(t.flags|=2,n):(t.flags|=1048576,n)}function a(t){return e&&null===t.alternate&&(t.flags|=2),t}function l(e,t,n,o){return null===t||6!==t.tag?((t=ju(n,e.mode,o)).return=e,t):((t=r(t,n)).return=e,t)}function s(e,t,n,o){var i=n.type;return i===Ee?u(e,t,n.props.children,o,n.key):null!==t&&(t.elementType===i||"object"==typeof i&&null!==i&&i.$$typeof===Le&&ya(i)===t.type)?((o=r(t,n.props)).ref=ba(e,t,n),o.return=e,o):((o=zu(n.type,n.key,n.props,null,e.mode,o)).ref=ba(e,t,n),o.return=e,o)}function c(e,t,n,o){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Du(n,e.mode,o)).return=e,t):((t=r(t,n.children||[])).return=e,t)}function u(e,t,n,o,i){return null===t||7!==t.tag?((t=Nu(n,e.mode,o,i)).return=e,t):((t=r(t,n)).return=e,t)}function d(e,t,n){if("string"==typeof t&&""!==t||"number"==typeof t)return(t=ju(""+t,e.mode,n)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case xe:return(n=zu(t.type,t.key,t.props,null,e.mode,n)).ref=ba(e,null,t),n.return=e,n;case ke:return(t=Du(t,e.mode,n)).return=e,t;case Le:return d(e,(0,t._init)(t._payload),n)}if(tt(t)||Ne(t))return(t=Nu(t,e.mode,n,null)).return=e,t;va(e,t)}return null}function f(e,t,n,o){var r=null!==t?t.key:null;if("string"==typeof n&&""!==n||"number"==typeof n)return null!==r?null:l(e,t,""+n,o);if("object"==typeof n&&null!==n){switch(n.$$typeof){case xe:return n.key===r?s(e,t,n,o):null;case ke:return n.key===r?c(e,t,n,o):null;case Le:return f(e,t,(r=n._init)(n._payload),o)}if(tt(n)||Ne(n))return null!==r?null:u(e,t,n,o,null);va(e,n)}return null}function p(e,t,n,o,r){if("string"==typeof o&&""!==o||"number"==typeof o)return l(t,e=e.get(n)||null,""+o,r);if("object"==typeof o&&null!==o){switch(o.$$typeof){case xe:return s(t,e=e.get(null===o.key?n:o.key)||null,o,r);case ke:return c(t,e=e.get(null===o.key?n:o.key)||null,o,r);case Le:return p(e,t,n,(0,o._init)(o._payload),r)}if(tt(o)||Ne(o))return u(t,e=e.get(n)||null,o,r,null);va(t,o)}return null}function h(r,a,l,s){for(var c=null,u=null,h=a,m=a=0,g=null;null!==h&&m<l.length;m++){h.index>m?(g=h,h=null):g=h.sibling;var b=f(r,h,l[m],s);if(null===b){null===h&&(h=g);break}e&&h&&null===b.alternate&&t(r,h),a=i(b,a,m),null===u?c=b:u.sibling=b,u=b,h=g}if(m===l.length)return n(r,h),ia&&Ji(r,m),c;if(null===h){for(;m<l.length;m++)null!==(h=d(r,l[m],s))&&(a=i(h,a,m),null===u?c=h:u.sibling=h,u=h);return ia&&Ji(r,m),c}for(h=o(r,h);m<l.length;m++)null!==(g=p(h,r,m,l[m],s))&&(e&&null!==g.alternate&&h.delete(null===g.key?m:g.key),a=i(g,a,m),null===u?c=g:u.sibling=g,u=g);return e&&h.forEach((function(e){return t(r,e)})),ia&&Ji(r,m),c}function m(r,a,l,s){var c=Ne(l);if("function"!=typeof c)throw Error(ie(150));if(null==(l=c.call(l)))throw Error(ie(151));for(var u=c=null,h=a,m=a=0,g=null,b=l.next();null!==h&&!b.done;m++,b=l.next()){h.index>m?(g=h,h=null):g=h.sibling;var v=f(r,h,b.value,s);if(null===v){null===h&&(h=g);break}e&&h&&null===v.alternate&&t(r,h),a=i(v,a,m),null===u?c=v:u.sibling=v,u=v,h=g}if(b.done)return n(r,h),ia&&Ji(r,m),c;if(null===h){for(;!b.done;m++,b=l.next())null!==(b=d(r,b.value,s))&&(a=i(b,a,m),null===u?c=b:u.sibling=b,u=b);return ia&&Ji(r,m),c}for(h=o(r,h);!b.done;m++,b=l.next())null!==(b=p(h,r,m,b.value,s))&&(e&&null!==b.alternate&&h.delete(null===b.key?m:b.key),a=i(b,a,m),null===u?c=b:u.sibling=b,u=b);return e&&h.forEach((function(e){return t(r,e)})),ia&&Ji(r,m),c}return function e(o,i,l,s){if("object"==typeof l&&null!==l&&l.type===Ee&&null===l.key&&(l=l.props.children),"object"==typeof l&&null!==l){switch(l.$$typeof){case xe:e:{for(var c=l.key,u=i;null!==u;){if(u.key===c){if((c=l.type)===Ee){if(7===u.tag){n(o,u.sibling),(i=r(u,l.props.children)).return=o,o=i;break e}}else if(u.elementType===c||"object"==typeof c&&null!==c&&c.$$typeof===Le&&ya(c)===u.type){n(o,u.sibling),(i=r(u,l.props)).ref=ba(o,u,l),i.return=o,o=i;break e}n(o,u);break}t(o,u),u=u.sibling}l.type===Ee?((i=Nu(l.props.children,o.mode,s,l.key)).return=o,o=i):((s=zu(l.type,l.key,l.props,null,o.mode,s)).ref=ba(o,i,l),s.return=o,o=s)}return a(o);case ke:e:{for(u=l.key;null!==i;){if(i.key===u){if(4===i.tag&&i.stateNode.containerInfo===l.containerInfo&&i.stateNode.implementation===l.implementation){n(o,i.sibling),(i=r(i,l.children||[])).return=o,o=i;break e}n(o,i);break}t(o,i),i=i.sibling}(i=Du(l,o.mode,s)).return=o,o=i}return a(o);case Le:return e(o,i,(u=l._init)(l._payload),s)}if(tt(l))return h(o,i,l,s);if(Ne(l))return m(o,i,l,s);va(o,l)}return"string"==typeof l&&""!==l||"number"==typeof l?(l=""+l,null!==i&&6===i.tag?(n(o,i.sibling),(i=r(i,l)).return=o,o=i):(n(o,i),(i=ju(l,o.mode,s)).return=o,o=i),a(o)):n(o,i)}}var xa=wa(!0),ka=wa(!1),Ea=Ci(null),Sa=null,Ca=null,$a=null;function Ta(){$a=Ca=Sa=null}function Pa(e){var t=Ea.current;$i(Ea),e._currentValue=t}function Ra(e,t,n){for(;null!==e;){var o=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==o&&(o.childLanes|=t)):null!==o&&(o.childLanes&t)!==t&&(o.childLanes|=t),e===n)break;e=e.return}}function Ia(e,t){Sa=e,$a=Ca=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(!!(e.lanes&t)&&(ys=!0),e.firstContext=null)}function Oa(e){var t=e._currentValue;if($a!==e)if(e={context:e,memoizedValue:t,next:null},null===Ca){if(null===Sa)throw Error(ie(308));Ca=e,Sa.dependencies={lanes:0,firstContext:e}}else Ca=Ca.next=e;return t}var La=null;function _a(e){null===La?La=[e]:La.push(e)}function za(e,t,n,o){var r=t.interleaved;return null===r?(n.next=n,_a(t)):(n.next=r.next,r.next=n),t.interleaved=n,Na(e,o)}function Na(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Ma=!1;function ja(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Da(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Aa(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Ba(e,t,n){var o=e.updateQueue;if(null===o)return null;if(o=o.shared,2&Pc){var r=o.pending;return null===r?t.next=t:(t.next=r.next,r.next=t),o.pending=t,Na(e,n)}return null===(r=o.interleaved)?(t.next=t,_a(o)):(t.next=r.next,r.next=t),o.interleaved=t,Na(e,n)}function Va(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,4194240&n)){var o=t.lanes;n|=o&=e.pendingLanes,t.lanes=n,yn(e,n)}}function Ha(e,t){var n=e.updateQueue,o=e.alternate;if(null!==o&&n===(o=o.updateQueue)){var r=null,i=null;if(null!==(n=n.firstBaseUpdate)){do{var a={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===i?r=i=a:i=i.next=a,n=n.next}while(null!==n);null===i?r=i=t:i=i.next=t}else r=i=t;return n={baseState:o.baseState,firstBaseUpdate:r,lastBaseUpdate:i,shared:o.shared,effects:o.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Fa(e,t,n,o){var r=e.updateQueue;Ma=!1;var i=r.firstBaseUpdate,a=r.lastBaseUpdate,l=r.shared.pending;if(null!==l){r.shared.pending=null;var s=l,c=s.next;s.next=null,null===a?i=c:a.next=c,a=s;var u=e.alternate;null!==u&&(l=(u=u.updateQueue).lastBaseUpdate)!==a&&(null===l?u.firstBaseUpdate=c:l.next=c,u.lastBaseUpdate=s)}if(null!==i){var d=r.baseState;for(a=0,u=c=s=null,l=i;;){var f=l.lane,p=l.eventTime;if((o&f)===f){null!==u&&(u=u.next={eventTime:p,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var h=e,m=l;switch(f=t,p=n,m.tag){case 1:if("function"==typeof(h=m.payload)){d=h.call(p,d,f);break e}d=h;break e;case 3:h.flags=-65537&h.flags|128;case 0:if(null==(f="function"==typeof(h=m.payload)?h.call(p,d,f):h))break e;d=je({},d,f);break e;case 2:Ma=!0}}null!==l.callback&&0!==l.lane&&(e.flags|=64,null===(f=r.effects)?r.effects=[l]:f.push(l))}else p={eventTime:p,lane:f,tag:l.tag,payload:l.payload,callback:l.callback,next:null},null===u?(c=u=p,s=d):u=u.next=p,a|=f;if(null===(l=l.next)){if(null===(l=r.shared.pending))break;l=(f=l).next,f.next=null,r.lastBaseUpdate=f,r.shared.pending=null}}if(null===u&&(s=d),r.baseState=s,r.firstBaseUpdate=c,r.lastBaseUpdate=u,null!==(t=r.shared.interleaved)){r=t;do{a|=r.lane,r=r.next}while(r!==t)}else null===i&&(r.shared.lanes=0);Mc|=a,e.lanes=a,e.memoizedState=d}}function Wa(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var o=e[t],r=o.callback;if(null!==r){if(o.callback=null,o=n,"function"!=typeof r)throw Error(ie(191,r));r.call(o)}}}var Ua={},qa=Ci(Ua),Qa=Ci(Ua),Ka=Ci(Ua);function Ya(e){if(e===Ua)throw Error(ie(174));return e}function Xa(e,t){switch(Ti(Ka,t),Ti(Qa,e),Ti(qa,Ua),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:st(null,"");break;default:t=st(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}$i(qa),Ti(qa,t)}function Ga(){$i(qa),$i(Qa),$i(Ka)}function Za(e){Ya(Ka.current);var t=Ya(qa.current),n=st(t,e.type);t!==n&&(Ti(Qa,e),Ti(qa,n))}function Ja(e){Qa.current===e&&($i(qa),$i(Qa))}var el=Ci(0);function tl(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(128&t.flags)return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var nl=[];function ol(){for(var e=0;e<nl.length;e++)nl[e]._workInProgressVersionPrimary=null;nl.length=0}var rl=we.ReactCurrentDispatcher,il=we.ReactCurrentBatchConfig,al=0,ll=null,sl=null,cl=null,ul=!1,dl=!1,fl=0,pl=0;function hl(){throw Error(ie(321))}function ml(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!sr(e[n],t[n]))return!1;return!0}function gl(e,t,n,o,r,i){if(al=i,ll=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,rl.current=null===e||null===e.memoizedState?Jl:es,e=n(o,r),dl){i=0;do{if(dl=!1,fl=0,25<=i)throw Error(ie(301));i+=1,cl=sl=null,t.updateQueue=null,rl.current=ts,e=n(o,r)}while(dl)}if(rl.current=Zl,t=null!==sl&&null!==sl.next,al=0,cl=sl=ll=null,ul=!1,t)throw Error(ie(300));return e}function bl(){var e=0!==fl;return fl=0,e}function vl(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===cl?ll.memoizedState=cl=e:cl=cl.next=e,cl}function yl(){if(null===sl){var e=ll.alternate;e=null!==e?e.memoizedState:null}else e=sl.next;var t=null===cl?ll.memoizedState:cl.next;if(null!==t)cl=t,sl=e;else{if(null===e)throw Error(ie(310));e={memoizedState:(sl=e).memoizedState,baseState:sl.baseState,baseQueue:sl.baseQueue,queue:sl.queue,next:null},null===cl?ll.memoizedState=cl=e:cl=cl.next=e}return cl}function wl(e,t){return"function"==typeof t?t(e):t}function xl(e){var t=yl(),n=t.queue;if(null===n)throw Error(ie(311));n.lastRenderedReducer=e;var o=sl,r=o.baseQueue,i=n.pending;if(null!==i){if(null!==r){var a=r.next;r.next=i.next,i.next=a}o.baseQueue=r=i,n.pending=null}if(null!==r){i=r.next,o=o.baseState;var l=a=null,s=null,c=i;do{var u=c.lane;if((al&u)===u)null!==s&&(s=s.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),o=c.hasEagerState?c.eagerState:e(o,c.action);else{var d={lane:u,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};null===s?(l=s=d,a=o):s=s.next=d,ll.lanes|=u,Mc|=u}c=c.next}while(null!==c&&c!==i);null===s?a=o:s.next=l,sr(o,t.memoizedState)||(ys=!0),t.memoizedState=o,t.baseState=a,t.baseQueue=s,n.lastRenderedState=o}if(null!==(e=n.interleaved)){r=e;do{i=r.lane,ll.lanes|=i,Mc|=i,r=r.next}while(r!==e)}else null===r&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function kl(e){var t=yl(),n=t.queue;if(null===n)throw Error(ie(311));n.lastRenderedReducer=e;var o=n.dispatch,r=n.pending,i=t.memoizedState;if(null!==r){n.pending=null;var a=r=r.next;do{i=e(i,a.action),a=a.next}while(a!==r);sr(i,t.memoizedState)||(ys=!0),t.memoizedState=i,null===t.baseQueue&&(t.baseState=i),n.lastRenderedState=i}return[i,o]}function El(){}function Sl(e,t){var n=ll,o=yl(),r=t(),i=!sr(o.memoizedState,r);if(i&&(o.memoizedState=r,ys=!0),o=o.queue,Ml(Tl.bind(null,n,o,e),[e]),o.getSnapshot!==t||i||null!==cl&&1&cl.memoizedState.tag){if(n.flags|=2048,Ol(9,$l.bind(null,n,o,r,t),void 0,null),null===Rc)throw Error(ie(349));30&al||Cl(n,t,r)}return r}function Cl(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=ll.updateQueue)?(t={lastEffect:null,stores:null},ll.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function $l(e,t,n,o){t.value=n,t.getSnapshot=o,Pl(t)&&Rl(e)}function Tl(e,t,n){return n((function(){Pl(t)&&Rl(e)}))}function Pl(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!sr(e,n)}catch(xf){return!0}}function Rl(e){var t=Na(e,1);null!==t&&nu(t,e,1,-1)}function Il(e){var t=vl();return"function"==typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:wl,lastRenderedState:e},t.queue=e,e=e.dispatch=Kl.bind(null,ll,e),[t.memoizedState,e]}function Ol(e,t,n,o){return e={tag:e,create:t,destroy:n,deps:o,next:null},null===(t=ll.updateQueue)?(t={lastEffect:null,stores:null},ll.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(o=n.next,n.next=e,e.next=o,t.lastEffect=e),e}function Ll(){return yl().memoizedState}function _l(e,t,n,o){var r=vl();ll.flags|=e,r.memoizedState=Ol(1|t,n,void 0,void 0===o?null:o)}function zl(e,t,n,o){var r=yl();o=void 0===o?null:o;var i=void 0;if(null!==sl){var a=sl.memoizedState;if(i=a.destroy,null!==o&&ml(o,a.deps))return void(r.memoizedState=Ol(t,n,i,o))}ll.flags|=e,r.memoizedState=Ol(1|t,n,i,o)}function Nl(e,t){return _l(8390656,8,e,t)}function Ml(e,t){return zl(2048,8,e,t)}function jl(e,t){return zl(4,2,e,t)}function Dl(e,t){return zl(4,4,e,t)}function Al(e,t){return"function"==typeof t?(e=e(),t(e),function(){t(null)}):null!=t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Bl(e,t,n){return n=null!=n?n.concat([e]):null,zl(4,4,Al.bind(null,t,e),n)}function Vl(){}function Hl(e,t){var n=yl();t=void 0===t?null:t;var o=n.memoizedState;return null!==o&&null!==t&&ml(t,o[1])?o[0]:(n.memoizedState=[e,t],e)}function Fl(e,t){var n=yl();t=void 0===t?null:t;var o=n.memoizedState;return null!==o&&null!==t&&ml(t,o[1])?o[0]:(e=e(),n.memoizedState=[e,t],e)}function Wl(e,t,n){return 21&al?(sr(n,t)||(n=gn(),ll.lanes|=n,Mc|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,ys=!0),e.memoizedState=n)}function Ul(e,t){var n=wn;wn=0!==n&&4>n?n:4,e(!0);var o=il.transition;il.transition={};try{e(!1),t()}finally{wn=n,il.transition=o}}function ql(){return yl().memoizedState}function Ql(e,t,n){var o=tu(e);n={lane:o,action:n,hasEagerState:!1,eagerState:null,next:null},Yl(e)?Xl(t,n):null!==(n=za(e,t,n,o))&&(nu(n,e,o,eu()),Gl(n,t,o))}function Kl(e,t,n){var o=tu(e),r={lane:o,action:n,hasEagerState:!1,eagerState:null,next:null};if(Yl(e))Xl(t,r);else{var i=e.alternate;if(0===e.lanes&&(null===i||0===i.lanes)&&null!==(i=t.lastRenderedReducer))try{var a=t.lastRenderedState,l=i(a,n);if(r.hasEagerState=!0,r.eagerState=l,sr(l,a)){var s=t.interleaved;return null===s?(r.next=r,_a(t)):(r.next=s.next,s.next=r),void(t.interleaved=r)}}catch(Tf){}null!==(n=za(e,t,r,o))&&(nu(n,e,o,r=eu()),Gl(n,t,o))}}function Yl(e){var t=e.alternate;return e===ll||null!==t&&t===ll}function Xl(e,t){dl=ul=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Gl(e,t,n){if(4194240&n){var o=t.lanes;n|=o&=e.pendingLanes,t.lanes=n,yn(e,n)}}var Zl={readContext:Oa,useCallback:hl,useContext:hl,useEffect:hl,useImperativeHandle:hl,useInsertionEffect:hl,useLayoutEffect:hl,useMemo:hl,useReducer:hl,useRef:hl,useState:hl,useDebugValue:hl,useDeferredValue:hl,useTransition:hl,useMutableSource:hl,useSyncExternalStore:hl,useId:hl,unstable_isNewReconciler:!1},Jl={readContext:Oa,useCallback:function(e,t){return vl().memoizedState=[e,void 0===t?null:t],e},useContext:Oa,useEffect:Nl,useImperativeHandle:function(e,t,n){return n=null!=n?n.concat([e]):null,_l(4194308,4,Al.bind(null,t,e),n)},useLayoutEffect:function(e,t){return _l(4194308,4,e,t)},useInsertionEffect:function(e,t){return _l(4,2,e,t)},useMemo:function(e,t){var n=vl();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var o=vl();return t=void 0!==n?n(t):t,o.memoizedState=o.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},o.queue=e,e=e.dispatch=Ql.bind(null,ll,e),[o.memoizedState,e]},useRef:function(e){return e={current:e},vl().memoizedState=e},useState:Il,useDebugValue:Vl,useDeferredValue:function(e){return vl().memoizedState=e},useTransition:function(){var e=Il(!1),t=e[0];return e=Ul.bind(null,e[1]),vl().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var o=ll,r=vl();if(ia){if(void 0===n)throw Error(ie(407));n=n()}else{if(n=t(),null===Rc)throw Error(ie(349));30&al||Cl(o,t,n)}r.memoizedState=n;var i={value:n,getSnapshot:t};return r.queue=i,Nl(Tl.bind(null,o,i,e),[e]),o.flags|=2048,Ol(9,$l.bind(null,o,i,n,t),void 0,null),n},useId:function(){var e=vl(),t=Rc.identifierPrefix;if(ia){var n=Zi;t=":"+t+"R"+(n=(Gi&~(1<<32-ln(Gi)-1)).toString(32)+n),0<(n=fl++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=pl++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},es={readContext:Oa,useCallback:Hl,useContext:Oa,useEffect:Ml,useImperativeHandle:Bl,useInsertionEffect:jl,useLayoutEffect:Dl,useMemo:Fl,useReducer:xl,useRef:Ll,useState:function(){return xl(wl)},useDebugValue:Vl,useDeferredValue:function(e){return Wl(yl(),sl.memoizedState,e)},useTransition:function(){return[xl(wl)[0],yl().memoizedState]},useMutableSource:El,useSyncExternalStore:Sl,useId:ql,unstable_isNewReconciler:!1},ts={readContext:Oa,useCallback:Hl,useContext:Oa,useEffect:Ml,useImperativeHandle:Bl,useInsertionEffect:jl,useLayoutEffect:Dl,useMemo:Fl,useReducer:kl,useRef:Ll,useState:function(){return kl(wl)},useDebugValue:Vl,useDeferredValue:function(e){var t=yl();return null===sl?t.memoizedState=e:Wl(t,sl.memoizedState,e)},useTransition:function(){return[kl(wl)[0],yl().memoizedState]},useMutableSource:El,useSyncExternalStore:Sl,useId:ql,unstable_isNewReconciler:!1};function ns(e,t){if(e&&e.defaultProps){for(var n in t=je({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function os(e,t,n,o){n=null==(n=n(o,t=e.memoizedState))?t:je({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var rs={isMounted:function(e){return!!(e=e._reactInternals)&&Ht(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var o=eu(),r=tu(e),i=Aa(o,r);i.payload=t,null!=n&&(i.callback=n),null!==(t=Ba(e,i,r))&&(nu(t,e,r,o),Va(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var o=eu(),r=tu(e),i=Aa(o,r);i.tag=1,i.payload=t,null!=n&&(i.callback=n),null!==(t=Ba(e,i,r))&&(nu(t,e,r,o),Va(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=eu(),o=tu(e),r=Aa(n,o);r.tag=2,null!=t&&(r.callback=t),null!==(t=Ba(e,r,o))&&(nu(t,e,o,n),Va(t,e,o))}};function is(e,t,n,o,r,i,a){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(o,i,a):!(t.prototype&&t.prototype.isPureReactComponent&&cr(n,o)&&cr(r,i))}function as(e,t,n){var o=!1,r=Pi,i=t.contextType;return"object"==typeof i&&null!==i?i=Oa(i):(r=_i(t)?Oi:Ri.current,i=(o=null!=(o=t.contextTypes))?Li(e,r):Pi),t=new t(n,i),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=rs,e.stateNode=t,t._reactInternals=e,o&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=r,e.__reactInternalMemoizedMaskedChildContext=i),t}function ls(e,t,n,o){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,o),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,o),t.state!==e&&rs.enqueueReplaceState(t,t.state,null)}function ss(e,t,n,o){var r=e.stateNode;r.props=n,r.state=e.memoizedState,r.refs={},ja(e);var i=t.contextType;"object"==typeof i&&null!==i?r.context=Oa(i):(i=_i(t)?Oi:Ri.current,r.context=Li(e,i)),r.state=e.memoizedState,"function"==typeof(i=t.getDerivedStateFromProps)&&(os(e,t,i,n),r.state=e.memoizedState),"function"==typeof t.getDerivedStateFromProps||"function"==typeof r.getSnapshotBeforeUpdate||"function"!=typeof r.UNSAFE_componentWillMount&&"function"!=typeof r.componentWillMount||(t=r.state,"function"==typeof r.componentWillMount&&r.componentWillMount(),"function"==typeof r.UNSAFE_componentWillMount&&r.UNSAFE_componentWillMount(),t!==r.state&&rs.enqueueReplaceState(r,r.state,null),Fa(e,n,r,o),r.state=e.memoizedState),"function"==typeof r.componentDidMount&&(e.flags|=4194308)}function cs(e,t){try{var n="",o=t;do{n+=Ve(o),o=o.return}while(o);var r=n}catch(Ef){r="\nError generating stack: "+Ef.message+"\n"+Ef.stack}return{value:e,source:t,stack:r,digest:null}}function us(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function ds(e,t){try{console.error(t.value)}catch(wf){setTimeout((function(){throw wf}))}}var fs="function"==typeof WeakMap?WeakMap:Map;function ps(e,t,n){(n=Aa(-1,n)).tag=3,n.payload={element:null};var o=t.value;return n.callback=function(){Wc||(Wc=!0,Uc=o),ds(0,t)},n}function hs(e,t,n){(n=Aa(-1,n)).tag=3;var o=e.type.getDerivedStateFromError;if("function"==typeof o){var r=t.value;n.payload=function(){return o(r)},n.callback=function(){ds(0,t)}}var i=e.stateNode;return null!==i&&"function"==typeof i.componentDidCatch&&(n.callback=function(){ds(0,t),"function"!=typeof o&&(null===qc?qc=new Set([this]):qc.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function ms(e,t,n){var o=e.pingCache;if(null===o){o=e.pingCache=new fs;var r=new Set;o.set(t,r)}else void 0===(r=o.get(t))&&(r=new Set,o.set(t,r));r.has(n)||(r.add(n),e=Cu.bind(null,e,t,n),t.then(e,e))}function gs(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function bs(e,t,n,o,r){return 1&e.mode?(e.flags|=65536,e.lanes=r,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Aa(-1,1)).tag=2,Ba(n,t,1))),n.lanes|=1),e)}var vs=we.ReactCurrentOwner,ys=!1;function ws(e,t,n,o){t.child=null===e?ka(t,null,n,o):xa(t,e.child,n,o)}function xs(e,t,n,o,r){n=n.render;var i=t.ref;return Ia(t,r),o=gl(e,t,n,o,i,r),n=bl(),null===e||ys?(ia&&n&&ta(t),t.flags|=1,ws(e,t,o,r),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~r,Ws(e,t,r))}function ks(e,t,n,o,r){if(null===e){var i=n.type;return"function"!=typeof i||Lu(i)||void 0!==i.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=zu(n.type,null,o,t,t.mode,r)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=i,Es(e,t,i,o,r))}if(i=e.child,!(e.lanes&r)){var a=i.memoizedProps;if((n=null!==(n=n.compare)?n:cr)(a,o)&&e.ref===t.ref)return Ws(e,t,r)}return t.flags|=1,(e=_u(i,o)).ref=t.ref,e.return=t,t.child=e}function Es(e,t,n,o,r){if(null!==e){var i=e.memoizedProps;if(cr(i,o)&&e.ref===t.ref){if(ys=!1,t.pendingProps=o=i,!(e.lanes&r))return t.lanes=e.lanes,Ws(e,t,r);131072&e.flags&&(ys=!0)}}return $s(e,t,n,o,r)}function Ss(e,t,n){var o=t.pendingProps,r=o.children,i=null!==e?e.memoizedState:null;if("hidden"===o.mode)if(1&t.mode){if(!(1073741824&n))return e=null!==i?i.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Ti(_c,Lc),Lc|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},o=null!==i?i.baseLanes:n,Ti(_c,Lc),Lc|=o}else t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ti(_c,Lc),Lc|=n;else null!==i?(o=i.baseLanes|n,t.memoizedState=null):o=n,Ti(_c,Lc),Lc|=o;return ws(e,t,r,n),t.child}function Cs(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function $s(e,t,n,o,r){var i=_i(n)?Oi:Ri.current;return i=Li(t,i),Ia(t,r),n=gl(e,t,n,o,i,r),o=bl(),null===e||ys?(ia&&o&&ta(t),t.flags|=1,ws(e,t,n,r),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~r,Ws(e,t,r))}function Ts(e,t,n,o,r){if(_i(n)){var i=!0;ji(t)}else i=!1;if(Ia(t,r),null===t.stateNode)Fs(e,t),as(t,n,o),ss(t,n,o,r),o=!0;else if(null===e){var a=t.stateNode,l=t.memoizedProps;a.props=l;var s=a.context,c=n.contextType;c="object"==typeof c&&null!==c?Oa(c):Li(t,c=_i(n)?Oi:Ri.current);var u=n.getDerivedStateFromProps,d="function"==typeof u||"function"==typeof a.getSnapshotBeforeUpdate;d||"function"!=typeof a.UNSAFE_componentWillReceiveProps&&"function"!=typeof a.componentWillReceiveProps||(l!==o||s!==c)&&ls(t,a,o,c),Ma=!1;var f=t.memoizedState;a.state=f,Fa(t,o,a,r),s=t.memoizedState,l!==o||f!==s||Ii.current||Ma?("function"==typeof u&&(os(t,n,u,o),s=t.memoizedState),(l=Ma||is(t,n,l,o,f,s,c))?(d||"function"!=typeof a.UNSAFE_componentWillMount&&"function"!=typeof a.componentWillMount||("function"==typeof a.componentWillMount&&a.componentWillMount(),"function"==typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount()),"function"==typeof a.componentDidMount&&(t.flags|=4194308)):("function"==typeof a.componentDidMount&&(t.flags|=4194308),t.memoizedProps=o,t.memoizedState=s),a.props=o,a.state=s,a.context=c,o=l):("function"==typeof a.componentDidMount&&(t.flags|=4194308),o=!1)}else{a=t.stateNode,Da(e,t),l=t.memoizedProps,c=t.type===t.elementType?l:ns(t.type,l),a.props=c,d=t.pendingProps,f=a.context,s="object"==typeof(s=n.contextType)&&null!==s?Oa(s):Li(t,s=_i(n)?Oi:Ri.current);var p=n.getDerivedStateFromProps;(u="function"==typeof p||"function"==typeof a.getSnapshotBeforeUpdate)||"function"!=typeof a.UNSAFE_componentWillReceiveProps&&"function"!=typeof a.componentWillReceiveProps||(l!==d||f!==s)&&ls(t,a,o,s),Ma=!1,f=t.memoizedState,a.state=f,Fa(t,o,a,r);var h=t.memoizedState;l!==d||f!==h||Ii.current||Ma?("function"==typeof p&&(os(t,n,p,o),h=t.memoizedState),(c=Ma||is(t,n,c,o,f,h,s)||!1)?(u||"function"!=typeof a.UNSAFE_componentWillUpdate&&"function"!=typeof a.componentWillUpdate||("function"==typeof a.componentWillUpdate&&a.componentWillUpdate(o,h,s),"function"==typeof a.UNSAFE_componentWillUpdate&&a.UNSAFE_componentWillUpdate(o,h,s)),"function"==typeof a.componentDidUpdate&&(t.flags|=4),"function"==typeof a.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!=typeof a.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof a.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=o,t.memoizedState=h),a.props=o,a.state=h,a.context=s,o=c):("function"!=typeof a.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!=typeof a.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),o=!1)}return Ps(e,t,n,o,i,r)}function Ps(e,t,n,o,r,i){Cs(e,t);var a=!!(128&t.flags);if(!o&&!a)return r&&Di(t,n,!1),Ws(e,t,i);o=t.stateNode,vs.current=t;var l=a&&"function"!=typeof n.getDerivedStateFromError?null:o.render();return t.flags|=1,null!==e&&a?(t.child=xa(t,e.child,null,i),t.child=xa(t,null,l,i)):ws(e,t,l,i),t.memoizedState=o.state,r&&Di(t,n,!0),t.child}function Rs(e){var t=e.stateNode;t.pendingContext?Ni(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Ni(0,t.context,!1),Xa(e,t.containerInfo)}function Is(e,t,n,o,r){return ha(),ma(r),t.flags|=256,ws(e,t,n,o),t.child}var Os,Ls,_s,zs,Ns={dehydrated:null,treeContext:null,retryLane:0};function Ms(e){return{baseLanes:e,cachePool:null,transitions:null}}function js(e,t,n){var o,r=t.pendingProps,i=el.current,a=!1,l=!!(128&t.flags);if((o=l)||(o=(null===e||null!==e.memoizedState)&&!!(2&i)),o?(a=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(i|=1),Ti(el,1&i),null===e)return ua(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(1&t.mode?"$!"===e.data?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(l=r.children,e=r.fallback,a?(r=t.mode,a=t.child,l={mode:"hidden",children:l},1&r||null===a?a=Mu(l,r,0,null):(a.childLanes=0,a.pendingProps=l),e=Nu(e,r,n,null),a.return=t,e.return=t,a.sibling=e,t.child=a,t.child.memoizedState=Ms(n),t.memoizedState=Ns,e):Ds(t,l));if(null!==(i=e.memoizedState)&&null!==(o=i.dehydrated))return function(e,t,n,o,r,i,a){if(n)return 256&t.flags?(t.flags&=-257,As(e,t,a,o=us(Error(ie(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(i=o.fallback,r=t.mode,o=Mu({mode:"visible",children:o.children},r,0,null),(i=Nu(i,r,a,null)).flags|=2,o.return=t,i.return=t,o.sibling=i,t.child=o,1&t.mode&&xa(t,e.child,null,a),t.child.memoizedState=Ms(a),t.memoizedState=Ns,i);if(!(1&t.mode))return As(e,t,a,null);if("$!"===r.data){if(o=r.nextSibling&&r.nextSibling.dataset)var l=o.dgst;return o=l,As(e,t,a,o=us(i=Error(ie(419)),o,void 0))}if(l=!!(a&e.childLanes),ys||l){if(null!==(o=Rc)){switch(a&-a){case 4:r=2;break;case 16:r=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:r=32;break;case 536870912:r=268435456;break;default:r=0}0!==(r=r&(o.suspendedLanes|a)?0:r)&&r!==i.retryLane&&(i.retryLane=r,Na(e,r),nu(o,e,r,-1))}return mu(),As(e,t,a,o=us(Error(ie(421))))}return"$?"===r.data?(t.flags|=128,t.child=e.child,t=Tu.bind(null,e),r._reactRetry=t,null):(e=i.treeContext,ra=ui(r.nextSibling),oa=t,ia=!0,aa=null,null!==e&&(Ki[Yi++]=Gi,Ki[Yi++]=Zi,Ki[Yi++]=Xi,Gi=e.id,Zi=e.overflow,Xi=t),(t=Ds(t,o.children)).flags|=4096,t)}(e,t,l,r,o,i,n);if(a){a=r.fallback,l=t.mode,o=(i=e.child).sibling;var s={mode:"hidden",children:r.children};return 1&l||t.child===i?(r=_u(i,s)).subtreeFlags=14680064&i.subtreeFlags:((r=t.child).childLanes=0,r.pendingProps=s,t.deletions=null),null!==o?a=_u(o,a):(a=Nu(a,l,n,null)).flags|=2,a.return=t,r.return=t,r.sibling=a,t.child=r,r=a,a=t.child,l=null===(l=e.child.memoizedState)?Ms(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},a.memoizedState=l,a.childLanes=e.childLanes&~n,t.memoizedState=Ns,r}return e=(a=e.child).sibling,r=_u(a,{mode:"visible",children:r.children}),!(1&t.mode)&&(r.lanes=n),r.return=t,r.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Ds(e,t){return(t=Mu({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function As(e,t,n,o){return null!==o&&ma(o),xa(t,e.child,null,n),(e=Ds(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Bs(e,t,n){e.lanes|=t;var o=e.alternate;null!==o&&(o.lanes|=t),Ra(e.return,t,n)}function Vs(e,t,n,o,r){var i=e.memoizedState;null===i?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:o,tail:n,tailMode:r}:(i.isBackwards=t,i.rendering=null,i.renderingStartTime=0,i.last=o,i.tail=n,i.tailMode=r)}function Hs(e,t,n){var o=t.pendingProps,r=o.revealOrder,i=o.tail;if(ws(e,t,o.children,n),2&(o=el.current))o=1&o|2,t.flags|=128;else{if(null!==e&&128&e.flags)e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Bs(e,n,t);else if(19===e.tag)Bs(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}o&=1}if(Ti(el,o),1&t.mode)switch(r){case"forwards":for(n=t.child,r=null;null!==n;)null!==(e=n.alternate)&&null===tl(e)&&(r=n),n=n.sibling;null===(n=r)?(r=t.child,t.child=null):(r=n.sibling,n.sibling=null),Vs(t,!1,r,n,i);break;case"backwards":for(n=null,r=t.child,t.child=null;null!==r;){if(null!==(e=r.alternate)&&null===tl(e)){t.child=r;break}e=r.sibling,r.sibling=n,n=r,r=e}Vs(t,!0,n,null,i);break;case"together":Vs(t,!1,null,null,void 0);break;default:t.memoizedState=null}else t.memoizedState=null;return t.child}function Fs(e,t){!(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Ws(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Mc|=t.lanes,!(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(ie(153));if(null!==t.child){for(n=_u(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=_u(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Us(e,t){if(!ia)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var o=null;null!==n;)null!==n.alternate&&(o=n),n=n.sibling;null===o?t||null===e.tail?e.tail=null:e.tail.sibling=null:o.sibling=null}}function qs(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,o=0;if(t)for(var r=e.child;null!==r;)n|=r.lanes|r.childLanes,o|=14680064&r.subtreeFlags,o|=14680064&r.flags,r.return=e,r=r.sibling;else for(r=e.child;null!==r;)n|=r.lanes|r.childLanes,o|=r.subtreeFlags,o|=r.flags,r.return=e,r=r.sibling;return e.subtreeFlags|=o,e.childLanes=n,t}function Qs(e,t,n){var o=t.pendingProps;switch(na(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return qs(t),null;case 1:case 17:return _i(t.type)&&zi(),qs(t),null;case 3:return o=t.stateNode,Ga(),$i(Ii),$i(Ri),ol(),o.pendingContext&&(o.context=o.pendingContext,o.pendingContext=null),null!==e&&null!==e.child||(fa(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&!(256&t.flags)||(t.flags|=1024,null!==aa&&(au(aa),aa=null))),Ls(e,t),qs(t),null;case 5:Ja(t);var r=Ya(Ka.current);if(n=t.type,null!==e&&null!=t.stateNode)_s(e,t,n,o,r),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!o){if(null===t.stateNode)throw Error(ie(166));return qs(t),null}if(e=Ya(qa.current),fa(t)){o=t.stateNode,n=t.type;var i=t.memoizedProps;switch(o[pi]=t,o[hi]=i,e=!!(1&t.mode),n){case"dialog":Br("cancel",o),Br("close",o);break;case"iframe":case"object":case"embed":Br("load",o);break;case"video":case"audio":for(r=0;r<Mr.length;r++)Br(Mr[r],o);break;case"source":Br("error",o);break;case"img":case"image":case"link":Br("error",o),Br("load",o);break;case"details":Br("toggle",o);break;case"input":Xe(o,i),Br("invalid",o);break;case"select":o._wrapperState={wasMultiple:!!i.multiple},Br("invalid",o);break;case"textarea":rt(o,i),Br("invalid",o)}for(var a in vt(n,i),r=null,i)if(i.hasOwnProperty(a)){var l=i[a];"children"===a?"string"==typeof l?o.textContent!==l&&(!0!==i.suppressHydrationWarning&&Jr(o.textContent,l,e),r=["children",l]):"number"==typeof l&&o.textContent!==""+l&&(!0!==i.suppressHydrationWarning&&Jr(o.textContent,l,e),r=["children",""+l]):le.hasOwnProperty(a)&&null!=l&&"onScroll"===a&&Br("scroll",o)}switch(n){case"input":qe(o),Je(o,i,!0);break;case"textarea":qe(o),at(o);break;case"select":case"option":break;default:"function"==typeof i.onClick&&(o.onclick=ei)}o=r,t.updateQueue=o,null!==o&&(t.flags|=4)}else{a=9===r.nodeType?r:r.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=lt(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=a.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"==typeof o.is?e=a.createElement(n,{is:o.is}):(e=a.createElement(n),"select"===n&&(a=e,o.multiple?a.multiple=!0:o.size&&(a.size=o.size))):e=a.createElementNS(e,n),e[pi]=t,e[hi]=o,Os(e,t,!1,!1),t.stateNode=e;e:{switch(a=yt(n,o),n){case"dialog":Br("cancel",e),Br("close",e),r=o;break;case"iframe":case"object":case"embed":Br("load",e),r=o;break;case"video":case"audio":for(r=0;r<Mr.length;r++)Br(Mr[r],e);r=o;break;case"source":Br("error",e),r=o;break;case"img":case"image":case"link":Br("error",e),Br("load",e),r=o;break;case"details":Br("toggle",e),r=o;break;case"input":Xe(e,o),r=Ye(e,o),Br("invalid",e);break;case"option":default:r=o;break;case"select":e._wrapperState={wasMultiple:!!o.multiple},r=je({},o,{value:void 0}),Br("invalid",e);break;case"textarea":rt(e,o),r=ot(e,o),Br("invalid",e)}for(i in vt(n,r),l=r)if(l.hasOwnProperty(i)){var s=l[i];"style"===i?gt(e,s):"dangerouslySetInnerHTML"===i?null!=(s=s?s.__html:void 0)&&dt(e,s):"children"===i?"string"==typeof s?("textarea"!==n||""!==s)&&ft(e,s):"number"==typeof s&&ft(e,""+s):"suppressContentEditableWarning"!==i&&"suppressHydrationWarning"!==i&&"autoFocus"!==i&&(le.hasOwnProperty(i)?null!=s&&"onScroll"===i&&Br("scroll",e):null!=s&&ye(e,i,s,a))}switch(n){case"input":qe(e),Je(e,o,!1);break;case"textarea":qe(e),at(e);break;case"option":null!=o.value&&e.setAttribute("value",""+We(o.value));break;case"select":e.multiple=!!o.multiple,null!=(i=o.value)?nt(e,!!o.multiple,i,!1):null!=o.defaultValue&&nt(e,!!o.multiple,o.defaultValue,!0);break;default:"function"==typeof r.onClick&&(e.onclick=ei)}switch(n){case"button":case"input":case"select":case"textarea":o=!!o.autoFocus;break e;case"img":o=!0;break e;default:o=!1}}o&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return qs(t),null;case 6:if(e&&null!=t.stateNode)zs(e,t,e.memoizedProps,o);else{if("string"!=typeof o&&null===t.stateNode)throw Error(ie(166));if(n=Ya(Ka.current),Ya(qa.current),fa(t)){if(o=t.stateNode,n=t.memoizedProps,o[pi]=t,(i=o.nodeValue!==n)&&null!==(e=oa))switch(e.tag){case 3:Jr(o.nodeValue,n,!!(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Jr(o.nodeValue,n,!!(1&e.mode))}i&&(t.flags|=4)}else(o=(9===n.nodeType?n:n.ownerDocument).createTextNode(o))[pi]=t,t.stateNode=o}return qs(t),null;case 13:if($i(el),o=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(ia&&null!==ra&&1&t.mode&&!(128&t.flags))pa(),ha(),t.flags|=98560,i=!1;else if(i=fa(t),null!==o&&null!==o.dehydrated){if(null===e){if(!i)throw Error(ie(318));if(!(i=null!==(i=t.memoizedState)?i.dehydrated:null))throw Error(ie(317));i[pi]=t}else ha(),!(128&t.flags)&&(t.memoizedState=null),t.flags|=4;qs(t),i=!1}else null!==aa&&(au(aa),aa=null),i=!0;if(!i)return 65536&t.flags?t:null}return 128&t.flags?(t.lanes=n,t):((o=null!==o)!=(null!==e&&null!==e.memoizedState)&&o&&(t.child.flags|=8192,1&t.mode&&(null===e||1&el.current?0===zc&&(zc=3):mu())),null!==t.updateQueue&&(t.flags|=4),qs(t),null);case 4:return Ga(),Ls(e,t),null===e&&Fr(t.stateNode.containerInfo),qs(t),null;case 10:return Pa(t.type._context),qs(t),null;case 19:if($i(el),null===(i=t.memoizedState))return qs(t),null;if(o=!!(128&t.flags),null===(a=i.rendering))if(o)Us(i,!1);else{if(0!==zc||null!==e&&128&e.flags)for(e=t.child;null!==e;){if(null!==(a=tl(e))){for(t.flags|=128,Us(i,!1),null!==(o=a.updateQueue)&&(t.updateQueue=o,t.flags|=4),t.subtreeFlags=0,o=n,n=t.child;null!==n;)e=o,(i=n).flags&=14680066,null===(a=i.alternate)?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=a.childLanes,i.lanes=a.lanes,i.child=a.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=a.memoizedProps,i.memoizedState=a.memoizedState,i.updateQueue=a.updateQueue,i.type=a.type,e=a.dependencies,i.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Ti(el,1&el.current|2),t.child}e=e.sibling}null!==i.tail&&Gt()>Hc&&(t.flags|=128,o=!0,Us(i,!1),t.lanes=4194304)}else{if(!o)if(null!==(e=tl(a))){if(t.flags|=128,o=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),Us(i,!0),null===i.tail&&"hidden"===i.tailMode&&!a.alternate&&!ia)return qs(t),null}else 2*Gt()-i.renderingStartTime>Hc&&1073741824!==n&&(t.flags|=128,o=!0,Us(i,!1),t.lanes=4194304);i.isBackwards?(a.sibling=t.child,t.child=a):(null!==(n=i.last)?n.sibling=a:t.child=a,i.last=a)}return null!==i.tail?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Gt(),t.sibling=null,n=el.current,Ti(el,o?1&n|2:1&n),t):(qs(t),null);case 22:case 23:return du(),o=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==o&&(t.flags|=8192),o&&1&t.mode?!!(1073741824&Lc)&&(qs(t),6&t.subtreeFlags&&(t.flags|=8192)):qs(t),null;case 24:case 25:return null}throw Error(ie(156,t.tag))}function Ks(e,t){switch(na(t),t.tag){case 1:return _i(t.type)&&zi(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Ga(),$i(Ii),$i(Ri),ol(),65536&(e=t.flags)&&!(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Ja(t),null;case 13:if($i(el),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(ie(340));ha()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return $i(el),null;case 4:return Ga(),null;case 10:return Pa(t.type._context),null;case 22:case 23:return du(),null;default:return null}}Os=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Ls=function(){},_s=function(e,t,n,o){var r=e.memoizedProps;if(r!==o){e=t.stateNode,Ya(qa.current);var i,a=null;switch(n){case"input":r=Ye(e,r),o=Ye(e,o),a=[];break;case"select":r=je({},r,{value:void 0}),o=je({},o,{value:void 0}),a=[];break;case"textarea":r=ot(e,r),o=ot(e,o),a=[];break;default:"function"!=typeof r.onClick&&"function"==typeof o.onClick&&(e.onclick=ei)}for(c in vt(n,o),n=null,r)if(!o.hasOwnProperty(c)&&r.hasOwnProperty(c)&&null!=r[c])if("style"===c){var l=r[c];for(i in l)l.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else"dangerouslySetInnerHTML"!==c&&"children"!==c&&"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(le.hasOwnProperty(c)?a||(a=[]):(a=a||[]).push(c,null));for(c in o){var s=o[c];if(l=null!=r?r[c]:void 0,o.hasOwnProperty(c)&&s!==l&&(null!=s||null!=l))if("style"===c)if(l){for(i in l)!l.hasOwnProperty(i)||s&&s.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in s)s.hasOwnProperty(i)&&l[i]!==s[i]&&(n||(n={}),n[i]=s[i])}else n||(a||(a=[]),a.push(c,n)),n=s;else"dangerouslySetInnerHTML"===c?(s=s?s.__html:void 0,l=l?l.__html:void 0,null!=s&&l!==s&&(a=a||[]).push(c,s)):"children"===c?"string"!=typeof s&&"number"!=typeof s||(a=a||[]).push(c,""+s):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&(le.hasOwnProperty(c)?(null!=s&&"onScroll"===c&&Br("scroll",e),a||l===s||(a=[])):(a=a||[]).push(c,s))}n&&(a=a||[]).push("style",n);var c=a;(t.updateQueue=c)&&(t.flags|=4)}},zs=function(e,t,n,o){n!==o&&(t.flags|=4)};var Ys=!1,Xs=!1,Gs="function"==typeof WeakSet?WeakSet:Set,Zs=null;function Js(e,t){var n=e.ref;if(null!==n)if("function"==typeof n)try{n(null)}catch(xf){Su(e,t,xf)}else n.current=null}function ec(e,t,n){try{n()}catch(xf){Su(e,t,xf)}}var tc=!1;function nc(e,t,n){var o=t.updateQueue;if(null!==(o=null!==o?o.lastEffect:null)){var r=o=o.next;do{if((r.tag&e)===e){var i=r.destroy;r.destroy=void 0,void 0!==i&&ec(t,n,i)}r=r.next}while(r!==o)}}function oc(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var o=n.create;n.destroy=o()}n=n.next}while(n!==t)}}function rc(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"==typeof t?t(e):t.current=e}}function ic(e){var t=e.alternate;null!==t&&(e.alternate=null,ic(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&null!==(t=e.stateNode)&&(delete t[pi],delete t[hi],delete t[gi],delete t[bi],delete t[vi]),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function ac(e){return 5===e.tag||3===e.tag||4===e.tag}function lc(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||ac(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function sc(e,t,n){var o=e.tag;if(5===o||6===o)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!=(n=n._reactRootContainer)||null!==t.onclick||(t.onclick=ei));else if(4!==o&&null!==(e=e.child))for(sc(e,t,n),e=e.sibling;null!==e;)sc(e,t,n),e=e.sibling}function cc(e,t,n){var o=e.tag;if(5===o||6===o)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==o&&null!==(e=e.child))for(cc(e,t,n),e=e.sibling;null!==e;)cc(e,t,n),e=e.sibling}var uc=null,dc=!1;function fc(e,t,n){for(n=n.child;null!==n;)pc(e,t,n),n=n.sibling}function pc(e,t,n){if(an&&"function"==typeof an.onCommitFiberUnmount)try{an.onCommitFiberUnmount(rn,n)}catch(Zp){}switch(n.tag){case 5:Xs||Js(n,t);case 6:var o=uc,r=dc;uc=null,fc(e,t,n),dc=r,null!==(uc=o)&&(dc?(e=uc,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):uc.removeChild(n.stateNode));break;case 18:null!==uc&&(dc?(e=uc,n=n.stateNode,8===e.nodeType?ci(e.parentNode,n):1===e.nodeType&&ci(e,n),Fn(e)):ci(uc,n.stateNode));break;case 4:o=uc,r=dc,uc=n.stateNode.containerInfo,dc=!0,fc(e,t,n),uc=o,dc=r;break;case 0:case 11:case 14:case 15:if(!Xs&&null!==(o=n.updateQueue)&&null!==(o=o.lastEffect)){r=o=o.next;do{var i=r,a=i.destroy;i=i.tag,void 0!==a&&(2&i||4&i)&&ec(n,t,a),r=r.next}while(r!==o)}fc(e,t,n);break;case 1:if(!Xs&&(Js(n,t),"function"==typeof(o=n.stateNode).componentWillUnmount))try{o.props=n.memoizedProps,o.state=n.memoizedState,o.componentWillUnmount()}catch(Zp){Su(n,t,Zp)}fc(e,t,n);break;case 21:fc(e,t,n);break;case 22:1&n.mode?(Xs=(o=Xs)||null!==n.memoizedState,fc(e,t,n),Xs=o):fc(e,t,n);break;default:fc(e,t,n)}}function hc(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Gs),t.forEach((function(t){var o=Pu.bind(null,e,t);n.has(t)||(n.add(t),t.then(o,o))}))}}function mc(e,t){var n=t.deletions;if(null!==n)for(var o=0;o<n.length;o++){var r=n[o];try{var i=e,a=t,l=a;e:for(;null!==l;){switch(l.tag){case 5:uc=l.stateNode,dc=!1;break e;case 3:case 4:uc=l.stateNode.containerInfo,dc=!0;break e}l=l.return}if(null===uc)throw Error(ie(160));pc(i,a,r),uc=null,dc=!1;var s=r.alternate;null!==s&&(s.return=null),r.return=null}catch(Tf){Su(r,t,Tf)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)gc(t,e),t=t.sibling}function gc(e,t){var n=e.alternate,o=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(mc(t,e),bc(e),4&o){try{nc(3,e,e.return),oc(3,e)}catch(_f){Su(e,e.return,_f)}try{nc(5,e,e.return)}catch(_f){Su(e,e.return,_f)}}break;case 1:mc(t,e),bc(e),512&o&&null!==n&&Js(n,n.return);break;case 5:if(mc(t,e),bc(e),512&o&&null!==n&&Js(n,n.return),32&e.flags){var r=e.stateNode;try{ft(r,"")}catch(_f){Su(e,e.return,_f)}}if(4&o&&null!=(r=e.stateNode)){var i=e.memoizedProps,a=null!==n?n.memoizedProps:i,l=e.type,s=e.updateQueue;if(e.updateQueue=null,null!==s)try{"input"===l&&"radio"===i.type&&null!=i.name&&Ge(r,i),yt(l,a);var c=yt(l,i);for(a=0;a<s.length;a+=2){var u=s[a],d=s[a+1];"style"===u?gt(r,d):"dangerouslySetInnerHTML"===u?dt(r,d):"children"===u?ft(r,d):ye(r,u,d,c)}switch(l){case"input":Ze(r,i);break;case"textarea":it(r,i);break;case"select":var f=r._wrapperState.wasMultiple;r._wrapperState.wasMultiple=!!i.multiple;var p=i.value;null!=p?nt(r,!!i.multiple,p,!1):f!==!!i.multiple&&(null!=i.defaultValue?nt(r,!!i.multiple,i.defaultValue,!0):nt(r,!!i.multiple,i.multiple?[]:"",!1))}r[hi]=i}catch(_f){Su(e,e.return,_f)}}break;case 6:if(mc(t,e),bc(e),4&o){if(null===e.stateNode)throw Error(ie(162));r=e.stateNode,i=e.memoizedProps;try{r.nodeValue=i}catch(_f){Su(e,e.return,_f)}}break;case 3:if(mc(t,e),bc(e),4&o&&null!==n&&n.memoizedState.isDehydrated)try{Fn(t.containerInfo)}catch(_f){Su(e,e.return,_f)}break;case 4:default:mc(t,e),bc(e);break;case 13:mc(t,e),bc(e),8192&(r=e.child).flags&&(i=null!==r.memoizedState,r.stateNode.isHidden=i,!i||null!==r.alternate&&null!==r.alternate.memoizedState||(Vc=Gt())),4&o&&hc(e);break;case 22:if(u=null!==n&&null!==n.memoizedState,1&e.mode?(Xs=(c=Xs)||u,mc(t,e),Xs=c):mc(t,e),bc(e),8192&o){if(c=null!==e.memoizedState,(e.stateNode.isHidden=c)&&!u&&1&e.mode)for(Zs=e,u=e.child;null!==u;){for(d=Zs=u;null!==Zs;){switch(p=(f=Zs).child,f.tag){case 0:case 11:case 14:case 15:nc(4,f,f.return);break;case 1:Js(f,f.return);var h=f.stateNode;if("function"==typeof h.componentWillUnmount){o=f,n=f.return;try{t=o,h.props=t.memoizedProps,h.state=t.memoizedState,h.componentWillUnmount()}catch(_f){Su(o,n,_f)}}break;case 5:Js(f,f.return);break;case 22:if(null!==f.memoizedState){xc(d);continue}}null!==p?(p.return=f,Zs=p):xc(d)}u=u.sibling}e:for(u=null,d=e;;){if(5===d.tag){if(null===u){u=d;try{r=d.stateNode,c?"function"==typeof(i=r.style).setProperty?i.setProperty("display","none","important"):i.display="none":(l=d.stateNode,a=null!=(s=d.memoizedProps.style)&&s.hasOwnProperty("display")?s.display:null,l.style.display=mt("display",a))}catch(_f){Su(e,e.return,_f)}}}else if(6===d.tag){if(null===u)try{d.stateNode.nodeValue=c?"":d.memoizedProps}catch(_f){Su(e,e.return,_f)}}else if((22!==d.tag&&23!==d.tag||null===d.memoizedState||d===e)&&null!==d.child){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;null===d.sibling;){if(null===d.return||d.return===e)break e;u===d&&(u=null),d=d.return}u===d&&(u=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:mc(t,e),bc(e),4&o&&hc(e);case 21:}}function bc(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(ac(n)){var o=n;break e}n=n.return}throw Error(ie(160))}switch(o.tag){case 5:var r=o.stateNode;32&o.flags&&(ft(r,""),o.flags&=-33),cc(e,lc(e),r);break;case 3:case 4:var i=o.stateNode.containerInfo;sc(e,lc(e),i);break;default:throw Error(ie(161))}}catch($f){Su(e,e.return,$f)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function vc(e,t,n){Zs=e,yc(e)}function yc(e,t,n){for(var o=!!(1&e.mode);null!==Zs;){var r=Zs,i=r.child;if(22===r.tag&&o){var a=null!==r.memoizedState||Ys;if(!a){var l=r.alternate,s=null!==l&&null!==l.memoizedState||Xs;l=Ys;var c=Xs;if(Ys=a,(Xs=s)&&!c)for(Zs=r;null!==Zs;)s=(a=Zs).child,22===a.tag&&null!==a.memoizedState?kc(r):null!==s?(s.return=a,Zs=s):kc(r);for(;null!==i;)Zs=i,yc(i),i=i.sibling;Zs=r,Ys=l,Xs=c}wc(e)}else 8772&r.subtreeFlags&&null!==i?(i.return=r,Zs=i):wc(e)}}function wc(e){for(;null!==Zs;){var t=Zs;if(8772&t.flags){var n=t.alternate;try{if(8772&t.flags)switch(t.tag){case 0:case 11:case 15:Xs||oc(5,t);break;case 1:var o=t.stateNode;if(4&t.flags&&!Xs)if(null===n)o.componentDidMount();else{var r=t.elementType===t.type?n.memoizedProps:ns(t.type,n.memoizedProps);o.componentDidUpdate(r,n.memoizedState,o.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;null!==i&&Wa(t,i,o);break;case 3:var a=t.updateQueue;if(null!==a){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Wa(t,a,n)}break;case 5:var l=t.stateNode;if(null===n&&4&t.flags){n=l;var s=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":s.autoFocus&&n.focus();break;case"img":s.src&&(n.src=s.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var c=t.alternate;if(null!==c){var u=c.memoizedState;if(null!==u){var d=u.dehydrated;null!==d&&Fn(d)}}}break;default:throw Error(ie(163))}Xs||512&t.flags&&rc(t)}catch(Lf){Su(t,t.return,Lf)}}if(t===e){Zs=null;break}if(null!==(n=t.sibling)){n.return=t.return,Zs=n;break}Zs=t.return}}function xc(e){for(;null!==Zs;){var t=Zs;if(t===e){Zs=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Zs=n;break}Zs=t.return}}function kc(e){for(;null!==Zs;){var t=Zs;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{oc(4,t)}catch($f){Su(t,n,$f)}break;case 1:var o=t.stateNode;if("function"==typeof o.componentDidMount){var r=t.return;try{o.componentDidMount()}catch($f){Su(t,r,$f)}}var i=t.return;try{rc(t)}catch($f){Su(t,i,$f)}break;case 5:var a=t.return;try{rc(t)}catch($f){Su(t,a,$f)}}}catch($f){Su(t,t.return,$f)}if(t===e){Zs=null;break}var l=t.sibling;if(null!==l){l.return=t.return,Zs=l;break}Zs=t.return}}var Ec,Sc=Math.ceil,Cc=we.ReactCurrentDispatcher,$c=we.ReactCurrentOwner,Tc=we.ReactCurrentBatchConfig,Pc=0,Rc=null,Ic=null,Oc=0,Lc=0,_c=Ci(0),zc=0,Nc=null,Mc=0,jc=0,Dc=0,Ac=null,Bc=null,Vc=0,Hc=1/0,Fc=null,Wc=!1,Uc=null,qc=null,Qc=!1,Kc=null,Yc=0,Xc=0,Gc=null,Zc=-1,Jc=0;function eu(){return 6&Pc?Gt():-1!==Zc?Zc:Zc=Gt()}function tu(e){return 1&e.mode?2&Pc&&0!==Oc?Oc&-Oc:null!==ga.transition?(0===Jc&&(Jc=gn()),Jc):0!==(e=wn)?e:e=void 0===(e=window.event)?16:Gn(e.type):1}function nu(e,t,n,o){if(50<Xc)throw Xc=0,Gc=null,Error(ie(185));vn(e,n,o),2&Pc&&e===Rc||(e===Rc&&(!(2&Pc)&&(jc|=n),4===zc&&lu(e,Oc)),ou(e,o),1===n&&0===Pc&&!(1&t.mode)&&(Hc=Gt()+500,Bi&&Fi()))}function ou(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,o=e.pingedLanes,r=e.expirationTimes,i=e.pendingLanes;0<i;){var a=31-ln(i),l=1<<a,s=r[a];-1===s?l&n&&!(l&o)||(r[a]=hn(l,t)):s<=t&&(e.expiredLanes|=l),i&=~l}}(e,t);var o=pn(e,e===Rc?Oc:0);if(0===o)null!==n&&Kt(n),e.callbackNode=null,e.callbackPriority=0;else if(t=o&-o,e.callbackPriority!==t){if(null!=n&&Kt(n),1===t)0===e.tag?function(e){Bi=!0,Hi(e)}(su.bind(null,e)):Hi(su.bind(null,e)),li((function(){!(6&Pc)&&Fi()})),n=null;else{switch(xn(o)){case 1:n=Jt;break;case 4:n=en;break;case 16:default:n=tn;break;case 536870912:n=on}n=Ru(n,ru.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ru(e,t){if(Zc=-1,Jc=0,6&Pc)throw Error(ie(327));var n=e.callbackNode;if(ku()&&e.callbackNode!==n)return null;var o=pn(e,e===Rc?Oc:0);if(0===o)return null;if(30&o||o&e.expiredLanes||t)t=gu(e,o);else{t=o;var r=Pc;Pc|=2;var i=hu();for(Rc===e&&Oc===t||(Fc=null,Hc=Gt()+500,fu(e,t));;)try{vu();break}catch(Zp){pu(e,Zp)}Ta(),Cc.current=i,Pc=r,null!==Ic?t=0:(Rc=null,Oc=0,t=zc)}if(0!==t){if(2===t&&0!==(r=mn(e))&&(o=r,t=iu(e,r)),1===t)throw n=Nc,fu(e,0),lu(e,o),ou(e,Gt()),n;if(6===t)lu(e,o);else{if(r=e.current.alternate,!(30&o||function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var o=0;o<n.length;o++){var r=n[o],i=r.getSnapshot;r=r.value;try{if(!sr(i(),r))return!1}catch(a){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(r)||(t=gu(e,o),2===t&&(i=mn(e),0!==i&&(o=i,t=iu(e,i))),1!==t)))throw n=Nc,fu(e,0),lu(e,o),ou(e,Gt()),n;switch(e.finishedWork=r,e.finishedLanes=o,t){case 0:case 1:throw Error(ie(345));case 2:case 5:xu(e,Bc,Fc);break;case 3:if(lu(e,o),(130023424&o)===o&&10<(t=Vc+500-Gt())){if(0!==pn(e,0))break;if(((r=e.suspendedLanes)&o)!==o){eu(),e.pingedLanes|=e.suspendedLanes&r;break}e.timeoutHandle=ri(xu.bind(null,e,Bc,Fc),t);break}xu(e,Bc,Fc);break;case 4:if(lu(e,o),(4194240&o)===o)break;for(t=e.eventTimes,r=-1;0<o;){var a=31-ln(o);i=1<<a,(a=t[a])>r&&(r=a),o&=~i}if(o=r,10<(o=(120>(o=Gt()-o)?120:480>o?480:1080>o?1080:1920>o?1920:3e3>o?3e3:4320>o?4320:1960*Sc(o/1960))-o)){e.timeoutHandle=ri(xu.bind(null,e,Bc,Fc),o);break}xu(e,Bc,Fc);break;default:throw Error(ie(329))}}}return ou(e,Gt()),e.callbackNode===n?ru.bind(null,e):null}function iu(e,t){var n=Ac;return e.current.memoizedState.isDehydrated&&(fu(e,t).flags|=256),2!==(e=gu(e,t))&&(t=Bc,Bc=n,null!==t&&au(t)),e}function au(e){null===Bc?Bc=e:Bc.push.apply(Bc,e)}function lu(e,t){for(t&=~Dc,t&=~jc,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-ln(t),o=1<<n;e[n]=-1,t&=~o}}function su(e){if(6&Pc)throw Error(ie(327));ku();var t=pn(e,0);if(!(1&t))return ou(e,Gt()),null;var n=gu(e,t);if(0!==e.tag&&2===n){var o=mn(e);0!==o&&(t=o,n=iu(e,o))}if(1===n)throw n=Nc,fu(e,0),lu(e,t),ou(e,Gt()),n;if(6===n)throw Error(ie(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,xu(e,Bc,Fc),ou(e,Gt()),null}function cu(e,t){var n=Pc;Pc|=1;try{return e(t)}finally{0===(Pc=n)&&(Hc=Gt()+500,Bi&&Fi())}}function uu(e){null!==Kc&&0===Kc.tag&&!(6&Pc)&&ku();var t=Pc;Pc|=1;var n=Tc.transition,o=wn;try{if(Tc.transition=null,wn=1,e)return e()}finally{wn=o,Tc.transition=n,!(6&(Pc=t))&&Fi()}}function du(){Lc=_c.current,$i(_c)}function fu(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,ii(n)),null!==Ic)for(n=Ic.return;null!==n;){var o=n;switch(na(o),o.tag){case 1:null!=(o=o.type.childContextTypes)&&zi();break;case 3:Ga(),$i(Ii),$i(Ri),ol();break;case 5:Ja(o);break;case 4:Ga();break;case 13:case 19:$i(el);break;case 10:Pa(o.type._context);break;case 22:case 23:du()}n=n.return}if(Rc=e,Ic=e=_u(e.current,null),Oc=Lc=t,zc=0,Nc=null,Dc=jc=Mc=0,Bc=Ac=null,null!==La){for(t=0;t<La.length;t++)if(null!==(o=(n=La[t]).interleaved)){n.interleaved=null;var r=o.next,i=n.pending;if(null!==i){var a=i.next;i.next=r,o.next=a}n.pending=o}La=null}return e}function pu(e,t){for(;;){var n=Ic;try{if(Ta(),rl.current=Zl,ul){for(var o=ll.memoizedState;null!==o;){var r=o.queue;null!==r&&(r.pending=null),o=o.next}ul=!1}if(al=0,cl=sl=ll=null,dl=!1,fl=0,$c.current=null,null===n||null===n.return){zc=1,Nc=t,Ic=null;break}e:{var i=e,a=n.return,l=n,s=t;if(t=Oc,l.flags|=32768,null!==s&&"object"==typeof s&&"function"==typeof s.then){var c=s,u=l,d=u.tag;if(!(1&u.mode||0!==d&&11!==d&&15!==d)){var f=u.alternate;f?(u.updateQueue=f.updateQueue,u.memoizedState=f.memoizedState,u.lanes=f.lanes):(u.updateQueue=null,u.memoizedState=null)}var p=gs(a);if(null!==p){p.flags&=-257,bs(p,a,l,0,t),1&p.mode&&ms(i,c,t),s=c;var h=(t=p).updateQueue;if(null===h){var m=new Set;m.add(s),t.updateQueue=m}else h.add(s);break e}if(!(1&t)){ms(i,c,t),mu();break e}s=Error(ie(426))}else if(ia&&1&l.mode){var g=gs(a);if(null!==g){!(65536&g.flags)&&(g.flags|=256),bs(g,a,l,0,t),ma(cs(s,l));break e}}i=s=cs(s,l),4!==zc&&(zc=2),null===Ac?Ac=[i]:Ac.push(i),i=a;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t,Ha(i,ps(0,s,t));break e;case 1:l=s;var b=i.type,v=i.stateNode;if(!(128&i.flags||"function"!=typeof b.getDerivedStateFromError&&(null===v||"function"!=typeof v.componentDidCatch||null!==qc&&qc.has(v)))){i.flags|=65536,t&=-t,i.lanes|=t,Ha(i,hs(i,l,t));break e}}i=i.return}while(null!==i)}wu(n)}catch(y){t=y,Ic===n&&null!==n&&(Ic=n=n.return);continue}break}}function hu(){var e=Cc.current;return Cc.current=Zl,null===e?Zl:e}function mu(){0!==zc&&3!==zc&&2!==zc||(zc=4),null===Rc||!(268435455&Mc)&&!(268435455&jc)||lu(Rc,Oc)}function gu(e,t){var n=Pc;Pc|=2;var o=hu();for(Rc===e&&Oc===t||(Fc=null,fu(e,t));;)try{bu();break}catch(kf){pu(e,kf)}if(Ta(),Pc=n,Cc.current=o,null!==Ic)throw Error(ie(261));return Rc=null,Oc=0,zc}function bu(){for(;null!==Ic;)yu(Ic)}function vu(){for(;null!==Ic&&!Yt();)yu(Ic)}function yu(e){var t=Ec(e.alternate,e,Lc);e.memoizedProps=e.pendingProps,null===t?wu(e):Ic=t,$c.current=null}function wu(e){var t=e;do{var n=t.alternate;if(e=t.return,32768&t.flags){if(null!==(n=Ks(n,t)))return n.flags&=32767,void(Ic=n);if(null===e)return zc=6,void(Ic=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}else if(null!==(n=Qs(n,t,Lc)))return void(Ic=n);if(null!==(t=t.sibling))return void(Ic=t);Ic=t=e}while(null!==t);0===zc&&(zc=5)}function xu(e,t,n){var o=wn,r=Tc.transition;try{Tc.transition=null,wn=1,function(e,t,n,o){do{ku()}while(null!==Kc);if(6&Pc)throw Error(ie(327));n=e.finishedWork;var r=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(ie(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var o=e.eventTimes;for(e=e.expirationTimes;0<n;){var r=31-ln(n),i=1<<r;t[r]=0,o[r]=-1,e[r]=-1,n&=~i}}(e,i),e===Rc&&(Ic=Rc=null,Oc=0),!(2064&n.subtreeFlags)&&!(2064&n.flags)||Qc||(Qc=!0,Ru(tn,(function(){return ku(),null}))),i=!!(15990&n.flags),15990&n.subtreeFlags||i){i=Tc.transition,Tc.transition=null;var a=wn;wn=1;var l=Pc;Pc|=4,$c.current=null,function(e,t){if(ti=Un,hr(e=pr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var o=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(o&&0!==o.rangeCount){n=o.anchorNode;var r=o.anchorOffset,i=o.focusNode;o=o.focusOffset;try{n.nodeType,i.nodeType}catch(S){n=null;break e}var a=0,l=-1,s=-1,c=0,u=0,d=e,f=null;t:for(;;){for(var p;d!==n||0!==r&&3!==d.nodeType||(l=a+r),d!==i||0!==o&&3!==d.nodeType||(s=a+o),3===d.nodeType&&(a+=d.nodeValue.length),null!==(p=d.firstChild);)f=d,d=p;for(;;){if(d===e)break t;if(f===n&&++c===r&&(l=a),f===i&&++u===o&&(s=a),null!==(p=d.nextSibling))break;f=(d=f).parentNode}d=p}n=-1===l||-1===s?null:{start:l,end:s}}else n=null}n=n||{start:0,end:0}}else n=null;for(ni={focusedElem:e,selectionRange:n},Un=!1,Zs=t;null!==Zs;)if(e=(t=Zs).child,1028&t.subtreeFlags&&null!==e)e.return=t,Zs=e;else for(;null!==Zs;){t=Zs;try{var h=t.alternate;if(1024&t.flags)switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==h){var m=h.memoizedProps,g=h.memoizedState,b=t.stateNode,v=b.getSnapshotBeforeUpdate(t.elementType===t.type?m:ns(t.type,m),g);b.__reactInternalSnapshotBeforeUpdate=v}break;case 3:var y=t.stateNode.containerInfo;1===y.nodeType?y.textContent="":9===y.nodeType&&y.documentElement&&y.removeChild(y.documentElement);break;default:throw Error(ie(163))}}catch(S){Su(t,t.return,S)}if(null!==(e=t.sibling)){e.return=t.return,Zs=e;break}Zs=t.return}h=tc,tc=!1}(e,n),gc(n,e),mr(ni),Un=!!ti,ni=ti=null,e.current=n,vc(n),Xt(),Pc=l,wn=a,Tc.transition=i}else e.current=n;if(Qc&&(Qc=!1,Kc=e,Yc=r),0===(i=e.pendingLanes)&&(qc=null),function(e){if(an&&"function"==typeof an.onCommitFiberRoot)try{an.onCommitFiberRoot(rn,e,void 0,!(128&~e.current.flags))}catch(t){}}(n.stateNode),ou(e,Gt()),null!==t)for(o=e.onRecoverableError,n=0;n<t.length;n++)r=t[n],o(r.value,{componentStack:r.stack,digest:r.digest});if(Wc)throw Wc=!1,e=Uc,Uc=null,e;!!(1&Yc)&&0!==e.tag&&ku(),1&(i=e.pendingLanes)?e===Gc?Xc++:(Xc=0,Gc=e):Xc=0,Fi()}(e,t,n,o)}finally{Tc.transition=r,wn=o}return null}function ku(){if(null!==Kc){var e=xn(Yc),t=Tc.transition,n=wn;try{if(Tc.transition=null,wn=16>e?16:e,null===Kc)var o=!1;else{if(e=Kc,Kc=null,Yc=0,6&Pc)throw Error(ie(331));var r=Pc;for(Pc|=4,Zs=e.current;null!==Zs;){var i=Zs,a=i.child;if(16&Zs.flags){var l=i.deletions;if(null!==l){for(var s=0;s<l.length;s++){var c=l[s];for(Zs=c;null!==Zs;){var u=Zs;switch(u.tag){case 0:case 11:case 15:nc(8,u,i)}var d=u.child;if(null!==d)d.return=u,Zs=d;else for(;null!==Zs;){var f=(u=Zs).sibling,p=u.return;if(ic(u),u===c){Zs=null;break}if(null!==f){f.return=p,Zs=f;break}Zs=p}}}var h=i.alternate;if(null!==h){var m=h.child;if(null!==m){h.child=null;do{var g=m.sibling;m.sibling=null,m=g}while(null!==m)}}Zs=i}}if(2064&i.subtreeFlags&&null!==a)a.return=i,Zs=a;else e:for(;null!==Zs;){if(2048&(i=Zs).flags)switch(i.tag){case 0:case 11:case 15:nc(9,i,i.return)}var b=i.sibling;if(null!==b){b.return=i.return,Zs=b;break e}Zs=i.return}}var v=e.current;for(Zs=v;null!==Zs;){var y=(a=Zs).child;if(2064&a.subtreeFlags&&null!==y)y.return=a,Zs=y;else e:for(a=v;null!==Zs;){if(2048&(l=Zs).flags)try{switch(l.tag){case 0:case 11:case 15:oc(9,l)}}catch(x){Su(l,l.return,x)}if(l===a){Zs=null;break e}var w=l.sibling;if(null!==w){w.return=l.return,Zs=w;break e}Zs=l.return}}if(Pc=r,Fi(),an&&"function"==typeof an.onPostCommitFiberRoot)try{an.onPostCommitFiberRoot(rn,e)}catch(x){}o=!0}return o}finally{wn=n,Tc.transition=t}}return!1}function Eu(e,t,n){e=Ba(e,t=ps(0,t=cs(n,t),1),1),t=eu(),null!==e&&(vn(e,1,t),ou(e,t))}function Su(e,t,n){if(3===e.tag)Eu(e,e,n);else for(;null!==t;){if(3===t.tag){Eu(t,e,n);break}if(1===t.tag){var o=t.stateNode;if("function"==typeof t.type.getDerivedStateFromError||"function"==typeof o.componentDidCatch&&(null===qc||!qc.has(o))){t=Ba(t,e=hs(t,e=cs(n,e),1),1),e=eu(),null!==t&&(vn(t,1,e),ou(t,e));break}}t=t.return}}function Cu(e,t,n){var o=e.pingCache;null!==o&&o.delete(t),t=eu(),e.pingedLanes|=e.suspendedLanes&n,Rc===e&&(Oc&n)===n&&(4===zc||3===zc&&(130023424&Oc)===Oc&&500>Gt()-Vc?fu(e,0):Dc|=n),ou(e,t)}function $u(e,t){0===t&&(1&e.mode?(t=dn,!(130023424&(dn<<=1))&&(dn=4194304)):t=1);var n=eu();null!==(e=Na(e,t))&&(vn(e,t,n),ou(e,n))}function Tu(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),$u(e,n)}function Pu(e,t){var n=0;switch(e.tag){case 13:var o=e.stateNode,r=e.memoizedState;null!==r&&(n=r.retryLane);break;case 19:o=e.stateNode;break;default:throw Error(ie(314))}null!==o&&o.delete(t),$u(e,n)}function Ru(e,t){return Qt(e,t)}function Iu(e,t,n,o){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=o,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ou(e,t,n,o){return new Iu(e,t,n,o)}function Lu(e){return!(!(e=e.prototype)||!e.isReactComponent)}function _u(e,t){var n=e.alternate;return null===n?((n=Ou(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function zu(e,t,n,o,r,i){var a=2;if(o=e,"function"==typeof e)Lu(e)&&(a=1);else if("string"==typeof e)a=5;else e:switch(e){case Ee:return Nu(n.children,r,i,t);case Se:a=8,r|=8;break;case Ce:return(e=Ou(12,n,t,2|r)).elementType=Ce,e.lanes=i,e;case Re:return(e=Ou(13,n,t,r)).elementType=Re,e.lanes=i,e;case Ie:return(e=Ou(19,n,t,r)).elementType=Ie,e.lanes=i,e;case _e:return Mu(n,r,i,t);default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case $e:a=10;break e;case Te:a=9;break e;case Pe:a=11;break e;case Oe:a=14;break e;case Le:a=16,o=null;break e}throw Error(ie(130,null==e?e:typeof e,""))}return(t=Ou(a,n,t,r)).elementType=e,t.type=o,t.lanes=i,t}function Nu(e,t,n,o){return(e=Ou(7,e,o,t)).lanes=n,e}function Mu(e,t,n,o){return(e=Ou(22,e,o,t)).elementType=_e,e.lanes=n,e.stateNode={isHidden:!1},e}function ju(e,t,n){return(e=Ou(6,e,null,t)).lanes=n,e}function Du(e,t,n){return(t=Ou(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Au(e,t,n,o,r){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=bn(0),this.expirationTimes=bn(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=bn(0),this.identifierPrefix=o,this.onRecoverableError=r,this.mutableSourceEagerHydrationData=null}function Bu(e,t,n,o,r,i,a,l,s){return e=new Au(e,t,n,l,s),1===t?(t=1,!0===i&&(t|=8)):t=0,i=Ou(3,null,null,t),e.current=i,i.stateNode=e,i.memoizedState={element:o,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},ja(i),e}function Vu(e){if(!e)return Pi;e:{if(Ht(e=e._reactInternals)!==e||1!==e.tag)throw Error(ie(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(_i(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(ie(171))}if(1===e.tag){var n=e.type;if(_i(n))return Mi(e,n,t)}return t}function Hu(e,t,n,o,r,i,a,l,s){return(e=Bu(n,o,!0,e,0,i,0,l,s)).context=Vu(null),n=e.current,(i=Aa(o=eu(),r=tu(n))).callback=null!=t?t:null,Ba(n,i,r),e.current.lanes=r,vn(e,r,o),ou(e,o),e}function Fu(e,t,n,o){var r=t.current,i=eu(),a=tu(r);return n=Vu(n),null===t.context?t.context=n:t.pendingContext=n,(t=Aa(i,a)).payload={element:e},null!==(o=void 0===o?null:o)&&(t.callback=o),null!==(e=Ba(r,t,a))&&(nu(e,r,a,i),Va(e,r,a)),a}function Wu(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function Uu(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function qu(e,t){Uu(e,t),(e=e.alternate)&&Uu(e,t)}Ec=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Ii.current)ys=!0;else{if(!(e.lanes&n||128&t.flags))return ys=!1,function(e,t,n){switch(t.tag){case 3:Rs(t),ha();break;case 5:Za(t);break;case 1:_i(t.type)&&ji(t);break;case 4:Xa(t,t.stateNode.containerInfo);break;case 10:var o=t.type._context,r=t.memoizedProps.value;Ti(Ea,o._currentValue),o._currentValue=r;break;case 13:if(null!==(o=t.memoizedState))return null!==o.dehydrated?(Ti(el,1&el.current),t.flags|=128,null):n&t.child.childLanes?js(e,t,n):(Ti(el,1&el.current),null!==(e=Ws(e,t,n))?e.sibling:null);Ti(el,1&el.current);break;case 19:if(o=!!(n&t.childLanes),128&e.flags){if(o)return Hs(e,t,n);t.flags|=128}if(null!==(r=t.memoizedState)&&(r.rendering=null,r.tail=null,r.lastEffect=null),Ti(el,el.current),o)break;return null;case 22:case 23:return t.lanes=0,Ss(e,t,n)}return Ws(e,t,n)}(e,t,n);ys=!!(131072&e.flags)}else ys=!1,ia&&1048576&t.flags&&ea(t,Qi,t.index);switch(t.lanes=0,t.tag){case 2:var o=t.type;Fs(e,t),e=t.pendingProps;var r=Li(t,Ri.current);Ia(t,n),r=gl(null,t,o,e,r,n);var i=bl();return t.flags|=1,"object"==typeof r&&null!==r&&"function"==typeof r.render&&void 0===r.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,_i(o)?(i=!0,ji(t)):i=!1,t.memoizedState=null!==r.state&&void 0!==r.state?r.state:null,ja(t),r.updater=rs,t.stateNode=r,r._reactInternals=t,ss(t,o,e,n),t=Ps(null,t,o,!0,i,n)):(t.tag=0,ia&&i&&ta(t),ws(null,t,r,n),t=t.child),t;case 16:o=t.elementType;e:{switch(Fs(e,t),e=t.pendingProps,o=(r=o._init)(o._payload),t.type=o,r=t.tag=function(e){if("function"==typeof e)return Lu(e)?1:0;if(null!=e){if((e=e.$$typeof)===Pe)return 11;if(e===Oe)return 14}return 2}(o),e=ns(o,e),r){case 0:t=$s(null,t,o,e,n);break e;case 1:t=Ts(null,t,o,e,n);break e;case 11:t=xs(null,t,o,e,n);break e;case 14:t=ks(null,t,o,ns(o.type,e),n);break e}throw Error(ie(306,o,""))}return t;case 0:return o=t.type,r=t.pendingProps,$s(e,t,o,r=t.elementType===o?r:ns(o,r),n);case 1:return o=t.type,r=t.pendingProps,Ts(e,t,o,r=t.elementType===o?r:ns(o,r),n);case 3:e:{if(Rs(t),null===e)throw Error(ie(387));o=t.pendingProps,r=(i=t.memoizedState).element,Da(e,t),Fa(t,o,null,n);var a=t.memoizedState;if(o=a.element,i.isDehydrated){if(i={element:o,isDehydrated:!1,cache:a.cache,pendingSuspenseBoundaries:a.pendingSuspenseBoundaries,transitions:a.transitions},t.updateQueue.baseState=i,t.memoizedState=i,256&t.flags){t=Is(e,t,o,n,r=cs(Error(ie(423)),t));break e}if(o!==r){t=Is(e,t,o,n,r=cs(Error(ie(424)),t));break e}for(ra=ui(t.stateNode.containerInfo.firstChild),oa=t,ia=!0,aa=null,n=ka(t,null,o,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(ha(),o===r){t=Ws(e,t,n);break e}ws(e,t,o,n)}t=t.child}return t;case 5:return Za(t),null===e&&ua(t),o=t.type,r=t.pendingProps,i=null!==e?e.memoizedProps:null,a=r.children,oi(o,r)?a=null:null!==i&&oi(o,i)&&(t.flags|=32),Cs(e,t),ws(e,t,a,n),t.child;case 6:return null===e&&ua(t),null;case 13:return js(e,t,n);case 4:return Xa(t,t.stateNode.containerInfo),o=t.pendingProps,null===e?t.child=xa(t,null,o,n):ws(e,t,o,n),t.child;case 11:return o=t.type,r=t.pendingProps,xs(e,t,o,r=t.elementType===o?r:ns(o,r),n);case 7:return ws(e,t,t.pendingProps,n),t.child;case 8:case 12:return ws(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(o=t.type._context,r=t.pendingProps,i=t.memoizedProps,a=r.value,Ti(Ea,o._currentValue),o._currentValue=a,null!==i)if(sr(i.value,a)){if(i.children===r.children&&!Ii.current){t=Ws(e,t,n);break e}}else for(null!==(i=t.child)&&(i.return=t);null!==i;){var l=i.dependencies;if(null!==l){a=i.child;for(var s=l.firstContext;null!==s;){if(s.context===o){if(1===i.tag){(s=Aa(-1,n&-n)).tag=2;var c=i.updateQueue;if(null!==c){var u=(c=c.shared).pending;null===u?s.next=s:(s.next=u.next,u.next=s),c.pending=s}}i.lanes|=n,null!==(s=i.alternate)&&(s.lanes|=n),Ra(i.return,n,t),l.lanes|=n;break}s=s.next}}else if(10===i.tag)a=i.type===t.type?null:i.child;else if(18===i.tag){if(null===(a=i.return))throw Error(ie(341));a.lanes|=n,null!==(l=a.alternate)&&(l.lanes|=n),Ra(a,n,t),a=i.sibling}else a=i.child;if(null!==a)a.return=i;else for(a=i;null!==a;){if(a===t){a=null;break}if(null!==(i=a.sibling)){i.return=a.return,a=i;break}a=a.return}i=a}ws(e,t,r.children,n),t=t.child}return t;case 9:return r=t.type,o=t.pendingProps.children,Ia(t,n),o=o(r=Oa(r)),t.flags|=1,ws(e,t,o,n),t.child;case 14:return r=ns(o=t.type,t.pendingProps),ks(e,t,o,r=ns(o.type,r),n);case 15:return Es(e,t,t.type,t.pendingProps,n);case 17:return o=t.type,r=t.pendingProps,r=t.elementType===o?r:ns(o,r),Fs(e,t),t.tag=1,_i(o)?(e=!0,ji(t)):e=!1,Ia(t,n),as(t,o,r),ss(t,o,r,n),Ps(null,t,o,!0,e,n);case 19:return Hs(e,t,n);case 22:return Ss(e,t,n)}throw Error(ie(156,t.tag))};var Qu="function"==typeof reportError?reportError:function(e){console.error(e)};function Ku(e){this._internalRoot=e}function Yu(e){this._internalRoot=e}function Xu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Gu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Zu(){}function Ju(e,t,n,o,r){var i=n._reactRootContainer;if(i){var a=i;if("function"==typeof r){var l=r;r=function(){var e=Wu(a);l.call(e)}}Fu(t,a,e,r)}else a=function(e,t,n,o,r){if(r){if("function"==typeof o){var i=o;o=function(){var e=Wu(a);i.call(e)}}var a=Hu(t,o,e,0,null,!1,0,"",Zu);return e._reactRootContainer=a,e[mi]=a.current,Fr(8===e.nodeType?e.parentNode:e),uu(),a}for(;r=e.lastChild;)e.removeChild(r);if("function"==typeof o){var l=o;o=function(){var e=Wu(s);l.call(e)}}var s=Bu(e,0,!1,null,0,!1,0,"",Zu);return e._reactRootContainer=s,e[mi]=s.current,Fr(8===e.nodeType?e.parentNode:e),uu((function(){Fu(t,s,n,o)})),s}(n,t,e,r,o);return Wu(a)}Yu.prototype.render=Ku.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(ie(409));Fu(e,t,null,null)},Yu.prototype.unmount=Ku.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;uu((function(){Fu(null,e,null,null)})),t[mi]=null}},Yu.prototype.unstable_scheduleHydration=function(e){if(e){var t=Cn();e={blockedOn:null,target:e,priority:t};for(var n=0;n<zn.length&&0!==t&&t<zn[n].priority;n++);zn.splice(n,0,e),0===n&&Dn(e)}},kn=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=fn(t.pendingLanes);0!==n&&(yn(t,1|n),ou(t,Gt()),!(6&Pc)&&(Hc=Gt()+500,Fi()))}break;case 13:uu((function(){var t=Na(e,1);if(null!==t){var n=eu();nu(t,e,1,n)}})),qu(e,1)}},En=function(e){if(13===e.tag){var t=Na(e,134217728);null!==t&&nu(t,e,134217728,eu()),qu(e,134217728)}},Sn=function(e){if(13===e.tag){var t=tu(e),n=Na(e,t);null!==n&&nu(n,e,t,eu()),qu(e,t)}},Cn=function(){return wn},$n=function(e,t){var n=wn;try{return wn=e,t()}finally{wn=n}},kt=function(e,t,n){switch(t){case"input":if(Ze(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var o=n[t];if(o!==e&&o.form===e.form){var r=ki(o);if(!r)throw Error(ie(90));Qe(o),Ze(o,r)}}}break;case"textarea":it(e,n);break;case"select":null!=(t=n.value)&&nt(e,!!n.multiple,t,!1)}},Pt=cu,Rt=uu;var ed={usingClientEntryPoint:!1,Events:[wi,xi,ki,$t,Tt,cu]},td={findFiberByHostInstance:yi,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nd={bundleType:td.bundleType,version:td.version,rendererPackageName:td.rendererPackageName,rendererConfig:td.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:we.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=Ut(e))?null:e.stateNode},findFiberByHostInstance:td.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var od=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!od.isDisabled&&od.supportsFiber)try{rn=od.inject(nd),an=od}catch(ut){}}J.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ed,J.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Xu(t))throw Error(ie(200));return function(e,t,n){var o=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:ke,key:null==o?null:""+o,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},J.createRoot=function(e,t){if(!Xu(e))throw Error(ie(299));var n=!1,o="",r=Qu;return null!=t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(o=t.identifierPrefix),void 0!==t.onRecoverableError&&(r=t.onRecoverableError)),t=Bu(e,1,!1,null,0,n,0,o,r),e[mi]=t.current,Fr(8===e.nodeType?e.parentNode:e),new Ku(t)},J.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"==typeof e.render)throw Error(ie(188));throw e=Object.keys(e).join(","),Error(ie(268,e))}return e=null===(e=Ut(t))?null:e.stateNode},J.flushSync=function(e){return uu(e)},J.hydrate=function(e,t,n){if(!Gu(t))throw Error(ie(200));return Ju(null,e,t,!0,n)},J.hydrateRoot=function(e,t,n){if(!Xu(e))throw Error(ie(405));var o=null!=n&&n.hydratedSources||null,r=!1,i="",a=Qu;if(null!=n&&(!0===n.unstable_strictMode&&(r=!0),void 0!==n.identifierPrefix&&(i=n.identifierPrefix),void 0!==n.onRecoverableError&&(a=n.onRecoverableError)),t=Hu(t,null,e,1,null!=n?n:null,r,0,i,a),e[mi]=t.current,Fr(e),o)for(e=0;e<o.length;e++)r=(r=(n=o[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,r]:t.mutableSourceEagerHydrationData.push(n,r);return new Yu(t)},J.render=function(e,t,n){if(!Gu(t))throw Error(ie(200));return Ju(null,e,t,!1,n)},J.unmountComponentAtNode=function(e){if(!Gu(e))throw Error(ie(40));return!!e._reactRootContainer&&(uu((function(){Ju(null,null,e,!1,(function(){e._reactRootContainer=null,e[mi]=null}))})),!0)},J.unstable_batchedUpdates=cu,J.unstable_renderSubtreeIntoContainer=function(e,t,n,o){if(!Gu(n))throw Error(ie(200));if(null==e||void 0===e._reactInternals)throw Error(ie(38));return Ju(e,t,n,!1,o)},J.version="18.3.1-next-f1338f8080-20240426",function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(Up){console.error(Up)}}(),Z.exports=J;var rd=Z.exports;const id=o(rd);var ad,ld=rd;function sd(e,t){return sd=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},sd(e,t)}function cd(e,t){e.prototype=Object.create(t.prototype),e.prototype.constructor=e,sd(e,t)}ad=ld.createRoot,ld.hydrateRoot;var ud={exports:{}};function dd(){}function fd(){}fd.resetWarningCache=dd,ud.exports=function(){function e(e,t,n,o,r,i){if("SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"!==i){var a=new Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw a.name="Invariant Violation",a}}function t(){return e}e.isRequired=e;var n={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:fd,resetWarningCache:dd};return n.PropTypes=n,n}();const pd=o(ud.exports);function hd(){return hd=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var o in n)({}).hasOwnProperty.call(n,o)&&(e[o]=n[o])}return e},hd.apply(null,arguments)}function md(e){return"/"===e.charAt(0)}function gd(e,t){for(var n=t,o=n+1,r=e.length;o<r;n+=1,o+=1)e[n]=e[o];e.pop()}function bd(e){return e.valueOf?e.valueOf():Object.prototype.valueOf.call(e)}function vd(e,t){if(e===t)return!0;if(null==e||null==t)return!1;if(Array.isArray(e))return Array.isArray(t)&&e.length===t.length&&e.every((function(e,n){return vd(e,t[n])}));if("object"==typeof e||"object"==typeof t){var n=bd(e),o=bd(t);return n!==e||o!==t?vd(n,o):Object.keys(Object.assign({},e,t)).every((function(n){return vd(e[n],t[n])}))}return!1}function yd(e,t){throw new Error("Invariant failed")}function wd(e){return"/"===e.charAt(0)?e:"/"+e}function xd(e,t){return function(e,t){return 0===e.toLowerCase().indexOf(t.toLowerCase())&&-1!=="/?#".indexOf(e.charAt(t.length))}(e,t)?e.substr(t.length):e}function kd(e){return"/"===e.charAt(e.length-1)?e.slice(0,-1):e}function Ed(e){var t=e.pathname,n=e.search,o=e.hash,r=t||"/";return n&&"?"!==n&&(r+="?"===n.charAt(0)?n:"?"+n),o&&"#"!==o&&(r+="#"===o.charAt(0)?o:"#"+o),r}function Sd(e,t,n,o){var r;"string"==typeof e?(r=function(e){var t=e||"/",n="",o="",r=t.indexOf("#");-1!==r&&(o=t.substr(r),t=t.substr(0,r));var i=t.indexOf("?");return-1!==i&&(n=t.substr(i),t=t.substr(0,i)),{pathname:t,search:"?"===n?"":n,hash:"#"===o?"":o}}(e),r.state=t):(void 0===(r=hd({},e)).pathname&&(r.pathname=""),r.search?"?"!==r.search.charAt(0)&&(r.search="?"+r.search):r.search="",r.hash?"#"!==r.hash.charAt(0)&&(r.hash="#"+r.hash):r.hash="",void 0!==t&&void 0===r.state&&(r.state=t));try{r.pathname=decodeURI(r.pathname)}catch(kf){throw kf instanceof URIError?new URIError('Pathname "'+r.pathname+'" could not be decoded. This is likely caused by an invalid percent-encoding.'):kf}return n&&(r.key=n),o?r.pathname?"/"!==r.pathname.charAt(0)&&(r.pathname=function(e,t){void 0===t&&(t="");var n,o=e&&e.split("/")||[],r=t&&t.split("/")||[],i=e&&md(e),a=t&&md(t),l=i||a;if(e&&md(e)?r=o:o.length&&(r.pop(),r=r.concat(o)),!r.length)return"/";if(r.length){var s=r[r.length-1];n="."===s||".."===s||""===s}else n=!1;for(var c=0,u=r.length;u>=0;u--){var d=r[u];"."===d?gd(r,u):".."===d?(gd(r,u),c++):c&&(gd(r,u),c--)}if(!l)for(;c--;c)r.unshift("..");!l||""===r[0]||r[0]&&md(r[0])||r.unshift("");var f=r.join("/");return n&&"/"!==f.substr(-1)&&(f+="/"),f}(r.pathname,o.pathname)):r.pathname=o.pathname:r.pathname||(r.pathname="/"),r}function Cd(e,t){return e.pathname===t.pathname&&e.search===t.search&&e.hash===t.hash&&e.key===t.key&&vd(e.state,t.state)}function $d(){var e=null,t=[];return{setPrompt:function(t){return e=t,function(){e===t&&(e=null)}},confirmTransitionTo:function(t,n,o,r){if(null!=e){var i="function"==typeof e?e(t,n):e;"string"==typeof i?"function"==typeof o?o(i,r):r(!0):r(!1!==i)}else r(!0)},appendListener:function(e){var n=!0;function o(){n&&e.apply(void 0,arguments)}return t.push(o),function(){n=!1,t=t.filter((function(e){return e!==o}))}},notifyListeners:function(){for(var e=arguments.length,n=new Array(e),o=0;o<e;o++)n[o]=arguments[o];t.forEach((function(e){return e.apply(void 0,n)}))}}}var Td=!("undefined"==typeof window||!window.document||!window.document.createElement);function Pd(e,t){t(window.confirm(e))}var Rd="popstate",Id="hashchange";function Od(){try{return window.history.state||{}}catch(kf){return{}}}function Ld(e){void 0===e&&(e={}),Td||yd();var t=window.history,n=function(){var e=window.navigator.userAgent;return(-1===e.indexOf("Android 2.")&&-1===e.indexOf("Android 4.0")||-1===e.indexOf("Mobile Safari")||-1!==e.indexOf("Chrome")||-1!==e.indexOf("Windows Phone"))&&window.history&&"pushState"in window.history}(),o=!(-1===window.navigator.userAgent.indexOf("Trident")),r=e,i=r.forceRefresh,a=void 0!==i&&i,l=r.getUserConfirmation,s=void 0===l?Pd:l,c=r.keyLength,u=void 0===c?6:c,d=e.basename?kd(wd(e.basename)):"";function f(e){var t=e||{},n=t.key,o=t.state,r=window.location,i=r.pathname+r.search+r.hash;return d&&(i=xd(i,d)),Sd(i,o,n)}function p(){return Math.random().toString(36).substr(2,u)}var h=$d();function m(e){hd(T,e),T.length=t.length,h.notifyListeners(T.location,T.action)}function g(e){(function(e){return void 0===e.state&&-1===navigator.userAgent.indexOf("CriOS")})(e)||y(f(e.state))}function b(){y(f(Od()))}var v=!1;function y(e){v?(v=!1,m()):h.confirmTransitionTo(e,"POP",s,(function(t){t?m({action:"POP",location:e}):function(e){var t=T.location,n=x.indexOf(t.key);-1===n&&(n=0);var o=x.indexOf(e.key);-1===o&&(o=0);var r=n-o;r&&(v=!0,E(r))}(e)}))}var w=f(Od()),x=[w.key];function k(e){return d+Ed(e)}function E(e){t.go(e)}var S=0;function C(e){1===(S+=e)&&1===e?(window.addEventListener(Rd,g),o&&window.addEventListener(Id,b)):0===S&&(window.removeEventListener(Rd,g),o&&window.removeEventListener(Id,b))}var $=!1,T={length:t.length,action:"POP",location:w,createHref:k,push:function(e,o){var r="PUSH",i=Sd(e,o,p(),T.location);h.confirmTransitionTo(i,r,s,(function(e){if(e){var o=k(i),l=i.key,s=i.state;if(n)if(t.pushState({key:l,state:s},null,o),a)window.location.href=o;else{var c=x.indexOf(T.location.key),u=x.slice(0,c+1);u.push(i.key),x=u,m({action:r,location:i})}else window.location.href=o}}))},replace:function(e,o){var r="REPLACE",i=Sd(e,o,p(),T.location);h.confirmTransitionTo(i,r,s,(function(e){if(e){var o=k(i),l=i.key,s=i.state;if(n)if(t.replaceState({key:l,state:s},null,o),a)window.location.replace(o);else{var c=x.indexOf(T.location.key);-1!==c&&(x[c]=i.key),m({action:r,location:i})}else window.location.replace(o)}}))},go:E,goBack:function(){E(-1)},goForward:function(){E(1)},block:function(e){void 0===e&&(e=!1);var t=h.setPrompt(e);return $||(C(1),$=!0),function(){return $&&($=!1,C(-1)),t()}},listen:function(e){var t=h.appendListener(e);return C(1),function(){C(-1),t()}}};return T}var _d={exports:{}},zd=Array.isArray||function(e){return"[object Array]"==Object.prototype.toString.call(e)};_d.exports=Ud,_d.exports.parse=Md,_d.exports.compile=function(e,t){return Ad(Md(e,t),t)},_d.exports.tokensToFunction=Ad,_d.exports.tokensToRegExp=Wd;var Nd=new RegExp(["(\\\\.)","([\\/.])?(?:(?:\\:(\\w+)(?:\\(((?:\\\\.|[^\\\\()])+)\\))?|\\(((?:\\\\.|[^\\\\()])+)\\))([+*?])?|(\\*))"].join("|"),"g");function Md(e,t){for(var n,o=[],r=0,i=0,a="",l=t&&t.delimiter||"/";null!=(n=Nd.exec(e));){var s=n[0],c=n[1],u=n.index;if(a+=e.slice(i,u),i=u+s.length,c)a+=c[1];else{var d=e[i],f=n[2],p=n[3],h=n[4],m=n[5],g=n[6],b=n[7];a&&(o.push(a),a="");var v=null!=f&&null!=d&&d!==f,y="+"===g||"*"===g,w="?"===g||"*"===g,x=f||l,k=h||m,E=f||("string"==typeof o[o.length-1]?o[o.length-1]:"");o.push({name:p||r++,prefix:f||"",delimiter:x,optional:w,repeat:y,partial:v,asterisk:!!b,pattern:k?Vd(k):b?".*":jd(x,E)})}}return i<e.length&&(a+=e.substr(i)),a&&o.push(a),o}function jd(e,t){return!t||t.indexOf(e)>-1?"[^"+Bd(e)+"]+?":Bd(t)+"|(?:(?!"+Bd(t)+")[^"+Bd(e)+"])+?"}function Dd(e){return encodeURI(e).replace(/[\/?#]/g,(function(e){return"%"+e.charCodeAt(0).toString(16).toUpperCase()}))}function Ad(e,t){for(var n=new Array(e.length),o=0;o<e.length;o++)"object"==typeof e[o]&&(n[o]=new RegExp("^(?:"+e[o].pattern+")$",Fd(t)));return function(t,o){for(var r="",i=t||{},a=(o||{}).pretty?Dd:encodeURIComponent,l=0;l<e.length;l++){var s=e[l];if("string"!=typeof s){var c,u=i[s.name];if(null==u){if(s.optional){s.partial&&(r+=s.prefix);continue}throw new TypeError('Expected "'+s.name+'" to be defined')}if(zd(u)){if(!s.repeat)throw new TypeError('Expected "'+s.name+'" to not repeat, but received `'+JSON.stringify(u)+"`");if(0===u.length){if(s.optional)continue;throw new TypeError('Expected "'+s.name+'" to not be empty')}for(var d=0;d<u.length;d++){if(c=a(u[d]),!n[l].test(c))throw new TypeError('Expected all "'+s.name+'" to match "'+s.pattern+'", but received `'+JSON.stringify(c)+"`");r+=(0===d?s.prefix:s.delimiter)+c}}else{if(c=s.asterisk?encodeURI(u).replace(/[?#]/g,(function(e){return"%"+e.charCodeAt(0).toString(16).toUpperCase()})):a(u),!n[l].test(c))throw new TypeError('Expected "'+s.name+'" to match "'+s.pattern+'", but received "'+c+'"');r+=s.prefix+c}}else r+=s}return r}}function Bd(e){return e.replace(/([.+*?=^!:${}()[\]|\/\\])/g,"\\$1")}function Vd(e){return e.replace(/([=!:$\/()])/g,"\\$1")}function Hd(e,t){return e.keys=t,e}function Fd(e){return e&&e.sensitive?"":"i"}function Wd(e,t,n){zd(t)||(n=t||n,t=[]);for(var o=(n=n||{}).strict,r=!1!==n.end,i="",a=0;a<e.length;a++){var l=e[a];if("string"==typeof l)i+=Bd(l);else{var s=Bd(l.prefix),c="(?:"+l.pattern+")";t.push(l),l.repeat&&(c+="(?:"+s+c+")*"),i+=c=l.optional?l.partial?s+"("+c+")?":"(?:"+s+"("+c+"))?":s+"("+c+")"}}var u=Bd(n.delimiter||"/"),d=i.slice(-u.length)===u;return o||(i=(d?i.slice(0,-u.length):i)+"(?:"+u+"(?=$))?"),i+=r?"$":o&&d?"":"(?="+u+"|$)",Hd(new RegExp("^"+i,Fd(n)),t)}function Ud(e,t,n){return zd(t)||(n=t||n,t=[]),n=n||{},e instanceof RegExp?function(e,t){var n=e.source.match(/\((?!\?)/g);if(n)for(var o=0;o<n.length;o++)t.push({name:o,prefix:null,delimiter:null,optional:!1,repeat:!1,partial:!1,asterisk:!1,pattern:null});return Hd(e,t)}(e,t):zd(e)?function(e,t,n){for(var o=[],r=0;r<e.length;r++)o.push(Ud(e[r],t,n).source);return Hd(new RegExp("(?:"+o.join("|")+")",Fd(n)),t)}(e,t,n):function(e,t,n){return Wd(Md(e,n),t,n)}(e,t,n)}const qd=o(_d.exports);var Qd={},Kd="function"==typeof Symbol&&Symbol.for,Yd=Kd?Symbol.for("react.element"):60103,Xd=Kd?Symbol.for("react.portal"):60106,Gd=Kd?Symbol.for("react.fragment"):60107,Zd=Kd?Symbol.for("react.strict_mode"):60108,Jd=Kd?Symbol.for("react.profiler"):60114,ef=Kd?Symbol.for("react.provider"):60109,tf=Kd?Symbol.for("react.context"):60110,nf=Kd?Symbol.for("react.async_mode"):60111,of=Kd?Symbol.for("react.concurrent_mode"):60111,rf=Kd?Symbol.for("react.forward_ref"):60112,af=Kd?Symbol.for("react.suspense"):60113,lf=Kd?Symbol.for("react.suspense_list"):60120,sf=Kd?Symbol.for("react.memo"):60115,cf=Kd?Symbol.for("react.lazy"):60116,uf=Kd?Symbol.for("react.block"):60121,df=Kd?Symbol.for("react.fundamental"):60117,ff=Kd?Symbol.for("react.responder"):60118,pf=Kd?Symbol.for("react.scope"):60119;
/** @license React v16.13.1
             * react-is.production.min.js
             *
             * Copyright (c) Facebook, Inc. and its affiliates.
             *
             * This source code is licensed under the MIT license found in the
             * LICENSE file in the root directory of this source tree.
             */function hf(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case Yd:switch(e=e.type){case nf:case of:case Gd:case Jd:case Zd:case af:return e;default:switch(e=e&&e.$$typeof){case tf:case rf:case cf:case sf:case ef:return e;default:return t}}case Xd:return t}}}function mf(e){return hf(e)===of}function gf(e,t){if(null==e)return{};var n={};for(var o in e)if({}.hasOwnProperty.call(e,o)){if(t.includes(o))continue;n[o]=e[o]}return n}Qd.AsyncMode=nf,Qd.ConcurrentMode=of,Qd.ContextConsumer=tf,Qd.ContextProvider=ef,Qd.Element=Yd,Qd.ForwardRef=rf,Qd.Fragment=Gd,Qd.Lazy=cf,Qd.Memo=sf,Qd.Portal=Xd,Qd.Profiler=Jd,Qd.StrictMode=Zd,Qd.Suspense=af,Qd.isAsyncMode=function(e){return mf(e)||hf(e)===nf},Qd.isConcurrentMode=mf,Qd.isContextConsumer=function(e){return hf(e)===tf},Qd.isContextProvider=function(e){return hf(e)===ef},Qd.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===Yd},Qd.isForwardRef=function(e){return hf(e)===rf},Qd.isFragment=function(e){return hf(e)===Gd},Qd.isLazy=function(e){return hf(e)===cf},Qd.isMemo=function(e){return hf(e)===sf},Qd.isPortal=function(e){return hf(e)===Xd},Qd.isProfiler=function(e){return hf(e)===Jd},Qd.isStrictMode=function(e){return hf(e)===Zd},Qd.isSuspense=function(e){return hf(e)===af},Qd.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===Gd||e===of||e===Jd||e===Zd||e===af||e===lf||"object"==typeof e&&null!==e&&(e.$$typeof===cf||e.$$typeof===sf||e.$$typeof===ef||e.$$typeof===tf||e.$$typeof===rf||e.$$typeof===df||e.$$typeof===ff||e.$$typeof===pf||e.$$typeof===uf)},Qd.typeOf=hf;var bf={exports:{}},vf={},yf="function"==typeof Symbol&&Symbol.for,wf=yf?Symbol.for("react.element"):60103,xf=yf?Symbol.for("react.portal"):60106,kf=yf?Symbol.for("react.fragment"):60107,Ef=yf?Symbol.for("react.strict_mode"):60108,Sf=yf?Symbol.for("react.profiler"):60114,Cf=yf?Symbol.for("react.provider"):60109,$f=yf?Symbol.for("react.context"):60110,Tf=yf?Symbol.for("react.async_mode"):60111,Pf=yf?Symbol.for("react.concurrent_mode"):60111,Rf=yf?Symbol.for("react.forward_ref"):60112,If=yf?Symbol.for("react.suspense"):60113,Of=yf?Symbol.for("react.suspense_list"):60120,Lf=yf?Symbol.for("react.memo"):60115,_f=yf?Symbol.for("react.lazy"):60116,zf=yf?Symbol.for("react.block"):60121,Nf=yf?Symbol.for("react.fundamental"):60117,Mf=yf?Symbol.for("react.responder"):60118,jf=yf?Symbol.for("react.scope"):60119;function Df(e){if("object"==typeof e&&null!==e){var t=e.$$typeof;switch(t){case wf:switch(e=e.type){case Tf:case Pf:case kf:case Sf:case Ef:case If:return e;default:switch(e=e&&e.$$typeof){case $f:case Rf:case _f:case Lf:case Cf:return e;default:return t}}case xf:return t}}}function Af(e){return Df(e)===Pf}vf.AsyncMode=Tf,vf.ConcurrentMode=Pf,vf.ContextConsumer=$f,vf.ContextProvider=Cf,vf.Element=wf,vf.ForwardRef=Rf,vf.Fragment=kf,vf.Lazy=_f,vf.Memo=Lf,vf.Portal=xf,vf.Profiler=Sf,vf.StrictMode=Ef,vf.Suspense=If,vf.isAsyncMode=function(e){return Af(e)||Df(e)===Tf},vf.isConcurrentMode=Af,vf.isContextConsumer=function(e){return Df(e)===$f},vf.isContextProvider=function(e){return Df(e)===Cf},vf.isElement=function(e){return"object"==typeof e&&null!==e&&e.$$typeof===wf},vf.isForwardRef=function(e){return Df(e)===Rf},vf.isFragment=function(e){return Df(e)===kf},vf.isLazy=function(e){return Df(e)===_f},vf.isMemo=function(e){return Df(e)===Lf},vf.isPortal=function(e){return Df(e)===xf},vf.isProfiler=function(e){return Df(e)===Sf},vf.isStrictMode=function(e){return Df(e)===Ef},vf.isSuspense=function(e){return Df(e)===If},vf.isValidElementType=function(e){return"string"==typeof e||"function"==typeof e||e===kf||e===Pf||e===Sf||e===Ef||e===If||e===Of||"object"==typeof e&&null!==e&&(e.$$typeof===_f||e.$$typeof===Lf||e.$$typeof===Cf||e.$$typeof===$f||e.$$typeof===Rf||e.$$typeof===Nf||e.$$typeof===Mf||e.$$typeof===jf||e.$$typeof===zf)},vf.typeOf=Df,bf.exports=vf;var Bf=bf.exports,Vf={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},Hf={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},Ff={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},Wf={};function Uf(e){return Bf.isMemo(e)?Ff:Wf[e.$$typeof]||Vf}Wf[Bf.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},Wf[Bf.Memo]=Ff;var qf=Object.defineProperty,Qf=Object.getOwnPropertyNames,Kf=Object.getOwnPropertySymbols,Yf=Object.getOwnPropertyDescriptor,Xf=Object.getPrototypeOf,Gf=Object.prototype,Zf=function e(t,n,o){if("string"!=typeof n){if(Gf){var r=Xf(n);r&&r!==Gf&&e(t,r,o)}var i=Qf(n);Kf&&(i=i.concat(Kf(n)));for(var a=Uf(t),l=Uf(n),s=0;s<i.length;++s){var c=i[s];if(!(Hf[c]||o&&o[c]||l&&l[c]||a&&a[c])){var u=Yf(n,c);try{qf(t,c,u)}catch(kf){}}}}return t};const Jf=o(Zf);var ep=**********,tp="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{},np=F.createContext||function(e,t){var n,o,r,i="__create-react-context-"+(tp[r="__global_unique_id__"]=(tp[r]||0)+1)+"__",a=function(e){function n(){for(var t,n,o,r=arguments.length,i=new Array(r),a=0;a<r;a++)i[a]=arguments[a];return(t=e.call.apply(e,[this].concat(i))||this).emitter=(n=t.props.value,o=[],{on:function(e){o.push(e)},off:function(e){o=o.filter((function(t){return t!==e}))},get:function(){return n},set:function(e,t){n=e,o.forEach((function(e){return e(n,t)}))}}),t}cd(n,e);var o=n.prototype;return o.getChildContext=function(){var e;return(e={})[i]=this.emitter,e},o.componentWillReceiveProps=function(e){if(this.props.value!==e.value){var n,o=this.props.value,r=e.value;!function(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}(o,r)?(n="function"==typeof t?t(o,r):ep,0!=(n|=0)&&this.emitter.set(e.value,n)):n=0}},o.render=function(){return this.props.children},n}(F.Component);a.childContextTypes=((n={})[i]=pd.object.isRequired,n);var l=function(t){function n(){for(var e,n=arguments.length,o=new Array(n),r=0;r<n;r++)o[r]=arguments[r];return(e=t.call.apply(t,[this].concat(o))||this).observedBits=void 0,e.state={value:e.getValue()},e.onUpdate=function(t,n){e.observedBits&n&&e.setState({value:e.getValue()})},e}cd(n,t);var o=n.prototype;return o.componentWillReceiveProps=function(e){var t=e.observedBits;this.observedBits=null==t?ep:t},o.componentDidMount=function(){this.context[i]&&this.context[i].on(this.onUpdate);var e=this.props.observedBits;this.observedBits=null==e?ep:e},o.componentWillUnmount=function(){this.context[i]&&this.context[i].off(this.onUpdate)},o.getValue=function(){return this.context[i]?this.context[i].get():e},o.render=function(){return(e=this.props.children,Array.isArray(e)?e[0]:e)(this.state.value);var e},n}(F.Component);return l.contextTypes=((o={})[i]=pd.object,o),{Provider:a,Consumer:l}},op=function(e){var t=np();return t.displayName=e,t},rp=op("Router-History"),ip=op("Router"),ap=function(e){function t(t){var n;return(n=e.call(this,t)||this).state={location:t.history.location},n._isMounted=!1,n._pendingLocation=null,t.staticContext||(n.unlisten=t.history.listen((function(e){n._pendingLocation=e}))),n}cd(t,e),t.computeRootMatch=function(e){return{path:"/",url:"/",params:{},isExact:"/"===e}};var n=t.prototype;return n.componentDidMount=function(){var e=this;this._isMounted=!0,this.unlisten&&this.unlisten(),this.props.staticContext||(this.unlisten=this.props.history.listen((function(t){e._isMounted&&e.setState({location:t})}))),this._pendingLocation&&this.setState({location:this._pendingLocation})},n.componentWillUnmount=function(){this.unlisten&&(this.unlisten(),this._isMounted=!1,this._pendingLocation=null)},n.render=function(){return F.createElement(ip.Provider,{value:{history:this.props.history,location:this.state.location,match:t.computeRootMatch(this.state.location.pathname),staticContext:this.props.staticContext}},F.createElement(rp.Provider,{children:this.props.children||null,value:this.props.history}))},t}(F.Component);F.Component;var lp=function(e){function t(){return e.apply(this,arguments)||this}cd(t,e);var n=t.prototype;return n.componentDidMount=function(){this.props.onMount&&this.props.onMount.call(this,this)},n.componentDidUpdate=function(e){this.props.onUpdate&&this.props.onUpdate.call(this,this,e)},n.componentWillUnmount=function(){this.props.onUnmount&&this.props.onUnmount.call(this,this)},n.render=function(){return null},t}(F.Component),sp={},cp=0;function up(e,t){return void 0===e&&(e="/"),void 0===t&&(t={}),"/"===e?e:function(e){if(sp[e])return sp[e];var t=qd.compile(e);return cp<1e4&&(sp[e]=t,cp++),t}(e)(t,{pretty:!0})}function dp(e){var t=e.computedMatch,n=e.to,o=e.push,r=void 0!==o&&o;return F.createElement(ip.Consumer,null,(function(e){e||yd();var o=e.history,i=e.staticContext,a=r?o.push:o.replace,l=Sd(t?"string"==typeof n?up(n,t.params):hd({},n,{pathname:up(n.pathname,t.params)}):n);return i?(a(l),null):F.createElement(lp,{onMount:function(){a(l)},onUpdate:function(e,t){var n=Sd(t.to);Cd(n,hd({},l,{key:n.key}))||a(l)},to:n})}))}var fp={},pp=0;function hp(e,t){void 0===t&&(t={}),("string"==typeof t||Array.isArray(t))&&(t={path:t});var n=t,o=n.path,r=n.exact,i=void 0!==r&&r,a=n.strict,l=void 0!==a&&a,s=n.sensitive,c=void 0!==s&&s;return[].concat(o).reduce((function(t,n){if(!n&&""!==n)return null;if(t)return t;var o=function(e,t){var n=""+t.end+t.strict+t.sensitive,o=fp[n]||(fp[n]={});if(o[e])return o[e];var r=[],i={regexp:qd(e,r,t),keys:r};return pp<1e4&&(o[e]=i,pp++),i}(n,{end:i,strict:l,sensitive:c}),r=o.regexp,a=o.keys,s=r.exec(e);if(!s)return null;var u=s[0],d=s.slice(1),f=e===u;return i&&!f?null:{path:n,url:"/"===n&&""===u?"/":u,isExact:f,params:a.reduce((function(e,t,n){return e[t.name]=d[n],e}),{})}}),null)}var mp=function(e){function t(){return e.apply(this,arguments)||this}return cd(t,e),t.prototype.render=function(){var e=this;return F.createElement(ip.Consumer,null,(function(t){t||yd();var n=e.props.location||t.location,o=hd({},t,{location:n,match:e.props.computedMatch?e.props.computedMatch:e.props.path?hp(n.pathname,e.props):t.match}),r=e.props,i=r.children,a=r.component,l=r.render;return Array.isArray(i)&&function(e){return 0===F.Children.count(e)}(i)&&(i=null),F.createElement(ip.Provider,{value:o},o.match?i?"function"==typeof i?i(o):i:a?F.createElement(a,o):l?l(o):null:"function"==typeof i?i(o):null)}))},t}(F.Component);F.Component,F.Component,F.useContext;const gp=function(e,t,n){let o=Promise.resolve();return o.then((()=>e())).catch((e=>{const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}))};var bp={allRenderFn:!1,cmpDidLoad:!0,cmpDidUnload:!1,cmpDidUpdate:!0,cmpDidRender:!0,cmpWillLoad:!0,cmpWillUpdate:!0,cmpWillRender:!0,connectedCallback:!0,disconnectedCallback:!0,element:!0,event:!0,hasRenderFn:!0,lifecycle:!0,hostListener:!0,hostListenerTargetWindow:!0,hostListenerTargetDocument:!0,hostListenerTargetBody:!0,hostListenerTargetParent:!1,hostListenerTarget:!0,member:!0,method:!0,mode:!0,observeAttribute:!0,prop:!0,propMutable:!0,reflect:!0,scoped:!0,shadowDom:!0,slot:!0,cssAnnotations:!0,state:!0,style:!0,formAssociated:!1,svg:!0,updatable:!0,vdomAttribute:!0,vdomXlink:!0,vdomClass:!0,vdomFunctional:!0,vdomKey:!0,vdomListener:!0,vdomRef:!0,vdomPropOrAttr:!0,vdomRender:!0,vdomStyle:!0,vdomText:!0,watchCallback:!0,taskQueue:!0,hotModuleReplacement:!1,isDebug:!1,isDev:!1,isTesting:!1,hydrateServerSide:!1,hydrateClientSide:!1,lifecycleDOMEvents:!1,lazyLoad:!1,profile:!1,slotRelocation:!0,appendChildSlotFix:!1,cloneNodeFix:!1,hydratedAttribute:!1,hydratedClass:!0,scriptDataOpts:!1,scopedSlotTextContentFix:!1,shadowDomShim:!1,slotChildNodesFix:!1,invisiblePrehydration:!0,propBoolean:!0,propNumber:!0,propString:!0,constructableCSS:!0,cmpShouldUpdate:!0,devTools:!1,shadowDelegatesFocus:!0,initializeNextTick:!1,asyncLoading:!1,asyncQueue:!1,transformTagName:!1,attachStyles:!0,experimentalSlotFixes:!1},vp=Object.defineProperty,yp=!0,wp=new WeakMap,xp=e=>wp.get(e),kp=(e,t)=>t in e,Ep=(e,t)=>(0,console.error)(e,t),Sp=new Map,Cp=[],$p="http://www.w3.org/1999/xlink",Tp="undefined"!=typeof window?window:{},Pp=Tp.document||{head:{}},Rp=Tp.HTMLElement||class{},Ip={$flags$:0,$resourcesUrl$:"",jmp:e=>e(),raf:e=>requestAnimationFrame(e),ael:(e,t,n,o)=>e.addEventListener(t,n,o),rel:(e,t,n,o)=>e.removeEventListener(t,n,o),ce:(e,t)=>new CustomEvent(e,t)},Op=(()=>{let e=!1;try{Pp.addEventListener("e",null,Object.defineProperty({},"passive",{get(){e=!0}}))}catch(kf){}return e})(),Lp=(()=>{try{return new CSSStyleSheet,"function"==typeof(new CSSStyleSheet).replaceSync}catch(kf){}return!1})(),_p=!1,zp=[],Np=[],Mp=(e,t)=>n=>{e.push(n),_p||(_p=!0,t&&4&Ip.$flags$?Ap(Dp):Ip.raf(Dp))},jp=e=>{for(let t=0;t<e.length;t++)try{e[t](performance.now())}catch(kf){Ep(kf)}e.length=0},Dp=()=>{jp(zp),jp(Np),(_p=zp.length>0)&&Ip.raf(Dp)},Ap=e=>(e=>Promise.resolve(e))().then(e),Bp=e("r",Mp(zp,!1)),Vp=e("w",Mp(Np,!0)),Hp={},Fp=e=>"object"==(e=typeof e)||"function"===e;((e,t)=>{for(var n in t)vp(e,n,{get:t[n],enumerable:!0})})({},{err:()=>Up,map:()=>qp,ok:()=>Wp,unwrap:()=>Xp,unwrapErr:()=>Gp});var Wp=e=>({isOk:!0,isErr:!1,value:e}),Up=e=>({isOk:!1,isErr:!0,value:e});function qp(e,t){if(e.isOk){const n=t(e.value);return n instanceof Promise?n.then((e=>Wp(e))):Wp(n)}if(e.isErr){const t=e.value;return Up(t)}throw"should never get here"}var Qp,Kp,Yp,Xp=e=>{if(e.isOk)return e.value;throw e.value},Gp=e=>{if(e.isErr)return e.value;throw e.value},Zp=(e,t,...n)=>{let o=null,r=null,i=null,a=!1,l=!1;const s=[],c=t=>{for(let n=0;n<t.length;n++)o=t[n],Array.isArray(o)?c(o):null!=o&&"boolean"!=typeof o&&((a="function"!=typeof e&&!Fp(o))&&(o=String(o)),a&&l?s[s.length-1].$text$+=o:s.push(a?Jp(null,o):o),l=a)};if(c(n),t){t.key&&(r=t.key),t.name&&(i=t.name);{const e=t.className||t.class;e&&(t.class="object"!=typeof e?e:Object.keys(e).filter((t=>e[t])).join(" "))}}if("function"==typeof e)return e(null===t?{}:t,s,th);const u=Jp(e,null);return u.$attrs$=t,s.length>0&&(u.$children$=s),u.$key$=r,u.$name$=i,u},Jp=(e,t)=>{const n={$flags$:0,$tag$:e,$text$:t,$elm$:null,$children$:null,$attrs$:null,$key$:null,$name$:null};return n},eh={},th={forEach:(e,t)=>e.map(nh).forEach(t),map:(e,t)=>e.map(nh).map(t).map(oh)},nh=e=>({vattrs:e.$attrs$,vchildren:e.$children$,vkey:e.$key$,vname:e.$name$,vtag:e.$tag$,vtext:e.$text$}),oh=e=>{if("function"==typeof e.vtag){const t={...e.vattrs};return e.vkey&&(t.key=e.vkey),e.vname&&(t.name=e.vname),Zp(e.vtag,t,...e.vchildren||[])}const t=Jp(e.vtag,e.vtext);return t.$attrs$=e.vattrs,t.$children$=e.vchildren,t.$key$=e.vkey,t.$name$=e.vname,t},rh=(e,t,n)=>{const o=e;return{emit:e=>ih(o,t,{bubbles:!!(4&n),composed:!!(2&n),cancelable:!!(1&n),detail:e})}},ih=(e,t,n)=>{const o=Ip.ce(t,n);return e.dispatchEvent(o),o},ah=new WeakMap,lh=e=>{const t=e.$cmpMeta$,n=e.$hostElement$,o=t.$flags$,r=(t.$tagName$,()=>{}),i=((e,t,n)=>{var o;const r=sh(t,n),i=Sp.get(r);if(e=11===e.nodeType?e:Pp,i)if("string"==typeof i){e=e.head||e;let n,a=ah.get(e);if(a||ah.set(e,a=new Set),!a.has(r)){{n=Pp.createElement("style"),n.innerHTML=i;const r=null!=(o=Ip.$nonce$)?o:function(e){var t,n,o;return null!=(o=null==(n=null==(t=e.head)?void 0:t.querySelector('meta[name="csp-nonce"]'))?void 0:n.getAttribute("content"))?o:void 0}(Pp);null!=r&&n.setAttribute("nonce",r),(!(1&t.$flags$)||1&t.$flags$&&"HEAD"!==e.nodeName)&&e.insertBefore(n,e.querySelector("link"))}4&t.$flags$&&(n.innerHTML+="slot-fb{display:contents}slot-fb[hidden]{display:none}"),a&&a.add(r)}}else e.adoptedStyleSheets.includes(i)||(e.adoptedStyleSheets=[...e.adoptedStyleSheets,i]);return r})(n.shadowRoot?n.shadowRoot:n.getRootNode(),t,e.$modeName$);10&o&&2&o&&(n["s-sc"]=i,n.classList.add(i+"-h"),2&o&&n.classList.add(i+"-s")),r()},sh=(e,t)=>"sc-"+(t&&32&e.$flags$?e.$tagName$+"-"+t:e.$tagName$),ch=(e,t,n,o,r,i)=>{if(n!==o){let a=kp(e,t),l=t.toLowerCase();if("class"===t){const t=e.classList,r=dh(n),i=dh(o);t.remove(...r.filter((e=>e&&!i.includes(e)))),t.add(...i.filter((e=>e&&!r.includes(e))))}else if("style"===t){for(const t in n)o&&null!=o[t]||(t.includes("-")?e.style.removeProperty(t):e.style[t]="");for(const t in o)n&&o[t]===n[t]||(t.includes("-")?e.style.setProperty(t,o[t]):e.style[t]=o[t])}else if("key"===t);else if("ref"===t)o&&o(e);else if(e.__lookupSetter__(t)||"o"!==t[0]||"n"!==t[1]){const s=Fp(o);if((a||s&&null!==o)&&!r)try{if(e.tagName.includes("-"))e[t]=o;else{const r=null==o?"":o;"list"===t?a=!1:null!=n&&e[t]==r||(e[t]=r)}}catch(kf){}let c=!1;l!==(l=l.replace(/^xlink\:?/,""))&&(t=l,c=!0),null==o||!1===o?!1===o&&""!==e.getAttribute(t)||(c?e.removeAttributeNS($p,t):e.removeAttribute(t)):(!a||4&i||r)&&!s&&(o=!0===o?"":o,c?e.setAttributeNS($p,t,o):e.setAttribute(t,o))}else if(t="-"===t[2]?t.slice(3):kp(Tp,l)?l.slice(2):l[2]+t.slice(3),n||o){const r=t.endsWith(fh);t=t.replace(ph,""),n&&Ip.rel(e,t,n,r),o&&Ip.ael(e,t,o,r)}}},uh=/\s/,dh=e=>e?e.split(uh):[],fh="Capture",ph=new RegExp(fh+"$"),hh=(e,t,n)=>{const o=11===t.$elm$.nodeType&&t.$elm$.host?t.$elm$.host:t.$elm$,r=e&&e.$attrs$||Hp,i=t.$attrs$||Hp;for(const a of mh(Object.keys(r)))a in i||ch(o,a,r[a],void 0,n,t.$flags$);for(const a of mh(Object.keys(i)))ch(o,a,r[a],i[a],n,t.$flags$)};function mh(e){return e.includes("ref")?[...e.filter((e=>"ref"!==e)),"ref"]:e}var gh=!1,bh=!1,vh=!1,yh=!1,wh=(e,t,n,o)=>{var r;const i=t.$children$[n];let a,l,s,c=0;if(gh||(vh=!0,"slot"===i.$tag$&&(Qp&&o.classList.add(Qp+"-s"),i.$flags$|=i.$children$?2:1)),null!==i.$text$)a=i.$elm$=Pp.createTextNode(i.$text$);else if(1&i.$flags$)a=i.$elm$=Pp.createTextNode("");else{if(yh||(yh="svg"===i.$tag$),a=i.$elm$=Pp.createElementNS(yh?"http://www.w3.org/2000/svg":"http://www.w3.org/1999/xhtml",!gh&&bp.slotRelocation&&2&i.$flags$?"slot-fb":i.$tag$),yh&&"foreignObject"===i.$tag$&&(yh=!1),hh(null,i,yh),!!a.getRootNode().querySelector("body")&&bp.scoped&&(e=>null!=e)(Qp)&&a["s-si"]!==Qp&&a.classList.add(a["s-si"]=Qp),Nh(a,o),i.$children$)for(c=0;c<i.$children$.length;++c)l=wh(e,i,c,a),l&&a.appendChild(l);"svg"===i.$tag$?yh=!1:"foreignObject"===a.tagName&&(yh=!0)}return a["s-hn"]=Yp,3&i.$flags$&&(a["s-sr"]=!0,a["s-cr"]=Kp,a["s-sn"]=i.$name$||"",a["s-rf"]=null==(r=i.$attrs$)?void 0:r.ref,s=e&&e.$children$&&e.$children$[n],s&&s.$tag$===i.$tag$&&e.$elm$&&xh(e.$elm$,!1)),a},xh=(e,t)=>{Ip.$flags$|=1;const n=Array.from(e.childNodes);if(e["s-sr"]&&bp.experimentalSlotFixes){let t=e;for(;t=t.nextSibling;)t&&t["s-sn"]===e["s-sn"]&&t["s-sh"]===Yp&&n.push(t)}for(let o=n.length-1;o>=0;o--){const e=n[o];e["s-hn"]!==Yp&&e["s-ol"]&&(_h($h(e),e,Ch(e)),e["s-ol"].remove(),e["s-ol"]=void 0,e["s-sh"]=void 0,vh=!0),t&&xh(e,t)}Ip.$flags$&=-2},kh=(e,t,n,o,r,i)=>{let a,l=e["s-cr"]&&e["s-cr"].parentNode||e;for(l.shadowRoot&&l.tagName===Yp&&(l=l.shadowRoot);r<=i;++r)o[r]&&(a=wh(null,n,r,e),a&&(o[r].$elm$=a,_h(l,a,Ch(t))))},Eh=(e,t,n)=>{for(let o=t;o<=n;++o){const t=e[o];if(t){const e=t.$elm$;Lh(t),e&&(bh=!0,e["s-ol"]?e["s-ol"].remove():xh(e,!0),e.remove())}}},Sh=(e,t,n=!1)=>!(e.$tag$!==t.$tag$||("slot"===e.$tag$?"$nodeId$"in e&&n&&8!==e.$elm$.nodeType||e.$name$!==t.$name$:!n&&e.$key$!==t.$key$)),Ch=e=>e&&e["s-ol"]||e,$h=e=>(e["s-ol"]?e["s-ol"]:e).parentNode,Th=(e,t,n=!1)=>{const o=t.$elm$=e.$elm$,r=e.$children$,i=t.$children$,a=t.$tag$,l=t.$text$;let s;null===l?(yh="svg"===a||"foreignObject"!==a&&yh,("slot"!==a||gh)&&hh(e,t,yh),null!==r&&null!==i?((e,t,n,o,r=!1)=>{let i,a,l=0,s=0,c=0,u=0,d=t.length-1,f=t[0],p=t[d],h=o.length-1,m=o[0],g=o[h];for(;l<=d&&s<=h;)if(null==f)f=t[++l];else if(null==p)p=t[--d];else if(null==m)m=o[++s];else if(null==g)g=o[--h];else if(Sh(f,m,r))Th(f,m,r),f=t[++l],m=o[++s];else if(Sh(p,g,r))Th(p,g,r),p=t[--d],g=o[--h];else if(Sh(f,g,r))"slot"!==f.$tag$&&"slot"!==g.$tag$||xh(f.$elm$.parentNode,!1),Th(f,g,r),_h(e,f.$elm$,p.$elm$.nextSibling),f=t[++l],g=o[--h];else if(Sh(p,m,r))"slot"!==f.$tag$&&"slot"!==g.$tag$||xh(p.$elm$.parentNode,!1),Th(p,m,r),_h(e,p.$elm$,f.$elm$),p=t[--d],m=o[++s];else{for(c=-1,u=l;u<=d;++u)if(t[u]&&null!==t[u].$key$&&t[u].$key$===m.$key$){c=u;break}c>=0?(a=t[c],a.$tag$!==m.$tag$?i=wh(t&&t[s],n,c,e):(Th(a,m,r),t[c]=void 0,i=a.$elm$),m=o[++s]):(i=wh(t&&t[s],n,s,e),m=o[++s]),i&&_h($h(f.$elm$),i,Ch(f.$elm$))}l>d?kh(e,null==o[h+1]?null:o[h+1].$elm$,n,o,s,h):s>h&&Eh(t,l,d)})(o,r,t,i,n):null!==i?(null!==e.$text$&&(o.textContent=""),kh(o,null,t,i,0,i.length-1)):!n&&bp.updatable&&null!==r&&Eh(r,0,r.length-1),yh&&"svg"===a&&(yh=!1)):(s=o["s-cr"])?s.parentNode.textContent=l:e.$text$!==l&&(o.data=l)},Ph=e=>{const t=e.childNodes;for(const n of t)if(1===n.nodeType){if(n["s-sr"]){const e=n["s-sn"];n.hidden=!1;for(const o of t)if(o!==n)if(o["s-hn"]!==n["s-hn"]||""!==e){if(1===o.nodeType&&(e===o.getAttribute("slot")||e===o["s-sn"])||3===o.nodeType&&e===o["s-sn"]){n.hidden=!0;break}}else if(1===o.nodeType||3===o.nodeType&&""!==o.textContent.trim()){n.hidden=!0;break}}Ph(n)}},Rh=[],Ih=e=>{let t,n,o;for(const r of e.childNodes){if(r["s-sr"]&&(t=r["s-cr"])&&t.parentNode){n=t.parentNode.childNodes;const e=r["s-sn"];for(o=n.length-1;o>=0;o--)if(t=n[o],!t["s-cn"]&&!t["s-nr"]&&t["s-hn"]!==r["s-hn"]&&!bp.experimentalSlotFixes)if(Oh(t,e)){let n=Rh.find((e=>e.$nodeToRelocate$===t));bh=!0,t["s-sn"]=t["s-sn"]||e,n?(n.$nodeToRelocate$["s-sh"]=r["s-hn"],n.$slotRefNode$=r):(t["s-sh"]=r["s-hn"],Rh.push({$slotRefNode$:r,$nodeToRelocate$:t})),t["s-sr"]&&Rh.map((e=>{Oh(e.$nodeToRelocate$,t["s-sn"])&&(n=Rh.find((e=>e.$nodeToRelocate$===t)),n&&!e.$slotRefNode$&&(e.$slotRefNode$=n.$slotRefNode$))}))}else Rh.some((e=>e.$nodeToRelocate$===t))||Rh.push({$nodeToRelocate$:t})}1===r.nodeType&&Ih(r)}},Oh=(e,t)=>1===e.nodeType?null===e.getAttribute("slot")&&""===t||e.getAttribute("slot")===t:e["s-sn"]===t||""===t,Lh=e=>{e.$attrs$&&e.$attrs$.ref&&e.$attrs$.ref(null),e.$children$&&e.$children$.map(Lh)},_h=(e,t,n)=>{const o=null==e?void 0:e.insertBefore(t,n);return Nh(t,e),o},zh=e=>{const t=[];return e&&t.push(...e["s-scs"]||[],e["s-si"],e["s-sc"],...zh(e.parentElement)),t},Nh=(e,t,n=!1)=>{var o;if(e&&t&&1===e.nodeType){const r=new Set(zh(t).filter(Boolean));if(r.size&&(null==(o=e.classList)||o.add(...e["s-scs"]=[...r]),e["s-ol"]||n))for(const t of Array.from(e.childNodes))Nh(t,e,!0)}},Mh=(e,t,n=!1)=>{var o,r,i,a,l;const s=e.$hostElement$,c=e.$cmpMeta$,u=e.$vnode$||Jp(null,null),d=(f=t)&&f.$tag$===eh?t:Zp(null,null,t);var f;if(Yp=s.tagName,c.$attrsToReflect$&&(d.$attrs$=d.$attrs$||{},c.$attrsToReflect$.map((([e,t])=>d.$attrs$[t]=s[e]))),n&&d.$attrs$)for(const p of Object.keys(d.$attrs$))s.hasAttribute(p)&&!["key","ref","style","class"].includes(p)&&(d.$attrs$[p]=s[p]);if(d.$tag$=null,d.$flags$|=4,e.$vnode$=d,d.$elm$=u.$elm$=s.shadowRoot||s,Qp=s["s-sc"],gh=!!(1&c.$flags$),Kp=s["s-cr"],bh=!1,Th(u,d,n),Ip.$flags$|=1,vh){Ih(d.$elm$);for(const e of Rh){const t=e.$nodeToRelocate$;if(!t["s-ol"]){const e=Pp.createTextNode("");e["s-nr"]=t,_h(t.parentNode,t["s-ol"]=e,t)}}for(const e of Rh){const t=e.$nodeToRelocate$,l=e.$slotRefNode$;if(l){const e=l.parentNode;let n=l.nextSibling;{let i=null==(o=t["s-ol"])?void 0:o.previousSibling;for(;i;){let o=null!=(r=i["s-nr"])?r:null;if(o&&o["s-sn"]===t["s-sn"]&&e===o.parentNode){for(o=o.nextSibling;o===t||(null==o?void 0:o["s-sr"]);)o=null==o?void 0:o.nextSibling;if(!o||!o["s-nr"]){n=o;break}}i=i.previousSibling}}(!n&&e!==t.parentNode||t.nextSibling!==n)&&t!==n&&(!t["s-hn"]&&t["s-ol"]&&(t["s-hn"]=t["s-ol"].parentNode.nodeName),_h(e,t,n),1===t.nodeType&&(t.hidden=null!=(i=t["s-ih"])&&i)),t&&"function"==typeof l["s-rf"]&&l["s-rf"](t)}else 1===t.nodeType&&(n&&(t["s-ih"]=null!=(a=t.hidden)&&a),t.hidden=!0)}}if(bh&&Ph(d.$elm$),Ip.$flags$&=-2,Rh.length=0,bp.experimentalScopedSlotChanges&&2&c.$flags$)for(const p of d.$elm$.childNodes)p["s-hn"]===Yp||p["s-sh"]||(n&&null==p["s-ih"]&&(p["s-ih"]=null!=(l=p.hidden)&&l),p.hidden=!0);Kp=void 0},jh=(e,t)=>(e.$flags$|=16,e.$ancestorComponent$,Vp((()=>Dh(e,t)))),Dh=(e,t)=>{const n=e.$hostElement$,o=(e.$cmpMeta$.$tagName$,()=>{}),r=n;if(!r)throw new Error(`Can't render component <${n.tagName.toLowerCase()} /> with invalid Stencil runtime! Make sure this imported component is compiled with a \`externalRuntime: true\` flag. For more information, please refer to https://stenciljs.com/docs/custom-elements#externalruntime`);let i;return i=Uh(r,t?"componentWillLoad":"componentWillUpdate"),i=Ah(i,(()=>Uh(r,"componentWillRender"))),o(),Ah(i,(()=>Vh(e,r,t)))},Ah=(e,t)=>Bh(e)?e.then(t).catch((e=>{console.error(e),t()})):t(),Bh=e=>e instanceof Promise||e&&e.then&&"function"==typeof e.then,Vh=async(e,t,n)=>{const o=e.$hostElement$,r=(e.$cmpMeta$.$tagName$,()=>{});o["s-rc"],n&&lh(e);const i=(e.$cmpMeta$.$tagName$,()=>{});Hh(e,t,o,n),i(),r(),Fh(e)},Hh=(e,t,n,o)=>{try{t=t.render&&t.render(),e.$flags$&=-17,e.$flags$|=2,(bp.hasRenderFn||bp.reflect)&&(bp.vdomRender||bp.reflect)&&(bp.hydrateServerSide||Mh(e,t,o))}catch(kf){Ep(kf,e.$hostElement$)}return null},Fh=e=>{e.$cmpMeta$.$tagName$;const t=()=>{},n=e.$hostElement$;e.$ancestorComponent$,Uh(n,"componentDidRender"),64&e.$flags$?(Uh(n,"componentDidUpdate"),t()):(e.$flags$|=64,Uh(n,"componentDidLoad"),t())},Wh=e=>{{const t=xp(e),n=t.$hostElement$.isConnected;return n&&2==(18&t.$flags$)&&jh(t,!1),n}},Uh=(e,t,n)=>{if(e&&e[t])try{return e[t](n)}catch(kf){Ep(kf)}},qh=(e,t,n,o)=>{const r=xp(e),i=e,a=r.$instanceValues$.get(t),l=r.$flags$,s=i;var c,u;c=n,u=o.$members$[t][0],n=null==c||Fp(c)?c:4&u?"false"!==c&&(""===c||!!c):2&u?parseFloat(c):1&u?String(c):c;const d=Number.isNaN(a)&&Number.isNaN(n);if(n!==a&&!d){if(r.$instanceValues$.set(t,n),o.$watchers$&&128&l){const e=o.$watchers$[t];e&&e.map((e=>{try{s[e](n,a,t)}catch(kf){Ep(kf,i)}}))}if(2==(18&l)){if(s.componentShouldUpdate&&!1===s.componentShouldUpdate(n,a,t))return;jh(r,!1)}}},Qh=(e,t,n)=>{var o,r;const i=e.prototype;if(t.$members$||t.$watchers$||e.watchers){e.watchers&&!t.$watchers$&&(t.$watchers$=e.watchers);const n=Object.entries(null!=(o=t.$members$)?o:{});n.map((([e,[n]])=>{(31&n||32&n)&&Object.defineProperty(i,e,{get(){return t=e,xp(this).$instanceValues$.get(t);var t},set(n){qh(this,e,n,t)},configurable:!0,enumerable:!0})}));{const o=new Map;i.attributeChangedCallback=function(e,n,r){Ip.jmp((()=>{var a;const l=o.get(e);if(this.hasOwnProperty(l))r=this[l],delete this[l];else{if(i.hasOwnProperty(l)&&"number"==typeof this[l]&&this[l]==r)return;if(null==l){const o=xp(this),i=null==o?void 0:o.$flags$;if(i&&!(8&i)&&128&i&&r!==n){const o=this,i=null==(a=t.$watchers$)?void 0:a[e];null==i||i.forEach((t=>{null!=o[t]&&o[t].call(o,r,n,e)}))}return}}this[l]=(null!==r||"boolean"!=typeof this[l])&&r}))},e.observedAttributes=Array.from(new Set([...Object.keys(null!=(r=t.$watchers$)?r:{}),...n.filter((([e,t])=>15&t[0])).map((([e,n])=>{var r;const i=n[1]||e;return o.set(i,e),512&n[0]&&(null==(r=t.$attrsToReflect$)||r.push([e,i])),i}))]))}}return e},Kh=async(e,t,n,o)=>{let r;if(!(32&t.$flags$)){t.$flags$|=32,n.$lazyBundleId$;{r=e.constructor;const n=e.localName;customElements.whenDefined(n).then((()=>t.$flags$|=128))}if(r&&r.style){let o;"string"==typeof r.style?o=r.style:"string"!=typeof r.style&&(t.$modeName$=(e=>Cp.map((t=>t(e))).find((e=>!!e)))(e),t.$modeName$&&(o=r.style[t.$modeName$]));const i=sh(n,t.$modeName$);if(!Sp.has(i)){const e=(n.$tagName$,()=>{});((e,t,n)=>{let o=Sp.get(e);Lp&&n?(o=o||new CSSStyleSheet,"string"==typeof o?o=t:o.replaceSync(t)):o=t,Sp.set(e,o)})(i,o,!!(1&n.$flags$)),e()}}}t.$ancestorComponent$,jh(t,!0)},Yh=e=>{const t=e["s-cr"]=Pp.createComment("");t["s-cn"]=!0,_h(e,t,e.firstChild)},Xh=(e,t)=>{const n={$flags$:t[0],$tagName$:t[1]};n.$members$=t[2],n.$listeners$=t[3],n.$watchers$=e.$watchers$,n.$attrsToReflect$=[];const o=e.prototype.connectedCallback,r=e.prototype.disconnectedCallback;return Object.assign(e.prototype,{__registerHost(){((e,t)=>{const n={$flags$:0,$hostElement$:e,$cmpMeta$:t,$instanceValues$:new Map};wp.set(e,n)})(this,n)},connectedCallback(){const e=xp(this);Gh(this,e,n.$listeners$),(e=>{if(!(1&Ip.$flags$)){const t=xp(e),n=t.$cmpMeta$,o=(n.$tagName$,()=>{});1&t.$flags$?(Gh(e,t,n.$listeners$),(null==t?void 0:t.$lazyInstance$)?t.$lazyInstance$:(null==t?void 0:t.$onReadyPromise$)&&t.$onReadyPromise$.then((()=>{t.$lazyInstance$}))):(t.$flags$|=1,12&n.$flags$&&Yh(e),n.$members$&&Object.entries(n.$members$).map((([t,[n]])=>{if(31&n&&e.hasOwnProperty(t)){const n=e[t];delete e[t],e[t]=n}})),Kh(e,t,n)),o()}})(this),o&&o.call(this)},disconnectedCallback(){(async e=>{if(!(1&Ip.$flags$)){const t=xp(e);t.$rmListeners$&&(t.$rmListeners$.map((e=>e())),t.$rmListeners$=void 0)}})(this),r&&r.call(this)},__attachShadow(){if(this.shadowRoot){if("open"!==this.shadowRoot.mode)throw new Error(`Unable to re-use existing shadow root for ${n.$tagName$}! Mode is set to ${this.shadowRoot.mode} but Stencil only supports open shadow roots.`)}else this.attachShadow({mode:"open",delegatesFocus:!!(16&n.$flags$)})}}),e.is=n.$tagName$,Qh(e,n)},Gh=(e,t,n,o)=>{n&&n.map((([n,o,r])=>{const i=Jh(e,n),a=Zh(t,r),l=em(n);Ip.ael(i,o,a,l),(t.$rmListeners$=t.$rmListeners$||[]).push((()=>Ip.rel(i,o,a,l)))}))},Zh=(e,t)=>n=>{try{bp.lazyLoad||e.$hostElement$[t](n)}catch(kf){Ep(kf)}},Jh=(e,t)=>4&t?Pp:8&t?Tp:16&t?Pp.body:e,em=e=>Op?{passive:!!(1&e),capture:!!(2&e)}:!!(2&e)
/*!
             * (C) Ionic http://ionicframework.com - MIT License
             */;const tm=e("h","undefined"!=typeof window?window:void 0),nm=e("d","undefined"!=typeof document?document:void 0);
/*!
             * (C) Ionic http://ionicframework.com - MIT License
             */
let om;const rm=(e,t,n)=>{const o=t.startsWith("animation")?(e=>{if(void 0===om){const t=void 0!==e.style.animationName,n=void 0!==e.style.webkitAnimationName;om=!t&&n?"-webkit-":""}return om})(e):"";e.style.setProperty(o+t,n)},im=(e=[],t)=>{if(void 0!==t){const n=Array.isArray(t)?t:[t];return[...e,...n]}return e};e("m",(e=>{let t,n,o,r,i,a,l,s,c,u,d,f=[],p=[],h=[],m=!1,g={},b=[],v=[],y={},w=0,x=!1,k=!1,E=!0,S=!1,C=!0,$=!1;const T=e,P=[],R=[],I=[],O=[],L=[],_=[],z=[],N=[],M=[],j=[],D=[],A="function"==typeof AnimationEffect||void 0!==tm&&"function"==typeof tm.AnimationEffect,B="function"==typeof Element&&"function"==typeof Element.prototype.animate&&A,V=()=>D,H=e=>{q(),e&&Q()},F=(e,t)=>{const n=t.findIndex((t=>t.c===e));n>-1&&t.splice(n,1)},W=(e,t)=>(((null==t?void 0:t.oneTimeCallback)?R:P).push({c:e,o:t}),d),U=()=>(P.length=0,R.length=0,d),q=()=>{B&&(D.forEach((e=>{e.cancel()})),D.length=0)},Q=()=>{_.forEach((e=>{(null==e?void 0:e.parentNode)&&e.parentNode.removeChild(e)})),_.length=0},K=()=>void 0!==i?i:l?l.getFill():"both",Y=()=>void 0!==s?s:void 0!==a?a:l?l.getDirection():"normal",X=()=>x?"linear":void 0!==o?o:l?l.getEasing():"linear",G=()=>k?0:void 0!==c?c:void 0!==n?n:l?l.getDuration():0,Z=()=>void 0!==r?r:l?l.getIterations():1,J=()=>void 0!==u?u:void 0!==t?t:l?l.getDelay():0,ee=e=>{B&&V().forEach((t=>{const n=t.effect;if(n.setKeyframes)n.setKeyframes(e);else{const o=new KeyframeEffect(n.target,e,n.getTiming());t.effect=o}}))},te=()=>{0!==w&&(w--,0===w&&((()=>{M.forEach((e=>e())),j.forEach((e=>e()));const e=E?1:0,t=b,n=v,o=y;O.forEach((e=>{const r=e.classList;t.forEach((e=>r.add(e))),n.forEach((e=>r.remove(e)));for(const t in o)o.hasOwnProperty(t)&&rm(e,t,o[t])})),c=void 0,s=void 0,u=void 0,P.forEach((t=>t.c(e,d))),R.forEach((t=>t.c(e,d))),R.length=0,C=!0,E&&(S=!0),E=!0})(),l&&l.animationFinish()))},ne=()=>{(()=>{z.forEach((e=>e())),N.forEach((e=>e()));const e=p,t=h,n=g;O.forEach((o=>{const r=o.classList;e.forEach((e=>r.add(e))),t.forEach((e=>r.remove(e)));for(const e in n)n.hasOwnProperty(e)&&rm(o,e,n[e])}))})(),f.length>0&&B&&(O.forEach((e=>{const t=e.animate(f,{id:T,delay:J(),duration:G(),easing:X(),iterations:Z(),fill:K(),direction:Y()});t.pause(),D.push(t)})),D.length>0&&(D[0].onfinish=()=>{te()})),m=!0},oe=e=>{e=Math.min(Math.max(e,0),.9999),B&&D.forEach((t=>{t.currentTime=t.effect.getComputedTiming().delay+G()*e,t.pause()}))},re=e=>{D.forEach((e=>{e.effect.updateTiming({delay:J(),duration:G(),easing:X(),iterations:Z(),fill:K(),direction:Y()})})),void 0!==e&&oe(e)},ie=(e=!1,t=!0,n)=>(e&&L.forEach((o=>{o.update(e,t,n)})),B&&re(n),d),ae=()=>{m&&(B?D.forEach((e=>{e.pause()})):O.forEach((e=>{rm(e,"animation-play-state","paused")})),$=!0)},le=e=>new Promise((t=>{(null==e?void 0:e.sync)&&(k=!0,W((()=>k=!1),{oneTimeCallback:!0})),m||ne(),S&&(B&&(oe(0),re()),S=!1),C&&(w=L.length+1,C=!1);const n=()=>{F(o,R),t()},o=()=>{F(n,I),t()};W(o,{oneTimeCallback:!0}),((e,t)=>{I.push({c:e,o:t})})(n,{oneTimeCallback:!0}),L.forEach((e=>{e.play()})),B?(D.forEach((e=>{e.play()})),0!==f.length&&0!==O.length||te()):te(),$=!1})),se=(e,t)=>{const n=f[0];return void 0===n||void 0!==n.offset&&0!==n.offset?f=[{offset:0,[e]:t},...f]:n[e]=t,d};return d={parentAnimation:l,elements:O,childAnimations:L,id:T,animationFinish:te,from:se,to:(e,t)=>{const n=f[f.length-1];return void 0===n||void 0!==n.offset&&1!==n.offset?f=[...f,{offset:1,[e]:t}]:n[e]=t,d},fromTo:(e,t,n)=>se(e,t).to(e,n),parent:e=>(l=e,d),play:le,pause:()=>(L.forEach((e=>{e.pause()})),ae(),d),stop:()=>{L.forEach((e=>{e.stop()})),m&&(q(),m=!1),x=!1,k=!1,C=!0,s=void 0,c=void 0,u=void 0,w=0,S=!1,E=!0,$=!1,I.forEach((e=>e.c(0,d))),I.length=0},destroy:e=>(L.forEach((t=>{t.destroy(e)})),H(e),O.length=0,L.length=0,f.length=0,U(),m=!1,C=!0,d),keyframes:e=>{const t=f!==e;return f=e,t&&ee(f),d},addAnimation:e=>{if(null!=e)if(Array.isArray(e))for(const t of e)t.parent(d),L.push(t);else e.parent(d),L.push(e);return d},addElement:e=>{if(null!=e)if(1===e.nodeType)O.push(e);else if(e.length>=0)for(let t=0;t<e.length;t++)O.push(e[t]);else console.error("Invalid addElement value");return d},update:ie,fill:e=>(i=e,ie(!0),d),direction:e=>(a=e,ie(!0),d),iterations:e=>(r=e,ie(!0),d),duration:e=>(B||0!==e||(e=1),n=e,ie(!0),d),easing:e=>(o=e,ie(!0),d),delay:e=>(t=e,ie(!0),d),getWebAnimations:V,getKeyframes:()=>f,getFill:K,getDirection:Y,getDelay:J,getIterations:Z,getEasing:X,getDuration:G,afterAddRead:e=>(M.push(e),d),afterAddWrite:e=>(j.push(e),d),afterClearStyles:(e=[])=>{for(const t of e)y[t]="";return d},afterStyles:(e={})=>(y=e,d),afterRemoveClass:e=>(v=im(v,e),d),afterAddClass:e=>(b=im(b,e),d),beforeAddRead:e=>(z.push(e),d),beforeAddWrite:e=>(N.push(e),d),beforeClearStyles:(e=[])=>{for(const t of e)g[t]="";return d},beforeStyles:(e={})=>(g=e,d),beforeRemoveClass:e=>(h=im(h,e),d),beforeAddClass:e=>(p=im(p,e),d),onFinish:W,isRunning:()=>0!==w&&!$,progressStart:(e=!1,t)=>(L.forEach((n=>{n.progressStart(e,t)})),ae(),x=e,m||ne(),ie(!1,!0,t),d),progressStep:e=>(L.forEach((t=>{t.progressStep(e)})),oe(e),d),progressEnd:(e,t,n)=>(x=!1,L.forEach((o=>{o.progressEnd(e,t,n)})),void 0!==n&&(c=n),S=!1,E=!0,0===e?(s="reverse"===Y()?"normal":"reverse","reverse"===s&&(E=!1),B?(ie(),oe(1-t)):(u=(1-t)*G()*-1,ie(!1,!1))):1===e&&(B?(ie(),oe(t)):(u=t*G()*-1,ie(!1,!1))),void 0===e||l||le(),d)}}));
/*!
             * (C) Ionic http://ionicframework.com - MIT License
             */
class am{constructor(){this.m=new Map}reset(e){this.m=new Map(Object.entries(e))}get(e,t){const n=this.m.get(e);return void 0!==n?n:t}getBoolean(e,t=!1){const n=this.m.get(e);return void 0===n?t:"string"==typeof n?"true"===n:!!n}getNumber(e,t){const n=parseFloat(this.m.get(e));return isNaN(n)?void 0!==t?t:NaN:n}set(e,t){this.m.set(e,t)}}const lm=new am,sm=(e,t)=>e.substr(0,t.length)===t,cm="ionic:",um="ionic-persist-config",dm=(e,t)=>("string"==typeof e&&(t=e,e=void 0),(e=>fm(e))(e).includes(t)),fm=(e=window)=>{if(void 0===e)return[];e.Ionic=e.Ionic||{};let t=e.Ionic.platforms;return null==t&&(t=e.Ionic.platforms=pm(e),t.forEach((t=>e.document.documentElement.classList.add(`plt-${t}`)))),t},pm=e=>{const t=lm.get("platform");return Object.keys(km).filter((n=>{const o=null==t?void 0:t[n];return"function"==typeof o?o(e):km[n](e)}))},hm=e=>!!wm(e,/iPad/i)||!(!wm(e,/Macintosh/i)||!gm(e)),mm=e=>wm(e,/android|sink/i),gm=e=>xm(e,"(any-pointer:coarse)"),bm=e=>vm(e)||ym(e),vm=e=>!!(e.cordova||e.phonegap||e.PhoneGap),ym=e=>{const t=e.Capacitor;return!!(null==t?void 0:t.isNative)},wm=(e,t)=>t.test(e.navigator.userAgent),xm=(e,t)=>{var n;return null===(n=e.matchMedia)||void 0===n?void 0:n.call(e,t).matches},km={ipad:hm,iphone:e=>wm(e,/iPhone/i),ios:e=>wm(e,/iPhone|iPod/i)||hm(e),android:mm,phablet:e=>{const t=e.innerWidth,n=e.innerHeight,o=Math.min(t,n),r=Math.max(t,n);return o>390&&o<520&&r>620&&r<800},tablet:e=>{const t=e.innerWidth,n=e.innerHeight,o=Math.min(t,n),r=Math.max(t,n);return hm(e)||(e=>mm(e)&&!wm(e,/mobile/i))(e)||o>460&&o<820&&r>780&&r<1400},cordova:vm,capacitor:ym,electron:e=>wm(e,/electron/i),pwa:e=>{var t;return!(!(null===(t=e.matchMedia)||void 0===t?void 0:t.call(e,"(display-mode: standalone)").matches)&&!e.navigator.standalone)},mobile:gm,mobileweb:e=>gm(e)&&!bm(e),desktop:e=>!gm(e),hybrid:bm};let Em;const Sm=e=>e&&(e=>xp(e).$modeName$)(e)||Em,Cm=(e={})=>{if("undefined"==typeof window)return;const t=window.document,n=window,o=n.Ionic=n.Ionic||{},r={};var i;e._ael&&(r.ael=e._ael),e._rel&&(r.rel=e._rel),e._ce&&(r.ce=e._ce),i=r,Object.assign(Ip,i);const a=Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},(e=>{try{const t=e.sessionStorage.getItem(um);return null!==t?JSON.parse(t):{}}catch(kf){return{}}})(n)),{persistConfig:!1}),o.config),(e=>{const t={};return e.location.search.slice(1).split("&").map((e=>e.split("="))).map((([e,t])=>{try{return[decodeURIComponent(e),decodeURIComponent(t)]}catch(kf){return["",""]}})).filter((([e])=>sm(e,cm))).map((([e,t])=>[e.slice(cm.length),t])).forEach((([e,n])=>{t[e]=n})),t})(n)),e);lm.reset(a),lm.getBoolean("persistConfig")&&((e,t)=>{try{e.sessionStorage.setItem(um,JSON.stringify(t))}catch(kf){return}})(n,a),fm(n),o.config=lm,o.mode=Em=lm.get("mode",t.documentElement.getAttribute("mode")||(dm(n,"ios")?"ios":"md")),lm.set("mode",Em),t.documentElement.setAttribute("mode",Em),t.documentElement.classList.add(Em),lm.getBoolean("_testing")&&lm.set("animated",!1);const l=e=>{var t;return null===(t=e.tagName)||void 0===t?void 0:t.startsWith("ION-")},s=e=>["ios","md"].includes(e);var c;c=e=>{for(;e;){const t=e.mode||e.getAttribute("mode");if(t){if(s(t))return t;l(e)&&console.warn('Invalid ionic mode: "'+t+'", expected: "ios" or "md"')}e=e.parentElement}return Em},Cp.push(c)},$m=(e,...t)=>console.warn(`[Ionic Warning]: ${e}`,...t),Tm=e("a",((e,t)=>{e.componentOnReady?e.componentOnReady().then((e=>t(e))):Lm((()=>t(e)))})),Pm=e=>void 0!==e.componentOnReady,Rm=(e,t=[])=>{const n={};return t.forEach((t=>{e.hasAttribute(t)&&(null!==e.getAttribute(t)&&(n[t]=e.getAttribute(t)),e.removeAttribute(t))})),n},Im=["role","aria-activedescendant","aria-atomic","aria-autocomplete","aria-braillelabel","aria-brailleroledescription","aria-busy","aria-checked","aria-colcount","aria-colindex","aria-colindextext","aria-colspan","aria-controls","aria-current","aria-describedby","aria-description","aria-details","aria-disabled","aria-errormessage","aria-expanded","aria-flowto","aria-haspopup","aria-hidden","aria-invalid","aria-keyshortcuts","aria-label","aria-labelledby","aria-level","aria-live","aria-multiline","aria-multiselectable","aria-orientation","aria-owns","aria-placeholder","aria-posinset","aria-pressed","aria-readonly","aria-relevant","aria-required","aria-roledescription","aria-rowcount","aria-rowindex","aria-rowindextext","aria-rowspan","aria-selected","aria-setsize","aria-sort","aria-valuemax","aria-valuemin","aria-valuenow","aria-valuetext"],Om=(e,t)=>Rm(e,Im),Lm=(e("b",((e,t,n,o)=>{var r;if("undefined"!=typeof window){const i=window,a=null===(r=null==i?void 0:i.Ionic)||void 0===r?void 0:r.config;if(a){const r=a.get("_ael");if(r)return r(e,t,n,o);if(a._ael)return a._ael(e,t,n,o)}}return e.addEventListener(t,n,o)})),e("e",((e,t,n,o)=>{var r;if("undefined"!=typeof window){const i=window,a=null===(r=null==i?void 0:i.Ionic)||void 0===r?void 0:r.config;if(a){const r=a.get("_rel");if(r)return r(e,t,n,o);if(a._rel)return a._rel(e,t,n,o)}}return e.removeEventListener(t,n,o)})),e("j",(e=>"function"==typeof __zone_symbol__requestAnimationFrame?__zone_symbol__requestAnimationFrame(e):"function"==typeof requestAnimationFrame?requestAnimationFrame(e):setTimeout(e)))),_m=e("c",((e,t,n)=>Math.max(e,Math.min(t,n)))),zm=(e,t)=>{if(!e){const e="ASSERT: "+t;throw console.error(e),new Error(e)}},Nm=(e("p",(e=>{if(e){const t=e.changedTouches;if(t&&t.length>0){const e=t[0];return{x:e.clientX,y:e.clientY}}if(void 0!==e.pageX)return{x:e.pageX,y:e.pageY}}return{x:0,y:0}})),(e,t)=>{if(null!=e||(e={}),null!=t||(t={}),e===t)return!0;const n=Object.keys(e);if(n.length!==Object.keys(t).length)return!1;for(const o of n){if(!(o in t))return!1;if(e[o]!==t[o])return!1}return!0}),Mm="ionViewWillLeave",jm="ionViewDidLeave",Dm="ionViewWillUnload",Am=e=>{e.tabIndex=-1,e.focus()},Bm=e=>null!==e.offsetParent,Vm="ion-last-focus",Hm={saveViewFocus:e=>{if(lm.get("focusManagerPriority",!1)){const t=document.activeElement;null!==t&&(null==e?void 0:e.contains(t))&&t.setAttribute(Vm,"true")}},setViewFocus:e=>{const t=lm.get("focusManagerPriority",!1);if(Array.isArray(t)&&!e.contains(document.activeElement)){const n=e.querySelector(`[${Vm}]`);if(n&&Bm(n))return void Am(n);for(const o of t)switch(o){case"content":const t=e.querySelector('main, [role="main"]');if(t&&Bm(t))return void Am(t);break;case"heading":const n=e.querySelector('h1, [role="heading"][aria-level="1"]');if(n&&Bm(n))return void Am(n);break;case"banner":const r=e.querySelector('header, [role="banner"]');if(r&&Bm(r))return void Am(r);break;default:$m(`Unrecognized focus manager priority value ${o}`)}Am(e)}}},Fm=e=>new Promise(((t,n)=>{Vp((()=>{Wm(e),Um(e).then((n=>{n.animation&&n.animation.destroy(),qm(e),t(n)}),(t=>{qm(e),n(t)}))}))})),Wm=e=>{const t=e.enteringEl,n=e.leavingEl;Hm.saveViewFocus(n),rg(t,n,e.direction),e.showGoBack?t.classList.add("can-go-back"):t.classList.remove("can-go-back"),og(t,!1),t.style.setProperty("pointer-events","none"),n&&(og(n,!1),n.style.setProperty("pointer-events","none"))},Um=async e=>{const t=await Qm(e);return t&&yp?Km(t,e):Ym(e)},qm=e=>{const t=e.enteringEl,n=e.leavingEl;t.classList.remove("ion-page-invisible"),t.style.removeProperty("pointer-events"),void 0!==n&&(n.classList.remove("ion-page-invisible"),n.style.removeProperty("pointer-events")),Hm.setViewFocus(t)},Qm=async e=>{if(e.leavingEl&&e.animated&&0!==e.duration)return e.animationBuilder?e.animationBuilder:"ios"===e.mode?(await gp((()=>t.import("./ios.transition-legacy-CiTvk0sb.js")))).iosTransitionAnimation:(await gp((()=>t.import("./md.transition-legacy-CgFfI_oz.js")))).mdTransitionAnimation},Km=async(e,t)=>{await Xm(t,!0);const n=e(t.baseEl,t);Jm(t.enteringEl,t.leavingEl);const o=await Zm(n,t);return t.progressCallback&&t.progressCallback(void 0),o&&eg(t.enteringEl,t.leavingEl),{hasCompleted:o,animation:n}},Ym=async e=>{const t=e.enteringEl,n=e.leavingEl,o=lm.get("focusManagerPriority",!1);return await Xm(e,o),Jm(t,n),eg(t,n),{hasCompleted:!0}},Xm=async(e,t)=>{(void 0!==e.deepWait?e.deepWait:t)&&await Promise.all([ng(e.enteringEl),ng(e.leavingEl)]),await Gm(e.viewIsReady,e.enteringEl)},Gm=async(e,t)=>{e&&await e(t)},Zm=(e,t)=>{const n=t.progressCallback,o=new Promise((t=>{e.onFinish((e=>t(1===e)))}));return n?(e.progressStart(!0),n(e)):e.play(),o},Jm=(e,t)=>{tg(t,Mm),tg(e,"ionViewWillEnter")},eg=(e,t)=>{tg(e,"ionViewDidEnter"),tg(t,jm)},tg=(e,t)=>{if(e){const n=new CustomEvent(t,{bubbles:!1,cancelable:!1});e.dispatchEvent(n)}},ng=async e=>{const t=e;if(t){if(null!=t.componentOnReady){if(null!=await t.componentOnReady())return}else if(null!=t.__registerHost){const e=new Promise((e=>Lm(e)));return void(await e)}await Promise.all(Array.from(t.children).map(ng))}},og=(e,t)=>{t?(e.setAttribute("aria-hidden","true"),e.classList.add("ion-page-hidden")):(e.hidden=!1,e.removeAttribute("aria-hidden"),e.classList.remove("ion-page-hidden"))},rg=(e,t,n)=>{void 0!==e&&(e.style.zIndex="back"===n?"99":"101"),void 0!==t&&(t.style.zIndex="100")},ig=(e("n",(e=>{if(e.classList.contains("ion-page"))return e;const t=e.querySelector(":scope > .ion-page, :scope > ion-nav, :scope > ion-tabs");return t||e})),(e,t,n,o,r)=>lg(e[1],t[1],n[1],o[1],r).map((r=>ag(e[0],t[0],n[0],o[0],r)))),ag=(e,t,n,o,r)=>r*(3*t*Math.pow(r-1,2)+r*(-3*n*r+3*n+o*r))-e*Math.pow(r-1,3),lg=(e,t,n,o,r)=>sg((o-=r)-3*(n-=r)+3*(t-=r)-(e-=r),3*n-6*t+3*e,3*t-3*e,e).filter((e=>e>=0&&e<=1)),sg=(e,t,n,o)=>{if(0===e)return((e,t,n)=>{const o=t*t-4*e*n;return o<0?[]:[(-t+Math.sqrt(o))/(2*e),(-t-Math.sqrt(o))/(2*e)]})(t,n,o);const r=(3*(n/=e)-(t/=e)*t)/3,i=(2*t*t*t-9*t*n+27*(o/=e))/27;if(0===r)return[Math.pow(-i,1/3)];if(0===i)return[Math.sqrt(-r),-Math.sqrt(-r)];const a=Math.pow(i/2,2)+Math.pow(r/3,3);if(0===a)return[Math.pow(i/2,.5)-t/3];if(a>0)return[Math.pow(-i/2+Math.sqrt(a),1/3)-Math.pow(i/2+Math.sqrt(a),1/3)-t/3];const l=Math.sqrt(Math.pow(-r/3,3)),s=Math.acos(-i/(2*Math.sqrt(Math.pow(-r/3,3)))),c=2*Math.pow(l,1/3);return[c*Math.cos(s/3)-t/3,c*Math.cos((s+2*Math.PI)/3)-t/3,c*Math.cos((s+4*Math.PI)/3)-t/3]},cg=(e,t)=>null!==t.closest(e),ug=(e,t)=>"string"==typeof e&&e.length>0?Object.assign({"ion-color":!0,[`ion-color-${e}`]:!0},t):t,dg=()=>lm.get("experimentalCloseWatcher",!1)&&void 0!==tm&&"CloseWatcher"in tm,fg=Object.freeze(Object.defineProperty({__proto__:null,MENU_BACK_BUTTON_PRIORITY:99,OVERLAY_BACK_BUTTON_PRIORITY:100,blockHardwareBackButton:()=>{document.addEventListener("backbutton",(()=>{}))},shouldUseCloseWatcher:dg,startHardwareBackButton:()=>{const e=document;let t=!1;const n=()=>{if(t)return;let n=0,o=[];const r=new CustomEvent("ionBackButton",{bubbles:!1,detail:{register(e,t){o.push({priority:e,handler:t,id:n++})}}});e.dispatchEvent(r);const i=()=>{if(o.length>0){let e={priority:Number.MIN_SAFE_INTEGER,handler:()=>{},id:-1};o.forEach((t=>{t.priority>=e.priority&&(e=t)})),t=!0,o=o.filter((t=>t.id!==e.id)),(async e=>{try{if(null==e?void 0:e.handler){const t=e.handler(i);null!=t&&await t}}catch(kf){console.error(kf)}})(e).then((()=>t=!1))}};i()};if(dg()){let e;const t=()=>{null==e||e.destroy(),e=new tm.CloseWatcher,e.onclose=()=>{n(),t()}};t()}else e.addEventListener("backbutton",n)}},Symbol.toStringTag,{value:"Module"})),pg=async(e,t,n,o,r,i)=>{var a;if(e)return e.attachViewToDom(t,n,r,o);if("string"!=typeof n&&!(n instanceof HTMLElement))throw new Error("framework delegate is missing");const l="string"==typeof n?null===(a=t.ownerDocument)||void 0===a?void 0:a.createElement(n):n;return o&&o.forEach((e=>l.classList.add(e))),r&&Object.assign(l,r),t.appendChild(l),await new Promise((e=>Tm(l,e))),l};function hg(e,t){var n={};for(var o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.indexOf(o)<0&&(n[o]=e[o]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(e);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(e,o[r])&&(n[o[r]]=e[o[r]])}return n}"function"==typeof SuppressedError&&SuppressedError;
/*!
             * (C) Ionic http://ionicframework.com - MIT License
             */
const mg=Xh(class extends Rp{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.type="bounded"}async addRipple(e,t){return new Promise((n=>{Bp((()=>{const o=this.el.getBoundingClientRect(),r=o.width,i=o.height,a=Math.sqrt(r*r+i*i),l=Math.max(i,r),s=this.unbounded?l:a+bg,c=Math.floor(l*vg),u=s/c;let d=e-o.left,f=t-o.top;this.unbounded&&(d=.5*r,f=.5*i);const p=d-.5*c,h=f-.5*c,m=.5*r-d,g=.5*i-f;Vp((()=>{const e=document.createElement("div");e.classList.add("ripple-effect");const t=e.style;t.top=h+"px",t.left=p+"px",t.width=t.height=c+"px",t.setProperty("--final-scale",`${u}`),t.setProperty("--translate-end",`${m}px, ${g}px`),(this.el.shadowRoot||this.el).appendChild(e),setTimeout((()=>{n((()=>{gg(e)}))}),325)}))}))}))}get unbounded(){return"unbounded"===this.type}render(){const e=Sm(this);return Zp(eh,{key:"7ae559bda5d2c1856a45bfa150c2cb4f83373f8e",role:"presentation",class:{[e]:!0,unbounded:this.unbounded}})}get el(){return this}static get style(){return":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:strict;pointer-events:none}:host(.unbounded){contain:layout size style}.ripple-effect{border-radius:50%;position:absolute;background-color:currentColor;color:inherit;contain:strict;opacity:0;-webkit-animation:225ms rippleAnimation forwards, 75ms fadeInAnimation forwards;animation:225ms rippleAnimation forwards, 75ms fadeInAnimation forwards;will-change:transform, opacity;pointer-events:none}.fade-out{-webkit-transform:translate(var(--translate-end)) scale(var(--final-scale, 1));transform:translate(var(--translate-end)) scale(var(--final-scale, 1));-webkit-animation:150ms fadeOutAnimation forwards;animation:150ms fadeOutAnimation forwards}@-webkit-keyframes rippleAnimation{from{-webkit-animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);-webkit-transform:scale(1);transform:scale(1)}to{-webkit-transform:translate(var(--translate-end)) scale(var(--final-scale, 1));transform:translate(var(--translate-end)) scale(var(--final-scale, 1))}}@keyframes rippleAnimation{from{-webkit-animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);animation-timing-function:cubic-bezier(0.4, 0, 0.2, 1);-webkit-transform:scale(1);transform:scale(1)}to{-webkit-transform:translate(var(--translate-end)) scale(var(--final-scale, 1));transform:translate(var(--translate-end)) scale(var(--final-scale, 1))}}@-webkit-keyframes fadeInAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0}to{opacity:0.16}}@keyframes fadeInAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0}to{opacity:0.16}}@-webkit-keyframes fadeOutAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0.16}to{opacity:0}}@keyframes fadeOutAnimation{from{-webkit-animation-timing-function:linear;animation-timing-function:linear;opacity:0.16}to{opacity:0}}"}},[1,"ion-ripple-effect",{type:[1],addRipple:[64]}]),gg=e=>{e.classList.add("fade-out"),setTimeout((()=>{e.remove()}),200)},bg=10,vg=.5,yg="undefined"!=typeof window?window:void 0;yg&&yg.CSS&&yg.CSS.supports&&yg.CSS.supports("--a: 0");
/*!
             * (C) Ionic http://ionicframework.com - MIT License
             */
const wg=e("i",(e=>e&&""!==e.dir?"rtl"===e.dir.toLowerCase():"rtl"===(null===document||void 0===document?void 0:document.dir.toLowerCase()))),xg=Xh(class extends Rp{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionScrollStart=rh(this,"ionScrollStart",7),this.ionScroll=rh(this,"ionScroll",7),this.ionScrollEnd=rh(this,"ionScrollEnd",7),this.watchDog=null,this.isScrolling=!1,this.lastScroll=0,this.queued=!1,this.cTop=-1,this.cBottom=-1,this.isMainContent=!0,this.resizeTimeout=null,this.inheritedAttributes={},this.tabsElement=null,this.detail={scrollTop:0,scrollLeft:0,type:"scroll",event:void 0,startX:0,startY:0,startTime:0,currentX:0,currentY:0,velocityX:0,velocityY:0,deltaX:0,deltaY:0,currentTime:0,data:void 0,isScrolling:!0},this.color=void 0,this.fullscreen=!1,this.fixedSlotPlacement="after",this.forceOverscroll=void 0,this.scrollX=!1,this.scrollY=!0,this.scrollEvents=!1}componentWillLoad(){this.inheritedAttributes=Om(this.el)}connectedCallback(){if(this.isMainContent=null===this.el.closest("ion-menu, ion-popover, ion-modal"),Pm(this.el)){const e=this.tabsElement=this.el.closest("ion-tabs");null!==e&&(this.tabsLoadCallback=()=>this.resize(),e.addEventListener("ionTabBarLoaded",this.tabsLoadCallback))}}disconnectedCallback(){if(this.onScrollEnd(),Pm(this.el)){const{tabsElement:e,tabsLoadCallback:t}=this;null!==e&&void 0!==t&&e.removeEventListener("ionTabBarLoaded",t),this.tabsElement=null,this.tabsLoadCallback=void 0}}onResize(){this.resizeTimeout&&(clearTimeout(this.resizeTimeout),this.resizeTimeout=null),this.resizeTimeout=setTimeout((()=>{null!==this.el.offsetParent&&this.resize()}),100)}shouldForceOverscroll(){const{forceOverscroll:e}=this,t=Sm(this);return void 0===e?"ios"===t&&dm("ios"):e}resize(){this.fullscreen?Bp((()=>this.readDimensions())):0===this.cTop&&0===this.cBottom||(this.cTop=this.cBottom=0,Wh(this))}readDimensions(){const e=kg(this.el),t=Math.max(this.el.offsetTop,0),n=Math.max(e.offsetHeight-t-this.el.offsetHeight,0);(t!==this.cTop||n!==this.cBottom)&&(this.cTop=t,this.cBottom=n,Wh(this))}onScroll(e){const t=Date.now(),n=!this.isScrolling;this.lastScroll=t,n&&this.onScrollStart(),!this.queued&&this.scrollEvents&&(this.queued=!0,Bp((t=>{this.queued=!1,this.detail.event=e,Eg(this.detail,this.scrollEl,t,n),this.ionScroll.emit(this.detail)})))}async getScrollElement(){return this.scrollEl||await new Promise((e=>Tm(this.el,e))),Promise.resolve(this.scrollEl)}async getBackgroundElement(){return this.backgroundContentEl||await new Promise((e=>Tm(this.el,e))),Promise.resolve(this.backgroundContentEl)}scrollToTop(e=0){return this.scrollToPoint(void 0,0,e)}async scrollToBottom(e=0){const t=await this.getScrollElement(),n=t.scrollHeight-t.clientHeight;return this.scrollToPoint(void 0,n,e)}async scrollByPoint(e,t,n){const o=await this.getScrollElement();return this.scrollToPoint(e+o.scrollLeft,t+o.scrollTop,n)}async scrollToPoint(e,t,n=0){const o=await this.getScrollElement();if(n<32)return null!=t&&(o.scrollTop=t),void(null!=e&&(o.scrollLeft=e));let r,i=0;const a=new Promise((e=>r=e)),l=o.scrollTop,s=o.scrollLeft,c=null!=t?t-l:0,u=null!=e?e-s:0,d=e=>{const t=Math.min(1,(e-i)/n)-1,a=Math.pow(t,3)+1;0!==c&&(o.scrollTop=Math.floor(a*c+l)),0!==u&&(o.scrollLeft=Math.floor(a*u+s)),a<1?requestAnimationFrame(d):r()};return requestAnimationFrame((e=>{i=e,d(e)})),a}onScrollStart(){this.isScrolling=!0,this.ionScrollStart.emit({isScrolling:!0}),this.watchDog&&clearInterval(this.watchDog),this.watchDog=setInterval((()=>{this.lastScroll<Date.now()-120&&this.onScrollEnd()}),100)}onScrollEnd(){this.watchDog&&clearInterval(this.watchDog),this.watchDog=null,this.isScrolling&&(this.isScrolling=!1,this.ionScrollEnd.emit({isScrolling:!1}))}render(){const{fixedSlotPlacement:e,inheritedAttributes:t,isMainContent:n,scrollX:o,scrollY:r,el:i}=this,a=wg(i)?"rtl":"ltr",l=Sm(this),s=this.shouldForceOverscroll(),c="ios"===l;return this.resize(),Zp(eh,Object.assign({key:"f2a24aa66dbf5c76f9d4b06f708eb73cadc239df",role:n?"main":void 0,class:ug(this.color,{[l]:!0,"content-sizing":cg("ion-popover",this.el),overscroll:s,[`content-${a}`]:!0}),style:{"--offset-top":`${this.cTop}px`,"--offset-bottom":`${this.cBottom}px`}},t),Zp("div",{key:"6480ca7648b278abb36477b3838bccbcd4995e2a",ref:e=>this.backgroundContentEl=e,id:"background-content",part:"background"}),"before"===e?Zp("slot",{name:"fixed"}):null,Zp("div",{key:"29a23b663f5f0215bb000820c01e1814c0d55985",class:{"inner-scroll":!0,"scroll-x":o,"scroll-y":r,overscroll:(o||r)&&s},ref:e=>this.scrollEl=e,onScroll:this.scrollEvents?e=>this.onScroll(e):void 0,part:"scroll"},Zp("slot",{key:"0fe1bd05609a4b88ae2ce9addf5d5dc5dc1806f0"})),c?Zp("div",{class:"transition-effect"},Zp("div",{class:"transition-cover"}),Zp("div",{class:"transition-shadow"})):null,"after"===e?Zp("slot",{name:"fixed"}):null)}get el(){return this}static get style(){return':host{--background:var(--ion-background-color, #fff);--color:var(--ion-text-color, #000);--padding-top:0px;--padding-bottom:0px;--padding-start:0px;--padding-end:0px;--keyboard-offset:0px;--offset-top:0px;--offset-bottom:0px;--overflow:auto;display:block;position:relative;-ms-flex:1;flex:1;width:100%;height:100%;margin:0 !important;padding:0 !important;font-family:var(--ion-font-family, inherit);contain:size style}:host(.ion-color) .inner-scroll{background:var(--ion-color-base);color:var(--ion-color-contrast)}#background-content{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);position:absolute;background:var(--background)}.inner-scroll{left:0px;right:0px;top:calc(var(--offset-top) * -1);bottom:calc(var(--offset-bottom) * -1);-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:calc(var(--padding-top) + var(--offset-top));padding-bottom:calc(var(--padding-bottom) + var(--keyboard-offset) + var(--offset-bottom));position:absolute;color:var(--color);-webkit-box-sizing:border-box;box-sizing:border-box;overflow:hidden;-ms-touch-action:pan-x pan-y pinch-zoom;touch-action:pan-x pan-y pinch-zoom}.scroll-y,.scroll-x{-webkit-overflow-scrolling:touch;z-index:0;will-change:scroll-position}.scroll-y{overflow-y:var(--overflow);overscroll-behavior-y:contain}.scroll-x{overflow-x:var(--overflow);overscroll-behavior-x:contain}.overscroll::before,.overscroll::after{position:absolute;width:1px;height:1px;content:""}.overscroll::before{bottom:-1px}.overscroll::after{top:-1px}:host(.content-sizing){display:-ms-flexbox;display:flex;-ms-flex-direction:column;flex-direction:column;min-height:0;contain:none}:host(.content-sizing) .inner-scroll{position:relative;top:0;bottom:0;margin-top:calc(var(--offset-top) * -1);margin-bottom:calc(var(--offset-bottom) * -1)}.transition-effect{display:none;position:absolute;width:100%;height:100vh;opacity:0;pointer-events:none}:host(.content-ltr) .transition-effect{left:-100%;}:host(.content-rtl) .transition-effect{right:-100%;}.transition-cover{position:absolute;right:0;width:100%;height:100%;background:black;opacity:0.1}.transition-shadow{display:block;position:absolute;width:100%;height:100%;-webkit-box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03);box-shadow:inset -9px 0 9px 0 rgba(0, 0, 100, 0.03)}:host(.content-ltr) .transition-shadow{right:0;}:host(.content-rtl) .transition-shadow{left:0;-webkit-transform:scaleX(-1);transform:scaleX(-1)}::slotted([slot=fixed]){position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0)}'}},[1,"ion-content",{color:[513],fullscreen:[4],fixedSlotPlacement:[1,"fixed-slot-placement"],forceOverscroll:[1028,"force-overscroll"],scrollX:[4,"scroll-x"],scrollY:[4,"scroll-y"],scrollEvents:[4,"scroll-events"],getScrollElement:[64],getBackgroundElement:[64],scrollToTop:[64],scrollToBottom:[64],scrollByPoint:[64],scrollToPoint:[64]},[[9,"resize","onResize"]]]),kg=e=>{const t=e.closest("ion-tabs");if(t)return t;const n=e.closest("ion-app, ion-page, .ion-page, page-inner, .popover-content");return n||(e=>{var t;return e.parentElement?e.parentElement:(null===(t=e.parentNode)||void 0===t?void 0:t.host)?e.parentNode.host:null})(e)},Eg=(e,t,n,o)=>{const r=e.currentX,i=e.currentY,a=e.currentTime,l=t.scrollLeft,s=t.scrollTop,c=n-a;if(o&&(e.startTime=n,e.startX=l,e.startY=s,e.velocityX=e.velocityY=0),e.currentTime=n,e.currentX=e.scrollLeft=l,e.currentY=e.scrollTop=s,e.deltaX=l-e.startX,e.deltaY=s-e.startY,c>0&&c<100){const t=(l-r)/c,n=(s-i)/c;e.velocityX=.7*t+.3*e.velocityX,e.velocityY=.7*n+.3*e.velocityY}},Sg=function(){"undefined"!=typeof customElements&&["ion-content"].forEach((e=>{"ion-content"===e&&(customElements.get(e)||customElements.define(e,xg))}))}
/*!
             * (C) Ionic http://ionicframework.com - MIT License
             */,Cg="ion-content",$g=".ion-content-scroll-host",Tg=`${Cg}, ${$g}`,Pg=e=>"ION-CONTENT"===e.tagName,Rg=e("k",(async e=>Pg(e)?(await new Promise((t=>Tm(e,t))),e.getScrollElement()):e)),Ig=e=>{const t=e.querySelector($g);return t||e.querySelector(Tg)},Og=(e("f",(e=>e.closest(Tg))),e("s",((e,t)=>Pg(e)?e.scrollToTop(t):Promise.resolve(e.scrollTo({top:0,left:0,behavior:"smooth"})))),e("l",((e,t,n,o)=>Pg(e)?e.scrollByPoint(t,n,o):Promise.resolve(e.scrollBy({top:n,left:t,behavior:o>0?"smooth":"auto"})))),e=>((e,...t)=>console.error(`<${e.tagName.toLowerCase()}> must be used inside ${t.join(" or ")}.`))(e,Cg));
/*!
             * (C) Ionic http://ionicframework.com - MIT License
             */
/*!
             * (C) Ionic http://ionicframework.com - MIT License
             */
var Lg,_g;!function(e){e.Unimplemented="UNIMPLEMENTED",e.Unavailable="UNAVAILABLE"}(Lg||(Lg={})),e("g",_g),function(e){e.Body="body",e.Ionic="ionic",e.Native="native",e.None="none"}(_g||e("g",_g={}));const zg=e("K",{getEngine(){const e=(()=>{if(void 0!==tm)return tm.Capacitor})();if(null==e?void 0:e.isPluginAvailable("Keyboard"))return e.Plugins.Keyboard},getResizeMode(){const e=this.getEngine();return(null==e?void 0:e.getResizeMode)?e.getResizeMode().catch((e=>{if(e.code!==Lg.Unimplemented)throw e})):Promise.resolve(void 0)}}),Ng=e=>{if(void 0===nm||e===_g.None||void 0===e)return null;const t=nm.querySelector("ion-app");return null!=t?t:nm.body},Mg=e=>{const t=Ng(e);return null===t?0:t.clientHeight},jg=e=>{const t=document.querySelector(`${e}.ion-cloned-element`);if(null!==t)return t;const n=document.createElement(e);return n.classList.add("ion-cloned-element"),n.style.setProperty("display","none"),document.body.appendChild(n),n},Dg=e=>{if(!e)return;const t=e.querySelectorAll("ion-toolbar");return{el:e,toolbars:Array.from(t).map((e=>{const t=e.querySelector("ion-title");return{el:e,background:e.shadowRoot.querySelector(".toolbar-background"),ionTitleEl:t,innerTitleEl:t?t.shadowRoot.querySelector(".toolbar-title"):null,ionButtonsEl:Array.from(e.querySelectorAll("ion-buttons"))}}))}},Ag=(e,t)=>{"fade"!==e.collapse&&(void 0===t?e.style.removeProperty("--opacity-scale"):e.style.setProperty("--opacity-scale",t.toString()))},Bg=(e,t,n,o)=>{Vp((()=>{const r=o.scrollTop;((e,t,n)=>{if(!e[0].isIntersecting)return;const o=e[0].intersectionRatio>.9||n<=0?0:100*(1-e[0].intersectionRatio)/75;Ag(t.el,1===o?void 0:o)})(e,t,r);const i=e[0],a=i.intersectionRect,l=a.width*a.height,s=i.rootBounds.width*i.rootBounds.height,c=0===l&&0===s,u=Math.abs(a.left-i.boundingClientRect.left),d=Math.abs(a.right-i.boundingClientRect.right);c||l>0&&(u>=5||d>=5)||(i.isIntersecting?(Vg(t,!1),Vg(n)):(0===a.x&&0===a.y||0!==a.width&&0!==a.height)&&r>0&&(Vg(t),Vg(n,!1),Ag(t.el)))}))},Vg=(e,t=!0)=>{const n=e.el,o=e.toolbars.map((e=>e.ionTitleEl));t?(n.classList.remove("header-collapse-condense-inactive"),o.forEach((e=>{e&&e.removeAttribute("aria-hidden")}))):(n.classList.add("header-collapse-condense-inactive"),o.forEach((e=>{e&&e.setAttribute("aria-hidden","true")})))},Hg=(e=[],t=1,n=!1)=>{e.forEach((e=>{const o=e.ionTitleEl,r=e.innerTitleEl;o&&"large"===o.size&&(r.style.transition=n?"all 0.2s ease-in-out":"",r.style.transform=`scale3d(${t}, ${t}, 1)`)}))},Fg=(e,t,n)=>{Bp((()=>{const o=e.scrollTop,r=t.clientHeight,i=n?n.clientHeight:0;if(null!==n&&o<i)return t.style.setProperty("--opacity-scale","0"),void e.style.setProperty("clip-path",`inset(${r}px 0px 0px 0px)`);const a=_m(0,(o-i)/10,1);Vp((()=>{e.style.removeProperty("clip-path"),t.style.setProperty("--opacity-scale",a.toString())}))}))},Wg=Xh(class extends Rp{constructor(){super(),this.__registerHost(),this.inheritedAttributes={},this.setupFadeHeader=async(e,t)=>{const n=this.scrollEl=await Rg(e);this.contentScrollCallback=()=>{Fg(this.scrollEl,this.el,t)},n.addEventListener("scroll",this.contentScrollCallback),Fg(this.scrollEl,this.el,t)},this.collapse=void 0,this.translucent=!1}componentWillLoad(){this.inheritedAttributes=Om(this.el)}componentDidLoad(){this.checkCollapsibleHeader()}componentDidUpdate(){this.checkCollapsibleHeader()}disconnectedCallback(){this.destroyCollapsibleHeader()}async checkCollapsibleHeader(){if("ios"!==Sm(this))return;const{collapse:e}=this,t="condense"===e,n="fade"===e;if(this.destroyCollapsibleHeader(),t){const e=this.el.closest("ion-app,ion-page,.ion-page,page-inner"),t=e?Ig(e):null;Vp((()=>{jg("ion-title").size="large",jg("ion-back-button")})),await this.setupCondenseHeader(t,e)}else if(n){const e=this.el.closest("ion-app,ion-page,.ion-page,page-inner"),t=e?Ig(e):null;if(!t)return void Og(this.el);const n=t.querySelector('ion-header[collapse="condense"]');await this.setupFadeHeader(t,n)}}destroyCollapsibleHeader(){this.intersectionObserver&&(this.intersectionObserver.disconnect(),this.intersectionObserver=void 0),this.scrollEl&&this.contentScrollCallback&&(this.scrollEl.removeEventListener("scroll",this.contentScrollCallback),this.contentScrollCallback=void 0),this.collapsibleMainHeader&&(this.collapsibleMainHeader.classList.remove("header-collapse-main"),this.collapsibleMainHeader=void 0)}async setupCondenseHeader(e,t){if(!e||!t)return void Og(this.el);if("undefined"==typeof IntersectionObserver)return;this.scrollEl=await Rg(e);const n=t.querySelectorAll("ion-header");if(this.collapsibleMainHeader=Array.from(n).find((e=>"condense"!==e.collapse)),!this.collapsibleMainHeader)return;const o=Dg(this.collapsibleMainHeader),r=Dg(this.el);o&&r&&(Vg(o,!1),Ag(o.el,0),this.intersectionObserver=new IntersectionObserver((e=>{Bg(e,o,r,this.scrollEl)}),{root:e,threshold:[.25,.3,.4,.5,.6,.7,.8,.9,1]}),this.intersectionObserver.observe(r.toolbars[r.toolbars.length-1].el),this.contentScrollCallback=()=>{((e,t,n)=>{Bp((()=>{const o=e.scrollTop,r=_m(1,1+-o/500,1.1);null===n.querySelector("ion-refresher.refresher-native")&&Vp((()=>{Hg(t.toolbars,r)}))}))})(this.scrollEl,r,e)},this.scrollEl.addEventListener("scroll",this.contentScrollCallback),Vp((()=>{void 0!==this.collapsibleMainHeader&&this.collapsibleMainHeader.classList.add("header-collapse-main")})))}render(){const{translucent:e,inheritedAttributes:t}=this,n=Sm(this),o=this.collapse||"none",r=cg("ion-menu",this.el)?"none":"banner";return Zp(eh,Object.assign({key:"b6cc27f0b08afc9fcc889683525da765d80ba672",role:r,class:{[n]:!0,[`header-${n}`]:!0,"header-translucent":this.translucent,[`header-collapse-${o}`]:!0,[`header-translucent-${n}`]:this.translucent}},t),"ios"===n&&e&&Zp("div",{key:"395766d4dcee3398bc91960db21f922095292f14",class:"header-background"}),Zp("slot",{key:"09a67ece27b258ff1248805d43d92a49b2c6859a"}))}get el(){return this}static get style(){return{ios:"ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-ios ion-toolbar:last-of-type{--border-width:0 0 0.55px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){.header-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-backdrop-filter:saturate(180%) blur(20px);backdrop-filter:saturate(180%) blur(20px)}.header-translucent-ios ion-toolbar{--opacity:.8}.header-collapse-condense-inactive .header-background{-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px)}}.header-ios.ion-no-border ion-toolbar:last-of-type{--border-width:0}.header-collapse-fade ion-toolbar{--opacity-scale:inherit}.header-collapse-condense{z-index:9}.header-collapse-condense ion-toolbar{position:-webkit-sticky;position:sticky;top:0}.header-collapse-condense ion-toolbar:first-of-type{padding-top:0px;z-index:1}.header-collapse-condense ion-toolbar{--background:var(--ion-background-color, #fff);z-index:0}.header-collapse-condense ion-toolbar:last-of-type{--border-width:0px}.header-collapse-condense ion-toolbar ion-searchbar{padding-top:0px;padding-bottom:13px}.header-collapse-main{--opacity-scale:1}.header-collapse-main ion-toolbar{--opacity-scale:inherit}.header-collapse-main ion-toolbar.in-toolbar ion-title,.header-collapse-main ion-toolbar.in-toolbar ion-buttons{-webkit-transition:all 0.2s ease-in-out;transition:all 0.2s ease-in-out}.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive:not(.header-collapse-condense) ion-toolbar.in-toolbar ion-buttons.buttons-collapse{opacity:0;pointer-events:none}.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-title,.header-collapse-condense-inactive.header-collapse-condense ion-toolbar.in-toolbar ion-buttons.buttons-collapse{visibility:hidden}ion-header.header-ios:not(.header-collapse-main):has(~ion-content ion-header.header-ios[collapse=condense],~ion-content ion-header.header-ios.header-collapse-condense){opacity:0}",md:"ion-header{display:block;position:relative;-ms-flex-order:-1;order:-1;width:100%;z-index:10}ion-header ion-toolbar:first-of-type{padding-top:var(--ion-safe-area-top, 0)}.header-md{-webkit-box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12);box-shadow:0 2px 4px -1px rgba(0, 0, 0, 0.2), 0 4px 5px 0 rgba(0, 0, 0, 0.14), 0 1px 10px 0 rgba(0, 0, 0, 0.12)}.header-collapse-condense{display:none}.header-md.ion-no-border{-webkit-box-shadow:none;box-shadow:none}"}}},[36,"ion-header",{collapse:[1],translucent:[4]}]),Ug=function(){"undefined"!=typeof customElements&&["ion-header"].forEach((e=>{"ion-header"===e&&(customElements.get(e)||customElements.define(e,Wg))}))}
/*!
             * (C) Ionic http://ionicframework.com - MIT License
             */,qg=Xh(class extends Rp{constructor(){super(),this.__registerHost(),this.ionColor=rh(this,"ionColor",7),this.ionStyle=rh(this,"ionStyle",7),this.inRange=!1,this.color=void 0,this.position=void 0,this.noAnimate=!1}componentWillLoad(){this.inRange=!!this.el.closest("ion-range"),this.noAnimate="floating"===this.position,this.emitStyle(),this.emitColor()}componentDidLoad(){this.noAnimate&&setTimeout((()=>{this.noAnimate=!1}),1e3)}colorChanged(){this.emitColor()}positionChanged(){this.emitStyle()}emitColor(){const{color:e}=this;this.ionColor.emit({"item-label-color":void 0!==e,[`ion-color-${e}`]:void 0!==e})}emitStyle(){const{inRange:e,position:t}=this;e||this.ionStyle.emit({label:!0,[`label-${t}`]:void 0!==t})}render(){const e=this.position,t=Sm(this);return Zp(eh,{key:"6353a70565ef6fbbbf4042b000e536c61bcf99a9",class:ug(this.color,{[t]:!0,"in-item-color":cg("ion-item.ion-color",this.el),[`label-${e}`]:void 0!==e,"label-no-animate":this.noAnimate,"label-rtl":"rtl"===document.dir})},Zp("slot",{key:"6ef9c2758c0168442aa84941af0a6cec1ef1ec21"}))}get el(){return this}static get watchers(){return{color:["colorChanged"],position:["positionChanged"]}}static get style(){return{ios:".item.sc-ion-label-ios-h,.item .sc-ion-label-ios-h{--color:initial;display:block;color:var(--color);font-family:var(--ion-font-family, inherit);font-size:inherit;text-overflow:ellipsis;-webkit-box-sizing:border-box;box-sizing:border-box}.ion-color.sc-ion-label-ios-h{color:var(--ion-color-base)}.ion-text-nowrap.sc-ion-label-ios-h{overflow:hidden}.item-interactive-disabled.sc-ion-label-ios-h:not(.item-multiple-inputs),.item-interactive-disabled:not(.item-multiple-inputs) .sc-ion-label-ios-h{cursor:default;opacity:0.3;pointer-events:none}.item-input.sc-ion-label-ios-h,.item-input .sc-ion-label-ios-h{-ms-flex:initial;flex:initial;max-width:200px;pointer-events:none}.item-textarea.sc-ion-label-ios-h,.item-textarea .sc-ion-label-ios-h{-ms-flex-item-align:baseline;align-self:baseline}.item-skeleton-text.sc-ion-label-ios-h,.item-skeleton-text .sc-ion-label-ios-h{overflow:hidden}.label-fixed.sc-ion-label-ios-h{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.label-stacked.sc-ion-label-ios-h,.label-floating.sc-ion-label-ios-h{margin-bottom:0;-ms-flex-item-align:stretch;align-self:stretch;width:auto;max-width:100%}.label-no-animate.label-floating.sc-ion-label-ios-h{-webkit-transition:none;transition:none}.sc-ion-label-ios-s h1,.sc-ion-label-ios-s h2,.sc-ion-label-ios-s h3,.sc-ion-label-ios-s h4,.sc-ion-label-ios-s h5,.sc-ion-label-ios-s h6{text-overflow:inherit;overflow:inherit}.ion-text-wrap.sc-ion-label-ios-h{font-size:0.875rem;line-height:1.5}.label-stacked.sc-ion-label-ios-h{margin-bottom:4px;font-size:0.875rem}.label-floating.sc-ion-label-ios-h{margin-bottom:0;-webkit-transform:translate(0, 29px);transform:translate(0, 29px);-webkit-transform-origin:left top;transform-origin:left top;-webkit-transition:-webkit-transform 150ms ease-in-out;transition:-webkit-transform 150ms ease-in-out;transition:transform 150ms ease-in-out;transition:transform 150ms ease-in-out, -webkit-transform 150ms ease-in-out}[dir=rtl].sc-ion-label-ios-h -no-combinator.label-floating.sc-ion-label-ios-h,[dir=rtl] .sc-ion-label-ios-h -no-combinator.label-floating.sc-ion-label-ios-h,[dir=rtl].label-floating.sc-ion-label-ios-h,[dir=rtl] .label-floating.sc-ion-label-ios-h{-webkit-transform-origin:right top;transform-origin:right top}@supports selector(:dir(rtl)){.label-floating.sc-ion-label-ios-h:dir(rtl){-webkit-transform-origin:right top;transform-origin:right top}}.item-textarea.label-floating.sc-ion-label-ios-h,.item-textarea .label-floating.sc-ion-label-ios-h{-webkit-transform:translate(0, 28px);transform:translate(0, 28px)}.item-has-focus.label-floating.sc-ion-label-ios-h,.item-has-focus .label-floating.sc-ion-label-ios-h,.item-has-placeholder.sc-ion-label-ios-h:not(.item-input).label-floating,.item-has-placeholder:not(.item-input) .label-floating.sc-ion-label-ios-h,.item-has-value.label-floating.sc-ion-label-ios-h,.item-has-value .label-floating.sc-ion-label-ios-h{-webkit-transform:scale(0.82);transform:scale(0.82)}.sc-ion-label-ios-s h1{margin-left:0;margin-right:0;margin-top:3px;margin-bottom:2px;font-size:1.375rem;font-weight:normal}.sc-ion-label-ios-s h2{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:1.0625rem;font-weight:normal}.sc-ion-label-ios-s h3,.sc-ion-label-ios-s h4,.sc-ion-label-ios-s h5,.sc-ion-label-ios-s h6{margin-left:0;margin-right:0;margin-top:0;margin-bottom:3px;font-size:0.875rem;font-weight:normal;line-height:normal}.sc-ion-label-ios-s p{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:0.875rem;line-height:normal;text-overflow:inherit;overflow:inherit}.sc-ion-label-ios-s>p{color:var(--ion-color-step-400, var(--ion-text-color-step-600, #999999))}.sc-ion-label-ios-h.in-item-color.sc-ion-label-ios-s>p{color:inherit}.sc-ion-label-ios-s h2:last-child,.sc-ion-label-ios-s h3:last-child,.sc-ion-label-ios-s h4:last-child,.sc-ion-label-ios-s h5:last-child,.sc-ion-label-ios-s h6:last-child,.sc-ion-label-ios-s p:last-child{margin-bottom:0}",md:".item.sc-ion-label-md-h,.item .sc-ion-label-md-h{--color:initial;display:block;color:var(--color);font-family:var(--ion-font-family, inherit);font-size:inherit;text-overflow:ellipsis;-webkit-box-sizing:border-box;box-sizing:border-box}.ion-color.sc-ion-label-md-h{color:var(--ion-color-base)}.ion-text-nowrap.sc-ion-label-md-h{overflow:hidden}.item-interactive-disabled.sc-ion-label-md-h:not(.item-multiple-inputs),.item-interactive-disabled:not(.item-multiple-inputs) .sc-ion-label-md-h{cursor:default;opacity:0.3;pointer-events:none}.item-input.sc-ion-label-md-h,.item-input .sc-ion-label-md-h{-ms-flex:initial;flex:initial;max-width:200px;pointer-events:none}.item-textarea.sc-ion-label-md-h,.item-textarea .sc-ion-label-md-h{-ms-flex-item-align:baseline;align-self:baseline}.item-skeleton-text.sc-ion-label-md-h,.item-skeleton-text .sc-ion-label-md-h{overflow:hidden}.label-fixed.sc-ion-label-md-h{-ms-flex:0 0 100px;flex:0 0 100px;width:100px;min-width:100px;max-width:200px}.label-stacked.sc-ion-label-md-h,.label-floating.sc-ion-label-md-h{margin-bottom:0;-ms-flex-item-align:stretch;align-self:stretch;width:auto;max-width:100%}.label-no-animate.label-floating.sc-ion-label-md-h{-webkit-transition:none;transition:none}.sc-ion-label-md-s h1,.sc-ion-label-md-s h2,.sc-ion-label-md-s h3,.sc-ion-label-md-s h4,.sc-ion-label-md-s h5,.sc-ion-label-md-s h6{text-overflow:inherit;overflow:inherit}.ion-text-wrap.sc-ion-label-md-h{line-height:1.5}.label-stacked.sc-ion-label-md-h,.label-floating.sc-ion-label-md-h{margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-transform-origin:top left;transform-origin:top left}.label-stacked.label-rtl.sc-ion-label-md-h,.label-floating.label-rtl.sc-ion-label-md-h{-webkit-transform-origin:top right;transform-origin:top right}.label-stacked.sc-ion-label-md-h{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75);-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1)}.label-floating.sc-ion-label-md-h{-webkit-transform:translateY(96%);transform:translateY(96%);-webkit-transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1);transition:color 150ms cubic-bezier(0.4, 0, 0.2, 1), transform 150ms cubic-bezier(0.4, 0, 0.2, 1), -webkit-transform 150ms cubic-bezier(0.4, 0, 0.2, 1)}.ion-focused.label-floating.sc-ion-label-md-h,.ion-focused .label-floating.sc-ion-label-md-h,.item-has-focus.label-floating.sc-ion-label-md-h,.item-has-focus .label-floating.sc-ion-label-md-h,.item-has-placeholder.sc-ion-label-md-h:not(.item-input).label-floating,.item-has-placeholder:not(.item-input) .label-floating.sc-ion-label-md-h,.item-has-value.label-floating.sc-ion-label-md-h,.item-has-value .label-floating.sc-ion-label-md-h{-webkit-transform:translateY(50%) scale(0.75);transform:translateY(50%) scale(0.75)}.ion-focused.label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused .label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused.label-floating.sc-ion-label-md-h:not(.ion-color),.ion-focused .label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus.label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus .label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus.label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus .label-floating.sc-ion-label-md-h:not(.ion-color){color:var(--ion-color-primary, #0054e9)}.ion-focused.ion-color.label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused.ion-color .label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-focused.ion-color.label-floating.sc-ion-label-md-h:not(.ion-color),.ion-focused.ion-color .label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color.label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color .label-stacked.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color.label-floating.sc-ion-label-md-h:not(.ion-color),.item-has-focus.ion-color .label-floating.sc-ion-label-md-h:not(.ion-color){color:var(--ion-color-contrast)}.ion-invalid.ion-touched.label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-invalid.ion-touched .label-stacked.sc-ion-label-md-h:not(.ion-color),.ion-invalid.ion-touched.label-floating.sc-ion-label-md-h:not(.ion-color),.ion-invalid.ion-touched .label-floating.sc-ion-label-md-h:not(.ion-color){color:var(--highlight-color-invalid)}.sc-ion-label-md-s h1{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:1.5rem;font-weight:normal}.sc-ion-label-md-s h2{margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px;font-size:1rem;font-weight:normal}.sc-ion-label-md-s h3,.sc-ion-label-md-s h4,.sc-ion-label-md-s h5,.sc-ion-label-md-s h6{margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px;font-size:0.875rem;font-weight:normal;line-height:normal}.sc-ion-label-md-s p{margin-left:0;margin-right:0;margin-top:0;margin-bottom:2px;font-size:0.875rem;line-height:1.25rem;text-overflow:inherit;overflow:inherit}.sc-ion-label-md-s>p{color:var(--ion-color-step-600, var(--ion-text-color-step-400, #666666))}.sc-ion-label-md-h.in-item-color.sc-ion-label-md-s>p{color:inherit}"}}},[38,"ion-label",{color:[513],position:[1],noAnimate:[32]},void 0,{color:["colorChanged"],position:["positionChanged"]}]),Qg=function(){"undefined"!=typeof customElements&&["ion-label"].forEach((e=>{"ion-label"===e&&(customElements.get(e)||customElements.define(e,qg))}))}
/*!
             * (C) Ionic http://ionicframework.com - MIT License
             */;
/*!
             * (C) Ionic http://ionicframework.com - MIT License
             */class Kg{constructor(e,t){this.component=e,this.params=t,this.state=1}async init(e){if(this.state=2,!this.element){const t=this.component;this.element=await pg(this.delegate,e,t,["ion-page","ion-page-invisible"],this.params)}}_destroy(){zm(3!==this.state,"view state must be ATTACHED");const e=this.element;e&&(this.delegate?this.delegate.removeViewFromDom(e.parentElement,e):e.remove()),this.nav=void 0,this.state=3}}const Yg=(e,t,n)=>!!e&&e.component===t&&Nm(e.params,n),Xg=(e,t)=>e?e instanceof Kg?e:new Kg(e,t):null,Gg=Xh(class extends Rp{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionNavWillLoad=rh(this,"ionNavWillLoad",7),this.ionNavWillChange=rh(this,"ionNavWillChange",3),this.ionNavDidChange=rh(this,"ionNavDidChange",3),this.transInstr=[],this.gestureOrAnimationInProgress=!1,this.useRouter=!1,this.isTransitioning=!1,this.destroyed=!1,this.views=[],this.didLoad=!1,this.delegate=void 0,this.swipeGesture=void 0,this.animated=!0,this.animation=void 0,this.rootParams=void 0,this.root=void 0}swipeGestureChanged(){this.gesture&&this.gesture.enable(!0===this.swipeGesture)}rootChanged(){void 0!==this.root&&!1!==this.didLoad&&(this.useRouter||void 0!==this.root&&this.setRoot(this.root,this.rootParams))}componentWillLoad(){if(this.useRouter=null!==document.querySelector("ion-router")&&null===this.el.closest("[no-router]"),void 0===this.swipeGesture){const e=Sm(this);this.swipeGesture=lm.getBoolean("swipeBackEnabled","ios"===e)}this.ionNavWillLoad.emit()}async componentDidLoad(){this.didLoad=!0,this.rootChanged(),this.gesture=(await gp((()=>t.import("./swipe-back-legacy-CdR4zaeO.js")))).createSwipeBackGesture(this.el,this.canStart.bind(this),this.onStart.bind(this),this.onMove.bind(this),this.onEnd.bind(this)),this.swipeGestureChanged()}connectedCallback(){this.destroyed=!1}disconnectedCallback(){for(const e of this.views)tg(e.element,Dm),e._destroy();this.gesture&&(this.gesture.destroy(),this.gesture=void 0),this.transInstr.length=0,this.views.length=0,this.destroyed=!0}push(e,t,n,o){return this.insert(-1,e,t,n,o)}insert(e,t,n,o,r){return this.insertPages(e,[{component:t,componentProps:n}],o,r)}insertPages(e,t,n,o){return this.queueTrns({insertStart:e,insertViews:t,opts:n},o)}pop(e,t){return this.removeIndex(-1,1,e,t)}popTo(e,t,n){const o={removeStart:-1,removeCount:-1,opts:t};return"object"==typeof e&&e.component?(o.removeView=e,o.removeStart=1):"number"==typeof e&&(o.removeStart=e+1),this.queueTrns(o,n)}popToRoot(e,t){return this.removeIndex(1,-1,e,t)}removeIndex(e,t=1,n,o){return this.queueTrns({removeStart:e,removeCount:t,opts:n},o)}setRoot(e,t,n,o){return this.setPages([{component:e,componentProps:t}],n,o)}setPages(e,t,n){return null!=t||(t={}),!0!==t.animated&&(t.animated=!1),this.queueTrns({insertStart:0,insertViews:e,removeStart:0,removeCount:-1,opts:t},n)}setRouteId(e,t,n,o){const r=this.getActiveSync();if(Yg(r,e,t))return Promise.resolve({changed:!1,element:r.element});let i;const a=new Promise((e=>i=e));let l;const s={updateURL:!1,viewIsReady:e=>{let t;const n=new Promise((e=>t=e));return i({changed:!0,element:e,markVisible:async()=>{t(),await l}}),n}};if("root"===n)l=this.setRoot(e,t,s);else{const r=this.views.find((n=>Yg(n,e,t)));r?l=this.popTo(r,Object.assign(Object.assign({},s),{direction:"back",animationBuilder:o})):"forward"===n?l=this.push(e,t,Object.assign(Object.assign({},s),{animationBuilder:o})):"back"===n&&(l=this.setRoot(e,t,Object.assign(Object.assign({},s),{direction:"back",animated:!0,animationBuilder:o})))}return a}async getRouteId(){const e=this.getActiveSync();if(e)return{id:e.element.tagName,params:e.params,element:e.element}}async getActive(){return this.getActiveSync()}async getByIndex(e){return this.views[e]}async canGoBack(e){return this.canGoBackSync(e)}async getPrevious(e){return this.getPreviousSync(e)}async getLength(){return Promise.resolve(this.views.length)}getActiveSync(){return this.views[this.views.length-1]}canGoBackSync(e=this.getActiveSync()){return!(!e||!this.getPreviousSync(e))}getPreviousSync(e=this.getActiveSync()){if(!e)return;const t=this.views,n=t.indexOf(e);return n>0?t[n-1]:void 0}async queueTrns(e,t){var n,o;if(this.isTransitioning&&(null===(n=e.opts)||void 0===n?void 0:n.skipIfBusy))return!1;const r=new Promise(((t,n)=>{e.resolve=t,e.reject=n}));if(e.done=t,e.opts&&!1!==e.opts.updateURL&&this.useRouter){const t=document.querySelector("ion-router");if(t){const n=await t.canTransition();if(!1===n)return!1;if("string"==typeof n)return t.push(n,e.opts.direction||"back"),!1}}return 0===(null===(o=e.insertViews)||void 0===o?void 0:o.length)&&(e.insertViews=void 0),this.transInstr.push(e),this.nextTrns(),r}success(e,t){if(this.destroyed)this.fireError("nav controller was destroyed",t);else if(t.done&&t.done(e.hasCompleted,e.requiresTransition,e.enteringView,e.leavingView,e.direction),t.resolve(e.hasCompleted),!1!==t.opts.updateURL&&this.useRouter){const t=document.querySelector("ion-router");if(t){const n="back"===e.direction?"back":"forward";t.navChanged(n)}}}failed(e,t){this.destroyed?this.fireError("nav controller was destroyed",t):(this.transInstr.length=0,this.fireError(e,t))}fireError(e,t){t.done&&t.done(!1,!1,e),t.reject&&!this.destroyed?t.reject(e):t.resolve(!1)}nextTrns(){if(this.isTransitioning)return!1;const e=this.transInstr.shift();return!!e&&(this.runTransition(e),!0)}async runTransition(e){try{this.ionNavWillChange.emit(),this.isTransitioning=!0,this.prepareTI(e);const t=this.getActiveSync(),n=this.getEnteringView(e,t);if(!t&&!n)throw new Error("no views in the stack to be removed");n&&1===n.state&&await n.init(this.el),this.postViewInit(n,t,e);const o=(e.enteringRequiresTransition||e.leavingRequiresTransition)&&n!==t;let r;o&&e.opts&&t&&("back"===e.opts.direction&&(e.opts.animationBuilder=e.opts.animationBuilder||(null==n?void 0:n.animationBuilder)),t.animationBuilder=e.opts.animationBuilder),r=o?await this.transition(n,t,e):{hasCompleted:!0,requiresTransition:!1},this.success(r,e),this.ionNavDidChange.emit()}catch(t){this.failed(t,e)}this.isTransitioning=!1,this.nextTrns()}prepareTI(e){var t,n,o;const r=this.views.length;if(null!==(t=e.opts)&&void 0!==t||(e.opts={}),null!==(n=(o=e.opts).delegate)&&void 0!==n||(o.delegate=this.delegate),void 0!==e.removeView){zm(void 0!==e.removeStart,"removeView needs removeStart"),zm(void 0!==e.removeCount,"removeView needs removeCount");const t=this.views.indexOf(e.removeView);if(t<0)throw new Error("removeView was not found");e.removeStart+=t}void 0!==e.removeStart&&(e.removeStart<0&&(e.removeStart=r-1),e.removeCount<0&&(e.removeCount=r-e.removeStart),e.leavingRequiresTransition=e.removeCount>0&&e.removeStart+e.removeCount===r),e.insertViews&&((e.insertStart<0||e.insertStart>r)&&(e.insertStart=r),e.enteringRequiresTransition=e.insertStart===r);const i=e.insertViews;if(!i)return;zm(i.length>0,"length can not be zero");const a=i.map((e=>e instanceof Kg?e:"component"in e?Xg(e.component,null===e.componentProps?void 0:e.componentProps):Xg(e,void 0))).filter((e=>null!==e));if(0===a.length)throw new Error("invalid views to insert");for(const l of a){l.delegate=e.opts.delegate;const t=l.nav;if(t&&t!==this)throw new Error("inserted view was already inserted");if(3===l.state)throw new Error("inserted view was already destroyed")}e.insertViews=a}getEnteringView(e,t){const n=e.insertViews;if(void 0!==n)return n[n.length-1];const o=e.removeStart;if(void 0!==o){const n=this.views,r=o+e.removeCount;for(let e=n.length-1;e>=0;e--){const i=n[e];if((e<o||e>=r)&&i!==t)return i}}}postViewInit(e,t,n){var o,r,i;zm(t||e,"Both leavingView and enteringView are null"),zm(n.resolve,"resolve must be valid"),zm(n.reject,"reject must be valid");const a=n.opts,{insertViews:l,removeStart:s,removeCount:c}=n;let u;if(void 0!==s&&void 0!==c){zm(s>=0,"removeStart can not be negative"),zm(c>=0,"removeCount can not be negative"),u=[];for(let n=s;n<s+c;n++){const o=this.views[n];void 0!==o&&o!==e&&o!==t&&u.push(o)}null!==(o=a.direction)&&void 0!==o||(a.direction="back")}const d=this.views.length+(null!==(r=null==l?void 0:l.length)&&void 0!==r?r:0)-(null!=c?c:0);if(zm(d>=0,"final balance can not be negative"),0===d)throw console.warn("You can't remove all the pages in the navigation stack. nav.pop() is probably called too many times.",this,this.el),new Error("navigation stack needs at least one root page");if(l){let e=n.insertStart;for(const t of l)this.insertViewAt(t,e),e++;n.enteringRequiresTransition&&(null!==(i=a.direction)&&void 0!==i||(a.direction="forward"))}if(u&&u.length>0){for(const e of u)tg(e.element,Mm),tg(e.element,jm),tg(e.element,Dm);for(const e of u)this.destroyView(e)}}async transition(e,t,n){const o=n.opts,r=o.progressAnimation?e=>{void 0===e||this.gestureOrAnimationInProgress?this.sbAni=e:(this.gestureOrAnimationInProgress=!0,e.onFinish((()=>{this.gestureOrAnimationInProgress=!1}),{oneTimeCallback:!0}),e.progressEnd(0,0,0))}:void 0,i=Sm(this),a=e.element,l=t&&t.element,s=Object.assign(Object.assign({mode:i,showGoBack:this.canGoBackSync(e),baseEl:this.el,progressCallback:r,animated:this.animated&&lm.getBoolean("animated",!0),enteringEl:a,leavingEl:l},o),{animationBuilder:o.animationBuilder||this.animation||lm.get("navAnimation")}),{hasCompleted:c}=await Fm(s);return this.transitionFinish(c,e,t,o)}transitionFinish(e,t,n,o){const r=e?t:n;return r&&this.unmountInactiveViews(r),{hasCompleted:e,requiresTransition:!0,enteringView:t,leavingView:n,direction:o.direction}}insertViewAt(e,t){const n=this.views,o=n.indexOf(e);o>-1?(zm(e.nav===this,"view is not part of the nav"),n.splice(o,1),n.splice(t,0,e)):(zm(!e.nav,"nav is used"),e.nav=this,n.splice(t,0,e))}removeView(e){zm(2===e.state||3===e.state,"view state should be loaded or destroyed");const t=this.views,n=t.indexOf(e);zm(n>-1,"view must be part of the stack"),n>=0&&t.splice(n,1)}destroyView(e){e._destroy(),this.removeView(e)}unmountInactiveViews(e){if(this.destroyed)return;const t=this.views,n=t.indexOf(e);for(let o=t.length-1;o>=0;o--){const e=t[o],r=e.element;r&&(o>n?(tg(r,Dm),this.destroyView(e)):o<n&&og(r,!0))}}canStart(){return!this.gestureOrAnimationInProgress&&!!this.swipeGesture&&!this.isTransitioning&&0===this.transInstr.length&&this.canGoBackSync()}onStart(){this.gestureOrAnimationInProgress=!0,this.pop({direction:"back",progressAnimation:!0})}onMove(e){this.sbAni&&this.sbAni.progressStep(e)}onEnd(e,t,n){if(this.sbAni){this.sbAni.onFinish((()=>{this.gestureOrAnimationInProgress=!1}),{oneTimeCallback:!0});let o=e?-.001:.001;e?o+=ig([0,0],[.32,.72],[0,1],[1,1],t)[0]:(this.sbAni.easing("cubic-bezier(1, 0, 0.68, 0.28)"),o+=ig([0,0],[1,0],[.68,.28],[1,1],t)[0]),this.sbAni.progressEnd(e?1:0,o,n)}else this.gestureOrAnimationInProgress=!1}render(){return Zp("slot",{key:"dfe98cb6604a2015a49f41beffebdd2da957dfff"})}get el(){return this}static get watchers(){return{swipeGesture:["swipeGestureChanged"],root:["rootChanged"]}}static get style(){return":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;z-index:0}"}},[1,"ion-nav",{delegate:[16],swipeGesture:[1028,"swipe-gesture"],animated:[4],animation:[16],rootParams:[16],root:[1],push:[64],insert:[64],insertPages:[64],pop:[64],popTo:[64],popToRoot:[64],removeIndex:[64],setRoot:[64],setPages:[64],setRouteId:[64],getRouteId:[64],getActive:[64],getByIndex:[64],canGoBack:[64],getPrevious:[64],getLength:[64]},void 0,{swipeGesture:["swipeGestureChanged"],root:["rootChanged"]}]),Zg=function(){"undefined"!=typeof customElements&&["ion-nav"].forEach((e=>{"ion-nav"===e&&(customElements.get(e)||customElements.define(e,Gg))}))},Jg=Xh(class extends Rp{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionStyle=rh(this,"ionStyle",7),this.color=void 0,this.size=void 0}sizeChanged(){this.emitStyle()}connectedCallback(){this.emitStyle()}emitStyle(){const e=this.getSize();this.ionStyle.emit({[`title-${e}`]:!0})}getSize(){return void 0!==this.size?this.size:"default"}render(){const e=Sm(this),t=this.getSize();return Zp(eh,{key:"7293d2ecd6262feb0d8d769effbb208230baed89",class:ug(this.color,{[e]:!0,[`title-${t}`]:!0,"title-rtl":"rtl"===document.dir})},Zp("div",{key:"086ec3a361ebdf6506846a8704b457cda3a6f897",class:"toolbar-title"},Zp("slot",{key:"59add7eb92b82d6832a8f0894f897c51fdf4f214"})))}get el(){return this}static get watchers(){return{size:["sizeChanged"]}}static get style(){return{ios:":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{top:0;-webkit-padding-start:90px;padding-inline-start:90px;-webkit-padding-end:90px;padding-inline-end:90px;padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);position:absolute;width:100%;height:100%;-webkit-transform:translateZ(0);transform:translateZ(0);font-size:min(1.0625rem, 20.4px);font-weight:600;text-align:center;-webkit-box-sizing:border-box;box-sizing:border-box;pointer-events:none}:host{inset-inline-start:0}:host(.title-small){-webkit-padding-start:9px;padding-inline-start:9px;-webkit-padding-end:9px;padding-inline-end:9px;padding-top:6px;padding-bottom:16px;position:relative;font-size:min(0.8125rem, 23.4px);font-weight:normal}:host(.title-large){-webkit-padding-start:12px;padding-inline-start:12px;-webkit-padding-end:12px;padding-inline-end:12px;padding-top:2px;padding-bottom:4px;-webkit-transform-origin:left center;transform-origin:left center;position:static;-ms-flex-align:end;align-items:flex-end;min-width:100%;font-size:min(2.125rem, 61.2px);font-weight:700;text-align:start}:host(.title-large.title-rtl){-webkit-transform-origin:right center;transform-origin:right center}:host(.title-large.ion-cloned-element){--color:var(--ion-text-color, #000);font-family:var(--ion-font-family)}:host(.title-large) .toolbar-title{-webkit-transform-origin:inherit;transform-origin:inherit;width:auto}:host-context([dir=rtl]):host(.title-large) .toolbar-title,:host-context([dir=rtl]).title-large .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}@supports selector(:dir(rtl)){:host(.title-large:dir(rtl)) .toolbar-title{-webkit-transform-origin:calc(100% - inherit);transform-origin:calc(100% - inherit)}}",md:":host{--color:initial;display:-ms-flexbox;display:flex;-ms-flex:1;flex:1;-ms-flex-align:center;align-items:center;-webkit-transform:translateZ(0);transform:translateZ(0);color:var(--color)}:host(.ion-color){color:var(--ion-color-base)}.toolbar-title{display:block;width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;pointer-events:auto}:host(.title-small) .toolbar-title{white-space:normal}:host{-webkit-padding-start:20px;padding-inline-start:20px;-webkit-padding-end:20px;padding-inline-end:20px;padding-top:0;padding-bottom:0;font-size:1.25rem;font-weight:500;letter-spacing:0.0125em}:host(.title-small){width:100%;height:100%;font-size:0.9375rem;font-weight:normal}"}}},[33,"ion-title",{color:[513],size:[1]},void 0,{size:["sizeChanged"]}]),eb=Xh(class extends Rp{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.childrenStyles=new Map,this.color=void 0}componentWillLoad(){const e=Array.from(this.el.querySelectorAll("ion-buttons")),t=e.find((e=>"start"===e.slot));t&&t.classList.add("buttons-first-slot");const n=e.reverse(),o=n.find((e=>"end"===e.slot))||n.find((e=>"primary"===e.slot))||n.find((e=>"secondary"===e.slot));o&&o.classList.add("buttons-last-slot")}childrenStyle(e){e.stopPropagation();const t=e.target.tagName,n=e.detail,o={},r=this.childrenStyles.get(t)||{};let i=!1;Object.keys(n).forEach((e=>{const t=`toolbar-${e}`,a=n[e];a!==r[t]&&(i=!0),a&&(o[t]=!0)})),i&&(this.childrenStyles.set(t,o),Wh(this))}render(){const e=Sm(this),t={};return this.childrenStyles.forEach((e=>{Object.assign(t,e)})),Zp(eh,{key:"462538a5ecd01baf3cde116c9f029aeda26c81be",class:Object.assign(Object.assign({},t),ug(this.color,{[e]:!0,"in-toolbar":cg("ion-toolbar",this.el)}))},Zp("div",{key:"c0b4415d3b2472de643a9be7cb3b13b3b628621b",class:"toolbar-background"}),Zp("div",{key:"0ccb8a2dbeaa28d8f9bed87629c0c097446690c2",class:"toolbar-container"},Zp("slot",{key:"3e726dac359e923df21d80301651f16063a3de20",name:"start"}),Zp("slot",{key:"cd799330b56a7f8833bc61bb2807aafb21846f71",name:"secondary"}),Zp("div",{key:"395282e6ac8c53576922dcdb5f08c25d34638c86",class:"toolbar-content"},Zp("slot",{key:"a437c60e4ba5aae65e55169ae82a6f379868ec1d"})),Zp("slot",{key:"711af9b9d321a7b31ede924c9bdcad767aa9a1ca",name:"primary"}),Zp("slot",{key:"ecc02edeaf80a837890bcb08d5096df1e22a0b9a",name:"end"})))}get el(){return this}static get style(){return{ios:":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-color-step-50, var(--ion-background-color-step-50, #f7f7f7)));--color:var(--ion-toolbar-color, var(--ion-text-color, #000));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.2)))));--padding-top:3px;--padding-bottom:3px;--padding-start:4px;--padding-end:4px;--min-height:44px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:4;order:4;min-width:0}:host(.toolbar-segment) .toolbar-content{display:-ms-inline-flexbox;display:inline-flex}:host(.toolbar-searchbar) .toolbar-container{padding-top:0;padding-bottom:0}:host(.toolbar-searchbar) ::slotted(*){-ms-flex-item-align:start;align-self:start}:host(.toolbar-searchbar) ::slotted(ion-chip){margin-top:3px}::slotted(ion-buttons){min-height:38px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:3;order:3}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}:host(.toolbar-title-large) .toolbar-container{-ms-flex-wrap:wrap;flex-wrap:wrap;-ms-flex-align:start;align-items:flex-start}:host(.toolbar-title-large) .toolbar-content ion-title{-ms-flex:1;flex:1;-ms-flex-order:8;order:8;min-width:100%}",md:":host{--border-width:0;--border-style:solid;--opacity:1;--opacity-scale:1;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;display:block;position:relative;width:100%;padding-right:var(--ion-safe-area-right);padding-left:var(--ion-safe-area-left);color:var(--color);font-family:var(--ion-font-family, inherit);contain:content;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}:host(.ion-color){color:var(--ion-color-contrast)}:host(.ion-color) .toolbar-background{background:var(--ion-color-base)}.toolbar-container{-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:row;flex-direction:row;-ms-flex-align:center;align-items:center;-ms-flex-pack:justify;justify-content:space-between;width:100%;min-height:var(--min-height);contain:content;overflow:hidden;z-index:10;-webkit-box-sizing:border-box;box-sizing:border-box}.toolbar-background{left:0;right:0;top:0;bottom:0;position:absolute;-webkit-transform:translateZ(0);transform:translateZ(0);border-width:var(--border-width);border-style:var(--border-style);border-color:var(--border-color);background:var(--background);contain:strict;opacity:calc(var(--opacity) * var(--opacity-scale));z-index:-1;pointer-events:none}::slotted(ion-progress-bar){left:0;right:0;bottom:0;position:absolute}:host{--background:var(--ion-toolbar-background, var(--ion-background-color, #fff));--color:var(--ion-toolbar-color, var(--ion-text-color, #424242));--border-color:var(--ion-toolbar-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, #c1c4cd))));--padding-top:0;--padding-bottom:0;--padding-start:0;--padding-end:0;--min-height:56px}.toolbar-content{-ms-flex:1;flex:1;-ms-flex-order:3;order:3;min-width:0;max-width:100%}::slotted(.buttons-first-slot){-webkit-margin-start:4px;margin-inline-start:4px}::slotted(.buttons-last-slot){-webkit-margin-end:4px;margin-inline-end:4px}::slotted([slot=start]){-ms-flex-order:2;order:2}::slotted([slot=secondary]){-ms-flex-order:4;order:4}::slotted([slot=primary]){-ms-flex-order:5;order:5;text-align:end}::slotted([slot=end]){-ms-flex-order:6;order:6;text-align:end}"}}},[33,"ion-toolbar",{color:[513]},[[0,"ionStyle","childrenStyle"]]]),tb=Xh(class extends Rp{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.loaded=!1,this.active=!1,this.delegate=void 0,this.tab=void 0,this.component=void 0}async componentWillLoad(){this.active&&await this.setActive()}async setActive(){await this.prepareLazyLoaded(),this.active=!0}changeActive(e){e&&this.prepareLazyLoaded()}prepareLazyLoaded(){if(!this.loaded&&null!=this.component){this.loaded=!0;try{return pg(this.delegate,this.el,this.component,["ion-page"])}catch(kf){console.error(kf)}}return Promise.resolve(void 0)}render(){const{tab:e,active:t,component:n}=this;return Zp(eh,{key:"cb75d0877979b3b8df8f7e1952bfa9677da1eaa5",role:"tabpanel","aria-hidden":t?null:"true","aria-labelledby":`tab-button-${e}`,class:{"ion-page":void 0===n,"tab-hidden":!t}},Zp("slot",{key:"37fbb7b7a6b03eb93b1dacd2dc1025b78eb2aa6b"}))}get el(){return this}static get watchers(){return{active:["changeActive"]}}static get style(){return":host(.tab-hidden){display:none !important}"}},[1,"ion-tab",{active:[1028],delegate:[16],tab:[1],component:[1],setActive:[64]},void 0,{active:["changeActive"]}]),nb=function(){"undefined"!=typeof customElements&&["ion-tab"].forEach((e=>{"ion-tab"===e&&(customElements.get(e)||customElements.define(e,tb))}))},ob=function(){"undefined"!=typeof customElements&&["ion-title"].forEach((e=>{"ion-title"===e&&(customElements.get(e)||customElements.define(e,Jg))}))}
/*!
             * (C) Ionic http://ionicframework.com - MIT License
             */,rb=function(){"undefined"!=typeof customElements&&["ion-toolbar"].forEach((e=>{"ion-toolbar"===e&&(customElements.get(e)||customElements.define(e,eb))}))}
/*!
             * (C) Ionic http://ionicframework.com - MIT License
             */,ib=Xh(class extends Rp{constructor(){super(),this.__registerHost()}componentDidLoad(){lb((async()=>{const e=dm(window,"hybrid");if(lm.getBoolean("_testing")||gp((()=>t.import("./index9-legacy-trIJGx-m.js"))).then((e=>e.startTapClick(lm))),lm.getBoolean("statusTap",e)&&gp((()=>t.import("./status-tap-legacy-D0AfU4WE.js"))).then((e=>e.startStatusTap())),lm.getBoolean("inputShims",ab())){const e=dm(window,"ios")?"ios":"android";gp((()=>t.import("./input-shims-legacy-DPqpIJ8h.js"))).then((t=>t.startInputShims(lm,e)))}const n=await gp((()=>Promise.resolve().then((()=>fg)))),o=e||dg();lm.getBoolean("hardwareBackButton",o)?n.startHardwareBackButton():(dg()&&$m("experimentalCloseWatcher was set to `true`, but hardwareBackButton was set to `false`. Both config options must be `true` for the Close Watcher API to be used."),n.blockHardwareBackButton()),"undefined"!=typeof window&&gp((()=>t.import("./keyboard2-legacy-CGDjt9-y.js"))).then((e=>e.startKeyboardAssist(window))),gp((()=>t.import("./focus-visible-legacy-CdO5cX4I.js"))).then((e=>this.focusVisible=e.startFocusVisible()))}))}async setFocus(e){this.focusVisible&&this.focusVisible.setFocus(e)}render(){const e=Sm(this);return Zp(eh,{key:"96715520fd05d6f0e6fa26a8ba78792cfccd4c0a",class:{[e]:!0,"ion-page":!0,"force-statusbar-padding":lm.getBoolean("_forceStatusbarPadding")}})}get el(){return this}static get style(){return"html.plt-mobile ion-app{-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}html.plt-mobile ion-app [contenteditable]{-webkit-user-select:text;-moz-user-select:text;-ms-user-select:text;user-select:text}ion-app.force-statusbar-padding{--ion-safe-area-top:20px}"}},[0,"ion-app",{setFocus:[64]}]),ab=()=>!(!dm(window,"ios")||!dm(window,"mobile"))||!(!dm(window,"android")||!dm(window,"mobileweb")),lb=e=>{"requestIdleCallback"in window?window.requestIdleCallback(e):setTimeout(e,32)},sb=function(){"undefined"!=typeof customElements&&["ion-app"].forEach((e=>{"ion-app"===e&&(customElements.get(e)||customElements.define(e,ib))}))},cb=Xh(class extends Rp{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionNavWillLoad=rh(this,"ionNavWillLoad",7),this.ionNavWillChange=rh(this,"ionNavWillChange",3),this.ionNavDidChange=rh(this,"ionNavDidChange",3),this.lockController=(()=>{let e;return{lock:async()=>{const t=e;let n;return e=new Promise((e=>n=e)),void 0!==t&&await t,n}}})(),this.gestureOrAnimationInProgress=!1,this.mode=Sm(this),this.delegate=void 0,this.animated=!0,this.animation=void 0,this.swipeHandler=void 0}swipeHandlerChanged(){this.gesture&&this.gesture.enable(void 0!==this.swipeHandler)}async connectedCallback(){const e=()=>{this.gestureOrAnimationInProgress=!0,this.swipeHandler&&this.swipeHandler.onStart()};this.gesture=(await gp((()=>t.import("./swipe-back-legacy-CdR4zaeO.js")))).createSwipeBackGesture(this.el,(()=>!this.gestureOrAnimationInProgress&&!!this.swipeHandler&&this.swipeHandler.canStart()),(()=>e()),(e=>{var t;return null===(t=this.ani)||void 0===t?void 0:t.progressStep(e)}),((e,t,n)=>{if(this.ani){this.ani.onFinish((()=>{this.gestureOrAnimationInProgress=!1,this.swipeHandler&&this.swipeHandler.onEnd(e)}),{oneTimeCallback:!0});let o=e?-.001:.001;e?o+=ig([0,0],[.32,.72],[0,1],[1,1],t)[0]:(this.ani.easing("cubic-bezier(1, 0, 0.68, 0.28)"),o+=ig([0,0],[1,0],[.68,.28],[1,1],t)[0]),this.ani.progressEnd(e?1:0,o,n)}else this.gestureOrAnimationInProgress=!1})),this.swipeHandlerChanged()}componentWillLoad(){this.ionNavWillLoad.emit()}disconnectedCallback(){this.gesture&&(this.gesture.destroy(),this.gesture=void 0)}async commit(e,t,n){const o=await this.lockController.lock();let r=!1;try{r=await this.transition(e,t,n)}catch(kf){console.error(kf)}return o(),r}async setRouteId(e,t,n,o){return{changed:await this.setRoot(e,t,{duration:"root"===n?0:void 0,direction:"back"===n?"back":"forward",animationBuilder:o}),element:this.activeEl}}async getRouteId(){const e=this.activeEl;return e?{id:e.tagName,element:e,params:this.activeParams}:void 0}async setRoot(e,t,n){if(this.activeComponent===e&&Nm(t,this.activeParams))return!1;const o=this.activeEl,r=await pg(this.delegate,this.el,e,["ion-page","ion-page-invisible"],t);return this.activeComponent=e,this.activeEl=r,this.activeParams=t,await this.commit(r,o,n),await((e,t)=>{if(t){if(e){const n=t.parentElement;return e.removeViewFromDom(n,t)}t.remove()}return Promise.resolve()})(this.delegate,o),!0}async transition(e,t,n={}){if(t===e)return!1;this.ionNavWillChange.emit();const{el:o,mode:r}=this,i=this.animated&&lm.getBoolean("animated",!0),a=n.animationBuilder||this.animation||lm.get("navAnimation");return await Fm(Object.assign(Object.assign({mode:r,animated:i,enteringEl:e,leavingEl:t,baseEl:o,deepWait:Pm(o),progressCallback:n.progressAnimation?e=>{void 0===e||this.gestureOrAnimationInProgress?this.ani=e:(this.gestureOrAnimationInProgress=!0,e.onFinish((()=>{this.gestureOrAnimationInProgress=!1,this.swipeHandler&&this.swipeHandler.onEnd(!1)}),{oneTimeCallback:!0}),e.progressEnd(0,0,0))}:void 0},n),{animationBuilder:a})),this.ionNavDidChange.emit(),!0}render(){return Zp("slot",{key:"a70341f58d19df55de1dad00e3464388e446aa2a"})}get el(){return this}static get watchers(){return{swipeHandler:["swipeHandlerChanged"]}}static get style(){return":host{left:0;right:0;top:0;bottom:0;position:absolute;contain:layout size style;z-index:0}"}},[1,"ion-router-outlet",{mode:[1025],delegate:[16],animated:[4],animation:[16],swipeHandler:[16],commit:[64],setRouteId:[64],getRouteId:[64]},void 0,{swipeHandler:["swipeHandlerChanged"]}]),ub=function(){"undefined"!=typeof customElements&&["ion-router-outlet"].forEach((e=>{"ion-router-outlet"===e&&(customElements.get(e)||customElements.define(e,cb))}))},db=Xh(class extends Rp{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionTabBarChanged=rh(this,"ionTabBarChanged",7),this.ionTabBarLoaded=rh(this,"ionTabBarLoaded",7),this.keyboardCtrl=null,this.keyboardVisible=!1,this.color=void 0,this.selectedTab=void 0,this.translucent=!1}selectedTabChanged(){void 0!==this.selectedTab&&this.ionTabBarChanged.emit({tab:this.selectedTab})}componentWillLoad(){this.selectedTabChanged()}async connectedCallback(){this.keyboardCtrl=await(async e=>{let t,n,o,r;const i=async()=>{const e=await zg.getResizeMode(),i=void 0===e?void 0:e.mode;t=()=>{void 0===r&&(r=Mg(i)),o=!0,a(o,i)},n=()=>{o=!1,a(o,i)},null==tm||tm.addEventListener("keyboardWillShow",t),null==tm||tm.addEventListener("keyboardWillHide",n)},a=(t,n)=>{e&&e(t,l(n))},l=e=>{if(0===r||r===Mg(e))return;const t=Ng(e);return null!==t?new Promise((e=>{const n=new ResizeObserver((()=>{t.clientHeight===r&&(n.disconnect(),e())}));n.observe(t)})):void 0};return await i(),{init:i,destroy:()=>{null==tm||tm.removeEventListener("keyboardWillShow",t),null==tm||tm.removeEventListener("keyboardWillHide",n),t=n=void 0},isKeyboardVisible:()=>o}})((async(e,t)=>{!1===e&&void 0!==t&&await t,this.keyboardVisible=e}))}disconnectedCallback(){this.keyboardCtrl&&this.keyboardCtrl.destroy()}componentDidLoad(){this.ionTabBarLoaded.emit()}render(){const{color:e,translucent:t,keyboardVisible:n}=this,o=Sm(this),r=n&&"top"!==this.el.getAttribute("slot");return Zp(eh,{key:"a87fd2ea5df053705a37ea7ffec043e75c4a9907",role:"tablist","aria-hidden":r?"true":null,class:ug(e,{[o]:!0,"tab-bar-translucent":t,"tab-bar-hidden":r})},Zp("slot",{key:"81a6223299b6cab29d7ddced590e9152e2b3ded0"}))}get el(){return this}static get watchers(){return{selectedTab:["selectedTabChanged"]}}static get style(){return{ios:":host{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:auto;padding-right:var(--ion-safe-area-right);padding-bottom:var(--ion-safe-area-bottom, 0);padding-left:var(--ion-safe-area-left);border-top:var(--border);background:var(--background);color:var(--color);text-align:center;contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:10;-webkit-box-sizing:content-box !important;box-sizing:content-box !important}:host(.ion-color) ::slotted(ion-tab-button){--background-focused:var(--ion-color-shade);--color-selected:var(--ion-color-contrast)}:host(.ion-color) ::slotted(.tab-selected){color:var(--ion-color-contrast)}:host(.ion-color),:host(.ion-color) ::slotted(ion-tab-button){color:rgba(var(--ion-color-contrast-rgb), 0.7)}:host(.ion-color),:host(.ion-color) ::slotted(ion-tab-button){background:var(--ion-color-base)}:host(.ion-color) ::slotted(ion-tab-button.ion-focused),:host(.tab-bar-translucent) ::slotted(ion-tab-button.ion-focused){background:var(--background-focused)}:host(.tab-bar-translucent) ::slotted(ion-tab-button){background:transparent}:host([slot=top]){padding-top:var(--ion-safe-area-top, 0);padding-bottom:0;border-top:0;border-bottom:var(--border)}:host(.tab-bar-hidden){display:none !important}:host{--background:var(--ion-tab-bar-background, var(--ion-color-step-50, var(--ion-background-color-step-50, #f7f7f7)));--background-focused:var(--ion-tab-bar-background-focused, #e0e0e0);--border:0.55px solid var(--ion-tab-bar-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.2)))));--color:var(--ion-tab-bar-color, var(--ion-color-step-600, var(--ion-text-color-step-400, #666666)));--color-selected:var(--ion-tab-bar-color-selected, var(--ion-color-primary, #0054e9));height:50px}@supports ((-webkit-backdrop-filter: blur(0)) or (backdrop-filter: blur(0))){:host(.tab-bar-translucent){--background:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.8);-webkit-backdrop-filter:saturate(210%) blur(20px);backdrop-filter:saturate(210%) blur(20px)}:host(.ion-color.tab-bar-translucent){background:rgba(var(--ion-color-base-rgb), 0.8)}:host(.tab-bar-translucent) ::slotted(ion-tab-button.ion-focused){background:rgba(var(--ion-background-color-rgb, 255, 255, 255), 0.6)}}",md:":host{display:-ms-flexbox;display:flex;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;width:auto;padding-right:var(--ion-safe-area-right);padding-bottom:var(--ion-safe-area-bottom, 0);padding-left:var(--ion-safe-area-left);border-top:var(--border);background:var(--background);color:var(--color);text-align:center;contain:strict;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;z-index:10;-webkit-box-sizing:content-box !important;box-sizing:content-box !important}:host(.ion-color) ::slotted(ion-tab-button){--background-focused:var(--ion-color-shade);--color-selected:var(--ion-color-contrast)}:host(.ion-color) ::slotted(.tab-selected){color:var(--ion-color-contrast)}:host(.ion-color),:host(.ion-color) ::slotted(ion-tab-button){color:rgba(var(--ion-color-contrast-rgb), 0.7)}:host(.ion-color),:host(.ion-color) ::slotted(ion-tab-button){background:var(--ion-color-base)}:host(.ion-color) ::slotted(ion-tab-button.ion-focused),:host(.tab-bar-translucent) ::slotted(ion-tab-button.ion-focused){background:var(--background-focused)}:host(.tab-bar-translucent) ::slotted(ion-tab-button){background:transparent}:host([slot=top]){padding-top:var(--ion-safe-area-top, 0);padding-bottom:0;border-top:0;border-bottom:var(--border)}:host(.tab-bar-hidden){display:none !important}:host{--background:var(--ion-tab-bar-background, var(--ion-background-color, #fff));--background-focused:var(--ion-tab-bar-background-focused, #e0e0e0);--border:1px solid var(--ion-tab-bar-border-color, var(--ion-border-color, var(--ion-color-step-150, var(--ion-background-color-step-150, rgba(0, 0, 0, 0.07)))));--color:var(--ion-tab-bar-color, var(--ion-color-step-650, var(--ion-text-color-step-350, #595959)));--color-selected:var(--ion-tab-bar-color-selected, var(--ion-color-primary, #0054e9));height:56px}"}}},[33,"ion-tab-bar",{color:[513],selectedTab:[1,"selected-tab"],translucent:[4],keyboardVisible:[32]},void 0,{selectedTab:["selectedTabChanged"]}]),fb=function(){"undefined"!=typeof customElements&&["ion-tab-bar"].forEach((e=>{"ion-tab-bar"===e&&(customElements.get(e)||customElements.define(e,db))}))},pb=Xh(class extends Rp{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionTabButtonClick=rh(this,"ionTabButtonClick",7),this.inheritedAttributes={},this.onKeyUp=e=>{"Enter"!==e.key&&" "!==e.key||this.selectTab(e)},this.onClick=e=>{this.selectTab(e)},this.disabled=!1,this.download=void 0,this.href=void 0,this.rel=void 0,this.layout=void 0,this.selected=!1,this.tab=void 0,this.target=void 0}onTabBarChanged(e){const t=e.target,n=this.el.parentElement;(e.composedPath().includes(n)||(null==t?void 0:t.contains(this.el)))&&(this.selected=this.tab===e.detail.tab)}componentWillLoad(){this.inheritedAttributes=Object.assign({},Rm(this.el,["aria-label"])),void 0===this.layout&&(this.layout=lm.get("tabButtonLayout","icon-top"))}selectTab(e){void 0!==this.tab&&(this.disabled||this.ionTabButtonClick.emit({tab:this.tab,href:this.href,selected:this.selected}),e.preventDefault())}get hasLabel(){return!!this.el.querySelector("ion-label")}get hasIcon(){return!!this.el.querySelector("ion-icon")}render(){const{disabled:e,hasIcon:t,hasLabel:n,href:o,rel:r,target:i,layout:a,selected:l,tab:s,inheritedAttributes:c}=this,u=Sm(this),d={download:this.download,href:o,rel:r,target:i};return Zp(eh,{key:"5976c45943ea7ea8e7c1a85fc9996de421439f08",onClick:this.onClick,onKeyup:this.onKeyUp,id:void 0!==s?`tab-button-${s}`:null,class:{[u]:!0,"tab-selected":l,"tab-disabled":e,"tab-has-label":n,"tab-has-icon":t,"tab-has-label-only":n&&!t,"tab-has-icon-only":t&&!n,[`tab-layout-${a}`]:!0,"ion-activatable":!0,"ion-selectable":!0,"ion-focusable":!0}},Zp("a",Object.assign({key:"1db09d861b67ff292018fb4b0dc7b85bd4677eb8"},d,{class:"button-native",part:"native",role:"tab","aria-selected":l?"true":null,"aria-disabled":e?"true":null,tabindex:e?"-1":void 0},c),Zp("span",{key:"4381eafcb27e8c7bb0d86d4f115ceb0caf03b9b4",class:"button-inner"},Zp("slot",{key:"1981135f6fbb88376c1bd923c55c70fe8b5c5159"})),"md"===u&&Zp("ion-ripple-effect",{key:"0509bc7155d055d1ed710600e9cf4df135881491",type:"unbounded"})))}get el(){return this}static get style(){return{ios:':host{--ripple-color:var(--color-selected);--background-focused-opacity:1;-ms-flex:1;flex:1;-ms-flex-direction:column;flex-direction:column;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;height:100%;outline:none;background:var(--background);color:var(--color)}.button-native{border-radius:inherit;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;height:100%;border:0;outline:none;background:transparent;text-decoration:none;cursor:pointer;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-user-drag:none}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:"";opacity:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:inherit;flex-flow:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;height:100%;z-index:1}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}@media (any-hover: hover){a:hover{color:var(--color-selected)}}:host(.tab-selected){color:var(--color-selected)}:host(.tab-hidden){display:none !important}:host(.tab-disabled){pointer-events:none;opacity:0.4}::slotted(ion-label),::slotted(ion-icon){display:block;-ms-flex-item-align:center;align-self:center;max-width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box}::slotted(ion-label){-ms-flex-order:0;order:0}::slotted(ion-icon){-ms-flex-order:-1;order:-1;height:1em}:host(.tab-has-label-only) ::slotted(ion-label){white-space:normal}::slotted(ion-badge){-webkit-box-sizing:border-box;box-sizing:border-box;position:absolute;z-index:1}:host(.tab-layout-icon-start){-ms-flex-direction:row;flex-direction:row}:host(.tab-layout-icon-end){-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.tab-layout-icon-bottom){-ms-flex-direction:column-reverse;flex-direction:column-reverse}:host(.tab-layout-icon-hide) ::slotted(ion-icon){display:none}:host(.tab-layout-label-hide) ::slotted(ion-label){display:none}ion-ripple-effect{color:var(--ripple-color)}:host{--padding-top:0;--padding-end:2px;--padding-bottom:0;--padding-start:2px;max-width:240px;font-size:10px}::slotted(ion-badge){-webkit-padding-start:6px;padding-inline-start:6px;-webkit-padding-end:6px;padding-inline-end:6px;padding-top:1px;padding-bottom:1px;top:4px;height:auto;font-size:12px;line-height:16px}::slotted(ion-badge){inset-inline-start:calc(50% + 6px)}::slotted(ion-icon){margin-top:2px;margin-bottom:2px;font-size:24px}::slotted(ion-icon::before){vertical-align:top}::slotted(ion-label){margin-top:0;margin-bottom:1px;min-height:11px;font-weight:500}:host(.tab-has-label-only) ::slotted(ion-label){margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px;font-size:12px;font-size:14px;line-height:1.1}:host(.tab-layout-icon-end) ::slotted(ion-label),:host(.tab-layout-icon-start) ::slotted(ion-label),:host(.tab-layout-icon-hide) ::slotted(ion-label){margin-top:2px;margin-bottom:2px;font-size:14px;line-height:1.1}:host(.tab-layout-icon-end) ::slotted(ion-icon),:host(.tab-layout-icon-start) ::slotted(ion-icon){min-width:24px;height:26px;margin-top:2px;margin-bottom:1px;font-size:24px}:host(.tab-layout-icon-bottom) ::slotted(ion-badge){inset-inline-start:calc(50% + 12px)}:host(.tab-layout-icon-bottom) ::slotted(ion-icon){margin-top:0;margin-bottom:1px}:host(.tab-layout-icon-bottom) ::slotted(ion-label){margin-top:4px}:host(.tab-layout-icon-start) ::slotted(ion-badge),:host(.tab-layout-icon-end) ::slotted(ion-badge){top:10px}:host(.tab-layout-icon-start) ::slotted(ion-badge),:host(.tab-layout-icon-end) ::slotted(ion-badge){inset-inline-start:calc(50% + 35px)}:host(.tab-layout-icon-hide) ::slotted(ion-badge),:host(.tab-has-label-only) ::slotted(ion-badge){top:10px}:host(.tab-layout-icon-hide) ::slotted(ion-badge),:host(.tab-has-label-only) ::slotted(ion-badge){inset-inline-start:calc(50% + 30px)}:host(.tab-layout-label-hide) ::slotted(ion-badge),:host(.tab-has-icon-only) ::slotted(ion-badge){top:10px}:host(.tab-layout-label-hide) ::slotted(ion-icon){margin-left:0;margin-right:0;margin-top:0;margin-bottom:0}:host(.tab-layout-label-hide) ::slotted(ion-icon),:host(.tab-has-icon-only) ::slotted(ion-icon){font-size:30px}',md:':host{--ripple-color:var(--color-selected);--background-focused-opacity:1;-ms-flex:1;flex:1;-ms-flex-direction:column;flex-direction:column;-ms-flex-align:center;align-items:center;-ms-flex-pack:center;justify-content:center;height:100%;outline:none;background:var(--background);color:var(--color)}.button-native{border-radius:inherit;margin-left:0;margin-right:0;margin-top:0;margin-bottom:0;-webkit-padding-start:var(--padding-start);padding-inline-start:var(--padding-start);-webkit-padding-end:var(--padding-end);padding-inline-end:var(--padding-end);padding-top:var(--padding-top);padding-bottom:var(--padding-bottom);font-family:inherit;font-size:inherit;font-style:inherit;font-weight:inherit;letter-spacing:inherit;text-decoration:inherit;text-indent:inherit;text-overflow:inherit;text-transform:inherit;text-align:inherit;white-space:inherit;color:inherit;display:-ms-flexbox;display:flex;position:relative;-ms-flex-direction:inherit;flex-direction:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;height:100%;border:0;outline:none;background:transparent;text-decoration:none;cursor:pointer;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box;-webkit-user-drag:none}.button-native::after{left:0;right:0;top:0;bottom:0;position:absolute;content:"";opacity:0}.button-inner{display:-ms-flexbox;display:flex;position:relative;-ms-flex-flow:inherit;flex-flow:inherit;-ms-flex-align:inherit;align-items:inherit;-ms-flex-pack:inherit;justify-content:inherit;width:100%;height:100%;z-index:1}:host(.ion-focused) .button-native{color:var(--color-focused)}:host(.ion-focused) .button-native::after{background:var(--background-focused);opacity:var(--background-focused-opacity)}@media (any-hover: hover){a:hover{color:var(--color-selected)}}:host(.tab-selected){color:var(--color-selected)}:host(.tab-hidden){display:none !important}:host(.tab-disabled){pointer-events:none;opacity:0.4}::slotted(ion-label),::slotted(ion-icon){display:block;-ms-flex-item-align:center;align-self:center;max-width:100%;text-overflow:ellipsis;white-space:nowrap;overflow:hidden;-webkit-box-sizing:border-box;box-sizing:border-box}::slotted(ion-label){-ms-flex-order:0;order:0}::slotted(ion-icon){-ms-flex-order:-1;order:-1;height:1em}:host(.tab-has-label-only) ::slotted(ion-label){white-space:normal}::slotted(ion-badge){-webkit-box-sizing:border-box;box-sizing:border-box;position:absolute;z-index:1}:host(.tab-layout-icon-start){-ms-flex-direction:row;flex-direction:row}:host(.tab-layout-icon-end){-ms-flex-direction:row-reverse;flex-direction:row-reverse}:host(.tab-layout-icon-bottom){-ms-flex-direction:column-reverse;flex-direction:column-reverse}:host(.tab-layout-icon-hide) ::slotted(ion-icon){display:none}:host(.tab-layout-label-hide) ::slotted(ion-label){display:none}ion-ripple-effect{color:var(--ripple-color)}:host{--padding-top:0;--padding-end:12px;--padding-bottom:0;--padding-start:12px;max-width:168px;font-size:12px;font-weight:normal;letter-spacing:0.03em}::slotted(ion-label){margin-left:0;margin-right:0;margin-top:2px;margin-bottom:2px;text-transform:none}::slotted(ion-icon){margin-left:0;margin-right:0;margin-top:16px;margin-bottom:16px;-webkit-transform-origin:center center;transform-origin:center center;font-size:22px}:host-context([dir=rtl]) ::slotted(ion-icon){-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}[dir=rtl] ::slotted(ion-icon){-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}@supports selector(:dir(rtl)){::slotted(ion-icon):dir(rtl){-webkit-transform-origin:calc(100% - center) center;transform-origin:calc(100% - center) center}}::slotted(ion-badge){border-radius:8px;-webkit-padding-start:2px;padding-inline-start:2px;-webkit-padding-end:2px;padding-inline-end:2px;padding-top:3px;padding-bottom:2px;top:8px;min-width:12px;font-size:8px;font-weight:normal}::slotted(ion-badge){inset-inline-start:calc(50% + 6px)}::slotted(ion-badge:empty){display:block;min-width:8px;height:8px}:host(.tab-layout-icon-top) ::slotted(ion-icon){margin-top:6px;margin-bottom:2px}:host(.tab-layout-icon-top) ::slotted(ion-label){margin-top:0;margin-bottom:6px}:host(.tab-layout-icon-bottom) ::slotted(ion-badge){top:8px}:host(.tab-layout-icon-bottom) ::slotted(ion-badge){inset-inline-start:70%}:host(.tab-layout-icon-bottom) ::slotted(ion-icon){margin-top:0;margin-bottom:6px}:host(.tab-layout-icon-bottom) ::slotted(ion-label){margin-top:6px;margin-bottom:0}:host(.tab-layout-icon-start) ::slotted(ion-badge),:host(.tab-layout-icon-end) ::slotted(ion-badge){top:16px}:host(.tab-layout-icon-start) ::slotted(ion-badge),:host(.tab-layout-icon-end) ::slotted(ion-badge){inset-inline-start:80%}:host(.tab-layout-icon-start) ::slotted(ion-icon){-webkit-margin-end:6px;margin-inline-end:6px}:host(.tab-layout-icon-end) ::slotted(ion-icon){-webkit-margin-start:6px;margin-inline-start:6px}:host(.tab-layout-icon-hide) ::slotted(ion-badge),:host(.tab-has-label-only) ::slotted(ion-badge){top:16px}:host(.tab-layout-icon-hide) ::slotted(ion-badge),:host(.tab-has-label-only) ::slotted(ion-badge){inset-inline-start:70%}:host(.tab-layout-icon-hide) ::slotted(ion-label),:host(.tab-has-label-only) ::slotted(ion-label){margin-top:0;margin-bottom:0}:host(.tab-layout-label-hide) ::slotted(ion-badge),:host(.tab-has-icon-only) ::slotted(ion-badge){top:16px}:host(.tab-layout-label-hide) ::slotted(ion-icon),:host(.tab-has-icon-only) ::slotted(ion-icon){margin-top:0;margin-bottom:0;font-size:24px}'}}},[33,"ion-tab-button",{disabled:[4],download:[1],href:[1],rel:[1],layout:[1025],selected:[1028],tab:[1],target:[1]},[[8,"ionTabBarChanged","onTabBarChanged"]]]),hb=function(){"undefined"!=typeof customElements&&["ion-tab-button","ion-ripple-effect"].forEach((e=>{switch(e){case"ion-tab-button":customElements.get(e)||customElements.define(e,pb);break;case"ion-ripple-effect":customElements.get(e)||"undefined"!=typeof customElements&&["ion-ripple-effect"].forEach((e=>{"ion-ripple-effect"===e&&(customElements.get(e)||customElements.define(e,mg))}))}}))},mb=Xh(class extends Rp{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.ionNavWillLoad=rh(this,"ionNavWillLoad",7),this.ionTabsWillChange=rh(this,"ionTabsWillChange",3),this.ionTabsDidChange=rh(this,"ionTabsDidChange",3),this.transitioning=!1,this.onTabClicked=e=>{const{href:t,tab:n}=e.detail;if(this.useRouter&&void 0!==t){const e=document.querySelector("ion-router");e&&e.push(t)}else this.select(n)},this.selectedTab=void 0,this.useRouter=!1}async componentWillLoad(){if(this.useRouter||(this.useRouter=!(!this.el.querySelector("ion-router-outlet")&&!document.querySelector("ion-router")||this.el.closest("[no-router]"))),!this.useRouter){const e=this.tabs;e.length>0&&await this.select(e[0])}this.ionNavWillLoad.emit()}componentWillRender(){const e=this.el.querySelector("ion-tab-bar");if(e){const t=this.selectedTab?this.selectedTab.tab:void 0;e.selectedTab=t}}async select(e){const t=gb(this.tabs,e);return!!this.shouldSwitch(t)&&(await this.setActive(t),await this.notifyRouter(),this.tabSwitch(),!0)}async getTab(e){return gb(this.tabs,e)}getSelected(){return Promise.resolve(this.selectedTab?this.selectedTab.tab:void 0)}async setRouteId(e){const t=gb(this.tabs,e);return this.shouldSwitch(t)?(await this.setActive(t),{changed:!0,element:this.selectedTab,markVisible:()=>this.tabSwitch()}):{changed:!1,element:this.selectedTab}}async getRouteId(){var e;const t=null===(e=this.selectedTab)||void 0===e?void 0:e.tab;return void 0!==t?{id:t,element:this.selectedTab}:void 0}setActive(e){return this.transitioning?Promise.reject("transitioning already happening"):(this.transitioning=!0,this.leavingTab=this.selectedTab,this.selectedTab=e,this.ionTabsWillChange.emit({tab:e.tab}),e.active=!0,Promise.resolve())}tabSwitch(){const e=this.selectedTab,t=this.leavingTab;this.leavingTab=void 0,this.transitioning=!1,e&&t!==e&&(t&&(t.active=!1),this.ionTabsDidChange.emit({tab:e.tab}))}notifyRouter(){if(this.useRouter){const e=document.querySelector("ion-router");if(e)return e.navChanged("forward")}return Promise.resolve(!1)}shouldSwitch(e){const t=this.selectedTab;return void 0!==e&&e!==t&&!this.transitioning}get tabs(){return Array.from(this.el.querySelectorAll("ion-tab"))}render(){return Zp(eh,{key:"e01ccf6bfaccad094515be50e407399c733fc226",onIonTabButtonClick:this.onTabClicked},Zp("slot",{key:"38d2d01dbfd8a08f01e6f0e27274b21d75424e37",name:"top"}),Zp("div",{key:"7e894f0f423e2d43e1c68daff5f9f6c442fad237",class:"tabs-inner"},Zp("slot",{key:"df16be529a0370a26d0adf850530b31607507c23"})),Zp("slot",{key:"44642e1cb24c3281c43db75fd69a32fe0defe40a",name:"bottom"}))}get el(){return this}static get style(){return":host{left:0;right:0;top:0;bottom:0;display:-ms-flexbox;display:flex;position:absolute;-ms-flex-direction:column;flex-direction:column;width:100%;height:100%;contain:layout size style;z-index:0}.tabs-inner{position:relative;-ms-flex:1;flex:1;contain:layout size style}"}},[1,"ion-tabs",{useRouter:[1028,"use-router"],selectedTab:[32],select:[64],getTab:[64],getSelected:[64],setRouteId:[64],getRouteId:[64]}]),gb=(e,t)=>{const n="string"==typeof t?e.find((e=>e.tab===t)):t;return n||console.error(`tab with id: "${n}" does not exist`),n},bb=function(){"undefined"!=typeof customElements&&["ion-tabs"].forEach((e=>{"ion-tabs"===e&&(customElements.get(e)||customElements.define(e,mb))}))};let vb;const yb=(e,t)=>{const n=(()=>{if("undefined"==typeof window)return new Map;if(!vb){const e=window;e.Ionicons=e.Ionicons||{},vb=e.Ionicons.map=e.Ionicons.map||new Map}return vb})().get(e);if(n)return n;try{return(e=>{const t=new URL(e,Ip.$resourcesUrl$);return t.origin!==Tp.location.origin?t.href:t.pathname})(`svg/${e}.svg`)}catch(kf){console.warn(`[Ionicons Warning]: Could not load icon with name "${e}". Ensure that the icon is registered using addIcons or that the icon SVG data is passed directly to the icon component.`,t)}},wb=(e,t,n,o,r)=>(n="ios"===(n&&Sb(n))?"ios":"md",o&&"ios"===n?e=Sb(o):r&&"md"===n?e=Sb(r):(e||!t||kb(t)||(e=t),Eb(e)&&(e=Sb(e))),Eb(e)&&""!==e.trim()?""!==e.replace(/[a-z]|-|\d/gi,"")?null:e:null),xb=e=>Eb(e)&&(e=e.trim(),kb(e))?e:null,kb=e=>e.length>0&&/(\/|\.)/.test(e),Eb=e=>"string"==typeof e,Sb=e=>e.toLowerCase(),Cb=e=>{if(1===e.nodeType){if("script"===e.nodeName.toLowerCase())return!1;for(let t=0;t<e.attributes.length;t++){const n=e.attributes[t].name;if(Eb(n)&&0===n.toLowerCase().indexOf("on"))return!1}for(let t=0;t<e.childNodes.length;t++)if(!Cb(e.childNodes[t]))return!1}return!0},$b=new Map,Tb=new Map;let Pb;const Rb=(e,t)=>{let n=Tb.get(e);if(!n){if("undefined"==typeof fetch||"undefined"==typeof document)return $b.set(e,""),Promise.resolve();if((e=>e.startsWith("data:image/svg+xml"))(e)&&(e=>-1!==e.indexOf(";utf8,"))(e)){Pb||(Pb=new DOMParser);const t=Pb.parseFromString(e,"text/html").querySelector("svg");return t&&$b.set(e,t.outerHTML),Promise.resolve()}n=fetch(e).then((n=>{if(n.ok)return n.text().then((n=>{n&&!1!==t&&(n=(e=>{const t=document.createElement("div");t.innerHTML=e;for(let o=t.childNodes.length-1;o>=0;o--)"svg"!==t.childNodes[o].nodeName.toLowerCase()&&t.removeChild(t.childNodes[o]);const n=t.firstElementChild;if(n&&"svg"===n.nodeName.toLowerCase()){const e=n.getAttribute("class")||"";if(n.setAttribute("class",(e+" s-ion-icon").trim()),Cb(n))return t.innerHTML}return""})(n)),$b.set(e,n||"")}));$b.set(e,"")})),Tb.set(e,n)}return n},Ib=Xh(class extends Rp{constructor(){super(),this.__registerHost(),this.__attachShadow(),this.iconName=null,this.inheritedAttributes={},this.didLoadIcon=!1,this.svgContent=void 0,this.isVisible=!1,this.mode=Ob(),this.color=void 0,this.ios=void 0,this.md=void 0,this.flipRtl=void 0,this.name=void 0,this.src=void 0,this.icon=void 0,this.size=void 0,this.lazy=!1,this.sanitize=!0}componentWillLoad(){this.inheritedAttributes=((e,t=[])=>{const n={};return t.forEach((t=>{e.hasAttribute(t)&&(null!==e.getAttribute(t)&&(n[t]=e.getAttribute(t)),e.removeAttribute(t))})),n})(this.el,["aria-label"])}connectedCallback(){this.waitUntilVisible(this.el,"50px",(()=>{this.isVisible=!0,this.loadIcon()}))}componentDidLoad(){this.didLoadIcon||this.loadIcon()}disconnectedCallback(){this.io&&(this.io.disconnect(),this.io=void 0)}waitUntilVisible(e,t,n){if(this.lazy&&"undefined"!=typeof window&&window.IntersectionObserver){const o=this.io=new window.IntersectionObserver((e=>{e[0].isIntersecting&&(o.disconnect(),this.io=void 0,n())}),{rootMargin:t});o.observe(e)}else n()}loadIcon(){if(this.isVisible){const e=(e=>{let t=xb(e.src);if(t)return t;if(t=wb(e.name,e.icon,e.mode,e.ios,e.md),t)return yb(t,e);if(e.icon){if(t=xb(e.icon),t)return t;if(t=xb(e.icon[e.mode]),t)return t}return null})(this);e&&($b.has(e)?this.svgContent=$b.get(e):Rb(e,this.sanitize).then((()=>this.svgContent=$b.get(e))),this.didLoadIcon=!0)}this.iconName=wb(this.name,this.icon,this.mode,this.ios,this.md)}render(){const{flipRtl:e,iconName:t,inheritedAttributes:n,el:o}=this,r=this.mode||"md",i=!!t&&(t.includes("arrow")||t.includes("chevron"))&&!1!==e,a=e||i;return Zp(eh,Object.assign({role:"img",class:Object.assign(Object.assign({[r]:!0},Lb(this.color)),{[`icon-${this.size}`]:!!this.size,"flip-rtl":a,"icon-rtl":a&&(l=o,l&&""!==l.dir?"rtl"===l.dir.toLowerCase():"rtl"===(null===document||void 0===document?void 0:document.dir.toLowerCase()))})},n),this.svgContent?Zp("div",{class:"icon-inner",innerHTML:this.svgContent}):Zp("div",{class:"icon-inner"}));var l}static get assetsDirs(){return["svg"]}get el(){return this}static get watchers(){return{name:["loadIcon"],src:["loadIcon"],icon:["loadIcon"],ios:["loadIcon"],md:["loadIcon"]}}static get style(){return":host{display:inline-block;width:1em;height:1em;contain:strict;fill:currentColor;-webkit-box-sizing:content-box !important;box-sizing:content-box !important}:host .ionicon{stroke:currentColor}.ionicon-fill-none{fill:none}.ionicon-stroke-width{stroke-width:32px;stroke-width:var(--ionicon-stroke-width, 32px)}.icon-inner,.ionicon,svg{display:block;height:100%;width:100%}@supports (background: -webkit-named-image(i)){:host(.icon-rtl) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}}@supports not selector(:dir(rtl)) and selector(:host-context([dir='rtl'])){:host(.icon-rtl) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}}:host(.flip-rtl):host-context([dir='rtl']) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}@supports selector(:dir(rtl)){:host(.flip-rtl:dir(rtl)) .icon-inner{-webkit-transform:scaleX(-1);transform:scaleX(-1)}:host(.flip-rtl:dir(ltr)) .icon-inner{-webkit-transform:scaleX(1);transform:scaleX(1)}}:host(.icon-small){font-size:1.125rem !important}:host(.icon-large){font-size:2rem !important}:host(.ion-color){color:var(--ion-color-base) !important}:host(.ion-color-primary){--ion-color-base:var(--ion-color-primary, #3880ff)}:host(.ion-color-secondary){--ion-color-base:var(--ion-color-secondary, #0cd1e8)}:host(.ion-color-tertiary){--ion-color-base:var(--ion-color-tertiary, #f4a942)}:host(.ion-color-success){--ion-color-base:var(--ion-color-success, #10dc60)}:host(.ion-color-warning){--ion-color-base:var(--ion-color-warning, #ffce00)}:host(.ion-color-danger){--ion-color-base:var(--ion-color-danger, #f14141)}:host(.ion-color-light){--ion-color-base:var(--ion-color-light, #f4f5f8)}:host(.ion-color-medium){--ion-color-base:var(--ion-color-medium, #989aa2)}:host(.ion-color-dark){--ion-color-base:var(--ion-color-dark, #222428)}"}},[1,"ion-icon",{mode:[1025],color:[1],ios:[1],md:[1],flipRtl:[4,"flip-rtl"],name:[513],src:[1],icon:[8],size:[1],lazy:[4],sanitize:[4],svgContent:[32],isVisible:[32]}]),Ob=()=>"undefined"!=typeof document&&document.documentElement.getAttribute("mode")||"md",Lb=e=>e?{"ion-color":!0,[`ion-color-${e}`]:!0}:null,_b=function(){"undefined"!=typeof customElements&&["ion-icon"].forEach((e=>{"ion-icon"===e&&(customElements.get(e)||customElements.define(e,Ib))}))},zb=F.createContext({onIonViewWillEnter:()=>{},ionViewWillEnter:()=>{},onIonViewDidEnter:()=>{},ionViewDidEnter:()=>{},onIonViewWillLeave:()=>{},ionViewWillLeave:()=>{},onIonViewDidLeave:()=>{},ionViewDidLeave:()=>{},cleanupIonViewWillEnter:()=>{},cleanupIonViewDidEnter:()=>{},cleanupIonViewWillLeave:()=>{},cleanupIonViewDidLeave:()=>{}}),Nb=class{constructor(){this.ionViewWillEnterCallbacks=[],this.ionViewDidEnterCallbacks=[],this.ionViewWillLeaveCallbacks=[],this.ionViewDidLeaveCallbacks=[],this.ionViewWillEnterDestructorCallbacks=[],this.ionViewDidEnterDestructorCallbacks=[],this.ionViewWillLeaveDestructorCallbacks=[],this.ionViewDidLeaveDestructorCallbacks=[]}onIonViewWillEnter(e){if(e.id){const t=this.ionViewWillEnterCallbacks.findIndex((t=>t.id===e.id));t>-1?this.ionViewWillEnterCallbacks[t]=e:this.ionViewWillEnterCallbacks.push(e)}else this.ionViewWillEnterCallbacks.push(e)}teardownCallback(e,t){const n=t.filter((t=>t.id===e.id));0!==n.length&&(n.forEach((e=>{e&&"function"==typeof e.destructor&&e.destructor()})),t=t.filter((t=>t.id!==e.id)))}cleanupIonViewWillEnter(e){this.teardownCallback(e,this.ionViewWillEnterDestructorCallbacks)}cleanupIonViewDidEnter(e){this.teardownCallback(e,this.ionViewDidEnterDestructorCallbacks)}cleanupIonViewWillLeave(e){this.teardownCallback(e,this.ionViewWillLeaveDestructorCallbacks)}cleanupIonViewDidLeave(e){this.teardownCallback(e,this.ionViewDidLeaveDestructorCallbacks)}ionViewWillEnter(){this.ionViewWillEnterCallbacks.forEach((e=>{const t=e();e.id&&this.ionViewWillEnterDestructorCallbacks.push({id:e.id,destructor:t})}))}onIonViewDidEnter(e){if(e.id){const t=this.ionViewDidEnterCallbacks.findIndex((t=>t.id===e.id));t>-1?this.ionViewDidEnterCallbacks[t]=e:this.ionViewDidEnterCallbacks.push(e)}else this.ionViewDidEnterCallbacks.push(e)}ionViewDidEnter(){this.ionViewDidEnterCallbacks.forEach((e=>{const t=e();e.id&&this.ionViewDidEnterDestructorCallbacks.push({id:e.id,destructor:t})}))}onIonViewWillLeave(e){if(e.id){const t=this.ionViewWillLeaveCallbacks.findIndex((t=>t.id===e.id));t>-1?this.ionViewWillLeaveCallbacks[t]=e:this.ionViewWillLeaveCallbacks.push(e)}else this.ionViewWillLeaveCallbacks.push(e)}ionViewWillLeave(){this.ionViewWillLeaveCallbacks.forEach((e=>{const t=e();e.id&&this.ionViewWillLeaveDestructorCallbacks.push({id:e.id,destructor:t})}))}onIonViewDidLeave(e){if(e.id){const t=this.ionViewDidLeaveCallbacks.findIndex((t=>t.id===e.id));t>-1?this.ionViewDidLeaveCallbacks[t]=e:this.ionViewDidLeaveCallbacks.push(e)}else this.ionViewDidLeaveCallbacks.push(e)}ionViewDidLeave(){this.ionViewDidLeaveCallbacks.forEach((e=>{const t=e();e.id&&this.ionViewDidLeaveDestructorCallbacks.push({id:e.id,destructor:t})})),this.componentCanBeDestroyed()}onComponentCanBeDestroyed(e){this.componentCanBeDestroyedCallback=e}componentCanBeDestroyed(){this.componentCanBeDestroyedCallback&&this.componentCanBeDestroyedCallback()}},Mb=F.createContext({getIonRedirect:()=>{},getIonRoute:()=>{},getPageManager:()=>{},getStackManager:()=>{},goBack:e=>{"undefined"!=typeof window&&("string"==typeof e?window.location.pathname=e:window.history.back())},navigate:e=>{"undefined"!=typeof window&&(window.location.pathname=e)},hasIonicRouter:()=>!1,routeInfo:void 0,setCurrentTab:()=>{},changeTab:(e,t)=>{"undefined"!=typeof window&&(window.location.pathname=t)},resetTab:(e,t)=>{"undefined"!=typeof window&&(window.location.pathname=t)}}),jb=e=>e.replace(/([A-Z])/g,(e=>`-${e[0].toLowerCase()}`)),Db=(e,t,n)=>{const o=t.className||t.class,r=n.className||n.class,i=Vb(e),a=Vb(o?o.split(" "):[]),l=Vb(r?r.split(" "):[]),s=[];return i.forEach((e=>{a.has(e)?(s.push(e),a.delete(e)):l.has(e)||s.push(e)})),a.forEach((e=>s.push(e))),s.join(" ")},Ab=e=>{if("undefined"==typeof document)return!0;{const t="on"+(e=>"doubleclick"===e?"dblclick":e)(e);let n=t in document;if(!n){const e=document.createElement("div");e.setAttribute(t,"return;"),n="function"==typeof e[t]}return n}},Bb=(e,t,n)=>{const o=e.__events||(e.__events={}),r=o[t];r&&e.removeEventListener(t,r),e.addEventListener(t,o[t]=function(e){n&&n.call(this,e)})},Vb=e=>{const t=new Map;return e.forEach((e=>t.set(e,e))),t},Hb=(...e)=>t=>{e.forEach((e=>{((e,t)=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})(e,t)}))},Fb=(e,t,n,o)=>{void 0!==o&&o();const r=e.toLowerCase().split("-").map((e=>e.charAt(0).toUpperCase()+e.slice(1))).join("");return((e,t)=>{const n=(t,n)=>F.createElement(e,Object.assign({},t,{forwardedRef:n}));return n.displayName=t,F.forwardRef(n)})(class extends F.Component{constructor(e){super(e),this.setComponentElRef=e=>{this.componentEl=e}}componentDidMount(){this.componentDidUpdate(this.props)}componentDidUpdate(e){((e,t,n={})=>{if(e instanceof Element){const o=Db(e.classList,t,n);""!==o&&(e.className=o),Object.keys(t).forEach((n=>{if("children"!==n&&"style"!==n&&"ref"!==n&&"class"!==n&&"className"!==n&&"forwardedRef"!==n)if(0===n.indexOf("on")&&n[2]===n[2].toUpperCase()){const o=n.substring(2),r=o[0].toLowerCase()+o.substring(1);Ab(r)||Bb(e,r,t[n])}else e[n]=t[n],"string"==typeof t[n]&&e.setAttribute(jb(n),t[n])}))}})(this.componentEl,this.props,e)}render(){const t=this.props,{children:n,forwardedRef:o,style:r,className:i,ref:a}=t,l=hg(t,["children","forwardedRef","style","className","ref"]);let s=Object.keys(l).reduce(((e,t)=>{const n=l[t];if(0===t.indexOf("on")&&t[2]===t[2].toUpperCase()){const o=t.substring(2).toLowerCase();"undefined"!=typeof document&&Ab(o)&&(e[t]=n)}else{const o=typeof n;"string"!==o&&"boolean"!==o&&"number"!==o||(e[jb(t)]=n)}return e}),{});const c=Object.assign(Object.assign({},s),{ref:Hb(o,this.setComponentElRef),style:r});return H.createElement(e,c,n)}static get displayName(){return r}},r)},Wb=Fb("ion-content",0,0,Sg),Ub=Fb("ion-header",0,0,Ug),qb=Fb("ion-label",0,0,Qg),Qb=Fb("ion-tab",0,0,nb),Kb=Fb("ion-title",0,0,ob),Yb=Fb("ion-toolbar",0,0,rb),Xb=(e,t)=>{const n=(t,n)=>F.createElement(e,Object.assign({},t,{forwardedRef:n}));return n.displayName=t,F.forwardRef(n)},Gb=()=>{if("undefined"!=typeof window){const e=window.Ionic;if(e&&e.config)return e.config}return null},Zb=F.createContext({addOverlay:()=>{},removeOverlay:()=>{}}),Jb=({onAddOverlay:e,onRemoveOverlay:t})=>{const[n,o]=H.useState({}),r=H.useRef({});H.useEffect((()=>{e(i),t(a)}),[]);const i=(e,t,n)=>{const i=Object.assign({},r.current);i[e]={component:t,containerElement:n},r.current=i,o(i)},a=e=>{const t=Object.assign({},r.current);delete t[e],r.current=t,o(t)},l=Object.keys(n);return F.createElement(F.Fragment,null,l.map((e=>{const t=n[e];return id.createPortal(t.component,t.containerElement,`overlay-${e}`)})))},ev=Fb("ion-tab-button",0,0,hb),tv=Fb("ion-tab-bar",0,0,fb),nv=Fb("ion-tabs",0,0,bb),ov=Fb("ion-router-outlet",0,0,ub),rv=Fb("ion-app",0,0,sb),iv=Fb("ion-icon",0,0,_b),av=(()=>class extends F.Component{constructor(e){super(e),this.ionContext={addOverlay:(e,t,n)=>{this.addOverlayCallback&&this.addOverlayCallback(e,t,n)},removeOverlay:e=>{this.removeOverlayCallback&&this.removeOverlayCallback(e)}}}render(){return F.createElement(Zb.Provider,{value:this.ionContext},F.createElement(rv,Object.assign({},this.props),this.props.children),F.createElement(Jb,{onAddOverlay:e=>{this.addOverlayCallback=e},onRemoveOverlay:e=>{this.removeOverlayCallback=e}}))}static get displayName(){return"IonApp"}})(),lv=F.createContext({registerIonPage:()=>{},isInOutlet:()=>!1});class sv extends F.PureComponent{constructor(e){super(e),this.ionPageElementRef=F.createRef(),this.stableMergedRefs=Hb(this.ionPageElementRef,this.props.forwardedRef),this.ionViewWillEnterHandler=this.ionViewWillEnterHandler.bind(this),this.ionViewDidEnterHandler=this.ionViewDidEnterHandler.bind(this),this.ionViewWillLeaveHandler=this.ionViewWillLeaveHandler.bind(this),this.ionViewDidLeaveHandler=this.ionViewDidLeaveHandler.bind(this)}componentDidMount(){this.ionPageElementRef.current&&(this.context.isInOutlet()&&this.ionPageElementRef.current.classList.add("ion-page-invisible"),this.context.registerIonPage(this.ionPageElementRef.current,this.props.routeInfo),this.ionPageElementRef.current.addEventListener("ionViewWillEnter",this.ionViewWillEnterHandler),this.ionPageElementRef.current.addEventListener("ionViewDidEnter",this.ionViewDidEnterHandler),this.ionPageElementRef.current.addEventListener("ionViewWillLeave",this.ionViewWillLeaveHandler),this.ionPageElementRef.current.addEventListener("ionViewDidLeave",this.ionViewDidLeaveHandler))}componentWillUnmount(){this.ionPageElementRef.current&&(this.ionPageElementRef.current.removeEventListener("ionViewWillEnter",this.ionViewWillEnterHandler),this.ionPageElementRef.current.removeEventListener("ionViewDidEnter",this.ionViewDidEnterHandler),this.ionPageElementRef.current.removeEventListener("ionViewWillLeave",this.ionViewWillLeaveHandler))}ionViewWillEnterHandler(){this.ionLifeCycleContext.ionViewWillEnter()}ionViewDidEnterHandler(){this.ionLifeCycleContext.ionViewDidEnter()}ionViewWillLeaveHandler(){this.ionLifeCycleContext.ionViewWillLeave()}ionViewDidLeaveHandler(){this.ionLifeCycleContext.ionViewDidLeave()}render(){const e=this.props,{className:t,children:n,routeInfo:o,forwardedRef:r}=e,i=hg(e,["className","children","routeInfo","forwardedRef"]);return F.createElement(zb.Consumer,null,(e=>(this.ionLifeCycleContext=e,F.createElement("div",Object.assign({className:t?`${t} ion-page`:"ion-page",ref:this.stableMergedRefs},i),n))))}static get contextType(){return lv}}class cv extends F.Component{constructor(e){super(e)}render(){const e=this.props,{className:t,children:n,forwardedRef:o}=e,r=hg(e,["className","children","forwardedRef"]);return this.context.hasIonicRouter()?F.createElement(sv,Object.assign({className:t?`${t}`:"",routeInfo:this.context.routeInfo,forwardedRef:o},r),n):F.createElement("div",Object.assign({className:t?`ion-page ${t}`:"ion-page",ref:o},r),n)}static get displayName(){return"IonPage"}static get contextType(){return Mb}}const uv=Xb(cv,"IonPage"),dv={main:0},fv=(e="main")=>{var t;const n=(null!==(t=dv[e])&&void 0!==t?t:0)+1;return dv[e]=n,n.toString()},pv=Fb("ion-nav",0,0,Zg);Xb((e=>{var{children:t,forwardedRef:n}=e,o=hg(e,["children","forwardedRef"]);const[r,i]=H.useState([]),a=e=>i((t=>[...t,e])),l=e=>i((t=>t.filter((t=>t!==e)))),s=H.useMemo((()=>((e,t)=>{const n=new WeakMap,o=`react-delegate-${fv()}`;let r=0;return{attachViewToDom:async(t,i,a,l)=>{const s=document.createElement("div");l&&s.classList.add(...l),t.appendChild(s);const c=i(a),u=`${o}-${r++}`,d=rd.createPortal(c,s,u);return n.set(s,d),e(d),Promise.resolve(s)},removeViewFromDom:(e,o)=>{const r=n.get(o);return r&&t(r),o.remove(),Promise.resolve()}}})(a,l)),[]);return F.createElement(pv,Object.assign({delegate:s,ref:n},o),r)}),"IonNav");const hv=F.createContext({activeTab:void 0,selectTab:()=>!1,hasRouterOutlet:!1,tabBarProps:{ref:F.createRef()}}),mv="undefined"!=typeof HTMLElement?HTMLElement:class{};class gv extends F.Component{constructor(e){super(e),this.outletIsReady=!1,this.ionViewWillEnterHandler=this.ionViewWillEnterHandler.bind(this),this.ionViewDidEnterHandler=this.ionViewDidEnterHandler.bind(this),this.ionViewWillLeaveHandler=this.ionViewWillLeaveHandler.bind(this),this.ionViewDidLeaveHandler=this.ionViewDidLeaveHandler.bind(this)}componentDidMount(){this.ionRouterOutlet&&(this.outletIsReady||Tm(this.ionRouterOutlet,(()=>{this.outletIsReady=!0,this.context.registerIonPage(this.ionRouterOutlet,this.props.routeInfo)})),this.ionRouterOutlet.addEventListener("ionViewWillEnter",this.ionViewWillEnterHandler),this.ionRouterOutlet.addEventListener("ionViewDidEnter",this.ionViewDidEnterHandler),this.ionRouterOutlet.addEventListener("ionViewWillLeave",this.ionViewWillLeaveHandler),this.ionRouterOutlet.addEventListener("ionViewDidLeave",this.ionViewDidLeaveHandler))}componentWillUnmount(){this.ionRouterOutlet&&(this.ionRouterOutlet.removeEventListener("ionViewWillEnter",this.ionViewWillEnterHandler),this.ionRouterOutlet.removeEventListener("ionViewDidEnter",this.ionViewDidEnterHandler),this.ionRouterOutlet.removeEventListener("ionViewWillLeave",this.ionViewWillLeaveHandler),this.ionRouterOutlet.removeEventListener("ionViewDidLeave",this.ionViewDidLeaveHandler))}ionViewWillEnterHandler(){this.ionLifeCycleContext.ionViewWillEnter()}ionViewDidEnterHandler(){this.ionLifeCycleContext.ionViewDidEnter()}ionViewWillLeaveHandler(){this.ionLifeCycleContext.ionViewWillLeave()}ionViewDidLeaveHandler(){this.ionLifeCycleContext.ionViewDidLeave()}render(){const e=this.props,{StackManager:t,children:n,routeInfo:o}=e,r=hg(e,["StackManager","children","routeInfo"]);return F.createElement(zb.Consumer,null,(e=>(this.ionLifeCycleContext=e,F.createElement(t,{routeInfo:o},F.createElement(ov,Object.assign({setRef:e=>this.ionRouterOutlet=e},r),n)))))}static get contextType(){return lv}}class bv extends F.Component{constructor(e){super(e)}render(){const e=this.context.getStackManager(),t=this.props,{children:n,forwardedRef:o}=t,r=hg(t,["children","forwardedRef"]);return this.context.hasIonicRouter()?r.ionPage?F.createElement(gv,Object.assign({StackManager:e,routeInfo:this.context.routeInfo},r),n):F.createElement(e,{routeInfo:this.context.routeInfo},F.createElement(ov,Object.assign({},r,{forwardedRef:o}),n)):F.createElement(ov,Object.assign({ref:o},this.props),this.props.children)}static get contextType(){return Mb}}const vv=Xb(bv,"IonRouterOutlet");class yv extends mv{constructor(){super()}}"undefined"!=typeof window&&window.customElements&&(window.customElements.get("ion-tabs")||window.customElements.define("ion-tabs",yv));const wv=(()=>class extends F.Component{constructor(e){super(e),this.routerOutletRef=F.createRef(),this.tabBarRef=F.createRef(),this.ionTabContextState={activeTab:void 0,selectTab:()=>!1,hasRouterOutlet:!1,tabBarProps:{ref:this.tabBarRef}}}componentDidMount(){this.tabBarRef.current&&(this.ionTabContextState.activeTab=this.tabBarRef.current.state.activeTab,this.tabBarRef.current.setActiveTabOnContext=e=>{this.ionTabContextState.activeTab=e},this.ionTabContextState.selectTab=this.tabBarRef.current.selectTab)}renderTabsInner(e,t){return F.createElement(nv,Object.assign({},this.props),F.Children.map(e,(e=>F.isValidElement(e)&&(e.type===vv||e.type.isRouterOutlet||e.type===H.Fragment&&e.props.children[0].type===vv)?t:e)))}render(){let e,t=!1;const n=this.props,{className:o,onIonTabsDidChange:r,onIonTabsWillChange:i}=n,a=hg(n,["className","onIonTabsDidChange","onIonTabsWillChange"]),l="function"==typeof this.props.children?this.props.children(this.ionTabContextState):this.props.children;if(F.Children.forEach(l,(n=>{if(null==n||"object"!=typeof n||!n.hasOwnProperty("type"))return;n.type===vv||n.type.isRouterOutlet?e=F.cloneElement(n):n.type===H.Fragment&&n.props.children[0].type===vv?e=F.cloneElement(n.props.children[0]):n.type===Qb&&(t=!0),this.ionTabContextState.hasRouterOutlet=!!e;let o=Object.assign({},this.ionTabContextState.tabBarProps);void 0!==r&&(o=Object.assign(Object.assign({},o),{onIonTabsDidChange:r})),void 0!==i&&(o=Object.assign(Object.assign({},o),{onIonTabsWillChange:i})),this.ionTabContextState.tabBarProps=o})),!e&&!t)throw new Error("IonTabs must contain an IonRouterOutlet or an IonTab");if(e&&t)throw new Error("IonTabs cannot contain an IonRouterOutlet and an IonTab at the same time");return t?F.createElement(nv,Object.assign({},this.props)):F.createElement(hv.Provider,{value:this.ionTabContextState},this.context.hasIonicRouter()?F.createElement(sv,Object.assign({className:o?`${o}`:"",routeInfo:this.context.routeInfo},a),this.renderTabsInner(l,e)):this.renderTabsInner(l,e))}static get contextType(){return Mb}})(),xv=(()=>class extends F.Component{constructor(e){super(e),this.handleIonTabButtonClick=this.handleIonTabButtonClick.bind(this)}handleIonTabButtonClick(){this.props.onClick&&this.props.onClick(new CustomEvent("ionTabButtonClick",{detail:{tab:this.props.tab,href:this.props.href,routeOptions:this.props.routerOptions}}))}render(){const e=hg(this.props,["onClick"]);return F.createElement(ev,Object.assign({onIonTabButtonClick:this.handleIonTabButtonClick},e))}static get displayName(){return"IonTabButton"}})();class kv extends F.PureComponent{constructor(e){super(e),this.setActiveTabOnContext=e=>{};const t={};F.Children.forEach(e.children,(n=>{var o,r,i,a;null!=n&&"object"==typeof n&&n.props&&(n.type===xv||n.type.isTabButton)&&(t[n.props.tab]={originalHref:n.props.href,currentHref:n.props.href,originalRouteOptions:n.props.href===(null===(o=e.routeInfo)||void 0===o?void 0:o.pathname)?null===(r=e.routeInfo)||void 0===r?void 0:r.routeOptions:void 0,currentRouteOptions:n.props.href===(null===(i=e.routeInfo)||void 0===i?void 0:i.pathname)?null===(a=e.routeInfo)||void 0===a?void 0:a.routeOptions:void 0})})),this.state={tabs:t},this.onTabButtonClick=this.onTabButtonClick.bind(this),this.renderTabButton=this.renderTabButton.bind(this),this.setActiveTabOnContext=this.setActiveTabOnContext.bind(this),this.selectTab=this.selectTab.bind(this)}componentDidMount(){const e=this.state.tabs,t=Object.keys(e).find((t=>{const n=e[t].originalHref;return this.props.routeInfo.pathname.startsWith(n)}));t&&this.setState({activeTab:t})}componentDidUpdate(){this.state.activeTab&&this.setActiveTabOnContext(this.state.activeTab)}selectTab(e){const t=this.state.tabs[e];return!!t&&(this.onTabButtonClick(new CustomEvent("ionTabButtonClick",{detail:{href:t.currentHref,tab:e,selected:e===this.state.activeTab,routeOptions:void 0}})),!0)}static getDerivedStateFromProps(e,t){var n,o,r;const i=Object.assign({},t.tabs),a=Object.keys(t.tabs).find((n=>{const o=t.tabs[n].originalHref;return e.routeInfo.pathname.startsWith(o)}));F.Children.forEach(e.children,(e=>{if(null!=e&&"object"==typeof e&&e.props&&(e.type===xv||e.type.isTabButton)){const t=i[e.props.tab];t&&t.originalHref===e.props.href||(i[e.props.tab]={originalHref:e.props.href,currentHref:e.props.href,originalRouteOptions:e.props.routeOptions,currentRouteOptions:e.props.routeOptions})}}));const{activeTab:l}=t;if(a&&l){const s=t.tabs[l].currentHref,c=t.tabs[l].currentRouteOptions;a===l&&s===(null===(n=e.routeInfo)||void 0===n?void 0:n.pathname)&&c===(null===(o=e.routeInfo)||void 0===o?void 0:o.routeOptions)||(i[a]={originalHref:i[a].originalHref,currentHref:e.routeInfo.pathname+(e.routeInfo.search||""),originalRouteOptions:i[a].originalRouteOptions,currentRouteOptions:null===(r=e.routeInfo)||void 0===r?void 0:r.routeOptions},"pop"===e.routeInfo.routeAction&&a!==l&&(i[l]={originalHref:i[l].originalHref,currentHref:i[l].originalHref,originalRouteOptions:i[l].originalRouteOptions,currentRouteOptions:i[l].currentRouteOptions}))}return a&&e.onSetCurrentTab(a,e.routeInfo),{activeTab:a,tabs:i}}onTabButtonClick(e,t){var n;const o=this.state.tabs[e.detail.tab],r=o.originalHref,i=null===(n=this.props.tabsContext)||void 0===n?void 0:n.hasRouterOutlet,a=i?e.detail.href:"",{activeTab:l}=this.state;t&&t(e),l===e.detail.tab?r!==a&&this.context.resetTab(e.detail.tab,r,o.originalRouteOptions):(this.props.onIonTabsWillChange&&this.props.onIonTabsWillChange(new CustomEvent("ionTabWillChange",{detail:{tab:e.detail.tab}})),this.props.onIonTabsDidChange&&this.props.onIonTabsDidChange(new CustomEvent("ionTabDidChange",{detail:{tab:e.detail.tab}})),i&&(this.setActiveTabOnContext(e.detail.tab),this.context.changeTab(e.detail.tab,a,e.detail.routeOptions)))}renderTabButton(e){return t=>{var n,o;if(null!=t&&t.props&&(t.type===xv||t.type.isTabButton)){const r=t.props.tab===e?null===(n=this.props.routeInfo)||void 0===n?void 0:n.pathname:this.state.tabs[t.props.tab].currentHref,i=t.props.tab===e?null===(o=this.props.routeInfo)||void 0===o?void 0:o.routeOptions:this.state.tabs[t.props.tab].currentRouteOptions;return F.cloneElement(t,{href:r,routeOptions:i,onClick:e=>this.onTabButtonClick(e,t.props.onClick)})}return null}}render(){const{activeTab:e}=this.state;return F.createElement(tv,Object.assign({},this.props,{selectedTab:e}),F.Children.map(this.props.children,this.renderTabButton(e)))}static get contextType(){return Mb}}const Ev=F.memo((e=>{var{forwardedRef:t}=e,n=hg(e,["forwardedRef"]);const o=H.useContext(Mb),r=H.useContext(hv),i=t||r.tabBarProps.ref,a=Object.assign(Object.assign({},r.tabBarProps),{ref:i});return F.createElement(kv,Object.assign({ref:i},n,{routeInfo:n.routeInfo||o.routeInfo||{pathname:window.location.pathname},onSetCurrentTab:o.setCurrentTab,tabsContext:Object.assign(Object.assign({},r),{tabBarProps:a})}),n.children)})),Sv=Xb(Ev,"IonTabBar");class Cv extends F.PureComponent{constructor(e){super(e),this.props.name&&console.warn('In Ionic React, you import icons from "ionicons/icons" and set the icon you imported to the "icon" property. Setting the "name" property has no effect.')}render(){var e,t;const n=this.props,{icon:o,ios:r,md:i,mode:a}=n,l=hg(n,["icon","ios","md","mode"]);let s;const c=Gb(),u=a||(null==c?void 0:c.get("mode"));return s=r||i?"ios"===u?null!==(e=null!=r?r:i)&&void 0!==e?e:o:null!==(t=null!=i?i:r)&&void 0!==t?t:o:o,F.createElement(iv,Object.assign({ref:this.props.forwardedRef,icon:s},l),this.props.children)}static get contextType(){return Mb}}const $v=Xb(Cv,"IonIcon");class Tv extends F.PureComponent{render(){const e=this.context.getIonRoute();return this.context.hasIonicRouter()&&Tv?F.createElement(e,Object.assign({},this.props)):(console.error("You either do not have an Ionic Router package, or your router does not support using <IonRoute>"),null)}static get contextType(){return Mb}}F.PureComponent;const Pv=F.createContext({routeInfo:void 0,push:()=>{throw new Error("An Ionic Router is required for IonRouterContext")},back:()=>{throw new Error("An Ionic Router is required for IonRouterContext")},canGoBack:()=>{throw new Error("An Ionic Router is required for IonRouterContext")},nativeBack:()=>{throw new Error("An Ionic Router is required for IonRouterContext")}});F.PureComponent;const Rv=F.createContext({addViewItem:()=>{},canGoBack:()=>{},clearOutlet:()=>{},createViewItem:()=>{},findViewItemByPathname:()=>{},findLeavingViewItemByRouteInfo:()=>{},findViewItemByRouteInfo:()=>{},getChildrenToRender:()=>{},goBack:()=>{},unMountViewItem:()=>{}});class Iv extends F.Component{constructor(e){super(e),this.ionLifeCycleContext=new Nb,this._isMounted=!1,this.ionLifeCycleContext.onComponentCanBeDestroyed((()=>{this.props.mount||this._isMounted&&this.setState({show:!1},(()=>this.props.removeView()))})),this.state={show:!0}}componentDidMount(){this._isMounted=!0}componentWillUnmount(){this._isMounted=!1}render(){const{show:e}=this.state;return F.createElement(zb.Provider,{value:this.ionLifeCycleContext},e&&this.props.children)}}class Ov{constructor(){this.locationHistory=[],this.tabHistory={}}add(e){"push"===e.routeAction||null==e.routeAction?this._add(e):"pop"===e.routeAction?this._pop(e):"replace"===e.routeAction&&this._replace(e),"root"===e.routeDirection&&(this._clear(),this._add(e))}clearTabStack(e){const t=this._getRouteInfosByKey(e);t&&(t.forEach((e=>{this.locationHistory=this.locationHistory.filter((t=>t.id!==e.id))})),this.tabHistory[e]=[])}update(e){const t=this.locationHistory.findIndex((t=>t.id===e.id));t>-1&&this.locationHistory.splice(t,1,e);const n=this.tabHistory[e.tab||""];if(n){const t=n.findIndex((t=>t.id===e.id));t>-1?n.splice(t,1,e):n.push(e)}else e.tab&&(this.tabHistory[e.tab]=[e])}_add(e){const t=this._getRouteInfosByKey(e.tab);t&&(this._areRoutesEqual(t[t.length-1],e)&&t.pop(),t.push(e)),this.locationHistory.push(e)}_areRoutesEqual(e,t){return!(!e||!t)&&e.pathname===t.pathname&&e.search===t.search}_pop(e){const t=this._getRouteInfosByKey(e.tab);t&&(t.pop(),t.pop(),t.push(e)),this.locationHistory.pop(),this.locationHistory.pop(),this.locationHistory.push(e)}_replace(e){const t=this._getRouteInfosByKey(e.tab);t&&t.pop(),this.locationHistory.pop(),this._add(e)}_clear(){Object.keys(this.tabHistory).forEach((e=>this.tabHistory[e]=[])),this.locationHistory=[]}_getRouteInfosByKey(e){let t;return e&&(t=this.tabHistory[e],t||(t=this.tabHistory[e]=[])),t}getFirstRouteInfoForTab(e){const t=this._getRouteInfosByKey(e);if(t)return t[0]}getCurrentRouteInfoForTab(e){const t=this._getRouteInfosByKey(e);if(t)return t[t.length-1]}findLastLocation(e){const t=this._getRouteInfosByKey(e.tab);if(t)for(let n=t.length-2;n>=0;n--){const o=t[n];if(o&&o.pathname===e.pushedByRoute)return o}for(let n=this.locationHistory.length-2;n>=0;n--){const t=this.locationHistory[n];if(t&&t.pathname===e.pushedByRoute)return t}}previous(){return this.locationHistory[this.locationHistory.length-2]||this.locationHistory[this.locationHistory.length-1]}current(){return this.locationHistory[this.locationHistory.length-1]}canGoBack(){return this.locationHistory.length>1}}class Lv extends F.PureComponent{constructor(e){super(e),this.ionRouterContextValue={push:(e,t,n,o,r)=>{this.navigate(e,t,n,r,o)},back:e=>{this.goBack(void 0,e)},canGoBack:()=>this.props.locationHistory.canGoBack(),nativeBack:()=>this.props.onNativeBack(),routeInfo:this.props.routeInfo},this.state={goBack:this.goBack.bind(this),hasIonicRouter:()=>!0,navigate:this.navigate.bind(this),getIonRedirect:this.getIonRedirect.bind(this),getIonRoute:this.getIonRoute.bind(this),getStackManager:this.getStackManager.bind(this),getPageManager:this.getPageManager.bind(this),routeInfo:this.props.routeInfo,setCurrentTab:this.props.onSetCurrentTab,changeTab:this.props.onChangeTab,resetTab:this.props.onResetTab}}componentDidMount(){"undefined"!=typeof document&&(this.handleHardwareBackButton=this.handleHardwareBackButton.bind(this),document.addEventListener("ionBackButton",this.handleHardwareBackButton))}componentWillUnmount(){"undefined"!=typeof document&&document.removeEventListener("ionBackButton",this.handleHardwareBackButton)}handleHardwareBackButton(e){e.detail.register(0,(e=>{this.nativeGoBack(),e()}))}goBack(e,t){this.props.onNavigateBack(e,t)}nativeGoBack(){this.props.onNativeBack()}navigate(e,t="forward",n="push",o,r,i){this.props.onNavigate(e,n,t,o,r,i)}getPageManager(){return sv}getIonRedirect(){return this.props.ionRedirect}getIonRoute(){return this.props.ionRoute}getStackManager(){return this.props.stackManager}render(){return F.createElement(Mb.Provider,{value:Object.assign(Object.assign({},this.state),{routeInfo:this.props.routeInfo})},F.createElement(Pv.Provider,{value:Object.assign(Object.assign({},this.ionRouterContextValue),{routeInfo:this.props.routeInfo})},this.props.children))}}class _v{constructor(){this.viewStacks={},this.add=this.add.bind(this),this.clear=this.clear.bind(this),this.getViewItemsForOutlet=this.getViewItemsForOutlet.bind(this),this.remove=this.remove.bind(this)}add(e){const{outletId:t}=e;this.viewStacks[t]?this.viewStacks[t].push(e):this.viewStacks[t]=[e]}clear(e){return setTimeout((()=>{delete this.viewStacks[e]}),500)}getViewItemsForOutlet(e){return this.viewStacks[e]||[]}remove(e){const{outletId:t}=e,n=this.viewStacks[t];if(n){const o=n.find((t=>t.id===e.id));o&&(o.mount=!1,this.viewStacks[t]=n.filter((e=>e.id!==o.id)))}}getStackIds(){return Object.keys(this.viewStacks)}getAllViewItems(){const e=this.getStackIds(),t=[];return e.forEach((e=>{t.push(...this.viewStacks[e])})),t}}class zv extends F.PureComponent{render(){return F.createElement(mp,Object.assign({path:this.props.path,exact:this.props.exact,render:this.props.render},void 0!==this.props.computedMatch?{computedMatch:this.props.computedMatch}:{}))}}const Nv=({pathname:e,componentProps:t})=>{const{exact:n,component:o}=t,r=hp(e,{exact:n,path:t.path||t.from,component:o});return r||!1};class Mv extends _v{constructor(){super(),this.createViewItem=this.createViewItem.bind(this),this.findViewItemByRouteInfo=this.findViewItemByRouteInfo.bind(this),this.findLeavingViewItemByRouteInfo=this.findLeavingViewItemByRouteInfo.bind(this),this.getChildrenToRender=this.getChildrenToRender.bind(this),this.findViewItemByPathname=this.findViewItemByPathname.bind(this)}createViewItem(e,t,n,o){const r={id:fv("viewItem"),outletId:e,ionPageElement:o,reactElement:t,mount:!0,ionRoute:!1};return t.type===Tv&&(r.ionRoute=!0,r.disableIonPageManagement=t.props.disableIonPageManagement),r.routeData={match:Nv({pathname:n.pathname,componentProps:t.props}),childProps:t.props},r}getChildrenToRender(e,t,n){const o=this.getViewItemsForOutlet(e);return F.Children.forEach(t.props.children,(e=>{const t=o.find((t=>jv(e,t.routeData.childProps.path||t.routeData.childProps.from)));t&&(t.reactElement=e)})),o.map((e=>{let t;if(e.ionRoute&&!e.disableIonPageManagement)t=F.createElement(Iv,{key:`view-${e.id}`,mount:e.mount,removeView:()=>this.remove(e)},F.cloneElement(e.reactElement,{computedMatch:e.routeData.match}));else{const o=jv(e.reactElement,n.pathname);t=F.createElement(Iv,{key:`view-${e.id}`,mount:e.mount,removeView:()=>this.remove(e)},F.cloneElement(e.reactElement,{computedMatch:e.routeData.match})),!o&&e.routeData.match&&(e.routeData.match=void 0,e.mount=!1)}return t}))}findViewItemByRouteInfo(e,t,n){const{viewItem:o,match:r}=this.findViewItemByPath(e.pathname,t);return(void 0===n||!0===n)&&o&&r&&(o.routeData.match=r),o}findLeavingViewItemByRouteInfo(e,t,n=!0){const{viewItem:o}=this.findViewItemByPath(e.lastPathname,t,n);return o}findViewItemByPathname(e,t){const{viewItem:n}=this.findViewItemByPath(e,t);return n}findViewItemByPath(e,t,n){let o,r,i;if(t)i=this.getViewItemsForOutlet(t),i.some(a),o||i.some(l);else{const e=this.getAllViewItems();e.some(a),o||e.some(l)}return{viewItem:o,match:r};function a(t){var i,a;if(n&&!t.ionRoute)return!1;if(r=Nv({pathname:e,componentProps:t.routeData.childProps}),r){const e=r.path.includes(":");if(!e||e&&r.url===(null===(a=null===(i=t.routeData)||void 0===i?void 0:i.match)||void 0===a?void 0:a.url))return o=t,!0}return!1}function l(t){return!t.routeData.childProps.path&&!t.routeData.childProps.from&&(r={path:e,url:e,isExact:!0,params:{}},o=t,!0)}}}function jv(e,t){return Nv({pathname:t,componentProps:e.props})}const Dv=e=>!e.classList.contains("ion-page-invisible")&&!e.classList.contains("ion-page-hidden");class Av extends F.PureComponent{constructor(e){super(e),this.stackContextValue={registerIonPage:this.registerIonPage.bind(this),isInOutlet:()=>!0},this.pendingPageTransition=!1,this.registerIonPage=this.registerIonPage.bind(this),this.transitionPage=this.transitionPage.bind(this),this.handlePageTransition=this.handlePageTransition.bind(this),this.id=fv("routerOutlet"),this.prevProps=void 0,this.skipTransition=!1}componentDidMount(){this.clearOutletTimeout&&clearTimeout(this.clearOutletTimeout),this.routerOutletElement&&(this.setupRouterOutlet(this.routerOutletElement),this.handlePageTransition(this.props.routeInfo))}componentDidUpdate(e){const{pathname:t}=this.props.routeInfo,{pathname:n}=e.routeInfo;t!==n?(this.prevProps=e,this.handlePageTransition(this.props.routeInfo)):this.pendingPageTransition&&(this.handlePageTransition(this.props.routeInfo),this.pendingPageTransition=!1)}componentWillUnmount(){this.clearOutletTimeout=this.context.clearOutlet(this.id)}async handlePageTransition(e){var t,n;if(this.routerOutletElement&&this.routerOutletElement.commit){let o=this.context.findViewItemByRouteInfo(e,this.id),r=this.context.findLeavingViewItemByRouteInfo(e,this.id);!r&&e.prevRouteLastPathname&&(r=this.context.findViewItemByPathname(e.prevRouteLastPathname,this.id)),r&&("replace"===e.routeAction?r.mount=!1:"push"!==e.routeAction||"forward"!==e.routeDirection?"none"!==e.routeDirection&&o!==r&&(r.mount=!1):(null===(t=e.routeOptions)||void 0===t?void 0:t.unmount)&&(r.mount=!1));const i=function(e,t){let n;return F.Children.forEach(e,(e=>{Nv({pathname:t.pathname,componentProps:e.props})&&(n=e)})),n||(F.Children.forEach(e,(e=>{e.props.path||e.props.from||(n=e)})),n)}(null===(n=this.ionRouterOutlet)||void 0===n?void 0:n.props.children,e);if(o?o.reactElement=i:i&&(o=this.context.createViewItem(this.id,i,e),this.context.addViewItem(o)),o&&o.ionPageElement){if(o===r&&o.routeData.match.url!==e.pathname)return;if(!r&&this.props.routeInfo.prevRouteLastPathname&&(r=this.context.findViewItemByPathname(this.props.routeInfo.prevRouteLastPathname,this.id)),Dv(o.ionPageElement)&&void 0!==r&&!Dv(r.ionPageElement))return;this.transitionPage(e,o,r)}else!r||i||o||r.ionPageElement&&(r.ionPageElement.classList.add("ion-page-hidden"),r.ionPageElement.setAttribute("aria-hidden","true"));this.forceUpdate()}else this.pendingPageTransition=!0}registerIonPage(e,t){const n=this.context.findViewItemByRouteInfo(t,this.id);if(n){const t=n.ionPageElement;if(n.ionPageElement=e,n.ionRoute=!0,t===e)return}this.handlePageTransition(t)}async setupRouterOutlet(e){e.swipeHandler={canStart:()=>{const t=Gb();if(!t||!t.get("swipeBackEnabled","ios"===e.mode))return!1;const{routeInfo:n}=this.props,o=this.prevProps&&this.prevProps.routeInfo.pathname===n.pushedByRoute?this.prevProps.routeInfo:{pathname:n.pushedByRoute||""},r=this.context.findViewItemByRouteInfo(o,this.id,!1);return!!r&&r.mount&&r.routeData.match.path!==n.pathname},onStart:async()=>{const{routeInfo:e}=this.props,t=this.prevProps&&this.prevProps.routeInfo.pathname===e.pushedByRoute?this.prevProps.routeInfo:{pathname:e.pushedByRoute||""},n=this.context.findViewItemByRouteInfo(t,this.id,!1),o=this.context.findViewItemByRouteInfo(e,this.id,!1);return n&&o&&await this.transitionPage(e,n,o,"back",!0),Promise.resolve()},onEnd:e=>{if(e)this.skipTransition=!0,this.context.goBack();else{const{routeInfo:e}=this.props,t=this.prevProps&&this.prevProps.routeInfo.pathname===e.pushedByRoute?this.prevProps.routeInfo:{pathname:e.pushedByRoute||""},n=this.context.findViewItemByRouteInfo(t,this.id,!1);if(n!==this.context.findViewItemByRouteInfo(e,this.id,!1)&&void 0!==(null==n?void 0:n.ionPageElement)){const{ionPageElement:e}=n;e.setAttribute("aria-hidden","true"),e.classList.add("ion-page-hidden")}}}}}async transitionPage(e,t,n,o,r=!1){const i=async(t,n)=>{const o=this.skipTransition;o?this.skipTransition=!1:(t.classList.add("ion-page"),t.classList.add("ion-page-invisible")),await a.commit(t,n,{duration:o||void 0===s?0:void 0,direction:s,showGoBack:!!e.pushedByRoute,progressAnimation:r,animationBuilder:e.routeAnimation})},a=this.routerOutletElement,l="none"===e.routeDirection||"root"===e.routeDirection?void 0:e.routeDirection,s=null!=o?o:l;if(t&&t.ionPageElement&&this.routerOutletElement)if(n&&n.ionPageElement&&t===n)if(c=n.reactElement,u=e.pathname,d=!0,Nv({pathname:u,componentProps:Object.assign(Object.assign({},c.props),{exact:d})})){const e=function(e){let t;if(t="string"==typeof e?e:e.outerHTML,document){const e=document.createElement("div");e.innerHTML=t,e.style.zIndex="";const n=e.getElementsByTagName("ion-back-button");return n[0]&&n[0].remove(),e.firstChild}}(n.ionPageElement.outerHTML);e&&(this.routerOutletElement.appendChild(e),await i(t.ionPageElement,e),this.routerOutletElement.removeChild(e))}else await i(t.ionPageElement,void 0);else await i(t.ionPageElement,null==n?void 0:n.ionPageElement),n&&n.ionPageElement&&!r&&(n.ionPageElement.classList.add("ion-page-hidden"),n.ionPageElement.setAttribute("aria-hidden","true"));var c,u,d}render(){const{children:e}=this.props,t=F.Children.only(e);this.ionRouterOutlet=t;const n=this.context.getChildrenToRender(this.id,this.ionRouterOutlet,this.props.routeInfo,(()=>{this.forceUpdate()}));return F.createElement(lv.Provider,{value:this.stackContextValue},F.cloneElement(t,{ref:e=>{t.props.setRef&&t.props.setRef(e),t.props.forwardedRef&&(t.props.forwardedRef.current=e),this.routerOutletElement=e;const{ref:n}=t;"function"==typeof n&&n(e)}},n))}static get contextType(){return Rv}}class Bv extends F.PureComponent{constructor(e){super(e),this.exitViewFromOtherOutletHandlers=[],this.locationHistory=new Ov,this.viewStack=new Mv,this.routeMangerContextState={canGoBack:()=>this.locationHistory.canGoBack(),clearOutlet:this.viewStack.clear,findViewItemByPathname:this.viewStack.findViewItemByPathname,getChildrenToRender:this.viewStack.getChildrenToRender,goBack:()=>this.handleNavigateBack(),createViewItem:this.viewStack.createViewItem,findViewItemByRouteInfo:this.viewStack.findViewItemByRouteInfo,findLeavingViewItemByRouteInfo:this.viewStack.findLeavingViewItemByRouteInfo,addViewItem:this.viewStack.add,unMountViewItem:this.viewStack.remove};const t={id:fv("routeInfo"),pathname:this.props.location.pathname,search:this.props.location.search};this.locationHistory.add(t),this.handleChangeTab=this.handleChangeTab.bind(this),this.handleResetTab=this.handleResetTab.bind(this),this.handleNativeBack=this.handleNativeBack.bind(this),this.handleNavigate=this.handleNavigate.bind(this),this.handleNavigateBack=this.handleNavigateBack.bind(this),this.props.registerHistoryListener(this.handleHistoryChange.bind(this)),this.handleSetCurrentTab=this.handleSetCurrentTab.bind(this),this.state={routeInfo:t}}handleChangeTab(e,t,n){if(!t)return;const o=this.locationHistory.getCurrentRouteInfoForTab(e),[r,i]=t.split("?");o?(this.incomingRouteParams=Object.assign(Object.assign({},o),{routeAction:"push",routeDirection:"none"}),o.pathname===r?(this.incomingRouteParams.routeOptions=n,this.props.history.push(o.pathname+(o.search||""))):(this.incomingRouteParams.pathname=r,this.incomingRouteParams.search=i?"?"+i:void 0,this.incomingRouteParams.routeOptions=n,this.props.history.push(r+(i?"?"+i:"")))):this.handleNavigate(r,"push","none",void 0,n,e)}handleHistoryChange(e,t){var n,o,r;let i;if(i=this.incomingRouteParams&&"replace"===this.incomingRouteParams.routeAction?this.locationHistory.previous():this.locationHistory.current(),i.pathname+i.search!==e.pathname){if(!this.incomingRouteParams){if("REPLACE"===t&&(this.incomingRouteParams={routeAction:"replace",routeDirection:"none",tab:this.currentTab}),"POP"===t){const e=this.locationHistory.current();if(e&&e.pushedByRoute){const t=this.locationHistory.findLastLocation(e);this.incomingRouteParams=Object.assign(Object.assign({},t),{routeAction:"pop",routeDirection:"back"})}else this.incomingRouteParams={routeAction:"pop",routeDirection:"none",tab:this.currentTab}}this.incomingRouteParams||(this.incomingRouteParams={routeAction:"push",routeDirection:(null===(n=e.state)||void 0===n?void 0:n.direction)||"forward",routeOptions:null===(o=e.state)||void 0===o?void 0:o.routerOptions,tab:this.currentTab})}let a;if(null===(r=this.incomingRouteParams)||void 0===r?void 0:r.id)a=Object.assign(Object.assign({},this.incomingRouteParams),{lastPathname:i.pathname}),this.locationHistory.add(a);else{const t="push"===this.incomingRouteParams.routeAction&&"forward"===this.incomingRouteParams.routeDirection;if(a=Object.assign(Object.assign({id:fv("routeInfo")},this.incomingRouteParams),{lastPathname:i.pathname,pathname:e.pathname,search:e.search,params:this.props.match.params,prevRouteLastPathname:i.lastPathname}),t)a.tab=i.tab,a.pushedByRoute=i.pathname;else if("pop"===a.routeAction){const e=this.locationHistory.findLastLocation(a);a.pushedByRoute=null==e?void 0:e.pushedByRoute}else if("push"===a.routeAction&&a.tab!==i.tab){const e=this.locationHistory.getCurrentRouteInfoForTab(a.tab);a.pushedByRoute=null==e?void 0:e.pushedByRoute}else if("replace"===a.routeAction){const e=this.locationHistory.current(),t=null==e?void 0:e.pushedByRoute,n=void 0!==t&&t!==a.pathname?t:a.pushedByRoute;a.lastPathname=(null==e?void 0:e.pathname)||a.lastPathname,a.prevRouteLastPathname=null==e?void 0:e.lastPathname,a.pushedByRoute=n,a.routeDirection=a.routeDirection||(null==e?void 0:e.routeDirection),a.routeAnimation=a.routeAnimation||(null==e?void 0:e.routeAnimation)}this.locationHistory.add(a)}this.setState({routeInfo:a})}this.incomingRouteParams=void 0}handleNativeBack(){const e=this.props.history;(e.goBack||e.back)()}handleNavigate(e,t,n,o,r,i){this.incomingRouteParams=Object.assign(this.incomingRouteParams||{},{routeAction:t,routeDirection:n,routeOptions:r,routeAnimation:o,tab:i}),"push"===t?this.props.history.push(e):this.props.history.replace(e)}handleNavigateBack(e="/",t){const n=Gb();e=e||n&&n.get("backButtonDefaultHref");const o=this.locationHistory.current();if(o&&o.pushedByRoute){const n=this.locationHistory.findLastLocation(o);if(n){const e=t||o.routeAnimation;if(this.incomingRouteParams=Object.assign(Object.assign({},n),{routeAction:"pop",routeDirection:"back",routeAnimation:e}),o.lastPathname===o.pushedByRoute||n.pathname===o.pushedByRoute&&""===o.tab&&""===n.tab){const e=this.props.history;(e.goBack||e.back)()}else this.handleNavigate(n.pathname+(n.search||""),"pop","back",e)}else this.handleNavigate(e,"pop","back",t)}else this.handleNavigate(e,"pop","back",t)}handleResetTab(e,t,n){const o=this.locationHistory.getFirstRouteInfoForTab(e);if(o){const e=Object.assign({},o);e.pathname=t,e.routeOptions=n,this.incomingRouteParams=Object.assign(Object.assign({},e),{routeAction:"pop",routeDirection:"back"}),this.props.history.push(e.pathname+(e.search||""))}}handleSetCurrentTab(e){this.currentTab=e;const t=Object.assign({},this.locationHistory.current());t.tab!==e&&(t.tab=e,this.locationHistory.update(t))}render(){return F.createElement(Rv.Provider,{value:this.routeMangerContextState},F.createElement(Lv,{ionRoute:zv,ionRedirect:{},stackManager:Av,routeInfo:this.state.routeInfo,onNativeBack:this.handleNativeBack,onNavigateBack:this.handleNavigateBack,onNavigate:this.handleNavigate,onSetCurrentTab:this.handleSetCurrentTab,onChangeTab:this.handleChangeTab,onResetTab:this.handleResetTab,locationHistory:this.locationHistory},this.props.children))}}const Vv=function(e){var t="withRouter("+(e.displayName||e.name)+")",n=function(t){var n=t.wrappedComponentRef,o=gf(t,["wrappedComponentRef"]);return F.createElement(ip.Consumer,null,(function(t){return t||yd(),F.createElement(e,hd({},o,t,{ref:n}))}))};return n.displayName=t,n.WrappedComponent=e,Jf(n,e)}(Bv);Vv.displayName="IonRouter";class Hv extends F.Component{constructor(e){super(e);const{history:t}=e,n=hg(e,["history"]);this.history=t||Ld(n),this.history.listen(this.handleHistoryChange.bind(this)),this.registerHistoryListener=this.registerHistoryListener.bind(this)}handleHistoryChange(e,t){const n=e.location||e,o=e.action||t;this.historyListenHandler&&this.historyListenHandler(n,o)}registerHistoryListener(e){this.historyListenHandler=e}render(){const e=this.props,{children:t}=e,n=hg(e,["children"]);return F.createElement(ap,Object.assign({history:this.history},n),F.createElement(Vv,{registerHistoryListener:this.registerHistoryListener},t))}}F.Component,F.Component;const Fv=({name:e})=>G.jsxs("div",{className:"container",children:[G.jsx("strong",{children:e}),G.jsxs("p",{children:["Explore ",G.jsx("a",{target:"_blank",rel:"noopener noreferrer",href:"https://ionicframework.com/docs/components",children:"UI Components"})]})]}),Wv=()=>G.jsxs(uv,{children:[G.jsx(Ub,{children:G.jsx(Yb,{children:G.jsx(Kb,{children:"Tab 1"})})}),G.jsxs(Wb,{fullscreen:!0,children:[G.jsx(Ub,{collapse:"condense",children:G.jsx(Yb,{children:G.jsx(Kb,{size:"large",children:"Tab 1"})})}),G.jsx(Fv,{name:"Tab 1 page"})]})]}),Uv=()=>G.jsxs(uv,{children:[G.jsx(Ub,{children:G.jsx(Yb,{children:G.jsx(Kb,{children:"Tab 2"})})}),G.jsxs(Wb,{fullscreen:!0,children:[G.jsx(Ub,{collapse:"condense",children:G.jsx(Yb,{children:G.jsx(Kb,{size:"large",children:"Tab 2"})})}),G.jsx(Fv,{name:"Tab 2 page"})]})]}),qv=()=>G.jsxs(uv,{children:[G.jsx(Ub,{children:G.jsx(Yb,{children:G.jsx(Kb,{children:"Tab 3"})})}),G.jsxs(Wb,{fullscreen:!0,children:[G.jsx(Ub,{collapse:"condense",children:G.jsx(Yb,{children:G.jsx(Kb,{size:"large",children:"Tab 3"})})}),G.jsx(Fv,{name:"Tab 3 page"})]})]});((e={})=>{"undefined"!=typeof document&&document.documentElement.classList.add("ion-ce"),Cm(Object.assign({},e))})();const Qv=()=>G.jsx(av,{children:G.jsx(Hv,{children:G.jsxs(wv,{children:[G.jsxs(vv,{children:[G.jsx(mp,{exact:!0,path:"/tab1",children:G.jsx(Wv,{})}),G.jsx(mp,{exact:!0,path:"/tab2",children:G.jsx(Uv,{})}),G.jsx(mp,{path:"/tab3",children:G.jsx(qv,{})}),G.jsx(mp,{exact:!0,path:"/",children:G.jsx(dp,{to:"/tab1"})})]}),G.jsxs(Sv,{slot:"bottom",children:[G.jsxs(xv,{tab:"tab1",href:"/tab1",children:[G.jsx($v,{"aria-hidden":"true",icon:"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M464 464H48a16 16 0 01-14.07-23.62l208-384a16 16 0 0128.14 0l208 384A16 16 0 01464 464z'/></svg>"}),G.jsx(qb,{children:"Tab 1"})]}),G.jsxs(xv,{tab:"tab2",href:"/tab2",children:[G.jsx($v,{"aria-hidden":"true",icon:"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M256 464c-114.69 0-208-93.31-208-208S141.31 48 256 48s208 93.31 208 208-93.31 208-208 208z'/></svg>"}),G.jsx(qb,{children:"Tab 2"})]}),G.jsxs(xv,{tab:"tab3",href:"/tab3",children:[G.jsx($v,{"aria-hidden":"true",icon:"data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' class='ionicon' viewBox='0 0 512 512'><path d='M416 464H96a48.05 48.05 0 01-48-48V96a48.05 48.05 0 0148-48h320a48.05 48.05 0 0148 48v320a48.05 48.05 0 01-48 48z'/></svg>"}),G.jsx(qb,{children:"Tab 3"})]})]})]})})});ad(document.getElementById("root")).render(G.jsx(F.StrictMode,{children:G.jsx(Qv,{})}))}}}));
