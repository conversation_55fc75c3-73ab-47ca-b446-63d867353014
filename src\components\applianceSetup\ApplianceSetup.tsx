//@ts-nocheck
import React, { useState, useEffect, useRef, LegacyRef, RefObject } from 'react';
import {
    Box,
    Button,
    Typography,

} from '@material-ui/core';
import './ApplianceSetup.css';
import { WifiOutlined, CloudOutlined, SettingsOutlined } from '@material-ui/icons';
import { useSelector } from 'react-redux';
import { isRoomModalOpen, selectDevices } from '../../redux/slices/home/<USER>';
import { DeviceDetails } from '../deviceSetup/BleShow';
import { createGesture, Gesture } from '@ionic/react';
import { getAllRooms } from '../../redux/slices/home/<USER>';
import AssignToRoom, { Room } from '../assignToRoom/AssignToRoom';
import ApplianceCard from './ApplianceCard';
import { Link } from 'react-router-dom';
import BottomBar from '../bottomBar';
const ApplianceSetup: React.FC<any> = () => {
    const gestureRef = useRef<HTMLDivElement>();
    const [refList, setRefList] = useState<React.RefObject<HTMLDivElement>[]>([]);
    const devices: DeviceDetails[] = useSelector(selectDevices);
    const roomModalOpen: boolean = useSelector(isRoomModalOpen);
    const allAppliances = () => {
        let appliances = devices.map((device: DeviceDetails, index: any) => {
            if (device.connectedAppliances) {
                return device.connectedAppliances.map((appliance, index) => {
                    return <ApplianceCard key={index} appliance={appliance} />
                })
            }
        })
        if (appliances.length > 0) {
            return appliances
        } else {
            return false
        }
    }


    return <>
        {/* <BottomBar /> */}
        {roomModalOpen ? <AssignToRoom /> : ""}
        <div className={'setupContainer'}>
            <div style={{ display: "flex", flexDirection: "column", gap: "0.4em" }}>
                <div className={'roomName'}>
                    Appliances
                </div>
                <div style={{ fontSize: "15px", color: "#cccccc" }}>Assign room , edit individual appliance, check network status and more </div>
            </div>
            {allAppliances() ? allAppliances() : <div style={{ color: "#cccccc" }}>Oops . Looks like you have not installed any device. <br />
                Please go to <Link to={'/deviceSetup'}>Device Setup</Link> page</div>}
        </div>
    </>
}

export default ApplianceSetup