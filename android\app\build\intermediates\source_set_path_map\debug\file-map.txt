com.wellmetrix.wellmetrixprovider.app-fragment-1.6.2-0 /Users/<USER>/.gradle/caches/8.9/transforms/15ccf7d18f3b99ff0a370aad12224208/transformed/fragment-1.6.2/res
com.wellmetrix.wellmetrixprovider.app-core-1.12.0-1 /Users/<USER>/.gradle/caches/8.9/transforms/21e8fde1c723f1784c968ebe032c8280/transformed/core-1.12.0/res
com.wellmetrix.wellmetrixprovider.app-lifecycle-runtime-2.6.1-2 /Users/<USER>/.gradle/caches/8.9/transforms/312960c2027b1b5ff2aa17a8df3a2fd5/transformed/lifecycle-runtime-2.6.1/res
com.wellmetrix.wellmetrixprovider.app-appcompat-1.6.1-3 /Users/<USER>/.gradle/caches/8.9/transforms/3c980233d99f79c1041090d50e04c5a7/transformed/appcompat-1.6.1/res
com.wellmetrix.wellmetrixprovider.app-startup-runtime-1.1.1-4 /Users/<USER>/.gradle/caches/8.9/transforms/53de2126824d2ab13a54afc6e6606798/transformed/startup-runtime-1.1.1/res
com.wellmetrix.wellmetrixprovider.app-lifecycle-livedata-2.6.1-5 /Users/<USER>/.gradle/caches/8.9/transforms/583febcf697376b8d4e0ca568a37a51c/transformed/lifecycle-livedata-2.6.1/res
com.wellmetrix.wellmetrixprovider.app-lifecycle-viewmodel-2.6.1-6 /Users/<USER>/.gradle/caches/8.9/transforms/736c682e3f7127947f4483a6408fdd17/transformed/lifecycle-viewmodel-2.6.1/res
com.wellmetrix.wellmetrixprovider.app-coordinatorlayout-1.2.0-7 /Users/<USER>/.gradle/caches/8.9/transforms/776f91b65f06607d69121e143ce8c359/transformed/coordinatorlayout-1.2.0/res
com.wellmetrix.wellmetrixprovider.app-core-ktx-1.12.0-8 /Users/<USER>/.gradle/caches/8.9/transforms/79ec201b579516878354844f987f5ed0/transformed/core-ktx-1.12.0/res
com.wellmetrix.wellmetrixprovider.app-appcompat-resources-1.6.1-9 /Users/<USER>/.gradle/caches/8.9/transforms/7bcd193b70599b60cb1ed76c3fa0557e/transformed/appcompat-resources-1.6.1/res
com.wellmetrix.wellmetrixprovider.app-lifecycle-process-2.6.1-10 /Users/<USER>/.gradle/caches/8.9/transforms/867fa9a39ff14c3e0423d818576042b9/transformed/lifecycle-process-2.6.1/res
com.wellmetrix.wellmetrixprovider.app-emoji2-views-helper-1.2.0-11 /Users/<USER>/.gradle/caches/8.9/transforms/8e799b13ca45d5408e9c9d0b1a1810cd/transformed/emoji2-views-helper-1.2.0/res
com.wellmetrix.wellmetrixprovider.app-activity-1.8.0-12 /Users/<USER>/.gradle/caches/8.9/transforms/910960877edea38849c312d3dd5ed874/transformed/activity-1.8.0/res
com.wellmetrix.wellmetrixprovider.app-lifecycle-viewmodel-savedstate-2.6.1-13 /Users/<USER>/.gradle/caches/8.9/transforms/922e7b1245459a1c4c2bf8dfb97bacd4/transformed/lifecycle-viewmodel-savedstate-2.6.1/res
com.wellmetrix.wellmetrixprovider.app-core-runtime-2.2.0-14 /Users/<USER>/.gradle/caches/8.9/transforms/943fc219c43a1e7f1addf2bd1bbe1f51/transformed/core-runtime-2.2.0/res
com.wellmetrix.wellmetrixprovider.app-lifecycle-livedata-core-2.6.1-15 /Users/<USER>/.gradle/caches/8.9/transforms/9b869aebb19cd332b818a7fa3f4bc9fc/transformed/lifecycle-livedata-core-2.6.1/res
com.wellmetrix.wellmetrixprovider.app-annotation-experimental-1.3.0-16 /Users/<USER>/.gradle/caches/8.9/transforms/ab2aff48a399814f9214da23a31c14b5/transformed/annotation-experimental-1.3.0/res
com.wellmetrix.wellmetrixprovider.app-emoji2-1.2.0-17 /Users/<USER>/.gradle/caches/8.9/transforms/ab369cbe9c847149519a00a43c557222/transformed/emoji2-1.2.0/res
com.wellmetrix.wellmetrixprovider.app-savedstate-1.2.1-18 /Users/<USER>/.gradle/caches/8.9/transforms/b6f2770203bb2abf5bcb4c95c3858318/transformed/savedstate-1.2.1/res
com.wellmetrix.wellmetrixprovider.app-core-splashscreen-1.0.1-19 /Users/<USER>/.gradle/caches/8.9/transforms/bbe10166623997ee8d7219e721c25a79/transformed/core-splashscreen-1.0.1/res
com.wellmetrix.wellmetrixprovider.app-webkit-1.9.0-20 /Users/<USER>/.gradle/caches/8.9/transforms/d02d05f8411fe2f3a154d7cfd01fa6df/transformed/webkit-1.9.0/res
com.wellmetrix.wellmetrixprovider.app-profileinstaller-1.3.0-21 /Users/<USER>/.gradle/caches/8.9/transforms/fc4ac1be7145a6709950c10772659532/transformed/profileinstaller-1.3.0/res
com.wellmetrix.wellmetrixprovider.app-pngs-22 /Users/<USER>/well-metrix-patient/android/app/build/generated/res/pngs/debug
com.wellmetrix.wellmetrixprovider.app-resValues-23 /Users/<USER>/well-metrix-patient/android/app/build/generated/res/resValues/debug
com.wellmetrix.wellmetrixprovider.app-packageDebugResources-24 /Users/<USER>/well-metrix-patient/android/app/build/intermediates/incremental/debug/packageDebugResources/merged.dir
com.wellmetrix.wellmetrixprovider.app-packageDebugResources-25 /Users/<USER>/well-metrix-patient/android/app/build/intermediates/incremental/debug/packageDebugResources/stripped.dir
com.wellmetrix.wellmetrixprovider.app-merged_res-26 /Users/<USER>/well-metrix-patient/android/app/build/intermediates/merged_res/debug
com.wellmetrix.wellmetrixprovider.app-debug-27 /Users/<USER>/well-metrix-patient/android/app/src/debug/res
com.wellmetrix.wellmetrixprovider.app-main-28 /Users/<USER>/well-metrix-patient/android/app/src/main/res
com.wellmetrix.wellmetrixprovider.app-packaged_res-29 /Users/<USER>/well-metrix-patient/android/capacitor-cordova-android-plugins/build/intermediates/packaged_res/debug
com.wellmetrix.wellmetrixprovider.app-packaged_res-30 /Users/<USER>/well-metrix-patient/node_modules/@capacitor/android/capacitor/build/intermediates/packaged_res/debug
com.wellmetrix.wellmetrixprovider.app-packaged_res-31 /Users/<USER>/well-metrix-patient/node_modules/@capacitor/app/android/build/intermediates/packaged_res/debug
com.wellmetrix.wellmetrixprovider.app-packaged_res-32 /Users/<USER>/well-metrix-patient/node_modules/@capacitor/haptics/android/build/intermediates/packaged_res/debug
com.wellmetrix.wellmetrixprovider.app-packaged_res-33 /Users/<USER>/well-metrix-patient/node_modules/@capacitor/keyboard/android/build/intermediates/packaged_res/debug
com.wellmetrix.wellmetrixprovider.app-packaged_res-34 /Users/<USER>/well-metrix-patient/node_modules/@capacitor/status-bar/android/build/intermediates/packaged_res/debug
