// src/utils/oauth2.ts
export const OAUTH2_CONFIG = {
    authUrl: "https://oidc.wellnessmetrix.com/auth/protocol/oidc/auth",
    tokenUrl: "https://oidc.wellnessmetrix.com/auth/protocol/oidc/token",
    clientId: "ExerMetrixOIDC",
    redirectUri: "https://apps.exermetrix.com/auth/protocol/oidc",
    scope: "openid email Domino.user.all",
};

export function generateCodeVerifier() {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return btoa(String.fromCharCode(...array))
        .replace(/\+/g, "-")
        .replace(/\//g, "_")
        .replace(/=/g, "");
}

export async function generateCodeChallenge(codeVerifier: string) {
    const encoder = new TextEncoder();
    const data = encoder.encode(codeVerifier);
    const digest = await crypto.subtle.digest("SHA-256", data);
    return btoa(String.fromCharCode(...new Uint8Array(digest)))
        .replace(/\+/g, "-")
        .replace(/\//g, "_")
        .replace(/=/g, "");
}

export function getAuthUrl(codeChallenge: string) {
    const params = new URLSearchParams({
        response_type: "code",
        client_id: OAUTH2_CONFIG.clientId,
        scope: OAUTH2_CONFIG.scope,
        redirect_uri: OAUTH2_CONFIG.redirectUri,
        code_challenge: codeChallenge,
        code_challenge_method: "S256",
    });

    return `${OAUTH2_CONFIG.authUrl}?${params.toString()}`;
}

export async function exchangeCodeForToken(code: string, codeVerifier: string) {
    const params = new URLSearchParams({
        grant_type: "authorization_code",
        client_id: OAUTH2_CONFIG.clientId,
        redirect_uri: OAUTH2_CONFIG.redirectUri,
        code,
        code_verifier: codeVerifier,
    });

    const response = await fetch(OAUTH2_CONFIG.tokenUrl, {
        method: "POST",
        headers: { "Content-Type": "application/x-www-form-urlencoded" },
        body: params.toString(),
    });

    if (!response.ok) throw new Error("Failed to exchange code for token");

    return await response.json();
}
