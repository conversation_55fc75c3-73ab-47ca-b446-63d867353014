//@ts-nocheck
import React, { useState, useEffect } from 'react';
import {
    <PERSON>,
    <PERSON>po<PERSON>,
    <PERSON><PERSON>,
    Card,
    CardContent,
    IconButton,
    LinearProgress,
    Chip,
    Stack,
    Alert,
    Divider
} from '@mui/material';
import {
    PlayArrow,
    Stop,
    Pause,
    ArrowBack,
    Timer,
    Assignment,
    Person,
    BluetoothConnected,
    Bluetooth
} from '@mui/icons-material';
import { useHistory, useParams } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { selectDevice, setDeviceConnected, clearDeviceState } from '../redux/slices/deviceSlice';
import { rawThemePallete } from '../theme/theme';

interface TestScreenProps {}

const TestScreen: React.FC<TestScreenProps> = () => {
    const history = useHistory();
    const params = useParams<{ patientId?: string }>();
    const dispatch = useDispatch();
    const deviceState = useSelector(selectDevice);

    // Test state
    const [isTestRunning, setIsTestRunning] = useState(false);
    const [isPaused, setIsPaused] = useState(false);
    const [testDuration, setTestDuration] = useState(0);
    const [testProgress, setTestProgress] = useState(0);

    // Dummy test data
    const testInfo = {
        title: "Core Stability Assessment",
        testId: "CSA-2024-001",
        description: "Comprehensive core stability and balance assessment using MetaWear sensors",
        estimatedDuration: "5-10 minutes",
        patientName: "John Doe",
        patientId: params.patientId || "P-001"
    };

    // Timer effect
    useEffect(() => {
        let interval: NodeJS.Timeout;
        if (isTestRunning && !isPaused) {
            interval = setInterval(() => {
                setTestDuration(prev => prev + 1);
                setTestProgress(prev => Math.min(prev + 0.5, 100)); // Simulate progress
            }, 1000);
        }
        return () => clearInterval(interval);
    }, [isTestRunning, isPaused]);

    const formatTime = (seconds: number) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    };

    const handleStartTest = () => {
        if (!deviceState.connected) {
            // Navigate to device setup if no device connected
            history.push('/devicesetup');
            return;
        }
        setIsTestRunning(true);
        setIsPaused(false);
    };

    const handleStopTest = () => {
        setIsTestRunning(false);
        setIsPaused(false);
        setTestDuration(0);
        setTestProgress(0);
    };

    const handlePauseTest = () => {
        setIsPaused(!isPaused);
    };

    const handleDeviceConnection = () => {
        if (deviceState.value && deviceState.value.id) {
            // Try to reconnect to stored device
            // This would trigger the reconnection logic in BleShow
            history.push('/devicesetup');
        } else {
            // Navigate to device setup for new connection
            history.push('/devicesetup');
        }
    };

    const getTestStatus = () => {
        if (isTestRunning && !isPaused) return { text: "Running", color: "success" };
        if (isTestRunning && isPaused) return { text: "Paused", color: "warning" };
        return { text: "Ready", color: "default" };
    };

    const status = getTestStatus();

    return (
        <Box sx={{
            padding: 3,
            maxWidth: 800,
            margin: '0 auto',
            backgroundColor: '#f5f5f5',
            minHeight: '100vh'
        }}>
            {/* Header */}
            <Stack direction="row" alignItems="center" spacing={2} sx={{ mb: 3 }}>
                <IconButton
                    onClick={() => history.goBack()}
                    sx={{
                        backgroundColor: 'white',
                        boxShadow: 1,
                        '&:hover': { backgroundColor: '#f0f0f0' }
                    }}
                >
                    <ArrowBack />
                </IconButton>
                <Typography
                    variant="h4"
                    sx={{
                        fontWeight: 'bold',
                        color: rawThemePallete.palette.primary.main,
                        flexGrow: 1
                    }}
                >
                    Test Session
                </Typography>
                <Chip
                    label={status.text}
                    color={status.color as any}
                    variant="outlined"
                    size="medium"
                />
            </Stack>

            {/* Device Connection Status */}
            <Card sx={{ mb: 3, backgroundColor: 'white', boxShadow: 2 }}>
                <CardContent>
                    <Stack direction="row" alignItems="center" justifyContent="space-between">
                        <Stack direction="row" alignItems="center" spacing={2}>
                            {deviceState.connected ? (
                                <>
                                    <BluetoothConnected sx={{ color: '#4CAF50', fontSize: 28 }} />
                                    <Box>
                                        <Typography variant="h6" sx={{ color: '#4CAF50' }}>
                                            Device Connected
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary">
                                            {deviceState.value?.name || 'MetaWear Device'}
                                        </Typography>
                                    </Box>
                                </>
                            ) : (
                                <>
                                    <Bluetooth sx={{ color: '#607D8B', fontSize: 28 }} />
                                    <Box>
                                        <Typography variant="h6" sx={{ color: '#607D8B' }}>
                                            No Device Connected
                                        </Typography>
                                        <Typography variant="body2" color="text.secondary">
                                            Connect a device to start testing
                                        </Typography>
                                    </Box>
                                </>
                            )}
                        </Stack>
                        <Button
                            variant="outlined"
                            onClick={handleDeviceConnection}
                            startIcon={deviceState.connected ? <BluetoothConnected /> : <Bluetooth />}
                            sx={{
                                borderColor: deviceState.connected ? '#4CAF50' : '#607D8B',
                                color: deviceState.connected ? '#4CAF50' : '#607D8B',
                                '&:hover': {
                                    borderColor: deviceState.connected ? '#45a049' : '#546e7a',
                                    backgroundColor: deviceState.connected ? 'rgba(76, 175, 80, 0.04)' : 'rgba(96, 125, 139, 0.04)'
                                }
                            }}
                        >
                            {deviceState.connected ? 'Reconnect' : 'Connect Device'}
                        </Button>
                    </Stack>
                </CardContent>
            </Card>

            {/* Test Information */}
            <Card sx={{ mb: 3, backgroundColor: 'white', boxShadow: 2 }}>
                <CardContent>
                    <Stack spacing={2}>
                        <Stack direction="row" alignItems="center" spacing={1}>
                            <Assignment sx={{ color: rawThemePallete.palette.primary.main }} />
                            <Typography variant="h5" sx={{ fontWeight: 'bold' }}>
                                {testInfo.title}
                            </Typography>
                        </Stack>

                        <Typography variant="body1" color="text.secondary">
                            {testInfo.description}
                        </Typography>

                        <Divider />

                        <Stack direction="row" spacing={4}>
                            <Box>
                                <Typography variant="body2" color="text.secondary">Test ID</Typography>
                                <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                                    {testInfo.testId}
                                </Typography>
                            </Box>
                            <Box>
                                <Typography variant="body2" color="text.secondary">Patient</Typography>
                                <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                                    {testInfo.patientName}
                                </Typography>
                            </Box>
                            <Box>
                                <Typography variant="body2" color="text.secondary">Estimated Duration</Typography>
                                <Typography variant="body1" sx={{ fontWeight: 'medium' }}>
                                    {testInfo.estimatedDuration}
                                </Typography>
                            </Box>
                        </Stack>
                    </Stack>
                </CardContent>
            </Card>

            {/* Test Progress */}
            {isTestRunning && (
                <Card sx={{ mb: 3, backgroundColor: 'white', boxShadow: 2 }}>
                    <CardContent>
                        <Stack spacing={2}>
                            <Stack direction="row" alignItems="center" justifyContent="space-between">
                                <Stack direction="row" alignItems="center" spacing={1}>
                                    <Timer sx={{ color: rawThemePallete.palette.primary.main }} />
                                    <Typography variant="h6">Test Progress</Typography>
                                </Stack>
                                <Typography variant="h4" sx={{ fontWeight: 'bold', color: rawThemePallete.palette.primary.main }}>
                                    {formatTime(testDuration)}
                                </Typography>
                            </Stack>
                            <LinearProgress
                                variant="determinate"
                                value={testProgress}
                                sx={{
                                    height: 8,
                                    borderRadius: 4,
                                    backgroundColor: '#e0e0e0',
                                    '& .MuiLinearProgress-bar': {
                                        backgroundColor: rawThemePallete.palette.primary.main
                                    }
                                }}
                            />
                            <Typography variant="body2" color="text.secondary" align="center">
                                {Math.round(testProgress)}% Complete
                            </Typography>
                        </Stack>
                    </CardContent>
                </Card>
            )}

            {/* Control Buttons */}
            <Card sx={{ backgroundColor: 'white', boxShadow: 2 }}>
                <CardContent>
                    <Stack direction="row" spacing={2} justifyContent="center">
                        {!isTestRunning ? (
                            <Button
                                variant="contained"
                                size="large"
                                startIcon={<PlayArrow />}
                                onClick={handleStartTest}
                                disabled={!deviceState.connected}
                                sx={{
                                    backgroundColor: rawThemePallete.palette.primary.main,
                                    color: 'white',
                                    padding: '12px 32px',
                                    fontSize: '1.1rem',
                                    '&:hover': {
                                        backgroundColor: '#4a7fff'
                                    },
                                    '&:disabled': {
                                        backgroundColor: '#cccccc'
                                    }
                                }}
                            >
                                Start Test
                            </Button>
                        ) : (
                            <>
                                <Button
                                    variant="outlined"
                                    size="large"
                                    startIcon={isPaused ? <PlayArrow /> : <Pause />}
                                    onClick={handlePauseTest}
                                    sx={{
                                        borderColor: rawThemePallete.palette.primary.main,
                                        color: rawThemePallete.palette.primary.main,
                                        padding: '12px 24px'
                                    }}
                                >
                                    {isPaused ? 'Resume' : 'Pause'}
                                </Button>
                                <Button
                                    variant="contained"
                                    size="large"
                                    startIcon={<Stop />}
                                    onClick={handleStopTest}
                                    sx={{
                                        backgroundColor: '#f44336',
                                        color: 'white',
                                        padding: '12px 24px',
                                        '&:hover': {
                                            backgroundColor: '#d32f2f'
                                        }
                                    }}
                                >
                                    Stop Test
                                </Button>
                            </>
                        )}
                    </Stack>

                    {!deviceState.connected && (
                        <Alert severity="warning" sx={{ mt: 2 }}>
                            Please connect a device before starting the test.
                        </Alert>
                    )}
                </CardContent>
            </Card>
        </Box>
    );
};

export default TestScreen;
