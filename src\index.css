@import url('https://fonts.googleapis.com/css2?family=Quicksand&display=swap');
:root,
:root[mode="md"],
:root[mode="ios"]{
    --ion-font-family:'Quicksand',sans-serif;
    font-family: var(--ion-font-family) !important;
}
*{
    font-family: var(--ion-font-family) !important;
}
html,body,#root{
    height: 100%;
    width: 100%;
    box-sizing: border-box;
    padding: 0;
    margin: 0;
    scroll-behavior: smooth;
    display: flex;
    flex-direction: column;
    background-color: #ffffff;
    overflow: scroll;
}
body{
  flex:1;
}
#root{
  flex:1;
}
.shadow1 {
    box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
    transition: all 0.3s cubic-bezier(.25,.8,.25,1);
  }

.loader,
.loader:before,
.loader:after {
  border-radius: 50%;
  width: 2.5em;
  height: 2.5em;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
  -webkit-animation: load7 1.8s infinite ease-in-out;
  animation: load7 1.8s infinite ease-in-out;
}
.loader {
  color: #000;
  font-size: 10px;
  margin: 2px auto;
  position: relative;
  text-indent: -9999em;
  -webkit-transform: translateZ(0);
  -ms-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}
.loader:before,
.loader:after {
  content: '';
  position: absolute;
  top: 0;
}
.loader:before {
  left: -3.5em;
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}
.loader:after {
  left: 3.5em;
}
@-webkit-keyframes load7 {
  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em;
  }
  40% {
    box-shadow: 0 2.5em 0 0;
  }
}
@keyframes load7 {
  0%,
  80%,
  100% {
    box-shadow: 0 2.5em 0 -1.3em;
  }
  40% {
    box-shadow: 0 2.5em 0 0;
  }
}

.route-wrapper {
  /* position: relative; */
  height: 100%;
}

.route-wrapper > div {
  /* position: absolute; */
  height: 100%;
}
.heroTitle{
  font-size: 23px;
  color: #607D8B;
  font-weight: 600;
}
.heroSubTitle{
  font-size: 15px;
  color: #cccccc;
  text-align: justify;
}
.bottomPadder{
  padding: 1.6em;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: row;
  margin-bottom: 2.6em;
}
