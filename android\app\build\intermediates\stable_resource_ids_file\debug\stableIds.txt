com.wellmetrix.wellmetrixprovider:styleable/ViewStubCompat = 0x7f0f0030
com.wellmetrix.wellmetrixprovider:styleable/View = 0x7f0f002e
com.wellmetrix.wellmetrixprovider:styleable/SwitchCompat = 0x7f0f002b
com.wellmetrix.wellmetrixprovider:styleable/PopupWindow = 0x7f0f0024
com.wellmetrix.wellmetrixprovider:styleable/MenuItem = 0x7f0f0022
com.wellmetrix.wellmetrixprovider:styleable/MenuGroup = 0x7f0f0021
com.wellmetrix.wellmetrixprovider:styleable/ListPopupWindow = 0x7f0f0020
com.wellmetrix.wellmetrixprovider:styleable/FontFamilyFont = 0x7f0f0019
com.wellmetrix.wellmetrixprovider:styleable/CoordinatorLayout_Layout = 0x7f0f0016
com.wellmetrix.wellmetrixprovider:styleable/CoordinatorLayout = 0x7f0f0015
com.wellmetrix.wellmetrixprovider:styleable/CompoundButton = 0x7f0f0014
com.wellmetrix.wellmetrixprovider:styleable/CheckedTextView = 0x7f0f0012
com.wellmetrix.wellmetrixprovider:styleable/AppCompatTextView = 0x7f0f000e
com.wellmetrix.wellmetrixprovider:styleable/AppCompatTextHelper = 0x7f0f000d
com.wellmetrix.wellmetrixprovider:styleable/AppCompatSeekBar = 0x7f0f000c
com.wellmetrix.wellmetrixprovider:styleable/AppCompatImageView = 0x7f0f000b
com.wellmetrix.wellmetrixprovider:styleable/AnimatedStateListDrawableTransition = 0x7f0f0009
com.wellmetrix.wellmetrixprovider:styleable/AnimatedStateListDrawableItem = 0x7f0f0008
com.wellmetrix.wellmetrixprovider:styleable/ActivityChooserView = 0x7f0f0005
com.wellmetrix.wellmetrixprovider:style/Widget.Support.CoordinatorLayout = 0x7f0e016b
com.wellmetrix.wellmetrixprovider:style/Widget.Compat.NotificationActionText = 0x7f0e016a
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.TextView = 0x7f0e0165
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f0e0163
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.SearchView = 0x7f0e015d
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.RatingBar.Small = 0x7f0e015c
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.RatingBar.Indicator = 0x7f0e015b
com.wellmetrix.wellmetrixprovider:styleable/AppCompatEmojiHelper = 0x7f0f000a
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.ListView = 0x7f0e0152
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Light.SearchView = 0x7f0e014e
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f0e014d
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Light.PopupMenu = 0x7f0e014c
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f0e014b
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f0e014a
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f0e0144
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f0e0140
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f0e013e
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f0e013c
com.wellmetrix.wellmetrixprovider:styleable/ColorStateListItem = 0x7f0f0013
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Light.ActionBar = 0x7f0e013a
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f0e0132
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f0e012e
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f0e012d
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Button = 0x7f0e012b
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.ActionButton.Overflow = 0x7f0e0127
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.ActionBar.TabView = 0x7f0e0124
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.ActionBar.TabText = 0x7f0e0123
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.ActionBar.TabBar = 0x7f0e0122
com.wellmetrix.wellmetrixprovider:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f0e011e
com.wellmetrix.wellmetrixprovider:style/ThemeOverlay.AppCompat.Dialog = 0x7f0e011d
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f0e0148
com.wellmetrix.wellmetrixprovider:style/ThemeOverlay.AppCompat.DayNight = 0x7f0e011b
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.ActionMode = 0x7f0e0128
com.wellmetrix.wellmetrixprovider:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f0e011a
com.wellmetrix.wellmetrixprovider:style/ThemeOverlay.AppCompat.Dark = 0x7f0e0119
com.wellmetrix.wellmetrixprovider:style/Theme.SplashScreen.IconBackground = 0x7f0e0116
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f0e0145
com.wellmetrix.wellmetrixprovider:style/Theme.SplashScreen = 0x7f0e0114
com.wellmetrix.wellmetrixprovider:style/Theme.AppCompat.Light.NoActionBar = 0x7f0e0112
com.wellmetrix.wellmetrixprovider:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f0e0111
com.wellmetrix.wellmetrixprovider:style/ThemeOverlay.AppCompat.Light = 0x7f0e011f
com.wellmetrix.wellmetrixprovider:style/Theme.AppCompat.Light.Dialog = 0x7f0e010e
com.wellmetrix.wellmetrixprovider:style/Theme.AppCompat.DialogWhenLarge = 0x7f0e010a
com.wellmetrix.wellmetrixprovider:style/Theme.AppCompat.Dialog.MinWidth = 0x7f0e0109
com.wellmetrix.wellmetrixprovider:style/Theme.AppCompat.Dialog.Alert = 0x7f0e0108
com.wellmetrix.wellmetrixprovider:style/Theme.AppCompat.Dialog = 0x7f0e0107
com.wellmetrix.wellmetrixprovider:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f0e0106
com.wellmetrix.wellmetrixprovider:style/Theme.AppCompat = 0x7f0e00fe
com.wellmetrix.wellmetrixprovider:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f0e00fd
com.wellmetrix.wellmetrixprovider:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f0e00fb
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f0e00f5
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.PopupMenu = 0x7f0e0155
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Widget.Switch = 0x7f0e00f4
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f0e00f3
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f0e00ef
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f0e00ee
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f0e00ed
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f0e00ea
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f0e00e9
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f0e00e8
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f0e00e7
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f0e00e6
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f0e00e5
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Subhead = 0x7f0e00de
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f0e00db
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f0e00d8
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Medium = 0x7f0e00d7
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f0e00d6
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f0e00d4
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Large.Inverse = 0x7f0e00d2
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Headline = 0x7f0e00cf
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Display2 = 0x7f0e00cc
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Display1 = 0x7f0e00cb
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Button = 0x7f0e00c9
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Body2 = 0x7f0e00c8
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Body1 = 0x7f0e00c7
com.wellmetrix.wellmetrixprovider:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f0e00c4
com.wellmetrix.wellmetrixprovider:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f0e00c2
com.wellmetrix.wellmetrixprovider:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f0e00c1
com.wellmetrix.wellmetrixprovider:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f0e00bd
com.wellmetrix.wellmetrixprovider:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f0e00bc
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f0e0133
com.wellmetrix.wellmetrixprovider:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f0e00bb
com.wellmetrix.wellmetrixprovider:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f0e00ba
com.wellmetrix.wellmetrixprovider:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f0e00b6
com.wellmetrix.wellmetrixprovider:style/Platform.Widget.AppCompat.Spinner = 0x7f0e00b4
com.wellmetrix.wellmetrixprovider:style/Platform.V25.AppCompat.Light = 0x7f0e00b3
com.wellmetrix.wellmetrixprovider:style/Platform.V21.AppCompat.Light = 0x7f0e00b1
com.wellmetrix.wellmetrixprovider:style/Platform.V21.AppCompat = 0x7f0e00b0
com.wellmetrix.wellmetrixprovider:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f0e00af
com.wellmetrix.wellmetrixprovider:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f0e00ae
com.wellmetrix.wellmetrixprovider:style/Platform.ThemeOverlay.AppCompat = 0x7f0e00ad
com.wellmetrix.wellmetrixprovider:style/Platform.AppCompat.Light = 0x7f0e00ac
com.wellmetrix.wellmetrixprovider:style/Platform.AppCompat = 0x7f0e00ab
com.wellmetrix.wellmetrixprovider:style/Base.v27.Theme.SplashScreen.Light = 0x7f0e00aa
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.Toolbar = 0x7f0e00a5
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.TextView = 0x7f0e00a3
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.Spinner = 0x7f0e00a1
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f0e009c
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.RatingBar = 0x7f0e009a
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f0e0099
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.PopupWindow = 0x7f0e0097
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.PopupMenu = 0x7f0e0095
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.ListView.Menu = 0x7f0e0094
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f0e0093
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.ListView = 0x7f0e0092
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.ListMenuView = 0x7f0e0090
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f0e008e
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f0e008d
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f0e009e
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f0e0088
com.wellmetrix.wellmetrixprovider:styleable/GradientColor = 0x7f0f001c
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.ImageButton = 0x7f0e0087
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.EditText = 0x7f0e0086
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f0e0084
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.SearchView.ActionBar = 0x7f0e015e
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f0e0083
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f0e0082
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f0e007f
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.ButtonBar = 0x7f0e007e
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.Button.Small = 0x7f0e007d
com.wellmetrix.wellmetrixprovider:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f0e0110
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.Button.Colored = 0x7f0e007c
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f0e007a
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.Button.Borderless = 0x7f0e0079
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.Button = 0x7f0e0078
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f0e0077
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f0e0076
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f0e0074
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f0e0073
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.ActionButton = 0x7f0e0072
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f0e0071
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f0e0070
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f0e00df
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f0e006e
com.wellmetrix.wellmetrixprovider:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f0e006c
com.wellmetrix.wellmetrixprovider:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f0e0069
com.wellmetrix.wellmetrixprovider:style/Base.V7.Theme.AppCompat = 0x7f0e0065
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f0e007b
com.wellmetrix.wellmetrixprovider:style/Base.V28.Theme.AppCompat = 0x7f0e0063
com.wellmetrix.wellmetrixprovider:style/ThemeOverlay.AppCompat = 0x7f0e0117
com.wellmetrix.wellmetrixprovider:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f0e0062
com.wellmetrix.wellmetrixprovider:style/Theme.AppCompat.DayNight.Dialog = 0x7f0e0102
com.wellmetrix.wellmetrixprovider:style/Base.V26.Theme.AppCompat = 0x7f0e0060
com.wellmetrix.wellmetrixprovider:style/Base.V23.Theme.AppCompat = 0x7f0e005e
com.wellmetrix.wellmetrixprovider:style/Base.V22.Theme.AppCompat.Light = 0x7f0e005d
com.wellmetrix.wellmetrixprovider:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f0e005b
com.wellmetrix.wellmetrixprovider:style/Base.V21.Theme.AppCompat.Light = 0x7f0e0059
com.wellmetrix.wellmetrixprovider:style/Base.V21.Theme.AppCompat.Dialog = 0x7f0e0058
com.wellmetrix.wellmetrixprovider:style/Base.ThemeOverlay.AppCompat = 0x7f0e0050
com.wellmetrix.wellmetrixprovider:style/Base.Theme.SplashScreen.Light = 0x7f0e004f
com.wellmetrix.wellmetrixprovider:style/Base.Theme.SplashScreen = 0x7f0e004d
com.wellmetrix.wellmetrixprovider:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f0e004c
com.wellmetrix.wellmetrixprovider:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f0e004b
com.wellmetrix.wellmetrixprovider:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f0e0049
com.wellmetrix.wellmetrixprovider:style/Base.Theme.AppCompat.Light.Dialog = 0x7f0e0048
com.wellmetrix.wellmetrixprovider:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f0e0047
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f0e00d3
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f0e0081
com.wellmetrix.wellmetrixprovider:style/Base.Theme.AppCompat.Light = 0x7f0e0046
com.wellmetrix.wellmetrixprovider:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f0e0045
com.wellmetrix.wellmetrixprovider:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f0e0043
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.ActivityChooserView = 0x7f0e0129
com.wellmetrix.wellmetrixprovider:style/Base.Theme.AppCompat.Dialog = 0x7f0e0041
com.wellmetrix.wellmetrixprovider:style/Base.Theme.AppCompat.CompactMenu = 0x7f0e0040
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f0e003d
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f0e003c
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f0e003a
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f0e0141
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f0e0039
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f0e0038
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Display3 = 0x7f0e00cd
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f0e0035
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.SeekBar.Discrete = 0x7f0e0160
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f0e0034
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f0e0032
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f0e0030
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f0e002f
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f0e002e
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f0e002d
com.wellmetrix.wellmetrixprovider:styleable/MenuView = 0x7f0f0023
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f0e002c
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f0e0168
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f0e002b
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f0e002a
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Title = 0x7f0e0028
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Subhead = 0x7f0e0026
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f0e0025
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Small = 0x7f0e0024
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f0e0023
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f0e0022
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f0e0021
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Menu = 0x7f0e0020
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Medium = 0x7f0e001e
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Large = 0x7f0e001a
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Headline = 0x7f0e0018
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Display4 = 0x7f0e0017
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Display3 = 0x7f0e0016
com.wellmetrix.wellmetrixprovider:style/Base.ThemeOverlay.AppCompat.Light = 0x7f0e0056
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Body1 = 0x7f0e0010
com.wellmetrix.wellmetrixprovider:style/Theme.AppCompat.CompactMenu = 0x7f0e00ff
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat = 0x7f0e000f
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat = 0x7f0e00c6
com.wellmetrix.wellmetrixprovider:style/Base.Animation.AppCompat.Tooltip = 0x7f0e000c
com.wellmetrix.wellmetrixprovider:style/Base.Animation.AppCompat.DropDownUp = 0x7f0e000b
com.wellmetrix.wellmetrixprovider:style/AppTheme.NoActionBarLaunch = 0x7f0e0007
com.wellmetrix.wellmetrixprovider:style/AlertDialog.AppCompat = 0x7f0e0000
com.wellmetrix.wellmetrixprovider:string/title_activity_main = 0x7f0d0029
com.wellmetrix.wellmetrixprovider:string/status_bar_notification_info_overflow = 0x7f0d0028
com.wellmetrix.wellmetrixprovider:string/package_name = 0x7f0d0026
com.wellmetrix.wellmetrixprovider:string/no_webview_text = 0x7f0d0025
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f0e0166
com.wellmetrix.wellmetrixprovider:string/custom_url_scheme = 0x7f0d0024
com.wellmetrix.wellmetrixprovider:string/call_notification_ongoing_text = 0x7f0d0022
com.wellmetrix.wellmetrixprovider:style/Base.v21.Theme.SplashScreen = 0x7f0e00a7
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f0e00a0
com.wellmetrix.wellmetrixprovider:string/call_notification_incoming_text = 0x7f0d0021
com.wellmetrix.wellmetrixprovider:string/call_notification_hang_up_action = 0x7f0d0020
com.wellmetrix.wellmetrixprovider:string/call_notification_decline_action = 0x7f0d001f
com.wellmetrix.wellmetrixprovider:string/call_notification_answer_video_action = 0x7f0d001e
com.wellmetrix.wellmetrixprovider:string/androidx_startup = 0x7f0d001b
com.wellmetrix.wellmetrixprovider:styleable/ActionMenuView = 0x7f0f0003
com.wellmetrix.wellmetrixprovider:string/abc_toolbar_collapse_description = 0x7f0d001a
com.wellmetrix.wellmetrixprovider:string/abc_shareactionprovider_share_with_application = 0x7f0d0019
com.wellmetrix.wellmetrixprovider:string/abc_shareactionprovider_share_with = 0x7f0d0018
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f0e00f1
com.wellmetrix.wellmetrixprovider:string/abc_searchview_description_voice = 0x7f0d0017
com.wellmetrix.wellmetrixprovider:string/abc_searchview_description_query = 0x7f0d0014
com.wellmetrix.wellmetrixprovider:string/abc_search_hint = 0x7f0d0012
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f0e008b
com.wellmetrix.wellmetrixprovider:style/Base.V7.Theme.AppCompat.Light = 0x7f0e0067
com.wellmetrix.wellmetrixprovider:string/abc_prepend_shortcut_label = 0x7f0d0011
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f0e0126
com.wellmetrix.wellmetrixprovider:string/abc_menu_space_shortcut_label = 0x7f0d000f
com.wellmetrix.wellmetrixprovider:string/abc_menu_shift_shortcut_label = 0x7f0d000e
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f0e0137
com.wellmetrix.wellmetrixprovider:string/abc_menu_meta_shortcut_label = 0x7f0d000d
com.wellmetrix.wellmetrixprovider:style/Animation.AppCompat.DropDownUp = 0x7f0e0003
com.wellmetrix.wellmetrixprovider:string/abc_menu_function_shortcut_label = 0x7f0d000c
com.wellmetrix.wellmetrixprovider:string/abc_menu_enter_shortcut_label = 0x7f0d000b
com.wellmetrix.wellmetrixprovider:string/abc_menu_ctrl_shortcut_label = 0x7f0d0009
com.wellmetrix.wellmetrixprovider:style/Base.V26.Theme.AppCompat.Light = 0x7f0e0061
com.wellmetrix.wellmetrixprovider:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f0e004a
com.wellmetrix.wellmetrixprovider:string/abc_menu_alt_shortcut_label = 0x7f0d0008
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.ActionBar = 0x7f0e006d
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f0e003e
com.wellmetrix.wellmetrixprovider:string/abc_capital_off = 0x7f0d0006
com.wellmetrix.wellmetrixprovider:string/abc_activity_chooser_view_see_all = 0x7f0d0004
com.wellmetrix.wellmetrixprovider:string/abc_action_mode_done = 0x7f0d0003
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Caption = 0x7f0e00ca
com.wellmetrix.wellmetrixprovider:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f0e0055
com.wellmetrix.wellmetrixprovider:mipmap/ic_launcher_foreground = 0x7f0c0001
com.wellmetrix.wellmetrixprovider:layout/splash_screen_view = 0x7f0b002c
com.wellmetrix.wellmetrixprovider:layout/select_dialog_multichoice_material = 0x7f0b002a
com.wellmetrix.wellmetrixprovider:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f0e011c
com.wellmetrix.wellmetrixprovider:layout/notification_template_part_time = 0x7f0b0028
com.wellmetrix.wellmetrixprovider:layout/notification_template_part_chronometer = 0x7f0b0027
com.wellmetrix.wellmetrixprovider:layout/notification_template_icon_group = 0x7f0b0026
com.wellmetrix.wellmetrixprovider:layout/notification_template_custom_big = 0x7f0b0025
com.wellmetrix.wellmetrixprovider:layout/notification_action_tombstone = 0x7f0b0024
com.wellmetrix.wellmetrixprovider:layout/notification_action = 0x7f0b0023
com.wellmetrix.wellmetrixprovider:layout/no_webview = 0x7f0b0022
com.wellmetrix.wellmetrixprovider:style/TextAppearance.Compat.Notification.Title = 0x7f0e00fa
com.wellmetrix.wellmetrixprovider:layout/bridge_layout_main = 0x7f0b001d
com.wellmetrix.wellmetrixprovider:layout/activity_main = 0x7f0b001c
com.wellmetrix.wellmetrixprovider:layout/abc_tooltip = 0x7f0b001b
com.wellmetrix.wellmetrixprovider:layout/abc_select_dialog_material = 0x7f0b001a
com.wellmetrix.wellmetrixprovider:styleable/ActionMode = 0x7f0f0004
com.wellmetrix.wellmetrixprovider:layout/abc_search_view = 0x7f0b0019
com.wellmetrix.wellmetrixprovider:layout/abc_search_dropdown_item_icons_2line = 0x7f0b0018
com.wellmetrix.wellmetrixprovider:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f0e0044
com.wellmetrix.wellmetrixprovider:layout/abc_screen_toolbar = 0x7f0b0017
com.wellmetrix.wellmetrixprovider:layout/abc_screen_content_include = 0x7f0b0014
com.wellmetrix.wellmetrixprovider:layout/abc_popup_menu_item_layout = 0x7f0b0013
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.ProgressBar = 0x7f0e0098
com.wellmetrix.wellmetrixprovider:layout/abc_list_menu_item_radio = 0x7f0b0011
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f0e0027
com.wellmetrix.wellmetrixprovider:layout/abc_list_menu_item_layout = 0x7f0b0010
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f0e008f
com.wellmetrix.wellmetrixprovider:layout/abc_list_menu_item_checkbox = 0x7f0b000e
com.wellmetrix.wellmetrixprovider:string/search_menu_title = 0x7f0d0027
com.wellmetrix.wellmetrixprovider:layout/abc_activity_chooser_view_list_item = 0x7f0b0007
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f0e0147
com.wellmetrix.wellmetrixprovider:layout/abc_activity_chooser_view = 0x7f0b0006
com.wellmetrix.wellmetrixprovider:layout/abc_action_mode_close_item_material = 0x7f0b0005
com.wellmetrix.wellmetrixprovider:layout/abc_action_mode_bar = 0x7f0b0004
com.wellmetrix.wellmetrixprovider:layout/abc_action_menu_layout = 0x7f0b0003
com.wellmetrix.wellmetrixprovider:layout/abc_action_menu_item_layout = 0x7f0b0002
com.wellmetrix.wellmetrixprovider:layout/abc_action_bar_up_container = 0x7f0b0001
com.wellmetrix.wellmetrixprovider:layout/abc_action_bar_title_item = 0x7f0b0000
com.wellmetrix.wellmetrixprovider:interpolator/fast_out_slow_in = 0x7f0a0006
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f0e006f
com.wellmetrix.wellmetrixprovider:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0a0005
com.wellmetrix.wellmetrixprovider:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0a0004
com.wellmetrix.wellmetrixprovider:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0a0003
com.wellmetrix.wellmetrixprovider:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0a0001
com.wellmetrix.wellmetrixprovider:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0a0000
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f0e00d5
com.wellmetrix.wellmetrixprovider:integer/abc_config_activityDefaultDur = 0x7f090000
com.wellmetrix.wellmetrixprovider:styleable/SearchView = 0x7f0f0027
com.wellmetrix.wellmetrixprovider:id/webview = 0x7f0800bf
com.wellmetrix.wellmetrixprovider:id/visible_removing_fragment_view_tag = 0x7f0800be
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.DrawerArrowToggle = 0x7f0e0136
com.wellmetrix.wellmetrixprovider:id/view_tree_view_model_store_owner = 0x7f0800bd
com.wellmetrix.wellmetrixprovider:styleable/Toolbar = 0x7f0f002d
com.wellmetrix.wellmetrixprovider:style/Animation.AppCompat.Dialog = 0x7f0e0002
com.wellmetrix.wellmetrixprovider:id/view_tree_saved_state_registry_owner = 0x7f0800bc
com.wellmetrix.wellmetrixprovider:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f0800bb
com.wellmetrix.wellmetrixprovider:id/view_tree_lifecycle_owner = 0x7f0800ba
com.wellmetrix.wellmetrixprovider:id/useLogo = 0x7f0800b9
com.wellmetrix.wellmetrixprovider:id/unchecked = 0x7f0800b6
com.wellmetrix.wellmetrixprovider:id/topPanel = 0x7f0800b5
com.wellmetrix.wellmetrixprovider:id/top = 0x7f0800b4
com.wellmetrix.wellmetrixprovider:id/title_template = 0x7f0800b3
com.wellmetrix.wellmetrixprovider:integer/config_tooltipAnimTime = 0x7f090003
com.wellmetrix.wellmetrixprovider:id/titleDividerNoCustom = 0x7f0800b2
com.wellmetrix.wellmetrixprovider:id/textSpacerNoButtons = 0x7f0800ad
com.wellmetrix.wellmetrixprovider:id/tag_window_insets_animation_callback = 0x7f0800aa
com.wellmetrix.wellmetrixprovider:id/tag_unhandled_key_listeners = 0x7f0800a9
com.wellmetrix.wellmetrixprovider:id/tag_on_receive_content_mime_types = 0x7f0800a4
com.wellmetrix.wellmetrixprovider:id/tag_on_receive_content_listener = 0x7f0800a3
com.wellmetrix.wellmetrixprovider:id/tag_on_apply_window_listener = 0x7f0800a2
com.wellmetrix.wellmetrixprovider:id/tag_accessibility_pane_title = 0x7f0800a1
com.wellmetrix.wellmetrixprovider:id/tag_accessibility_heading = 0x7f0800a0
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f0e0029
com.wellmetrix.wellmetrixprovider:id/tag_accessibility_clickable_spans = 0x7f08009f
com.wellmetrix.wellmetrixprovider:id/tag_accessibility_actions = 0x7f08009e
com.wellmetrix.wellmetrixprovider:id/tabMode = 0x7f08009d
com.wellmetrix.wellmetrixprovider:id/submit_area = 0x7f08009c
com.wellmetrix.wellmetrixprovider:id/submenuarrow = 0x7f08009b
com.wellmetrix.wellmetrixprovider:id/src_over = 0x7f080099
com.wellmetrix.wellmetrixprovider:id/split_action_bar = 0x7f080096
com.wellmetrix.wellmetrixprovider:id/splashscreen_icon_view = 0x7f080095
com.wellmetrix.wellmetrixprovider:id/special_effects_controller_view_tag = 0x7f080094
com.wellmetrix.wellmetrixprovider:id/showTitle = 0x7f080092
com.wellmetrix.wellmetrixprovider:xml/file_paths = 0x7f100001
com.wellmetrix.wellmetrixprovider:id/shortcut = 0x7f08008f
com.wellmetrix.wellmetrixprovider:id/select_dialog_listview = 0x7f08008e
com.wellmetrix.wellmetrixprovider:id/search_src_text = 0x7f08008c
com.wellmetrix.wellmetrixprovider:id/search_plate = 0x7f08008b
com.wellmetrix.wellmetrixprovider:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f0e00c5
com.wellmetrix.wellmetrixprovider:id/search_mag_icon = 0x7f08008a
com.wellmetrix.wellmetrixprovider:layout/abc_screen_simple_overlay_action_mode = 0x7f0b0016
com.wellmetrix.wellmetrixprovider:id/search_go_btn = 0x7f080089
com.wellmetrix.wellmetrixprovider:id/search_close_btn = 0x7f080087
com.wellmetrix.wellmetrixprovider:id/search_button = 0x7f080086
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f0e013f
com.wellmetrix.wellmetrixprovider:id/search_bar = 0x7f080085
com.wellmetrix.wellmetrixprovider:id/search_badge = 0x7f080084
com.wellmetrix.wellmetrixprovider:id/report_drawn = 0x7f08007c
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Button.Colored = 0x7f0e012f
com.wellmetrix.wellmetrixprovider:id/radio = 0x7f08007b
com.wellmetrix.wellmetrixprovider:styleable/LinearLayoutCompat = 0x7f0f001e
com.wellmetrix.wellmetrixprovider:id/tag_screen_reader_focusable = 0x7f0800a5
com.wellmetrix.wellmetrixprovider:color/material_deep_teal_500 = 0x7f05003d
com.wellmetrix.wellmetrixprovider:color/ic_launcher_background = 0x7f050038
com.wellmetrix.wellmetrixprovider:id/progress_horizontal = 0x7f08007a
com.wellmetrix.wellmetrixprovider:id/progress_circular = 0x7f080079
com.wellmetrix.wellmetrixprovider:id/on = 0x7f080077
com.wellmetrix.wellmetrixprovider:id/notification_background = 0x7f080073
com.wellmetrix.wellmetrixprovider:id/list_item = 0x7f08006c
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Title = 0x7f0e00e0
com.wellmetrix.wellmetrixprovider:id/italic = 0x7f080067
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f0e0159
com.wellmetrix.wellmetrixprovider:style/Theme.AppCompat.Light.DarkActionBar = 0x7f0e010d
com.wellmetrix.wellmetrixprovider:attr/alertDialogStyle = 0x7f030026
com.wellmetrix.wellmetrixprovider:id/notification_main_column_container = 0x7f080075
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.ActionBar = 0x7f0e0120
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Body2 = 0x7f0e0011
com.wellmetrix.wellmetrixprovider:id/home = 0x7f080060
com.wellmetrix.wellmetrixprovider:id/hide_ime_id = 0x7f08005f
com.wellmetrix.wellmetrixprovider:dimen/splashscreen_icon_size_with_background = 0x7f060076
com.wellmetrix.wellmetrixprovider:id/fragment_container_view_tag = 0x7f08005d
com.wellmetrix.wellmetrixprovider:drawable/notification_action_background = 0x7f070063
com.wellmetrix.wellmetrixprovider:id/forever = 0x7f08005c
com.wellmetrix.wellmetrixprovider:styleable/ActionBarLayout = 0x7f0f0001
com.wellmetrix.wellmetrixprovider:id/fill_horizontal = 0x7f08005a
com.wellmetrix.wellmetrixprovider:id/edit_text_id = 0x7f080055
com.wellmetrix.wellmetrixprovider:id/edit_query = 0x7f080054
com.wellmetrix.wellmetrixprovider:attr/layout = 0x7f0300a0
com.wellmetrix.wellmetrixprovider:id/default_activity_button = 0x7f080051
com.wellmetrix.wellmetrixprovider:style/Base.AlertDialog.AppCompat = 0x7f0e0008
com.wellmetrix.wellmetrixprovider:attr/trackTint = 0x7f03011e
com.wellmetrix.wellmetrixprovider:id/decor_content_parent = 0x7f080050
com.wellmetrix.wellmetrixprovider:id/collapseActionView = 0x7f08004b
com.wellmetrix.wellmetrixprovider:id/end = 0x7f080056
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f0e00e4
com.wellmetrix.wellmetrixprovider:anim/abc_slide_out_bottom = 0x7f010008
com.wellmetrix.wellmetrixprovider:id/clip_horizontal = 0x7f080049
com.wellmetrix.wellmetrixprovider:id/scrollIndicatorUp = 0x7f080082
com.wellmetrix.wellmetrixprovider:id/chronometer = 0x7f080048
com.wellmetrix.wellmetrixprovider:id/checked = 0x7f080047
com.wellmetrix.wellmetrixprovider:id/checkbox = 0x7f080046
com.wellmetrix.wellmetrixprovider:id/center_horizontal = 0x7f080044
com.wellmetrix.wellmetrixprovider:id/buttonPanel = 0x7f080042
com.wellmetrix.wellmetrixprovider:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f0e00fc
com.wellmetrix.wellmetrixprovider:id/bottom = 0x7f080041
com.wellmetrix.wellmetrixprovider:id/middle = 0x7f08006e
com.wellmetrix.wellmetrixprovider:id/action_mode_bar = 0x7f080034
com.wellmetrix.wellmetrixprovider:id/action_container = 0x7f08002e
com.wellmetrix.wellmetrixprovider:id/async = 0x7f08003e
com.wellmetrix.wellmetrixprovider:id/action_bar_title = 0x7f08002d
com.wellmetrix.wellmetrixprovider:id/action_bar_subtitle = 0x7f08002c
com.wellmetrix.wellmetrixprovider:id/action_bar_spinner = 0x7f08002b
com.wellmetrix.wellmetrixprovider:dimen/abc_control_inset_material = 0x7f060019
com.wellmetrix.wellmetrixprovider:id/action_bar_root = 0x7f08002a
com.wellmetrix.wellmetrixprovider:dimen/abc_dropdownitem_icon_width = 0x7f060029
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_22 = 0x7f080016
com.wellmetrix.wellmetrixprovider:id/action_bar_container = 0x7f080029
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.ProgressBar = 0x7f0e0158
com.wellmetrix.wellmetrixprovider:id/action_bar = 0x7f080027
com.wellmetrix.wellmetrixprovider:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f0e00c3
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_9 = 0x7f080026
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_6 = 0x7f080023
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_5 = 0x7f080022
com.wellmetrix.wellmetrixprovider:styleable/ActionMenuItemView = 0x7f0f0002
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f0e0134
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_30 = 0x7f08001f
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f0e0033
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_3 = 0x7f08001e
com.wellmetrix.wellmetrixprovider:attr/dialogPreferredPadding = 0x7f030068
com.wellmetrix.wellmetrixprovider:attr/checkMarkTint = 0x7f030048
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_28 = 0x7f08001c
com.wellmetrix.wellmetrixprovider:dimen/splashscreen_icon_size_no_background = 0x7f060075
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_25 = 0x7f080019
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_23 = 0x7f080017
com.wellmetrix.wellmetrixprovider:drawable/abc_list_selector_disabled_holo_dark = 0x7f07002d
com.wellmetrix.wellmetrixprovider:id/listMode = 0x7f08006b
com.wellmetrix.wellmetrixprovider:color/androidx_core_ripple_material_light = 0x7f05001b
com.wellmetrix.wellmetrixprovider:attr/textAppearanceListItem = 0x7f0300fa
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_21 = 0x7f080015
com.wellmetrix.wellmetrixprovider:id/time = 0x7f0800b0
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_19 = 0x7f080012
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_0 = 0x7f080007
com.wellmetrix.wellmetrixprovider:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f0e00bf
com.wellmetrix.wellmetrixprovider:id/SYM = 0x7f080005
com.wellmetrix.wellmetrixprovider:id/SHIFT = 0x7f080004
com.wellmetrix.wellmetrixprovider:id/FUNCTION = 0x7f080002
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Inverse = 0x7f0e00d0
com.wellmetrix.wellmetrixprovider:id/ALT = 0x7f080000
com.wellmetrix.wellmetrixprovider:drawable/tooltip_frame_light = 0x7f070073
com.wellmetrix.wellmetrixprovider:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f0e0053
com.wellmetrix.wellmetrixprovider:dimen/notification_small_icon_size_as_large = 0x7f06006c
com.wellmetrix.wellmetrixprovider:drawable/notify_panel_notification_icon_bg = 0x7f07006f
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f0e013d
com.wellmetrix.wellmetrixprovider:id/screen = 0x7f080080
com.wellmetrix.wellmetrixprovider:attr/colorControlNormal = 0x7f030056
com.wellmetrix.wellmetrixprovider:drawable/notification_tile_bg = 0x7f07006e
com.wellmetrix.wellmetrixprovider:id/showHome = 0x7f080091
com.wellmetrix.wellmetrixprovider:drawable/notification_template_icon_bg = 0x7f07006c
com.wellmetrix.wellmetrixprovider:drawable/notification_icon_background = 0x7f07006a
com.wellmetrix.wellmetrixprovider:style/Base.v27.Theme.SplashScreen = 0x7f0e00a9
com.wellmetrix.wellmetrixprovider:drawable/notification_bg_normal = 0x7f070068
com.wellmetrix.wellmetrixprovider:drawable/notification_bg_low_pressed = 0x7f070067
com.wellmetrix.wellmetrixprovider:layout/ime_base_split_test_activity = 0x7f0b0020
com.wellmetrix.wellmetrixprovider:attr/actionModeCloseButtonStyle = 0x7f030011
com.wellmetrix.wellmetrixprovider:drawable/notification_bg_low = 0x7f070065
com.wellmetrix.wellmetrixprovider:id/uniform = 0x7f0800b7
com.wellmetrix.wellmetrixprovider:attr/iconTintMode = 0x7f030096
com.wellmetrix.wellmetrixprovider:attr/toolbarStyle = 0x7f030119
com.wellmetrix.wellmetrixprovider:drawable/icon_background = 0x7f070062
com.wellmetrix.wellmetrixprovider:drawable/ic_launcher_foreground = 0x7f070061
com.wellmetrix.wellmetrixprovider:drawable/abc_tab_indicator_mtrl_alpha = 0x7f070045
com.wellmetrix.wellmetrixprovider:drawable/ic_launcher_background = 0x7f070060
com.wellmetrix.wellmetrixprovider:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f
com.wellmetrix.wellmetrixprovider:drawable/ic_call_answer_video = 0x7f07005c
com.wellmetrix.wellmetrixprovider:drawable/compat_splash_screen_no_icon_background = 0x7f070059
com.wellmetrix.wellmetrixprovider:drawable/compat_splash_screen = 0x7f070058
com.wellmetrix.wellmetrixprovider:attr/dialogTheme = 0x7f030069
com.wellmetrix.wellmetrixprovider:attr/panelBackground = 0x7f0300c7
com.wellmetrix.wellmetrixprovider:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f070057
com.wellmetrix.wellmetrixprovider:drawable/btn_radio_on_mtrl = 0x7f070056
com.wellmetrix.wellmetrixprovider:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f070055
com.wellmetrix.wellmetrixprovider:id/customPanel = 0x7f08004f
com.wellmetrix.wellmetrixprovider:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f070053
com.wellmetrix.wellmetrixprovider:styleable/StateListDrawable = 0x7f0f0029
com.wellmetrix.wellmetrixprovider:attr/layout_anchor = 0x7f0300a1
com.wellmetrix.wellmetrixprovider:id/fill = 0x7f080059
com.wellmetrix.wellmetrixprovider:attr/titleTextStyle = 0x7f030117
com.wellmetrix.wellmetrixprovider:drawable/btn_checkbox_checked_mtrl = 0x7f070050
com.wellmetrix.wellmetrixprovider:drawable/abc_vector_test = 0x7f07004f
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_27 = 0x7f08001b
com.wellmetrix.wellmetrixprovider:drawable/abc_textfield_search_material = 0x7f07004e
com.wellmetrix.wellmetrixprovider:attr/colorPrimaryDark = 0x7f030059
com.wellmetrix.wellmetrixprovider:drawable/abc_textfield_default_mtrl_alpha = 0x7f07004b
com.wellmetrix.wellmetrixprovider:color/primary_material_light = 0x7f05004a
com.wellmetrix.wellmetrixprovider:drawable/abc_switch_track_mtrl_alpha = 0x7f070043
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.SeekBar = 0x7f0e009f
com.wellmetrix.wellmetrixprovider:drawable/abc_switch_thumb_material = 0x7f070042
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_14 = 0x7f08000d
com.wellmetrix.wellmetrixprovider:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f0e0104
com.wellmetrix.wellmetrixprovider:attr/fontProviderCerts = 0x7f030085
com.wellmetrix.wellmetrixprovider:drawable/notification_oversize_large_icon_bg = 0x7f07006b
com.wellmetrix.wellmetrixprovider:drawable/abc_star_half_black_48dp = 0x7f070041
com.wellmetrix.wellmetrixprovider:drawable/abc_spinner_textfield_background_material = 0x7f07003f
com.wellmetrix.wellmetrixprovider:styleable/AppCompatTheme = 0x7f0f000f
com.wellmetrix.wellmetrixprovider:id/actions = 0x7f080038
com.wellmetrix.wellmetrixprovider:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f0e00c0
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f0e0091
com.wellmetrix.wellmetrixprovider:attr/actionModeSplitBackground = 0x7f03001b
com.wellmetrix.wellmetrixprovider:drawable/abc_seekbar_thumb_material = 0x7f07003b
com.wellmetrix.wellmetrixprovider:drawable/btn_radio_off_mtrl = 0x7f070054
com.wellmetrix.wellmetrixprovider:drawable/abc_ratingbar_small_material = 0x7f070035
com.wellmetrix.wellmetrixprovider:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d
com.wellmetrix.wellmetrixprovider:attr/tooltipText = 0x7f03011c
com.wellmetrix.wellmetrixprovider:drawable/abc_ratingbar_indicator_material = 0x7f070033
com.wellmetrix.wellmetrixprovider:animator/fragment_fade_exit = 0x7f020003
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_1 = 0x7f080008
com.wellmetrix.wellmetrixprovider:drawable/abc_list_selector_disabled_holo_light = 0x7f07002e
com.wellmetrix.wellmetrixprovider:id/right_icon = 0x7f08007e
com.wellmetrix.wellmetrixprovider:drawable/abc_list_selector_background_transition_holo_dark = 0x7f07002b
com.wellmetrix.wellmetrixprovider:styleable/DrawerArrowToggle = 0x7f0f0017
com.wellmetrix.wellmetrixprovider:drawable/abc_list_pressed_holo_dark = 0x7f070029
com.wellmetrix.wellmetrixprovider:drawable/abc_list_focused_holo = 0x7f070027
com.wellmetrix.wellmetrixprovider:dimen/abc_action_bar_default_padding_end_material = 0x7f060003
com.wellmetrix.wellmetrixprovider:drawable/abc_list_divider_mtrl_alpha = 0x7f070026
com.wellmetrix.wellmetrixprovider:layout/select_dialog_item_material = 0x7f0b0029
com.wellmetrix.wellmetrixprovider:attr/windowSplashScreenIconBackgroundColor = 0x7f030130
com.wellmetrix.wellmetrixprovider:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f070051
com.wellmetrix.wellmetrixprovider:drawable/abc_list_divider_material = 0x7f070025
com.wellmetrix.wellmetrixprovider:color/notification_action_color_filter = 0x7f050045
com.wellmetrix.wellmetrixprovider:drawable/abc_item_background_holo_light = 0x7f070024
com.wellmetrix.wellmetrixprovider:drawable/abc_ic_voice_search_api_material = 0x7f070022
com.wellmetrix.wellmetrixprovider:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f07001f
com.wellmetrix.wellmetrixprovider:id/off = 0x7f080076
com.wellmetrix.wellmetrixprovider:drawable/abc_list_longpressed_holo = 0x7f070028
com.wellmetrix.wellmetrixprovider:drawable/abc_ic_menu_overflow_material = 0x7f07001d
com.wellmetrix.wellmetrixprovider:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f07001c
com.wellmetrix.wellmetrixprovider:attr/listMenuViewStyle = 0x7f0300ae
com.wellmetrix.wellmetrixprovider:drawable/abc_text_select_handle_middle_mtrl = 0x7f070048
com.wellmetrix.wellmetrixprovider:styleable/PopupWindowBackgroundState = 0x7f0f0025
com.wellmetrix.wellmetrixprovider:drawable/abc_ic_go_search_api_material = 0x7f07001a
com.wellmetrix.wellmetrixprovider:style/TextAppearance.Compat.Notification = 0x7f0e00f6
com.wellmetrix.wellmetrixprovider:drawable/abc_ic_clear_material = 0x7f070018
com.wellmetrix.wellmetrixprovider:attr/homeAsUpIndicator = 0x7f030092
com.wellmetrix.wellmetrixprovider:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f070017
com.wellmetrix.wellmetrixprovider:drawable/abc_ic_ab_back_material = 0x7f070016
com.wellmetrix.wellmetrixprovider:color/abc_search_url_text_selected = 0x7f050010
com.wellmetrix.wellmetrixprovider:drawable/abc_dialog_material_background = 0x7f070014
com.wellmetrix.wellmetrixprovider:drawable/abc_control_background_material = 0x7f070013
com.wellmetrix.wellmetrixprovider:id/action_divider = 0x7f080030
com.wellmetrix.wellmetrixprovider:drawable/abc_cab_background_top_mtrl_alpha = 0x7f070012
com.wellmetrix.wellmetrixprovider:drawable/abc_cab_background_internal_bg = 0x7f070010
com.wellmetrix.wellmetrixprovider:id/dialog_button = 0x7f080052
com.wellmetrix.wellmetrixprovider:attr/spinBars = 0x7f0300e4
com.wellmetrix.wellmetrixprovider:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f07000e
com.wellmetrix.wellmetrixprovider:color/background_floating_material_light = 0x7f05001e
com.wellmetrix.wellmetrixprovider:drawable/abc_scrubber_track_mtrl_alpha = 0x7f07003a
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Spinner.DropDown = 0x7f0e0162
com.wellmetrix.wellmetrixprovider:drawable/btn_checkbox_unchecked_mtrl = 0x7f070052
com.wellmetrix.wellmetrixprovider:attr/initialActivityCount = 0x7f03009a
com.wellmetrix.wellmetrixprovider:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f07000c
com.wellmetrix.wellmetrixprovider:attr/windowFixedHeightMinor = 0x7f030127
com.wellmetrix.wellmetrixprovider:drawable/abc_btn_radio_material_anim = 0x7f07000b
com.wellmetrix.wellmetrixprovider:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f070006
com.wellmetrix.wellmetrixprovider:drawable/abc_btn_check_material_anim = 0x7f070005
com.wellmetrix.wellmetrixprovider:attr/fontProviderQuery = 0x7f030089
com.wellmetrix.wellmetrixprovider:attr/actionViewClass = 0x7f030022
com.wellmetrix.wellmetrixprovider:dimen/splashscreen_icon_mask_stroke_no_background = 0x7f060072
com.wellmetrix.wellmetrixprovider:drawable/abc_btn_check_material = 0x7f070004
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Button.Small = 0x7f0e0130
com.wellmetrix.wellmetrixprovider:attr/splashScreenIconSize = 0x7f0300e7
com.wellmetrix.wellmetrixprovider:drawable/abc_btn_borderless_material = 0x7f070003
com.wellmetrix.wellmetrixprovider:id/scrollIndicatorDown = 0x7f080081
com.wellmetrix.wellmetrixprovider:anim/abc_slide_out_top = 0x7f010009
com.wellmetrix.wellmetrixprovider:dimen/tooltip_y_offset_touch = 0x7f06007e
com.wellmetrix.wellmetrixprovider:id/center_vertical = 0x7f080045
com.wellmetrix.wellmetrixprovider:dimen/tooltip_y_offset_non_touch = 0x7f06007d
com.wellmetrix.wellmetrixprovider:id/search_voice_btn = 0x7f08008d
com.wellmetrix.wellmetrixprovider:dimen/tooltip_vertical_padding = 0x7f06007c
com.wellmetrix.wellmetrixprovider:dimen/tooltip_precise_anchor_extra_offset = 0x7f06007a
com.wellmetrix.wellmetrixprovider:styleable/ButtonBarLayout = 0x7f0f0010
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_8 = 0x7f080025
com.wellmetrix.wellmetrixprovider:attr/editTextColor = 0x7f03007c
com.wellmetrix.wellmetrixprovider:dimen/tooltip_corner_radius = 0x7f060077
com.wellmetrix.wellmetrixprovider:attr/subtitleTextAppearance = 0x7f0300f0
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_7 = 0x7f080024
com.wellmetrix.wellmetrixprovider:dimen/splashscreen_icon_mask_size_no_background = 0x7f060070
com.wellmetrix.wellmetrixprovider:style/Base.V7.Theme.AppCompat.Dialog = 0x7f0e0066
com.wellmetrix.wellmetrixprovider:dimen/disabled_alpha_material_dark = 0x7f060058
com.wellmetrix.wellmetrixprovider:dimen/notification_subtext_size = 0x7f06006d
com.wellmetrix.wellmetrixprovider:style/AppTheme = 0x7f0e0005
com.wellmetrix.wellmetrixprovider:dimen/notification_right_icon_size = 0x7f060069
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f0e013b
com.wellmetrix.wellmetrixprovider:dimen/notification_media_narrow_margin = 0x7f060068
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.ListPopupWindow = 0x7f0e0151
com.wellmetrix.wellmetrixprovider:dimen/notification_large_icon_height = 0x7f060065
com.wellmetrix.wellmetrixprovider:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f0e010f
com.wellmetrix.wellmetrixprovider:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f0e0068
com.wellmetrix.wellmetrixprovider:id/withText = 0x7f0800c0
com.wellmetrix.wellmetrixprovider:dimen/notification_content_margin_start = 0x7f060064
com.wellmetrix.wellmetrixprovider:dimen/notification_big_circle_margin = 0x7f060063
com.wellmetrix.wellmetrixprovider:drawable/abc_list_selector_holo_light = 0x7f070030
com.wellmetrix.wellmetrixprovider:style/Base.Animation.AppCompat.Dialog = 0x7f0e000a
com.wellmetrix.wellmetrixprovider:dimen/notification_action_icon_size = 0x7f060061
com.wellmetrix.wellmetrixprovider:drawable/abc_edit_text_material = 0x7f070015
com.wellmetrix.wellmetrixprovider:id/wrap_content = 0x7f0800c1
com.wellmetrix.wellmetrixprovider:dimen/notification_main_column_padding_top = 0x7f060067
com.wellmetrix.wellmetrixprovider:dimen/hint_pressed_alpha_material_light = 0x7f060060
com.wellmetrix.wellmetrixprovider:style/Platform.V25.AppCompat = 0x7f0e00b2
com.wellmetrix.wellmetrixprovider:id/line3 = 0x7f08006a
com.wellmetrix.wellmetrixprovider:color/material_grey_50 = 0x7f050040
com.wellmetrix.wellmetrixprovider:id/always = 0x7f08003d
com.wellmetrix.wellmetrixprovider:style/Base.V23.Theme.AppCompat.Light = 0x7f0e005f
com.wellmetrix.wellmetrixprovider:drawable/abc_ic_search_api_material = 0x7f070021
com.wellmetrix.wellmetrixprovider:attr/textLocale = 0x7f030103
com.wellmetrix.wellmetrixprovider:dimen/highlight_alpha_material_light = 0x7f06005c
com.wellmetrix.wellmetrixprovider:dimen/highlight_alpha_material_colored = 0x7f06005a
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f0e001b
com.wellmetrix.wellmetrixprovider:id/normal = 0x7f080072
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Display4 = 0x7f0e00ce
com.wellmetrix.wellmetrixprovider:dimen/disabled_alpha_material_light = 0x7f060059
com.wellmetrix.wellmetrixprovider:dimen/compat_notification_large_icon_max_width = 0x7f060057
com.wellmetrix.wellmetrixprovider:attr/buttonBarNegativeButtonStyle = 0x7f03003b
com.wellmetrix.wellmetrixprovider:dimen/compat_notification_large_icon_max_height = 0x7f060056
com.wellmetrix.wellmetrixprovider:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f07001b
com.wellmetrix.wellmetrixprovider:id/add = 0x7f08003a
com.wellmetrix.wellmetrixprovider:string/abc_searchview_description_clear = 0x7f0d0013
com.wellmetrix.wellmetrixprovider:dimen/compat_control_corner_material = 0x7f060055
com.wellmetrix.wellmetrixprovider:id/fill_vertical = 0x7f08005b
com.wellmetrix.wellmetrixprovider:dimen/compat_button_padding_horizontal_material = 0x7f060053
com.wellmetrix.wellmetrixprovider:dimen/compat_button_inset_vertical_material = 0x7f060052
com.wellmetrix.wellmetrixprovider:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f0e00b7
com.wellmetrix.wellmetrixprovider:dimen/compat_button_inset_horizontal_material = 0x7f060051
com.wellmetrix.wellmetrixprovider:dimen/abc_text_size_title_material_toolbar = 0x7f060050
com.wellmetrix.wellmetrixprovider:dimen/abc_text_size_subtitle_material_toolbar = 0x7f06004e
com.wellmetrix.wellmetrixprovider:attr/colorPrimary = 0x7f030058
com.wellmetrix.wellmetrixprovider:dimen/abc_dialog_list_padding_top_no_title = 0x7f060021
com.wellmetrix.wellmetrixprovider:id/disableHome = 0x7f080053
com.wellmetrix.wellmetrixprovider:dimen/abc_text_size_menu_material = 0x7f06004b
com.wellmetrix.wellmetrixprovider:dimen/abc_text_size_medium_material = 0x7f060049
com.wellmetrix.wellmetrixprovider:dimen/abc_text_size_display_4_material = 0x7f060046
com.wellmetrix.wellmetrixprovider:style/Base.V22.Theme.AppCompat = 0x7f0e005c
com.wellmetrix.wellmetrixprovider:attr/arrowHeadLength = 0x7f03002b
com.wellmetrix.wellmetrixprovider:dimen/abc_text_size_large_material = 0x7f060048
com.wellmetrix.wellmetrixprovider:dimen/abc_text_size_caption_material = 0x7f060042
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f0e0146
com.wellmetrix.wellmetrixprovider:layout/custom_dialog = 0x7f0b001e
com.wellmetrix.wellmetrixprovider:dimen/abc_text_size_body_2_material = 0x7f060040
com.wellmetrix.wellmetrixprovider:layout/fragment_bridge = 0x7f0b001f
com.wellmetrix.wellmetrixprovider:attr/alertDialogTheme = 0x7f030027
com.wellmetrix.wellmetrixprovider:dimen/abc_switch_padding = 0x7f06003e
com.wellmetrix.wellmetrixprovider:id/homeAsUp = 0x7f080061
com.wellmetrix.wellmetrixprovider:style/Theme.AppCompat.NoActionBar = 0x7f0e0113
com.wellmetrix.wellmetrixprovider:dimen/abc_star_small = 0x7f06003d
com.wellmetrix.wellmetrixprovider:id/all = 0x7f08003c
com.wellmetrix.wellmetrixprovider:dimen/abc_star_big = 0x7f06003b
com.wellmetrix.wellmetrixprovider:id/icon_group = 0x7f080063
com.wellmetrix.wellmetrixprovider:dimen/abc_select_dialog_padding_start_material = 0x7f06003a
com.wellmetrix.wellmetrixprovider:dimen/abc_seekbar_track_progress_height_material = 0x7f060039
com.wellmetrix.wellmetrixprovider:style/Animation.AppCompat.Tooltip = 0x7f0e0004
com.wellmetrix.wellmetrixprovider:attr/actionDropDownStyle = 0x7f03000c
com.wellmetrix.wellmetrixprovider:dimen/abc_seekbar_track_background_height_material = 0x7f060038
com.wellmetrix.wellmetrixprovider:dimen/splashscreen_icon_mask_size_with_background = 0x7f060071
com.wellmetrix.wellmetrixprovider:dimen/abc_search_view_preferred_width = 0x7f060037
com.wellmetrix.wellmetrixprovider:layout/abc_expanded_menu_layout = 0x7f0b000d
com.wellmetrix.wellmetrixprovider:dimen/abc_search_view_preferred_height = 0x7f060036
com.wellmetrix.wellmetrixprovider:animator/fragment_close_enter = 0x7f020000
com.wellmetrix.wellmetrixprovider:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f070037
com.wellmetrix.wellmetrixprovider:dimen/abc_panel_menu_list_width = 0x7f060034
com.wellmetrix.wellmetrixprovider:color/primary_dark_material_light = 0x7f050048
com.wellmetrix.wellmetrixprovider:dimen/abc_list_item_padding_horizontal_material = 0x7f060033
com.wellmetrix.wellmetrixprovider:dimen/abc_list_item_height_small_material = 0x7f060032
com.wellmetrix.wellmetrixprovider:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0a0002
com.wellmetrix.wellmetrixprovider:dimen/abc_list_item_height_large_material = 0x7f060030
com.wellmetrix.wellmetrixprovider:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f0e00b8
com.wellmetrix.wellmetrixprovider:dimen/abc_edit_text_inset_horizontal_material = 0x7f06002d
com.wellmetrix.wellmetrixprovider:attr/drawableTopCompat = 0x7f030077
com.wellmetrix.wellmetrixprovider:id/action_context_bar = 0x7f08002f
com.wellmetrix.wellmetrixprovider:dimen/abc_edit_text_inset_top_material = 0x7f06002e
com.wellmetrix.wellmetrixprovider:dimen/abc_edit_text_inset_bottom_material = 0x7f06002c
com.wellmetrix.wellmetrixprovider:attr/searchIcon = 0x7f0300d9
com.wellmetrix.wellmetrixprovider:dimen/abc_dropdownitem_text_padding_right = 0x7f06002b
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Toolbar = 0x7f0e0167
com.wellmetrix.wellmetrixprovider:dimen/abc_disabled_alpha_material_dark = 0x7f060027
com.wellmetrix.wellmetrixprovider:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f0e00be
com.wellmetrix.wellmetrixprovider:drawable/notification_bg_normal_pressed = 0x7f070069
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.SearchView = 0x7f0e009d
com.wellmetrix.wellmetrixprovider:dimen/abc_dialog_padding_top_material = 0x7f060025
com.wellmetrix.wellmetrixprovider:dimen/abc_text_size_display_2_material = 0x7f060044
com.wellmetrix.wellmetrixprovider:attr/navigationMode = 0x7f0300bf
com.wellmetrix.wellmetrixprovider:id/center = 0x7f080043
com.wellmetrix.wellmetrixprovider:dimen/abc_dialog_padding_material = 0x7f060024
com.wellmetrix.wellmetrixprovider:dimen/abc_dialog_min_width_major = 0x7f060022
com.wellmetrix.wellmetrixprovider:dimen/abc_dialog_fixed_height_minor = 0x7f06001d
com.wellmetrix.wellmetrixprovider:id/icon = 0x7f080062
com.wellmetrix.wellmetrixprovider:dimen/abc_dialog_fixed_height_major = 0x7f06001c
com.wellmetrix.wellmetrixprovider:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f0e0103
com.wellmetrix.wellmetrixprovider:color/material_deep_teal_200 = 0x7f05003c
com.wellmetrix.wellmetrixprovider:attr/progressBarStyle = 0x7f0300d0
com.wellmetrix.wellmetrixprovider:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f070031
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.SeekBar = 0x7f0e015f
com.wellmetrix.wellmetrixprovider:integer/status_bar_notification_info_maxnum = 0x7f090005
com.wellmetrix.wellmetrixprovider:dimen/abc_config_prefDialogWidth = 0x7f060017
com.wellmetrix.wellmetrixprovider:dimen/abc_alert_dialog_button_bar_height = 0x7f060010
com.wellmetrix.wellmetrixprovider:dimen/abc_action_button_min_width_overflow_material = 0x7f06000f
com.wellmetrix.wellmetrixprovider:style/Theme.AppCompat.Empty = 0x7f0e010b
com.wellmetrix.wellmetrixprovider:dimen/abc_action_button_min_width_material = 0x7f06000e
com.wellmetrix.wellmetrixprovider:attr/postSplashScreenTheme = 0x7f0300cd
com.wellmetrix.wellmetrixprovider:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f06000c
com.wellmetrix.wellmetrixprovider:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f06000b
com.wellmetrix.wellmetrixprovider:dimen/abc_action_bar_overflow_padding_start_material = 0x7f060008
com.wellmetrix.wellmetrixprovider:color/dim_foreground_material_dark = 0x7f050030
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_24 = 0x7f080018
com.wellmetrix.wellmetrixprovider:color/abc_tint_btn_checkable = 0x7f050013
com.wellmetrix.wellmetrixprovider:dimen/abc_action_bar_overflow_padding_end_material = 0x7f060007
com.wellmetrix.wellmetrixprovider:dimen/tooltip_precise_anchor_threshold = 0x7f06007b
com.wellmetrix.wellmetrixprovider:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f060006
com.wellmetrix.wellmetrixprovider:dimen/abc_action_bar_elevation_material = 0x7f060005
com.wellmetrix.wellmetrixprovider:id/notification_main_column = 0x7f080074
com.wellmetrix.wellmetrixprovider:style/Theme.SplashScreen.Common = 0x7f0e0115
com.wellmetrix.wellmetrixprovider:dimen/abc_action_bar_default_padding_start_material = 0x7f060004
com.wellmetrix.wellmetrixprovider:dimen/abc_text_size_menu_header_material = 0x7f06004a
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_17 = 0x7f080010
com.wellmetrix.wellmetrixprovider:id/alertTitle = 0x7f08003b
com.wellmetrix.wellmetrixprovider:color/primary_text_default_material_dark = 0x7f05004b
com.wellmetrix.wellmetrixprovider:dimen/abc_action_bar_default_height_material = 0x7f060002
com.wellmetrix.wellmetrixprovider:style/Base.v21.Theme.SplashScreen.Light = 0x7f0e00a8
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f0e003b
com.wellmetrix.wellmetrixprovider:dimen/abc_action_bar_content_inset_with_nav = 0x7f060001
com.wellmetrix.wellmetrixprovider:styleable/GradientColorItem = 0x7f0f001d
com.wellmetrix.wellmetrixprovider:attr/titleMarginStart = 0x7f030112
com.wellmetrix.wellmetrixprovider:attr/switchPadding = 0x7f0300f5
com.wellmetrix.wellmetrixprovider:drawable/abc_cab_background_top_material = 0x7f070011
com.wellmetrix.wellmetrixprovider:color/tooltip_background_light = 0x7f05005c
com.wellmetrix.wellmetrixprovider:color/tooltip_background_dark = 0x7f05005b
com.wellmetrix.wellmetrixprovider:color/switch_thumb_material_light = 0x7f050058
com.wellmetrix.wellmetrixprovider:attr/colorSwitchThumbNormal = 0x7f03005a
com.wellmetrix.wellmetrixprovider:drawable/notification_bg_low_normal = 0x7f070066
com.wellmetrix.wellmetrixprovider:color/switch_thumb_disabled_material_light = 0x7f050056
com.wellmetrix.wellmetrixprovider:id/action_menu_presenter = 0x7f080033
com.wellmetrix.wellmetrixprovider:color/secondary_text_disabled_material_dark = 0x7f050053
com.wellmetrix.wellmetrixprovider:color/secondary_text_default_material_light = 0x7f050052
com.wellmetrix.wellmetrixprovider:id/never = 0x7f080070
com.wellmetrix.wellmetrixprovider:color/primary_text_default_material_light = 0x7f05004c
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_4 = 0x7f080021
com.wellmetrix.wellmetrixprovider:dimen/abc_button_padding_vertical_material = 0x7f060015
com.wellmetrix.wellmetrixprovider:attr/theme = 0x7f030104
com.wellmetrix.wellmetrixprovider:attr/dropdownListPreferredItemHeight = 0x7f03007a
com.wellmetrix.wellmetrixprovider:color/secondary_text_default_material_dark = 0x7f050051
com.wellmetrix.wellmetrixprovider:attr/backgroundTintMode = 0x7f030037
com.wellmetrix.wellmetrixprovider:color/primary_text_disabled_material_light = 0x7f05004e
com.wellmetrix.wellmetrixprovider:dimen/notification_top_pad = 0x7f06006e
com.wellmetrix.wellmetrixprovider:color/primary_material_dark = 0x7f050049
com.wellmetrix.wellmetrixprovider:id/parentPanel = 0x7f080078
com.wellmetrix.wellmetrixprovider:attr/actionLayout = 0x7f03000d
com.wellmetrix.wellmetrixprovider:color/primary_text_disabled_material_dark = 0x7f05004d
com.wellmetrix.wellmetrixprovider:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f07001e
com.wellmetrix.wellmetrixprovider:styleable/ViewBackgroundHelper = 0x7f0f002f
com.wellmetrix.wellmetrixprovider:dimen/abc_text_size_button_material = 0x7f060041
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_18 = 0x7f080011
com.wellmetrix.wellmetrixprovider:drawable/notification_template_icon_low_bg = 0x7f07006d
com.wellmetrix.wellmetrixprovider:attr/showText = 0x7f0300e1
com.wellmetrix.wellmetrixprovider:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f070007
com.wellmetrix.wellmetrixprovider:drawable/ic_call_answer = 0x7f07005a
com.wellmetrix.wellmetrixprovider:color/primary_dark_material_dark = 0x7f050047
com.wellmetrix.wellmetrixprovider:color/material_grey_900 = 0x7f050044
com.wellmetrix.wellmetrixprovider:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f0e000e
com.wellmetrix.wellmetrixprovider:attr/alertDialogCenterButtons = 0x7f030025
com.wellmetrix.wellmetrixprovider:drawable/tooltip_frame_dark = 0x7f070072
com.wellmetrix.wellmetrixprovider:color/material_grey_600 = 0x7f050041
com.wellmetrix.wellmetrixprovider:id/group_divider = 0x7f08005e
com.wellmetrix.wellmetrixprovider:color/material_grey_300 = 0x7f05003f
com.wellmetrix.wellmetrixprovider:style/Base.DialogWindowTitle.AppCompat = 0x7f0e000d
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_31 = 0x7f080020
com.wellmetrix.wellmetrixprovider:color/material_grey_100 = 0x7f05003e
com.wellmetrix.wellmetrixprovider:attr/actionBarTabTextStyle = 0x7f030008
com.wellmetrix.wellmetrixprovider:color/material_blue_grey_950 = 0x7f05003b
com.wellmetrix.wellmetrixprovider:attr/actionMenuTextColor = 0x7f03000f
com.wellmetrix.wellmetrixprovider:color/material_blue_grey_900 = 0x7f05003a
com.wellmetrix.wellmetrixprovider:attr/checkMarkTintMode = 0x7f030049
com.wellmetrix.wellmetrixprovider:color/highlighted_text_material_light = 0x7f050037
com.wellmetrix.wellmetrixprovider:color/highlighted_text_material_dark = 0x7f050036
com.wellmetrix.wellmetrixprovider:id/action_mode_bar_stub = 0x7f080035
com.wellmetrix.wellmetrixprovider:color/foreground_material_light = 0x7f050035
com.wellmetrix.wellmetrixprovider:attr/checkedTextViewStyle = 0x7f03004b
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.ListMenuView = 0x7f0e0150
com.wellmetrix.wellmetrixprovider:id/tag_unhandled_key_event_manager = 0x7f0800a8
com.wellmetrix.wellmetrixprovider:attr/menu = 0x7f0300bb
com.wellmetrix.wellmetrixprovider:color/error_color_material_light = 0x7f050033
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Large = 0x7f0e00d1
com.wellmetrix.wellmetrixprovider:color/dim_foreground_material_light = 0x7f050031
com.wellmetrix.wellmetrixprovider:attr/buttonBarButtonStyle = 0x7f03003a
com.wellmetrix.wellmetrixprovider:color/colorPrimary = 0x7f05002c
com.wellmetrix.wellmetrixprovider:color/colorAccent = 0x7f05002b
com.wellmetrix.wellmetrixprovider:styleable/FragmentContainerView = 0x7f0f001b
com.wellmetrix.wellmetrixprovider:dimen/notification_action_text_size = 0x7f060062
com.wellmetrix.wellmetrixprovider:color/call_notification_decline_color = 0x7f05002a
com.wellmetrix.wellmetrixprovider:attr/srcCompat = 0x7f0300e9
com.wellmetrix.wellmetrixprovider:attr/icon = 0x7f030094
com.wellmetrix.wellmetrixprovider:attr/textAppearanceListItemSmall = 0x7f0300fc
com.wellmetrix.wellmetrixprovider:color/bright_foreground_inverse_material_light = 0x7f050024
com.wellmetrix.wellmetrixprovider:id/action_bar_activity_content = 0x7f080028
com.wellmetrix.wellmetrixprovider:color/bright_foreground_material_dark = 0x7f050025
com.wellmetrix.wellmetrixprovider:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f07000f
com.wellmetrix.wellmetrixprovider:color/bright_foreground_disabled_material_dark = 0x7f050021
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_2 = 0x7f080013
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.ListView.Menu = 0x7f0e0154
com.wellmetrix.wellmetrixprovider:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f070019
com.wellmetrix.wellmetrixprovider:drawable/abc_seekbar_track_material = 0x7f07003d
com.wellmetrix.wellmetrixprovider:attr/titleMarginBottom = 0x7f030110
com.wellmetrix.wellmetrixprovider:color/ripple_material_light = 0x7f050050
com.wellmetrix.wellmetrixprovider:attr/allowStacking = 0x7f030028
com.wellmetrix.wellmetrixprovider:color/background_material_light = 0x7f050020
com.wellmetrix.wellmetrixprovider:string/call_notification_answer_action = 0x7f0d001d
com.wellmetrix.wellmetrixprovider:layout/abc_cascading_menu_item_layout = 0x7f0b000b
com.wellmetrix.wellmetrixprovider:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c
com.wellmetrix.wellmetrixprovider:color/dim_foreground_disabled_material_dark = 0x7f05002e
com.wellmetrix.wellmetrixprovider:attr/autoSizeTextType = 0x7f030032
com.wellmetrix.wellmetrixprovider:attr/drawableBottomCompat = 0x7f03006f
com.wellmetrix.wellmetrixprovider:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f07000d
com.wellmetrix.wellmetrixprovider:dimen/hint_alpha_material_dark = 0x7f06005d
com.wellmetrix.wellmetrixprovider:layout/abc_dialog_title_material = 0x7f0b000c
com.wellmetrix.wellmetrixprovider:color/background_material_dark = 0x7f05001f
com.wellmetrix.wellmetrixprovider:color/accent_material_light = 0x7f05001a
com.wellmetrix.wellmetrixprovider:style/Base.Theme.AppCompat = 0x7f0e003f
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Caption = 0x7f0e0013
com.wellmetrix.wellmetrixprovider:id/multiply = 0x7f08006f
com.wellmetrix.wellmetrixprovider:id/info = 0x7f080066
com.wellmetrix.wellmetrixprovider:attr/buttonIconDimen = 0x7f030041
com.wellmetrix.wellmetrixprovider:id/META = 0x7f080003
com.wellmetrix.wellmetrixprovider:color/abc_tint_switch_track = 0x7f050018
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.ListView.DropDown = 0x7f0e0153
com.wellmetrix.wellmetrixprovider:id/line1 = 0x7f080069
com.wellmetrix.wellmetrixprovider:styleable/FontFamily = 0x7f0f0018
com.wellmetrix.wellmetrixprovider:id/action_image = 0x7f080031
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f0e014f
com.wellmetrix.wellmetrixprovider:attr/colorButtonNormal = 0x7f030053
com.wellmetrix.wellmetrixprovider:color/abc_tint_spinner = 0x7f050017
com.wellmetrix.wellmetrixprovider:style/Base.V7.Widget.AppCompat.EditText = 0x7f0e006b
com.wellmetrix.wellmetrixprovider:color/abc_tint_seek_thumb = 0x7f050016
com.wellmetrix.wellmetrixprovider:layout/abc_screen_simple = 0x7f0b0015
com.wellmetrix.wellmetrixprovider:integer/cancel_button_image_alpha = 0x7f090002
com.wellmetrix.wellmetrixprovider:color/foreground_material_dark = 0x7f050034
com.wellmetrix.wellmetrixprovider:color/abc_tint_default = 0x7f050014
com.wellmetrix.wellmetrixprovider:dimen/compat_button_padding_vertical_material = 0x7f060054
com.wellmetrix.wellmetrixprovider:color/abc_secondary_text_material_light = 0x7f050012
com.wellmetrix.wellmetrixprovider:id/text = 0x7f0800ab
com.wellmetrix.wellmetrixprovider:color/abc_search_url_text_normal = 0x7f05000e
com.wellmetrix.wellmetrixprovider:id/contentPanel = 0x7f08004d
com.wellmetrix.wellmetrixprovider:id/action_mode_close_button = 0x7f080036
com.wellmetrix.wellmetrixprovider:layout/select_dialog_singlechoice_material = 0x7f0b002b
com.wellmetrix.wellmetrixprovider:attr/titleMarginTop = 0x7f030113
com.wellmetrix.wellmetrixprovider:drawable/abc_text_select_handle_right_mtrl = 0x7f070049
com.wellmetrix.wellmetrixprovider:drawable/abc_btn_radio_material = 0x7f07000a
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f0e0142
com.wellmetrix.wellmetrixprovider:color/abc_search_url_text = 0x7f05000d
com.wellmetrix.wellmetrixprovider:attr/contentInsetLeft = 0x7f03005f
com.wellmetrix.wellmetrixprovider:color/abc_primary_text_disable_only_material_dark = 0x7f050009
com.wellmetrix.wellmetrixprovider:color/abc_hint_foreground_material_light = 0x7f050008
com.wellmetrix.wellmetrixprovider:attr/actionBarPopupTheme = 0x7f030002
com.wellmetrix.wellmetrixprovider:xml/config = 0x7f100000
com.wellmetrix.wellmetrixprovider:styleable/Capability = 0x7f0f0011
com.wellmetrix.wellmetrixprovider:color/button_material_dark = 0x7f050027
com.wellmetrix.wellmetrixprovider:layout/abc_popup_menu_header_item_layout = 0x7f0b0012
com.wellmetrix.wellmetrixprovider:attr/fontProviderPackage = 0x7f030088
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Menu = 0x7f0e00d9
com.wellmetrix.wellmetrixprovider:id/title = 0x7f0800b1
com.wellmetrix.wellmetrixprovider:color/abc_hint_foreground_material_dark = 0x7f050007
com.wellmetrix.wellmetrixprovider:dimen/abc_text_size_headline_material = 0x7f060047
com.wellmetrix.wellmetrixprovider:color/abc_decor_view_status_guard_light = 0x7f050006
com.wellmetrix.wellmetrixprovider:layout/ime_secondary_split_test_activity = 0x7f0b0021
com.wellmetrix.wellmetrixprovider:id/left = 0x7f080068
com.wellmetrix.wellmetrixprovider:attr/drawableTintMode = 0x7f030076
com.wellmetrix.wellmetrixprovider:attr/actionMenuTextAppearance = 0x7f03000e
com.wellmetrix.wellmetrixprovider:color/button_material_light = 0x7f050028
com.wellmetrix.wellmetrixprovider:attr/showAsAction = 0x7f0300df
com.wellmetrix.wellmetrixprovider:color/abc_color_highlight_material = 0x7f050004
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Widget.Button = 0x7f0e00ec
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f0e001c
com.wellmetrix.wellmetrixprovider:id/right = 0x7f08007d
com.wellmetrix.wellmetrixprovider:color/abc_btn_colored_borderless_text_material = 0x7f050002
com.wellmetrix.wellmetrixprovider:attr/tintMode = 0x7f03010d
com.wellmetrix.wellmetrixprovider:attr/maxButtonHeight = 0x7f0300b9
com.wellmetrix.wellmetrixprovider:attr/titleTextAppearance = 0x7f030115
com.wellmetrix.wellmetrixprovider:attr/titleTextColor = 0x7f030116
com.wellmetrix.wellmetrixprovider:string/abc_searchview_description_submit = 0x7f0d0016
com.wellmetrix.wellmetrixprovider:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015
com.wellmetrix.wellmetrixprovider:attr/titleMargins = 0x7f030114
com.wellmetrix.wellmetrixprovider:color/abc_background_cache_hint_selector_material_dark = 0x7f050000
com.wellmetrix.wellmetrixprovider:attr/track = 0x7f03011d
com.wellmetrix.wellmetrixprovider:drawable/notification_bg = 0x7f070064
com.wellmetrix.wellmetrixprovider:bool/abc_config_actionMenuItemAllCaps = 0x7f040001
com.wellmetrix.wellmetrixprovider:dimen/abc_button_inset_vertical_material = 0x7f060013
com.wellmetrix.wellmetrixprovider:attr/voiceIcon = 0x7f030122
com.wellmetrix.wellmetrixprovider:bool/abc_action_bar_embed_tabs = 0x7f040000
com.wellmetrix.wellmetrixprovider:id/expanded_menu = 0x7f080058
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_13 = 0x7f08000c
com.wellmetrix.wellmetrixprovider:attr/windowSplashScreenBackground = 0x7f03012f
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.EditText = 0x7f0e0138
com.wellmetrix.wellmetrixprovider:attr/windowActionBarOverlay = 0x7f030124
com.wellmetrix.wellmetrixprovider:dimen/abc_floating_window_z = 0x7f06002f
com.wellmetrix.wellmetrixprovider:attr/windowActionBar = 0x7f030123
com.wellmetrix.wellmetrixprovider:attr/windowSplashScreenAnimatedIcon = 0x7f03012d
com.wellmetrix.wellmetrixprovider:id/src_in = 0x7f080098
com.wellmetrix.wellmetrixprovider:attr/windowNoTitle = 0x7f03012c
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Light.ActionButton = 0x7f0e0143
com.wellmetrix.wellmetrixprovider:attr/windowMinWidthMinor = 0x7f03012b
com.wellmetrix.wellmetrixprovider:dimen/hint_pressed_alpha_material_dark = 0x7f06005f
com.wellmetrix.wellmetrixprovider:attr/windowFixedWidthMajor = 0x7f030128
com.wellmetrix.wellmetrixprovider:id/beginning = 0x7f08003f
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f0e008c
com.wellmetrix.wellmetrixprovider:dimen/abc_text_size_subhead_material = 0x7f06004d
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Spinner.Underlined = 0x7f0e0164
com.wellmetrix.wellmetrixprovider:style/TextAppearance.Compat.Notification.Line2 = 0x7f0e00f8
com.wellmetrix.wellmetrixprovider:dimen/abc_text_size_display_3_material = 0x7f060045
com.wellmetrix.wellmetrixprovider:attr/gapBetweenBars = 0x7f03008e
com.wellmetrix.wellmetrixprovider:color/dim_foreground_disabled_material_light = 0x7f05002f
com.wellmetrix.wellmetrixprovider:attr/viewInflaterClass = 0x7f030121
com.wellmetrix.wellmetrixprovider:color/switch_thumb_normal_material_dark = 0x7f050059
com.wellmetrix.wellmetrixprovider:integer/default_icon_animation_duration = 0x7f090004
com.wellmetrix.wellmetrixprovider:id/textSpacerNoTitle = 0x7f0800ae
com.wellmetrix.wellmetrixprovider:attr/alphabeticModifiers = 0x7f03002a
com.wellmetrix.wellmetrixprovider:color/bright_foreground_material_light = 0x7f050026
com.wellmetrix.wellmetrixprovider:attr/tooltipFrameBackground = 0x7f03011b
com.wellmetrix.wellmetrixprovider:style/TextAppearance.Compat.Notification.Info = 0x7f0e00f7
com.wellmetrix.wellmetrixprovider:anim/abc_grow_fade_in_from_bottom = 0x7f010002
com.wellmetrix.wellmetrixprovider:attr/tooltipForegroundColor = 0x7f03011a
com.wellmetrix.wellmetrixprovider:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f0e0105
com.wellmetrix.wellmetrixprovider:attr/layout_insetEdge = 0x7f0300a5
com.wellmetrix.wellmetrixprovider:color/abc_primary_text_disable_only_material_light = 0x7f05000a
com.wellmetrix.wellmetrixprovider:attr/textAllCaps = 0x7f0300f8
com.wellmetrix.wellmetrixprovider:dimen/abc_action_bar_stacked_max_height = 0x7f060009
com.wellmetrix.wellmetrixprovider:attr/paddingTopNoTitle = 0x7f0300c6
com.wellmetrix.wellmetrixprovider:id/tag_state_description = 0x7f0800a6
com.wellmetrix.wellmetrixprovider:attr/titleMarginEnd = 0x7f030111
com.wellmetrix.wellmetrixprovider:dimen/abc_cascading_menus_min_smallest_width = 0x7f060016
com.wellmetrix.wellmetrixprovider:style/TextAppearance.Compat.Notification.Time = 0x7f0e00f9
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f0e00a6
com.wellmetrix.wellmetrixprovider:anim/abc_popup_exit = 0x7f010004
com.wellmetrix.wellmetrixprovider:attr/titleMargin = 0x7f03010f
com.wellmetrix.wellmetrixprovider:attr/drawableStartCompat = 0x7f030074
com.wellmetrix.wellmetrixprovider:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f0e0051
com.wellmetrix.wellmetrixprovider:attr/tint = 0x7f03010c
com.wellmetrix.wellmetrixprovider:attr/progressBarPadding = 0x7f0300cf
com.wellmetrix.wellmetrixprovider:attr/tickMarkTint = 0x7f03010a
com.wellmetrix.wellmetrixprovider:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f0e005a
com.wellmetrix.wellmetrixprovider:dimen/abc_disabled_alpha_material_light = 0x7f060028
com.wellmetrix.wellmetrixprovider:attr/closeItemLayout = 0x7f03004d
com.wellmetrix.wellmetrixprovider:attr/actionModeWebSearchDrawable = 0x7f03001e
com.wellmetrix.wellmetrixprovider:attr/tickMark = 0x7f030109
com.wellmetrix.wellmetrixprovider:styleable/Fragment = 0x7f0f001a
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_11 = 0x7f08000a
com.wellmetrix.wellmetrixprovider:attr/customNavigationLayout = 0x7f030065
com.wellmetrix.wellmetrixprovider:color/abc_secondary_text_material_dark = 0x7f050011
com.wellmetrix.wellmetrixprovider:attr/thumbTintMode = 0x7f030108
com.wellmetrix.wellmetrixprovider:attr/thumbTint = 0x7f030107
com.wellmetrix.wellmetrixprovider:attr/thickness = 0x7f030105
com.wellmetrix.wellmetrixprovider:dimen/abc_dialog_corner_radius_material = 0x7f06001b
com.wellmetrix.wellmetrixprovider:id/showCustom = 0x7f080090
com.wellmetrix.wellmetrixprovider:color/abc_tint_edittext = 0x7f050015
com.wellmetrix.wellmetrixprovider:attr/homeLayout = 0x7f030093
com.wellmetrix.wellmetrixprovider:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f07004d
com.wellmetrix.wellmetrixprovider:id/custom = 0x7f08004e
com.wellmetrix.wellmetrixprovider:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
com.wellmetrix.wellmetrixprovider:color/switch_thumb_normal_material_light = 0x7f05005a
com.wellmetrix.wellmetrixprovider:mipmap/ic_launcher = 0x7f0c0000
com.wellmetrix.wellmetrixprovider:attr/textAppearanceSmallPopupMenu = 0x7f030100
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.ButtonBar = 0x7f0e0131
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.ActionMode = 0x7f0e0075
com.wellmetrix.wellmetrixprovider:id/up = 0x7f0800b8
com.wellmetrix.wellmetrixprovider:attr/textAppearanceSearchResultTitle = 0x7f0300ff
com.wellmetrix.wellmetrixprovider:attr/navigationContentDescription = 0x7f0300bd
com.wellmetrix.wellmetrixprovider:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f070001
com.wellmetrix.wellmetrixprovider:attr/textAppearancePopupMenuHeader = 0x7f0300fd
com.wellmetrix.wellmetrixprovider:id/right_side = 0x7f08007f
com.wellmetrix.wellmetrixprovider:attr/subtitleTextColor = 0x7f0300f1
com.wellmetrix.wellmetrixprovider:color/material_grey_800 = 0x7f050042
com.wellmetrix.wellmetrixprovider:attr/title = 0x7f03010e
com.wellmetrix.wellmetrixprovider:string/app_name = 0x7f0d001c
com.wellmetrix.wellmetrixprovider:attr/switchTextAppearance = 0x7f0300f7
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_20 = 0x7f080014
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Tooltip = 0x7f0e00e2
com.wellmetrix.wellmetrixprovider:dimen/abc_list_item_height_material = 0x7f060031
com.wellmetrix.wellmetrixprovider:attr/alertDialogButtonGroupStyle = 0x7f030024
com.wellmetrix.wellmetrixprovider:attr/suggestionRowLayout = 0x7f0300f3
com.wellmetrix.wellmetrixprovider:style/AppTheme.NoActionBar = 0x7f0e0006
com.wellmetrix.wellmetrixprovider:layout/abc_alert_dialog_material = 0x7f0b0009
com.wellmetrix.wellmetrixprovider:attr/subtitleTextStyle = 0x7f0300f2
com.wellmetrix.wellmetrixprovider:id/CTRL = 0x7f080001
com.wellmetrix.wellmetrixprovider:attr/hideOnContentScroll = 0x7f030091
com.wellmetrix.wellmetrixprovider:string/abc_menu_sym_shortcut_label = 0x7f0d0010
com.wellmetrix.wellmetrixprovider:color/androidx_core_secondary_text_default_material_light = 0x7f05001c
com.wellmetrix.wellmetrixprovider:attr/submitBackground = 0x7f0300ee
com.wellmetrix.wellmetrixprovider:attr/layout_behavior = 0x7f0300a3
com.wellmetrix.wellmetrixprovider:style/AlertDialog.AppCompat.Light = 0x7f0e0001
com.wellmetrix.wellmetrixprovider:color/abc_btn_colored_text_material = 0x7f050003
com.wellmetrix.wellmetrixprovider:attr/start_dir = 0x7f0300ea
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Display2 = 0x7f0e0015
com.wellmetrix.wellmetrixprovider:id/expand_activities_button = 0x7f080057
com.wellmetrix.wellmetrixprovider:id/action_text = 0x7f080037
com.wellmetrix.wellmetrixprovider:drawable/splash = 0x7f070070
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f0e0156
com.wellmetrix.wellmetrixprovider:attr/drawableRightCompat = 0x7f030072
com.wellmetrix.wellmetrixprovider:dimen/abc_button_padding_horizontal_material = 0x7f060014
com.wellmetrix.wellmetrixprovider:drawable/abc_btn_colored_material = 0x7f070008
com.wellmetrix.wellmetrixprovider:attr/spinnerStyle = 0x7f0300e6
com.wellmetrix.wellmetrixprovider:id/src_atop = 0x7f080097
com.wellmetrix.wellmetrixprovider:attr/spinnerDropDownItemStyle = 0x7f0300e5
com.wellmetrix.wellmetrixprovider:attr/state_above_anchor = 0x7f0300eb
com.wellmetrix.wellmetrixprovider:attr/buttonGravity = 0x7f030040
com.wellmetrix.wellmetrixprovider:attr/alpha = 0x7f030029
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f0e0085
com.wellmetrix.wellmetrixprovider:attr/singleChoiceItemLayout = 0x7f0300e3
com.wellmetrix.wellmetrixprovider:color/abc_background_cache_hint_selector_material_light = 0x7f050001
com.wellmetrix.wellmetrixprovider:attr/showTitle = 0x7f0300e2
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_12 = 0x7f08000b
com.wellmetrix.wellmetrixprovider:attr/autoSizeStepGranularity = 0x7f030031
com.wellmetrix.wellmetrixprovider:styleable/StateListDrawableItem = 0x7f0f002a
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Small = 0x7f0e00dc
com.wellmetrix.wellmetrixprovider:attr/textColorAlertDialogListItem = 0x7f030101
com.wellmetrix.wellmetrixprovider:attr/shortcutMatchRequired = 0x7f0300de
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f0e0149
com.wellmetrix.wellmetrixprovider:attr/logoDescription = 0x7f0300b8
com.wellmetrix.wellmetrixprovider:attr/selectableItemBackgroundBorderless = 0x7f0300dd
com.wellmetrix.wellmetrixprovider:style/Theme.AppCompat.DayNight = 0x7f0e0100
com.wellmetrix.wellmetrixprovider:color/colorPrimaryDark = 0x7f05002d
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_16 = 0x7f08000f
com.wellmetrix.wellmetrixprovider:attr/textAppearanceLargePopupMenu = 0x7f0300f9
com.wellmetrix.wellmetrixprovider:attr/colorError = 0x7f030057
com.wellmetrix.wellmetrixprovider:attr/selectableItemBackground = 0x7f0300dc
com.wellmetrix.wellmetrixprovider:dimen/abc_text_size_small_material = 0x7f06004c
com.wellmetrix.wellmetrixprovider:attr/seekBarStyle = 0x7f0300db
com.wellmetrix.wellmetrixprovider:style/ThemeOverlay.AppCompat.ActionBar = 0x7f0e0118
com.wellmetrix.wellmetrixprovider:attr/actionBarSize = 0x7f030003
com.wellmetrix.wellmetrixprovider:attr/searchViewStyle = 0x7f0300da
com.wellmetrix.wellmetrixprovider:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f070020
com.wellmetrix.wellmetrixprovider:drawable/abc_popup_background_mtrl_mult = 0x7f070032
com.wellmetrix.wellmetrixprovider:color/background_floating_material_dark = 0x7f05001d
com.wellmetrix.wellmetrixprovider:attr/ratingBarStyleSmall = 0x7f0300d7
com.wellmetrix.wellmetrixprovider:attr/actionButtonStyle = 0x7f03000b
com.wellmetrix.wellmetrixprovider:attr/ratingBarStyleIndicator = 0x7f0300d6
com.wellmetrix.wellmetrixprovider:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010
com.wellmetrix.wellmetrixprovider:attr/preserveIconSpacing = 0x7f0300ce
com.wellmetrix.wellmetrixprovider:attr/fontProviderAuthority = 0x7f030084
com.wellmetrix.wellmetrixprovider:attr/windowMinWidthMajor = 0x7f03012a
com.wellmetrix.wellmetrixprovider:style/Widget.Compat.NotificationActionContainer = 0x7f0e0169
com.wellmetrix.wellmetrixprovider:attr/height = 0x7f030090
com.wellmetrix.wellmetrixprovider:attr/ratingBarStyle = 0x7f0300d5
com.wellmetrix.wellmetrixprovider:attr/contentInsetStartWithNavigation = 0x7f030062
com.wellmetrix.wellmetrixprovider:anim/abc_slide_in_top = 0x7f010007
com.wellmetrix.wellmetrixprovider:attr/popupWindowStyle = 0x7f0300cc
com.wellmetrix.wellmetrixprovider:id/scrollView = 0x7f080083
com.wellmetrix.wellmetrixprovider:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014
com.wellmetrix.wellmetrixprovider:attr/popupTheme = 0x7f0300cb
com.wellmetrix.wellmetrixprovider:attr/numericModifiers = 0x7f0300c1
com.wellmetrix.wellmetrixprovider:attr/barLength = 0x7f030038
com.wellmetrix.wellmetrixprovider:id/text2 = 0x7f0800ac
com.wellmetrix.wellmetrixprovider:attr/popupMenuStyle = 0x7f0300ca
com.wellmetrix.wellmetrixprovider:attr/autoSizePresetSizes = 0x7f030030
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_10 = 0x7f080009
com.wellmetrix.wellmetrixprovider:attr/colorControlActivated = 0x7f030054
com.wellmetrix.wellmetrixprovider:attr/panelMenuListWidth = 0x7f0300c9
com.wellmetrix.wellmetrixprovider:dimen/abc_text_size_title_material = 0x7f06004f
com.wellmetrix.wellmetrixprovider:layout/support_simple_spinner_dropdown_item = 0x7f0b002d
com.wellmetrix.wellmetrixprovider:attr/thumbTextPadding = 0x7f030106
com.wellmetrix.wellmetrixprovider:attr/actionModePopupWindowStyle = 0x7f030018
com.wellmetrix.wellmetrixprovider:styleable/bridge_fragment = 0x7f0f0031
com.wellmetrix.wellmetrixprovider:attr/imageButtonStyle = 0x7f030098
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f0e00f0
com.wellmetrix.wellmetrixprovider:attr/ttcIndex = 0x7f030120
com.wellmetrix.wellmetrixprovider:styleable/AlertDialog = 0x7f0f0006
com.wellmetrix.wellmetrixprovider:attr/buttonBarPositiveButtonStyle = 0x7f03003d
com.wellmetrix.wellmetrixprovider:attr/listPreferredItemHeightLarge = 0x7f0300b1
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.ActionBar.Solid = 0x7f0e0121
com.wellmetrix.wellmetrixprovider:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f0e0054
com.wellmetrix.wellmetrixprovider:animator/fragment_open_exit = 0x7f020005
com.wellmetrix.wellmetrixprovider:drawable/abc_tab_indicator_material = 0x7f070044
com.wellmetrix.wellmetrixprovider:attr/multiChoiceItemLayout = 0x7f0300bc
com.wellmetrix.wellmetrixprovider:attr/paddingBottomNoButtons = 0x7f0300c3
com.wellmetrix.wellmetrixprovider:id/tag_transition_group = 0x7f0800a7
com.wellmetrix.wellmetrixprovider:drawable/abc_textfield_activated_mtrl_alpha = 0x7f07004a
com.wellmetrix.wellmetrixprovider:attr/background = 0x7f030033
com.wellmetrix.wellmetrixprovider:attr/measureWithLargestChild = 0x7f0300ba
com.wellmetrix.wellmetrixprovider:id/image = 0x7f080065
com.wellmetrix.wellmetrixprovider:styleable/LinearLayoutCompat_Layout = 0x7f0f001f
com.wellmetrix.wellmetrixprovider:style/Base.Theme.SplashScreen.DayNight = 0x7f0e004e
com.wellmetrix.wellmetrixprovider:attr/fontProviderFetchStrategy = 0x7f030086
com.wellmetrix.wellmetrixprovider:dimen/abc_alert_dialog_button_dimen = 0x7f060011
com.wellmetrix.wellmetrixprovider:attr/listPreferredItemPaddingStart = 0x7f0300b6
com.wellmetrix.wellmetrixprovider:dimen/highlight_alpha_material_dark = 0x7f06005b
com.wellmetrix.wellmetrixprovider:attr/panelMenuListTheme = 0x7f0300c8
com.wellmetrix.wellmetrixprovider:attr/listPreferredItemPaddingLeft = 0x7f0300b4
com.wellmetrix.wellmetrixprovider:attr/listPreferredItemPaddingEnd = 0x7f0300b3
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f0e0037
com.wellmetrix.wellmetrixprovider:attr/listPreferredItemHeight = 0x7f0300b0
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f0e008a
com.wellmetrix.wellmetrixprovider:attr/listPopupWindowStyle = 0x7f0300af
com.wellmetrix.wellmetrixprovider:color/call_notification_answer_color = 0x7f050029
com.wellmetrix.wellmetrixprovider:attr/listLayout = 0x7f0300ad
com.wellmetrix.wellmetrixprovider:attr/drawableLeftCompat = 0x7f030071
com.wellmetrix.wellmetrixprovider:color/bright_foreground_inverse_material_dark = 0x7f050023
com.wellmetrix.wellmetrixprovider:attr/navigationIcon = 0x7f0300be
com.wellmetrix.wellmetrixprovider:attr/listItemLayout = 0x7f0300ac
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.AutoCompleteTextView = 0x7f0e012a
com.wellmetrix.wellmetrixprovider:mipmap/ic_launcher_round = 0x7f0c0002
com.wellmetrix.wellmetrixprovider:attr/listDividerAlertDialog = 0x7f0300ab
com.wellmetrix.wellmetrixprovider:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f0e00b5
com.wellmetrix.wellmetrixprovider:attr/listChoiceIndicatorSingleAnimated = 0x7f0300aa
com.wellmetrix.wellmetrixprovider:drawable/abc_list_selector_holo_dark = 0x7f07002f
com.wellmetrix.wellmetrixprovider:attr/textAppearanceSearchResultSubtitle = 0x7f0300fe
com.wellmetrix.wellmetrixprovider:attr/listChoiceIndicatorMultipleAnimated = 0x7f0300a9
com.wellmetrix.wellmetrixprovider:dimen/abc_control_corner_material = 0x7f060018
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f0e0096
com.wellmetrix.wellmetrixprovider:attr/subMenuArrow = 0x7f0300ed
com.wellmetrix.wellmetrixprovider:attr/listChoiceBackgroundIndicator = 0x7f0300a8
com.wellmetrix.wellmetrixprovider:color/accent_material_dark = 0x7f050019
com.wellmetrix.wellmetrixprovider:attr/splitTrack = 0x7f0300e8
com.wellmetrix.wellmetrixprovider:id/textView = 0x7f0800af
com.wellmetrix.wellmetrixprovider:attr/lineHeight = 0x7f0300a7
com.wellmetrix.wellmetrixprovider:attr/layout_dodgeInsetEdges = 0x7f0300a4
com.wellmetrix.wellmetrixprovider:drawable/$ic_launcher_foreground__0 = 0x7f070000
com.wellmetrix.wellmetrixprovider:attr/buttonPanelSideLayout = 0x7f030042
com.wellmetrix.wellmetrixprovider:attr/drawableTint = 0x7f030075
com.wellmetrix.wellmetrixprovider:dimen/abc_dialog_fixed_width_major = 0x7f06001e
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f0e0089
com.wellmetrix.wellmetrixprovider:attr/fontFamily = 0x7f030083
com.wellmetrix.wellmetrixprovider:attr/dialogCornerRadius = 0x7f030067
com.wellmetrix.wellmetrixprovider:attr/colorAccent = 0x7f030051
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Button.Borderless = 0x7f0e012c
com.wellmetrix.wellmetrixprovider:drawable/abc_spinner_mtrl_am_alpha = 0x7f07003e
com.wellmetrix.wellmetrixprovider:drawable/abc_seekbar_tick_mark_material = 0x7f07003c
com.wellmetrix.wellmetrixprovider:dimen/abc_button_inset_horizontal_material = 0x7f060012
com.wellmetrix.wellmetrixprovider:attr/lStar = 0x7f03009e
com.wellmetrix.wellmetrixprovider:attr/keylines = 0x7f03009d
com.wellmetrix.wellmetrixprovider:attr/fontStyle = 0x7f03008b
com.wellmetrix.wellmetrixprovider:attr/isLightTheme = 0x7f03009b
com.wellmetrix.wellmetrixprovider:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013
com.wellmetrix.wellmetrixprovider:attr/paddingStart = 0x7f0300c5
com.wellmetrix.wellmetrixprovider:dimen/tooltip_horizontal_padding = 0x7f060078
com.wellmetrix.wellmetrixprovider:attr/fontWeight = 0x7f03008d
com.wellmetrix.wellmetrixprovider:attr/contentInsetStart = 0x7f030061
com.wellmetrix.wellmetrixprovider:attr/iconTint = 0x7f030095
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Inverse = 0x7f0e0019
com.wellmetrix.wellmetrixprovider:attr/fontProviderSystemFontFamily = 0x7f03008a
com.wellmetrix.wellmetrixprovider:attr/textAppearanceListItemSecondary = 0x7f0300fb
com.wellmetrix.wellmetrixprovider:styleable/Spinner = 0x7f0f0028
com.wellmetrix.wellmetrixprovider:attr/contentInsetRight = 0x7f030060
com.wellmetrix.wellmetrixprovider:attr/goIcon = 0x7f03008f
com.wellmetrix.wellmetrixprovider:attr/toolbarNavigationButtonStyle = 0x7f030118
com.wellmetrix.wellmetrixprovider:color/bright_foreground_disabled_material_light = 0x7f050022
com.wellmetrix.wellmetrixprovider:color/abc_decor_view_status_guard = 0x7f050005
com.wellmetrix.wellmetrixprovider:attr/itemPadding = 0x7f03009c
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_15 = 0x7f08000e
com.wellmetrix.wellmetrixprovider:dimen/splashscreen_icon_mask_stroke_with_background = 0x7f060073
com.wellmetrix.wellmetrixprovider:dimen/hint_alpha_material_light = 0x7f06005e
com.wellmetrix.wellmetrixprovider:attr/actionModeFindDrawable = 0x7f030016
com.wellmetrix.wellmetrixprovider:attr/firstBaselineToTopHeight = 0x7f030081
com.wellmetrix.wellmetrixprovider:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f0e0042
com.wellmetrix.wellmetrixprovider:attr/iconifiedByDefault = 0x7f030097
com.wellmetrix.wellmetrixprovider:attr/fontProviderFetchTimeout = 0x7f030087
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f0e00da
com.wellmetrix.wellmetrixprovider:attr/listPreferredItemHeightSmall = 0x7f0300b2
com.wellmetrix.wellmetrixprovider:attr/collapseContentDescription = 0x7f03004e
com.wellmetrix.wellmetrixprovider:attr/font = 0x7f030082
com.wellmetrix.wellmetrixprovider:id/content = 0x7f08004c
com.wellmetrix.wellmetrixprovider:string/abc_searchview_description_search = 0x7f0d0015
com.wellmetrix.wellmetrixprovider:id/action_menu_divider = 0x7f080032
com.wellmetrix.wellmetrixprovider:drawable/abc_ratingbar_material = 0x7f070034
com.wellmetrix.wellmetrixprovider:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f060020
com.wellmetrix.wellmetrixprovider:attr/windowFixedHeightMajor = 0x7f030126
com.wellmetrix.wellmetrixprovider:attr/autoCompleteTextViewStyle = 0x7f03002d
com.wellmetrix.wellmetrixprovider:dimen/abc_dropdownitem_text_padding_left = 0x7f06002a
com.wellmetrix.wellmetrixprovider:attr/arrowShaftLength = 0x7f03002c
com.wellmetrix.wellmetrixprovider:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f0e00b9
com.wellmetrix.wellmetrixprovider:id/message = 0x7f08006d
com.wellmetrix.wellmetrixprovider:anim/abc_tooltip_enter = 0x7f01000a
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.ImageButton = 0x7f0e0139
com.wellmetrix.wellmetrixprovider:attr/expandActivityOverflowButtonDrawable = 0x7f030080
com.wellmetrix.wellmetrixprovider:attr/defaultQueryHint = 0x7f030066
com.wellmetrix.wellmetrixprovider:attr/editTextBackground = 0x7f03007b
com.wellmetrix.wellmetrixprovider:color/ripple_material_dark = 0x7f05004f
com.wellmetrix.wellmetrixprovider:attr/lastBaselineToBottomHeight = 0x7f03009f
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f0e009b
com.wellmetrix.wellmetrixprovider:color/secondary_text_disabled_material_light = 0x7f050054
com.wellmetrix.wellmetrixprovider:color/abc_primary_text_material_dark = 0x7f05000b
com.wellmetrix.wellmetrixprovider:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f0e0101
com.wellmetrix.wellmetrixprovider:attr/queryBackground = 0x7f0300d1
com.wellmetrix.wellmetrixprovider:drawable/abc_btn_default_mtrl_shape = 0x7f070009
com.wellmetrix.wellmetrixprovider:attr/dividerVertical = 0x7f03006e
com.wellmetrix.wellmetrixprovider:dimen/notification_right_side_padding_top = 0x7f06006a
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.Spinner = 0x7f0e0161
com.wellmetrix.wellmetrixprovider:attr/dividerPadding = 0x7f03006d
com.wellmetrix.wellmetrixprovider:string/abc_action_bar_home_description = 0x7f0d0000
com.wellmetrix.wellmetrixprovider:attr/dividerHorizontal = 0x7f03006c
com.wellmetrix.wellmetrixprovider:color/material_grey_850 = 0x7f050043
com.wellmetrix.wellmetrixprovider:anim/abc_popup_enter = 0x7f010003
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.RatingBar = 0x7f0e015a
com.wellmetrix.wellmetrixprovider:attr/coordinatorLayoutStyle = 0x7f030064
com.wellmetrix.wellmetrixprovider:attr/controlBackground = 0x7f030063
com.wellmetrix.wellmetrixprovider:attr/buttonTint = 0x7f030045
com.wellmetrix.wellmetrixprovider:dimen/abc_action_button_min_height_material = 0x7f06000d
com.wellmetrix.wellmetrixprovider:drawable/abc_action_bar_item_background_material = 0x7f070002
com.wellmetrix.wellmetrixprovider:dimen/notification_top_pad_large_text = 0x7f06006f
com.wellmetrix.wellmetrixprovider:styleable/ActionBar = 0x7f0f0000
com.wellmetrix.wellmetrixprovider:attr/tickMarkTintMode = 0x7f03010b
com.wellmetrix.wellmetrixprovider:layout/abc_alert_dialog_button_bar_material = 0x7f0b0008
com.wellmetrix.wellmetrixprovider:attr/overlapAnchor = 0x7f0300c2
com.wellmetrix.wellmetrixprovider:drawable/abc_text_select_handle_left_mtrl = 0x7f070047
com.wellmetrix.wellmetrixprovider:attr/colorControlHighlight = 0x7f030055
com.wellmetrix.wellmetrixprovider:anim/abc_tooltip_exit = 0x7f01000b
com.wellmetrix.wellmetrixprovider:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011
com.wellmetrix.wellmetrixprovider:attr/drawableSize = 0x7f030073
com.wellmetrix.wellmetrixprovider:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f07004c
com.wellmetrix.wellmetrixprovider:attr/contentInsetEnd = 0x7f03005d
com.wellmetrix.wellmetrixprovider:attr/contentDescription = 0x7f03005c
com.wellmetrix.wellmetrixprovider:attr/windowSplashScreenAnimationDuration = 0x7f03012e
com.wellmetrix.wellmetrixprovider:attr/actionBarStyle = 0x7f030005
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.PopupWindow = 0x7f0e0157
com.wellmetrix.wellmetrixprovider:dimen/abc_dialog_title_divider_material = 0x7f060026
com.wellmetrix.wellmetrixprovider:id/none = 0x7f080071
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_29 = 0x7f08001d
com.wellmetrix.wellmetrixprovider:attr/listPreferredItemPaddingRight = 0x7f0300b5
com.wellmetrix.wellmetrixprovider:drawable/abc_text_cursor_material = 0x7f070046
com.wellmetrix.wellmetrixprovider:attr/divider = 0x7f03006b
com.wellmetrix.wellmetrixprovider:integer/abc_config_activityShortDur = 0x7f090001
com.wellmetrix.wellmetrixprovider:attr/paddingEnd = 0x7f0300c4
com.wellmetrix.wellmetrixprovider:dimen/abc_control_padding_material = 0x7f06001a
com.wellmetrix.wellmetrixprovider:attr/actionModeStyle = 0x7f03001c
com.wellmetrix.wellmetrixprovider:attr/color = 0x7f030050
com.wellmetrix.wellmetrixprovider:dimen/tooltip_margin = 0x7f060079
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f0e001f
com.wellmetrix.wellmetrixprovider:dimen/splashscreen_icon_size = 0x7f060074
com.wellmetrix.wellmetrixprovider:attr/collapseIcon = 0x7f03004f
com.wellmetrix.wellmetrixprovider:layout/abc_list_menu_item_icon = 0x7f0b000f
com.wellmetrix.wellmetrixprovider:attr/checkMarkCompat = 0x7f030047
com.wellmetrix.wellmetrixprovider:attr/drawableEndCompat = 0x7f030070
com.wellmetrix.wellmetrixprovider:id/ifRoom = 0x7f080064
com.wellmetrix.wellmetrixprovider:drawable/abc_star_black_48dp = 0x7f070040
com.wellmetrix.wellmetrixprovider:attr/closeIcon = 0x7f03004c
com.wellmetrix.wellmetrixprovider:attr/logo = 0x7f0300b7
com.wellmetrix.wellmetrixprovider:id/accessibility_custom_action_26 = 0x7f08001a
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f0e00f2
com.wellmetrix.wellmetrixprovider:attr/checkboxStyle = 0x7f03004a
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f0e00a4
com.wellmetrix.wellmetrixprovider:drawable/abc_list_pressed_holo_light = 0x7f07002a
com.wellmetrix.wellmetrixprovider:color/error_color_material_dark = 0x7f050032
com.wellmetrix.wellmetrixprovider:attr/statusBarBackground = 0x7f0300ec
com.wellmetrix.wellmetrixprovider:attr/drawerArrowStyle = 0x7f030078
com.wellmetrix.wellmetrixprovider:attr/buttonStyleSmall = 0x7f030044
com.wellmetrix.wellmetrixprovider:id/spacer = 0x7f080093
com.wellmetrix.wellmetrixprovider:attr/actionBarTabStyle = 0x7f030007
com.wellmetrix.wellmetrixprovider:drawable/test_level_drawable = 0x7f070071
com.wellmetrix.wellmetrixprovider:attr/buttonCompat = 0x7f03003f
com.wellmetrix.wellmetrixprovider:dimen/abc_star_medium = 0x7f06003c
com.wellmetrix.wellmetrixprovider:id/start = 0x7f08009a
com.wellmetrix.wellmetrixprovider:attr/buttonBarStyle = 0x7f03003e
com.wellmetrix.wellmetrixprovider:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f0e006a
com.wellmetrix.wellmetrixprovider:dimen/notification_large_icon_width = 0x7f060066
com.wellmetrix.wellmetrixprovider:attr/indeterminateProgressStyle = 0x7f030099
com.wellmetrix.wellmetrixprovider:drawable/abc_list_selector_background_transition_holo_light = 0x7f07002c
com.wellmetrix.wellmetrixprovider:attr/actionModeSelectAllDrawable = 0x7f030019
com.wellmetrix.wellmetrixprovider:color/switch_thumb_disabled_material_dark = 0x7f050055
com.wellmetrix.wellmetrixprovider:layout/abc_alert_dialog_title_material = 0x7f0b000a
com.wellmetrix.wellmetrixprovider:color/material_blue_grey_800 = 0x7f050039
com.wellmetrix.wellmetrixprovider:attr/borderlessButtonStyle = 0x7f030039
com.wellmetrix.wellmetrixprovider:string/abc_action_menu_overflow_description = 0x7f0d0002
com.wellmetrix.wellmetrixprovider:attr/contentInsetEndWithActions = 0x7f03005e
com.wellmetrix.wellmetrixprovider:attr/actionBarTabBarStyle = 0x7f030006
com.wellmetrix.wellmetrixprovider:attr/backgroundTint = 0x7f030036
com.wellmetrix.wellmetrixprovider:attr/editTextStyle = 0x7f03007d
com.wellmetrix.wellmetrixprovider:attr/trackTintMode = 0x7f03011f
com.wellmetrix.wellmetrixprovider:attr/nestedScrollViewStyle = 0x7f0300c0
com.wellmetrix.wellmetrixprovider:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f0e0052
com.wellmetrix.wellmetrixprovider:color/abc_primary_text_material_light = 0x7f05000c
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Small.Inverse = 0x7f0e00dd
com.wellmetrix.wellmetrixprovider:drawable/ic_call_decline = 0x7f07005e
com.wellmetrix.wellmetrixprovider:attr/buttonTintMode = 0x7f030046
com.wellmetrix.wellmetrixprovider:attr/backgroundStacked = 0x7f030035
com.wellmetrix.wellmetrixprovider:attr/autoSizeMaxTextSize = 0x7f03002e
com.wellmetrix.wellmetrixprovider:dimen/abc_action_bar_content_inset_material = 0x7f060000
com.wellmetrix.wellmetrixprovider:id/search_edit_frame = 0x7f080088
com.wellmetrix.wellmetrixprovider:attr/actionModeCloseContentDescription = 0x7f030012
com.wellmetrix.wellmetrixprovider:attr/actionModeCopyDrawable = 0x7f030014
com.wellmetrix.wellmetrixprovider:attr/actionModeTheme = 0x7f03001d
com.wellmetrix.wellmetrixprovider:string/abc_activitychooserview_choose_application = 0x7f0d0005
com.wellmetrix.wellmetrixprovider:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012
com.wellmetrix.wellmetrixprovider:attr/elevation = 0x7f03007e
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.ActionButton = 0x7f0e0125
com.wellmetrix.wellmetrixprovider:dimen/abc_progress_bar_height_material = 0x7f060035
com.wellmetrix.wellmetrixprovider:style/Base.V21.Theme.AppCompat = 0x7f0e0057
com.wellmetrix.wellmetrixprovider:color/switch_thumb_material_dark = 0x7f050057
com.wellmetrix.wellmetrixprovider:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017
com.wellmetrix.wellmetrixprovider:attr/emojiCompatEnabled = 0x7f03007f
com.wellmetrix.wellmetrixprovider:attr/actionOverflowMenuStyle = 0x7f030020
com.wellmetrix.wellmetrixprovider:anim/abc_slide_in_bottom = 0x7f010006
com.wellmetrix.wellmetrixprovider:string/abc_capital_on = 0x7f0d0007
com.wellmetrix.wellmetrixprovider:dimen/abc_action_bar_stacked_tab_max_width = 0x7f06000a
com.wellmetrix.wellmetrixprovider:attr/queryPatterns = 0x7f0300d3
com.wellmetrix.wellmetrixprovider:id/accessibility_action_clickable_span = 0x7f080006
com.wellmetrix.wellmetrixprovider:attr/actionOverflowButtonStyle = 0x7f03001f
com.wellmetrix.wellmetrixprovider:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016
com.wellmetrix.wellmetrixprovider:animator/fragment_fade_enter = 0x7f020002
com.wellmetrix.wellmetrixprovider:attr/fontVariationSettings = 0x7f03008c
com.wellmetrix.wellmetrixprovider:attr/buttonStyle = 0x7f030043
com.wellmetrix.wellmetrixprovider:styleable/TextAppearance = 0x7f0f002c
com.wellmetrix.wellmetrixprovider:animator/fragment_close_exit = 0x7f020001
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f0e00eb
com.wellmetrix.wellmetrixprovider:attr/actionModeShareDrawable = 0x7f03001a
com.wellmetrix.wellmetrixprovider:dimen/notification_small_icon_background_padding = 0x7f06006b
com.wellmetrix.wellmetrixprovider:style/Base.AlertDialog.AppCompat.Light = 0x7f0e0009
com.wellmetrix.wellmetrixprovider:attr/windowFixedWidthMinor = 0x7f030129
com.wellmetrix.wellmetrixprovider:attr/showDividers = 0x7f0300e0
com.wellmetrix.wellmetrixprovider:attr/displayOptions = 0x7f03006a
com.wellmetrix.wellmetrixprovider:id/activity_chooser_view_content = 0x7f080039
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f0e001d
com.wellmetrix.wellmetrixprovider:attr/layout_keyline = 0x7f0300a6
com.wellmetrix.wellmetrixprovider:styleable/AnimatedStateListDrawableCompat = 0x7f0f0007
com.wellmetrix.wellmetrixprovider:attr/actionBarSplitStyle = 0x7f030004
com.wellmetrix.wellmetrixprovider:attr/switchMinWidth = 0x7f0300f4
com.wellmetrix.wellmetrixprovider:attr/actionModeCutDrawable = 0x7f030015
com.wellmetrix.wellmetrixprovider:attr/actionModePasteDrawable = 0x7f030017
com.wellmetrix.wellmetrixprovider:attr/textColorSearchUrl = 0x7f030102
com.wellmetrix.wellmetrixprovider:styleable/RecycleListView = 0x7f0f0026
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f0e0036
com.wellmetrix.wellmetrixprovider:string/abc_menu_delete_shortcut_label = 0x7f0d000a
com.wellmetrix.wellmetrixprovider:drawable/ic_call_decline_low = 0x7f07005f
com.wellmetrix.wellmetrixprovider:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f070036
com.wellmetrix.wellmetrixprovider:attr/subtitle = 0x7f0300ef
com.wellmetrix.wellmetrixprovider:attr/autoSizeMinTextSize = 0x7f03002f
com.wellmetrix.wellmetrixprovider:attr/layout_anchorGravity = 0x7f0300a2
com.wellmetrix.wellmetrixprovider:attr/searchHintIcon = 0x7f0300d8
com.wellmetrix.wellmetrixprovider:attr/actionModeCloseDrawable = 0x7f030013
com.wellmetrix.wellmetrixprovider:attr/actionModeBackground = 0x7f030010
com.wellmetrix.wellmetrixprovider:anim/abc_fade_in = 0x7f010000
com.wellmetrix.wellmetrixprovider:id/clip_vertical = 0x7f08004a
com.wellmetrix.wellmetrixprovider:style/Theme.AppCompat.Light = 0x7f0e010c
com.wellmetrix.wellmetrixprovider:attr/windowActionModeOverlay = 0x7f030125
com.wellmetrix.wellmetrixprovider:anim/abc_fade_out = 0x7f010001
com.wellmetrix.wellmetrixprovider:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f070038
com.wellmetrix.wellmetrixprovider:attr/queryHint = 0x7f0300d2
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f0e00a2
com.wellmetrix.wellmetrixprovider:drawable/ic_call_answer_video_low = 0x7f07005d
com.wellmetrix.wellmetrixprovider:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f0e0080
com.wellmetrix.wellmetrixprovider:drawable/ic_call_answer_low = 0x7f07005b
com.wellmetrix.wellmetrixprovider:string/call_notification_screening_text = 0x7f0d0023
com.wellmetrix.wellmetrixprovider:attr/actionBarWidgetTheme = 0x7f03000a
com.wellmetrix.wellmetrixprovider:style/Base.V28.Theme.AppCompat.Light = 0x7f0e0064
com.wellmetrix.wellmetrixprovider:attr/actionBarTheme = 0x7f030009
com.wellmetrix.wellmetrixprovider:attr/radioButtonStyle = 0x7f0300d4
com.wellmetrix.wellmetrixprovider:attr/activityChooserViewStyle = 0x7f030023
com.wellmetrix.wellmetrixprovider:color/notification_icon_bg_color = 0x7f050046
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Button = 0x7f0e0012
com.wellmetrix.wellmetrixprovider:anim/fragment_fast_out_extra_slow_in = 0x7f010018
com.wellmetrix.wellmetrixprovider:animator/fragment_open_enter = 0x7f020004
com.wellmetrix.wellmetrixprovider:attr/actionBarItemBackground = 0x7f030001
com.wellmetrix.wellmetrixprovider:color/abc_search_url_text_pressed = 0x7f05000f
com.wellmetrix.wellmetrixprovider:drawable/abc_item_background_holo_dark = 0x7f070023
com.wellmetrix.wellmetrixprovider:attr/actionBarDivider = 0x7f030000
com.wellmetrix.wellmetrixprovider:attr/backgroundSplit = 0x7f030034
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Display1 = 0x7f0e0014
com.wellmetrix.wellmetrixprovider:dimen/abc_text_size_body_1_material = 0x7f06003f
com.wellmetrix.wellmetrixprovider:dimen/abc_dialog_fixed_width_minor = 0x7f06001f
com.wellmetrix.wellmetrixprovider:style/Widget.AppCompat.CompoundButton.Switch = 0x7f0e0135
com.wellmetrix.wellmetrixprovider:attr/dropDownListViewStyle = 0x7f030079
com.wellmetrix.wellmetrixprovider:dimen/abc_dialog_min_width_minor = 0x7f060023
com.wellmetrix.wellmetrixprovider:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000e
com.wellmetrix.wellmetrixprovider:string/abc_action_bar_up_description = 0x7f0d0001
com.wellmetrix.wellmetrixprovider:dimen/abc_text_size_display_1_material = 0x7f060043
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f0e00e3
com.wellmetrix.wellmetrixprovider:attr/commitIcon = 0x7f03005b
com.wellmetrix.wellmetrixprovider:id/blocking = 0x7f080040
com.wellmetrix.wellmetrixprovider:style/TextAppearance.AppCompat.Title.Inverse = 0x7f0e00e1
com.wellmetrix.wellmetrixprovider:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f070039
com.wellmetrix.wellmetrixprovider:attr/switchStyle = 0x7f0300f6
com.wellmetrix.wellmetrixprovider:attr/buttonBarNeutralButtonStyle = 0x7f03003c
com.wellmetrix.wellmetrixprovider:attr/colorBackgroundFloating = 0x7f030052
com.wellmetrix.wellmetrixprovider:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f0e0031
com.wellmetrix.wellmetrixprovider:attr/actionProviderClass = 0x7f030021
