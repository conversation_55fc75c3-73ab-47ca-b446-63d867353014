//@ts-ignore
// @ts-nocheck
import React from 'react';
import './deviceSetup.css'
// import { BLE } from "@ionic-native/ble";
import { IonCard, IonContent} from '@ionic/react';



export default class SelectDevice extends React.Component{
    constructor(props){
        super(props);
    }
    componentDidMount(){}
    render(){
        return <>
        {/* NEVER USE ION-CONTENT HERE, ION CARDS ARE NOT VISBLE, IT MIGHT CREATE ERROR*/}
        {!!this.props.scanResult ? (this.props.scanResult.map((devices)=>{
            return <IonCard
                button={true}
                onClick={() => this.props.selectDevice(devices.id)}
                >
                {devices.name}
            </IonCard>
        })):<h2>NO DEVICES FOUND</h2>}

        </>
    }
}