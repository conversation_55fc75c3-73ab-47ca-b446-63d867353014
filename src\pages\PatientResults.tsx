//@ts-ignore
// @ts-nocheck
import {  IonPage } from '@ionic/react';
import React from 'react';
import Onboarding from '../components/onboarding/onboarding';
import DynamicTables from '../components/DynamicTables';
import { Typography } from '@mui/material';
import { rawThemePallete } from '../theme/theme';


const PatientResults: React.FC = () => {
    return<>
    {/* <Typography color={rawThemePallete.typography.color}>
        View Results
    </Typography> */}
        <DynamicTables/>
    
    </>
}

export default PatientResults;