import React from 'react';
import BatteryLevel from '../components/BatteryLevel';
import { Typo<PERSON>, <PERSON>ton, Card, CardContent, Grid } from '@mui/material';
import { Line } from 'react-chartjs-2';

const LandingPage: React.FC = () => {
  // Dummy data for charts
  const chartData = {
    labels: ['1', '2', '3', '4', '5'], // X-axis labels
    datasets: [
      {
        label: 'Progress',
        data: [20, 40, 60, 80, 100], // Y-axis data
        fill: false,
        backgroundColor: '#3f51b5',
        borderColor: '#3f51b5',
      },
    ],
  };

  // Chart options
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { display: false },
    },
    scales: {
      x: { display: false },
      y: { display: false },
    },
  };

  return (
    <div style={{ padding: '16px' }}>
      {/* Header */}
      <header style={{ marginBottom: '24px' }}>
        <Typography variant="h4" align="center">
          Patient Dashboard
        </Typography>
      </header>

      {/* Battery Level Section */}
      <BatteryLevel batteryPercentage={75} />

      {/* Activities Grid */}
      <Grid container spacing={2}>
        {[
          'Cervical Chin Tuck',
          'Cervical Lateral Bending (Left)',
          'Cervical Rotation (Right)',
          'Core Pushup',
          'Core Squat (Assistance)',
          'Hip Abduction',
          'Hip Extension',
        ].map((activity, index) => (
          <Grid item xs={12} sm={6} md={4} key={index}>
            <Card style={{ textAlign: 'center', padding: '10px' }}>
              <CardContent>
                <Typography variant="subtitle1" gutterBottom>
                  {activity}
                </Typography>
                <div style={{ height: '100px', margin: '10px 0' }}>
                  <Line data={chartData} options={chartOptions} />
                </div>
                <Button
                  variant="outlined"
                  size="small"
                  href={`/activity/${index}`}
                  style={{ marginTop: '10px' }}
                >
                  View Details
                </Button>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </div>
  );
};

export default LandingPage;
