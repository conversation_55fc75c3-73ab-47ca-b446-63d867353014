//@ts-nocheck
import React, { useEffect, useState, useRef } from 'react';
import { Box, Typography, Button, IconButton } from '@mui/material';
import { Canvas } from '@react-three/fiber';
import { OrbitControls, Text } from '@react-three/drei';
import { Suspense } from 'react';
import { BluetoothLe } from '@capacitor-community/bluetooth-le';
import { Line } from 'react-chartjs-2';
import * as THREE from 'three';
import { useParams } from 'react-router';
import { rawThemePallete } from '../theme/theme';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';
import { ArrowForward, ArrowBack, PlayCircle, Upload } from '@mui/icons-material';


const patient = {
  name: '<PERSON>',
  age: 30,
  condition: 'Neck Pain',
  deviceId: 'ABC123',
};

const chartsData = {
  labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
  datasets: [
    {
      label: 'Movement Progress',
      data: [65, 59, 80, 81, 56],
      borderColor: '#3f51b5',
      tension: 0.4,
    },
  ],
};

const preloadModel = (url) => {
  const loader = new GLTFLoader();
  return new Promise((resolve, reject) => {
    loader.load(url, (gltf) => resolve(gltf), null, reject);
  });
};

const PatientPage = () => {
  const [loading, setLoading] = useState(true);
  const [bluetoothStatus, setBluetoothStatus] = useState('Disconnected');
  const [deviceData, setDeviceData] = useState(null);
  const [gltfScene, setGltfScene] = useState(null);
  const params = useParams();

  const canvasRef = useRef(null);

  useEffect(() => {
    // Load the 3D model
    preloadModel('/models/Robot.glb')
      .then((gltf) => {
        gltf.scene.traverse((child) => {
          if (child.isMesh) {
            child.material = new THREE.MeshStandardMaterial({
              color: rawThemePallete.palette.primary.main,
            });
          }
        });
        setGltfScene(gltf.scene);
        setLoading(false);
      })
      .catch((error) => console.error('Failed to load model:', error));
  }, []);

  const updateCanvasSize = () => {
    if (canvasRef.current) {
      canvasRef.current.style.height = '400px'; // Fixed size
      canvasRef.current.style.width = '100%';
    }
  };

  useEffect(() => {
    updateCanvasSize();
    window.addEventListener('resize', updateCanvasSize);

    return () => {
      window.removeEventListener('resize', updateCanvasSize);
    };
  }, []);

  const handleConnect = async () => {
    try {
      const device = await BluetoothLe.requestDevice({
        filters: [{ name: patient.deviceId }],
      });

      await BluetoothLe.connect({ deviceId: device.device.id });
      setBluetoothStatus('Connected');
      setDeviceData({ battery: 85, status: 'Normal' });
    } catch (error) {
      console.error('Bluetooth connection failed:', error);
      setBluetoothStatus('Failed to connect');
    }
  };

  const handleDisconnect = async () => {
    try {
      await BluetoothLe.disconnect({ deviceId: patient.deviceId });
      setBluetoothStatus('Disconnected');
      setDeviceData(null);
    } catch (error) {
      console.error('Bluetooth disconnection failed:', error);
      setBluetoothStatus('Failed to disconnect');
    }
  };

  // Handle the control button actions
  const handleLeft = () => {
    console.log('Move Left');
  };

  const handleRight = () => {
    console.log('Move Right');
  };

  const handleStartRecording = () => {
    console.log('Start Recording');
  };

  const handleUploadData = () => {
    console.log('Upload Data');
  };

  return (
    <Box sx={{ padding: '2em' }}>
      {/* Title */}
      <Box sx={{ marginBottom: '2em' }}>
        <Typography color="secondary" variant="h1" sx={{ fontSize: '32px', fontWeight: 'bold' }} gutterBottom>
          {params.category?.split('-').join(' ').toUpperCase()}
        </Typography>
      </Box>

      {/* Patient Details */}
      <Box sx={{ marginBottom: '2em' }}>
        <Typography color="primary" variant="h6">Name: {patient.name}</Typography>
        <Typography color="primary" variant="body1">Age: {patient.age}</Typography>
      </Box>

      {/* 3D Model */}
      <Box sx={{ marginBottom: '2em' }}>
        <Canvas ref={canvasRef} style={{ height: '100%', width: '100%' }}>
          <Suspense fallback={<Text color="#cccccc" fontSize={1} position={[0, 0, 0]} scale={[1.5, 1.5, 1.5]}>Loading 3D Model...</Text>}>
            <ambientLight />
            <spotLight position={[10, 10, 10]} angle={0.15} castShadow />
            <OrbitControls />
            {gltfScene ? (
              <primitive object={gltfScene} />
            ) : (
              <Text color="#cccccc" fontSize={1} position={[0, 0, 0]} scale={[1.5, 1.5, 1.5]}>Loading......</Text>
            )}
          </Suspense>
        </Canvas>
      </Box>

      {/* Bluetooth Device Control */}
      <Box sx={{ marginBottom: '2em' }}>
        <Typography variant="h6" color="textSecondary">
          Bluetooth Device Status: {bluetoothStatus}
        </Typography>
        {bluetoothStatus === 'Disconnected' ? (
          <Button variant="contained" onClick={handleConnect}>
            Connect to Device
          </Button>
        ) : (
          <Button variant="contained" onClick={handleDisconnect}>
            Disconnect
          </Button>
        )}
        {deviceData && (
          <Box sx={{ marginTop: '1em' }}>
            <Typography variant="body1">Battery: {deviceData.battery}%</Typography>
            <Typography variant="body1">Device Status: {deviceData.status}</Typography>
          </Box>
        )}
      </Box>

      {/* Control Buttons */}
      <Box sx={{ marginBottom: '2em', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <IconButton 
          onClick={handleLeft} 
          sx={{
            backgroundColor: '#3f51b5',
            color: 'white',
            padding: '16px',
            borderRadius: '50%',
            boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.3)',
            '&:hover': {
              backgroundColor: '#303f9f',
            },
          }}
        >
          <ArrowBack sx={{ fontSize: '2rem' }} />
        </IconButton>
        <IconButton 
          onClick={handleRight} 
          sx={{
            backgroundColor: '#3f51b5',
            color: 'white',
            padding: '16px',
            borderRadius: '50%',
            boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.3)',
            '&:hover': {
              backgroundColor: '#303f9f',
            },
          }}
        >
          <ArrowForward sx={{ fontSize: '2rem' }} />
        </IconButton>
        <IconButton 
          onClick={handleStartRecording} 
          sx={{
            backgroundColor: '#3f51b5',
            color: 'white',
            padding: '16px',
            borderRadius: '50%',
            boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.3)',
            '&:hover': {
              backgroundColor: '#303f9f',
            },
          }}
        >
          <PlayCircle sx={{ fontSize: '2rem' }} />
        </IconButton>
        <IconButton 
          onClick={handleUploadData} 
          sx={{
            backgroundColor: '#3f51b5',
            color: 'white',
            padding: '16px',
            borderRadius: '50%',
            boxShadow: '0px 4px 12px rgba(0, 0, 0, 0.3)',
            '&:hover': {
              backgroundColor: '#303f9f',
            },
          }}
        >
          <Upload sx={{ fontSize: '2rem' }} />
        </IconButton>
      </Box>

      {/* Charts */}
      <Box sx={{ marginBottom: '2em', height: '400px' }}>
        <Typography variant="h6" color="textSecondary">
          Patient Progress
        </Typography>
        <Line 
          data={chartsData} 
          options={{
            responsive: true,
            maintainAspectRatio: true,
            scales: {
              x: { 
                ticks: {
                  color: '#333' 
                }
              },
              y: { 
                ticks: {
                  color: '#333' 
                }
              }
            }
          }} 
        />
      </Box>
    </Box>
  );
};

export default PatientPage;
