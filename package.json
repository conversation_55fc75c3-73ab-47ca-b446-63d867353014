{"name": "well-metrix-provider", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test.e2e": "cypress run", "test.unit": "vitest", "lint": "eslint", "device:ios": "ionic cap sync ios && ionic cap copy ios && ionic cap build ios && ionic cap run ios", "device:android": "ionic cap sync android && ionic cap copy android && ionic cap build android && ionic cap run android", "release:android": "ionic cap build android && ionic cap sync android && ionic cap copy android"}, "dependencies": {"@capacitor-community/bluetooth-le": "^7.1.1", "@capacitor-community/http": "^1.4.1", "@capacitor-firebase/authentication": "^6.3.1", "@capacitor/android": "6.2.0", "@capacitor/app": "6.0.2", "@capacitor/core": "^6.2.0", "@capacitor/haptics": "6.0.2", "@capacitor/ios": "^6.2.0", "@capacitor/keyboard": "6.0.3", "@capacitor/status-bar": "6.0.2", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@hookform/resolvers": "^5.0.1", "@ionic-native/android-permissions": "^5.36.0", "@ionic-native/ble": "^5.36.0", "@ionic-native/bluetooth-le": "^5.36.0", "@ionic-native/core": "^5.36.0", "@ionic-native/device": "^5.36.0", "@ionic-native/firebase-authentication": "^5.36.0", "@ionic-native/google-plus": "^5.36.0", "@ionic-native/native-storage": "^5.36.0", "@ionic-native/network-interface": "^5.36.0", "@ionic-native/sms-retriever": "^5.36.0", "@ionic-native/web-server": "^5.36.0", "@ionic-native/zeroconf": "^5.36.0", "@ionic/react": "^8.0.0", "@ionic/react-router": "^8.0.0", "@material-ui/core": "^4.12.4", "@material-ui/icons": "^4.11.3", "@mui/icons-material": "^6.4.1", "@mui/lab": "^6.0.0-beta.21", "@mui/material": "^6.4.1", "@mui/styled-engine": "^6.2.0", "@mui/x-date-pickers": "^7.23.3", "@react-native-async-storage/async-storage": "^1.24.0", "@react-three/drei": "^9.120.4", "@react-three/fiber": "^8.17.10", "@reduxjs/toolkit": "^2.5.0", "@svgr/webpack": "^8.1.0", "@types/file-saver": "^2.0.7", "@types/react-router": "^5.1.20", "@types/react-router-dom": "^5.3.3", "axios": "^1.7.9", "chart.js": "^4.4.7", "cordova": "^12.0.0", "cordova-plugin-ble-central": "^2.0.0", "dayjs": "^1.11.13", "file-saver": "^2.0.5", "firebase": "^11.1.0", "formik": "^2.4.6", "framer-motion": "^11.15.0", "ionicons": "^7.0.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-google-charts": "^5.2.1", "react-hook-form": "^7.54.2", "react-redux": "^9.2.0", "react-router": "^5.3.4", "react-router-dom": "^5.3.4", "react-spring": "^9.7.5", "recharts": "^2.15.3", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "rxjs": "^6.6.7", "three": "^0.171.0", "uid": "^2.0.2", "uuid": "^11.0.3", "yup": "^1.6.1"}, "devDependencies": {"@capacitor/cli": "6.2.0", "@testing-library/dom": ">=7.21.4", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^29.5.14", "@types/react": "^18.0.27", "@types/react-dom": "^18.0.10", "@vitejs/plugin-legacy": "^5.0.0", "@vitejs/plugin-react": "^4.0.1", "cypress": "^13.5.0", "eslint": "^8.35.0", "eslint-plugin-react": "^7.32.2", "jsdom": "^22.1.0", "terser": "^5.4.0", "typescript": "^5.1.6", "vite": "~5.2.0", "vite-plugin-svgr": "^4.3.0", "vitest": "^0.34.6"}, "description": "An Ionic project"}