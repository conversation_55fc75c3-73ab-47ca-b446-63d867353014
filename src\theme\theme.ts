import { createTheme } from "@mui/material/styles";


export const rawThemePallete : any=   {
  palette: {
      primary: {
          main: "#5c95ff",
          rgb: "rgb(92, 149, 255)"
      },
      secondary: {
          main: "#8c93a8",
          rgb:"rgb(140, 147, 168)"
      },
  },
  typography: {
      fontFamily: "Arial, sans-serif",
      color : "#303135"
  },
  components: {
      MuiButton: {
          styleOverrides: {
              root: {
                  textTransform: "none", // Avoid uppercase transformation
              },
          },
      },
  },
}
const theme = createTheme(
rawThemePallete
);

export default theme;
