import { createSlice } from "@reduxjs/toolkit";
const homeSlice = createSlice({
    name: "home",
    initialState: {
        // rooms: [
        //     {
        //         roomName: "Living Room",
        //         roomId: "yello1",
        //         roomIcon: () => LivingRoomIcon,
        //         connectedAppliances: [],
        //     },
        //     {
        //         roomName: "Bedroom",
        //         roomId: "yello2",
        //         roomIcon: () => BedRoomIcon,
        //         connectedAppliances: [],
        //     },
        //     {
        //         roomName: "Bathroom",
        //         roomId: "yello3",
        //         roomIcon: () => BathRoomIcon,
        //         connectedAppliances: [],
        //     },
        //     {
        //         roomName: "Garden",
        //         roomId: "yello4",
        //         roomIcon: () => GardenIcon,
        //         connectedAppliances: [],
        //     },
        //     {
        //         roomName: "Hall",
        //         roomId: "yello5",
        //         roomIcon: () => GardenIcon,
        //         connectedAppliances: [],
        //     },
        //     {
        //         roomName: "Custom Room",
        //         roomId: "yello6",
        //         roomIcon: () => GardenIcon,
        //         connectedAppliances: [],
        //     },
        // ],
        rooms:[],
        moods: [],
        mobile: {
            my_ip: 'xxx.xxx.xx.xx',
            my_mac: 'AA.AA.AA.AA.AA.AA',
            internet: navigator.onLine
            // internet:false
        },
        wifi: null,
        installedDevices: [],
        connectedAppliances: [],
        openRoomsModal: false,
        dragValue: { x: -1, y: -1 },
        recordCode:"",
    },
    reducers: {
        assignApplianceToRoom: (state, action) => {
            state.installedDevices = state.installedDevices.map((device, index) => {
                device.connectedAppliances.map((appliance, index) => {
                    if (appliance.applianceName === action.payload.appliance.applianceName) {
                        let newAppliance = appliance;
                        newAppliance.room = action.payload.room
                    }

                })
                return device;
            })
        },
        updateApplianceState: (state, action) => {
            state.installedDevices = state.installedDevices.map((device, index) => {
                device.connectedAppliances.map((appliance, index) => {
                    if (appliance.uid === action.payload.appliance.uid) {
                        appliance.offlineState = { ...appliance.offlineState, ...action.payload.appliance.offlineState }
                    }
                })
                return device
            })
        },
        updateDragValue: (state, action) => {
            state.dragValue = action.payload
        },
        addAppliance: (state, action) => {
            state.connectedAppliances = state.connectedAppliances.concat(action.payload);
        },
        toggleRoomModal: (state, action) => {
            // You can also pass payload of boolean type
            state.openRoomsModal = action.payload;
        },
        setWifi: (state, action) => {
            state.wifi = {
                ...state.wifi,
                ssid: action.payload.ssid,
                password: action.payload.password
            }
        },
        addDevice: (state, action) => {
            state.installedDevices = state.installedDevices.concat(action.payload);
        },
        addRoom: (state, action) => {
            state.rooms = state.rooms.concat(action.payload);

        },
        setRooms: (state, data) => {

            state.rooms = data.payload;
        },
        setMoods: (state, data) => {
            state.moods = data.payload;
        },
        setMobile: (state, data) => {
            state.mobile = data.payload;
        },
        changeApplianceName:(state,action)=>{
            state.installedDevices = state.installedDevices.map((device)=>{
                device.connectedAppliances.forEach((appliance)=>{
                    if(appliance.uid === action.payload.appliance.uid){
                        appliance.applianceName = action.payload.appliance.applianceName
                    }
                })
                return device;
            })
        }, 
        updateAppliance: (state, data) => {
            let rooms = state.rooms;
            rooms.map((room) => {
                room.map((appliance) => {
                    if (appliance.mac === data.payload.mac && appliance.id === data.payload.id) {
                        if (data.payload.method === 'val') {
                            appliance.val = data.payload.data;
                        }
                        else if (data.payload.method === 'dynIp') {
                            appliance.dynIp = data.payload.data;
                        } else if (data.payload.method === 'routine') {
                            appliance.routine = data.payload.data;
                        } else if (data.payload.method === 'dimVal') {
                            appliance.dimVal = data.payload.data;
                        }
                    }
                })
            });
            state.rooms = rooms;
        },
        updateApplianceById: (state, data) => {
            let rooms = state.rooms;
            if (data.payload.method === 'dynIp') {
                rooms[data.payload.roomNo].appliance[data.payload.applianceNo].dynIp = data.payload.data;
            } else if (data.payload.method === 'val') {
                rooms[data.payload.roomNo].appliance[data.payload.applianceNo].val = data.payload.data;
            } else if (data.payload.method === 'routine') {
                rooms[data.payload.roomNo].appliance[data.payload.applianceNo].routine = data.payload.data;
            }else if (data.payload.method === 'dimVal') {
                rooms[data.payload.roomNo].appliance[data.payload.applianceNo].dimVal = data.payload.data;
            }else if (data.payload.method === 'layout') {
                rooms[data.payload.roomNo].appliance[data.payload.applianceNo].layout = data.payload.data;
                console.log(rooms,"here change")
            }

            state.rooms = rooms;
        },
        recordCode: (state,data)=>{
            // console.log("STATEN ",state)
            console.log("data",typeof state.recordCode)
          state.recordCode= data.payload;
          console.log("data aft",typeof data.payload)

        }
    }
})
// NOTE - why do you want to return all the key inside state.home separately ? 
export const getHome = (state) => {
    return state.home
}
export const getAllRooms = (state) => {
    return state.home.rooms
}
export const getRecordedCode =(state)=>{
    return state.home.recordCode
}
export const mobileInfo = (state) => {
    return state.home.mobile
}
export const selectWifi = (state) => {
    return state.home.wifi
}
export const selectDevices = (state) => {
    return state.home.installedDevices
}
export const selectConnectedAppliances = (state) => {
    return state.home.connectedAppliances
}
export const isRoomModalOpen = (state) => {
    return state.home.openRoomsModal
}
export const selectDragValue = (state) => {
    return state.home.dragValue
}
export const {
    changeApplianceName,
    updateApplianceState,
    assignApplianceToRoom,
    updateDragValue,
    toggleRoomModal,
    addDevice,
    addAppliance,
    setWifi,
    addRoom,
    setRooms,
    setMobile,
    setMoods,
    updateAppliance,
    updateApplianceById,
    recordCode
} = homeSlice.actions
export default homeSlice.reducer;
