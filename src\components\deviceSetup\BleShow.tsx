//@ts-nocheck
import React, { useState, useEffect, useRef } from 'react';
import './deviceSetup.css'
import { BLE } from "@ionic-native/ble";
import { AndroidPermissions } from '@ionic-native/android-permissions';
import { MetaWearSensorFusion } from './metaWearSensorFusion';
import { BleClient, numbersToDataView } from '@capacitor-community/bluetooth-le';
// import { IonCard, IonContent,IonInput,IonItem, IonButton} from '@ionic/react';
import  BluetoothIcon  from '../../assets/bluetooth.svg'
import {
    CircularProgress,
    useMediaQuery,
    useTheme,
    Slide,
    TextField,
    Button,
    Typography,
    makeStyles,
    Dialog,
    DialogActions,
    DialogContent,
    DialogContentText,
    DialogTitle,
    SlideProps,
    IconButton,
} from '@material-ui/core';
import { CreateAnimation } from '@ionic/react';
import axios, { AxiosRequestConfig } from 'axios';
import { useHistory } from 'react-router';
import { useSelector, useDispatch } from 'react-redux';
import { selectWifi, setWifi, addDevice, selectDevices } from '../../redux/slices/home/<USER>';
// import { Room } from '../assignToRoom/AssignToRoom';
import { uid } from 'uid';

import quote from '../../quotes.json';
import { rawThemePallete } from '../../theme/theme';
import {setDevice, setDeviceList, setDeviceConnected, selectDevice, clearDeviceState} from "../../redux/slices/deviceSlice.js"
import { BluetoothConnected, BluetoothDisabled } from '@material-ui/icons';

export type ConnectedAppliance = {
    isOnline?: boolean
    offlineState?: {
        on? : boolean
        online? : boolean
    }
    armourMode?: boolean
    routines?: any[]
    applianceName: string
    applianceIcon?: any
    // room?: Room
    dimmable?: boolean
    uid : string
}

export interface DeviceDetails {
    name?: string
    mac?: string
    bleName?: string
    connectedAppliances?: ConnectedAppliance[]
}

export interface QuoteObject {
    text: string
    from: string
}

const Transition = React.forwardRef(function Transition(props: SlideProps, ref) {
    return <Slide direction="up" ref={ref} {...props} />;
});

const useStyles = makeStyles((theme) => ({
    modal: {
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
    },
    paper: {
        backgroundColor: theme.palette.background.paper,
        border: '2px solid #000',
        boxShadow: theme.shadows[5],
        padding: theme.spacing(2, 4, 3),
        maxWidth: "90%"
    },
}));

interface BleShowProps {
    setNav?: (num: number) => void;
    sensorData?: any;
    isConnected?: boolean;
    isStreaming?: boolean;
    startSensorFusion?: () => void;
    stopSensorFusion?: () => void;
    connectToMetaWear?: () => void;
    connectToSpecificDevice?: (device: any) => void;
    scanResults?: any[];
}

const BleShow: React.FC<BleShowProps> = (props) => {
    const theme = useTheme();
    const fullScreen = useMediaQuery(theme.breakpoints.down('sm'));
    const WIFI = useSelector(selectWifi);
    const DEVICES = useSelector(selectDevices);
    const deviceState = useSelector(selectDevice);
    const dispatch = useDispatch();
    const classes = useStyles();
    const history = useHistory();
    const ring3ref = useRef() as React.MutableRefObject<CreateAnimation>;
    const ring2ref = useRef() as React.MutableRefObject<CreateAnimation>;
    const ring1ref = useRef() as React.MutableRefObject<CreateAnimation>;
    const [on, setOn] = useState(true);
    const [openModal, setOpenModal] = useState<boolean>(false);
    const [showAnimation, setShowAnimation] = useState<boolean>(false);
    const [scanResults, setScanResults] = useState<any[]>([]);
    const [lastLength, setLastLength] = useState(0);
    const [showDevices, setShowDevices] = useState<boolean>(false);
    const [ssid, setSsid] = useState<string | null>(null);
    const [password, setPassword] = useState<string | null>(null);
    const [loading, setLoading] = useState<boolean>(false);
    const [loadingValue, setLoadingValue] = useState<number>(0);
    const [quoteObject, setQuoteObject] = useState<QuoteObject | null>(null);
    const [openApplianceSetup, setOpenApplianceSetup] = useState<boolean>(false);

    // MetaWear sensor data states
    const [gyroData, setGyroData] = useState<{ time: number; x: number; y: number; z: number; }[]>([]);
    const [quatData, setQuatData] = useState<{ time: number; w: number; x: number; y: number; z: number; }[]>([]);
    const [accelData, setAccelData] = useState<{ time: number; x: number; y: number; z: number; }[]>([]);
    const [magData, setMagData] = useState<{ time: number; x: number; y: number; z: number; }[]>([]);
    const [isConnected, setIsConnected] = useState(false);
    const [isStreaming, setIsStreaming] = useState(false);
    const [scanStatus, setScanStatus] = useState<string>('');
    const [bluetoothNotificationOpen, setBluetoothNotificationOpen] = useState<boolean>(false);
    const deviceIdRef = useRef<string | null>(null);
    const notifStartedRef = useRef<boolean>(false);

    // Check if Bluetooth is enabled
    const checkBluetoothStatus = async (): Promise<boolean> => {
        try {
            console.log('🔵 Checking Bluetooth status...');
            await BLE.isEnabled();
            console.log('✅ Bluetooth is enabled');
            return true;
        } catch (error) {
            console.log('❌ Bluetooth is disabled:', error);
            setBluetoothNotificationOpen(true);
            return false;
        }
    };

    // Helper: write bytes to command characteristic (using MetaWear constants)
    const writeCommand = async (bytes: number[]) => {
        if (!deviceIdRef.current) return;
        const MW_SERVICE = '326a9000-85cb-9195-d9dd-464cfbbae75a';
        const MW_CHAR_CMD = '326a9001-85cb-9195-d9dd-464cfbbae75a';
        await BleClient.write(
            deviceIdRef.current,
            MW_SERVICE,
            MW_CHAR_CMD,
            numbersToDataView(bytes)
        );
    };

    // Initialize BLE and start scanning for devices
    const initializeBLE = async () => {
        try {
            console.log('🔵 Initializing BLE client...');
            await BleClient.initialize({ androidNeverForLocation: true });
            console.log('✅ BLE client initialized successfully');
            return true;
        } catch (err) {
            console.error('❌ Error initializing BLE client:', err);
            return false;
        }
    };

    // Scan for BLE devices (both MetaWear and other devices)
    const startBLEScan = async () => {
        try {
            console.log('🔍 Starting BLE scan...');
            setScanStatus('🔵 Initializing BLE...');
            setScanResults([]);
            setShowDevices(false);

            const initialized = await initializeBLE();
            if (!initialized) {
                console.error('❌ Failed to initialize BLE, cannot start scan');
                setScanStatus('❌ Failed to initialize BLE');
                return;
            }

            setScanStatus('🔍 Scanning for MetaWear devices...');
            console.log('🔍 Requesting BLE devices...');

            // Try to scan for MetaWear devices first
            try {
                console.log('🔍 Scanning for MetaWear devices...');
                const MW_SERVICE = '326a9000-85cb-9195-d9dd-464cfbbae75a';
                const device = await BleClient.requestDevice({
                    services: [MW_SERVICE],
                    // optionalServices: []
                });

                console.log('✅ Found MetaWear device:', device);
                setScanStatus('✅ MetaWear device found!');
                const deviceList = [{
                    id: device.deviceId,
                    name: device.name || 'MetaWear Device',
                    type: 'metawear'
                }];
                setScanResults(deviceList);
                // Store device list in Redux
                dispatch(setDeviceList(deviceList));
                // Let the useEffect timeout handle showing devices
                return;
            } catch (metaWearError) {
                console.log('⚠️ No MetaWear devices found, trying general BLE scan...');
                setScanStatus('⚠️ No MetaWear found, trying general scan...');
            }

            // If no MetaWear devices, try general BLE scan
            try {
                console.log('🔍 Scanning for general BLE devices...');
                const device = await BleClient.requestDevice({
                    acceptAllDevices: true,
                    optionalServices: []
                });

                console.log('✅ Found BLE device:', device);
                setScanStatus('✅ BLE device found!');
                const deviceList = [{
                    id: device.deviceId,
                    name: device.name || 'Unknown Device',
                    type: 'general'
                }];
                setScanResults(deviceList);
                // Store device list in Redux
                dispatch(setDeviceList(deviceList));
                // Let the useEffect timeout handle showing devices
            } catch (generalError) {
                console.error('❌ No BLE devices found:', generalError);
                setScanStatus('🔄 Trying legacy scan...');
                // Fallback to original BLE scanning method
                startLegacyBLEScan();
            }

        } catch (err) {
            console.error('❌ Error during BLE scan:', err);
            setScanStatus('❌ Scan error, trying legacy...');
            // Fallback to original BLE scanning method
            // startLegacyBLEScan();
        }
    };

    // Fallback to original BLE scanning method
    const startLegacyBLEScan = () => {
        console.log('🔄 Falling back to legacy BLE scan...');
        setScanStatus('🔄 Using legacy BLE scan...');

        BLE.startScan([]).subscribe(
            (success) => {
                console.log('📱 Legacy BLE scan found device:', success);
                if (success && success.name) {
                    console.log(`📱 Device found: ${success.name} (${success.id})`);

                    // Check for MetaWear devices (they usually have "MetaWear" in the name)
                    const isMetaWear = success.name.toLowerCase().includes('metawear') ||
                                      success.name.toLowerCase().includes('mbient');

                    // Also check for yellocanary devices (original logic)
                    const isYelloCanary = success.name.search("yellocanary") !== -1;

                    if (isMetaWear || isYelloCanary) {
                        console.log(`✅ Compatible device found: ${success.name}`);
                        setScanStatus(`✅ Found ${success.name}`);
                        setScanResults((prevState) => {
                            const newDevice = {
                                ...success,
                                type: isMetaWear ? 'metawear' : 'yellocanary'
                            };
                            return prevState.concat(newDevice);
                        });
                    } else {
                        console.log(`⚠️ Device ${success.name} not compatible, skipping...`);
                        setScanStatus(`⚠️ ${success.name} not compatible`);
                    }
                }
            },
            (failure) => {
                console.error('❌ Legacy BLE scan failed:', failure);
                setScanStatus('❌ Legacy scan failed');
            }
        );

        // The useEffect will handle the timeout and showing devices
        // No need for manual timeout here as the existing logic will handle it
    };

    // Connect to a specific device
    const connectToDevice = async (deviceInfo: any) => {
        try {
            console.log('🔗 Connecting to device:', deviceInfo);

            if (deviceInfo.type === 'metawear') {
                // Connect to MetaWear device
                console.log('🔗 Connecting to MetaWear device...');
                await BleClient.connect(deviceInfo.id);
                deviceIdRef.current = deviceInfo.id;
                setIsConnected(true);

                // Store device ID and connection status in Redux
                dispatch(setDevice({
                    id: deviceInfo.id,
                    name: deviceInfo.name,
                    type: deviceInfo.type
                }));
                dispatch(setDeviceConnected(true));

                console.log('✅ Connected to MetaWear device successfully');
            } else {
                // Connect to other BLE devices using legacy method
                console.log('🔗 Connecting to legacy BLE device...');
                BLE.connect(deviceInfo.id).subscribe(
                    (success) => {
                        console.log('✅ Connected to legacy device:', success);
                        deviceIdRef.current = deviceInfo.id;
                        setIsConnected(true);

                        // Store device ID and connection status in Redux
                        dispatch(setDevice({
                            id: deviceInfo.id,
                            name: deviceInfo.name,
                            type: deviceInfo.type
                        }));
                        dispatch(setDeviceConnected(true));
                    },
                    (error) => {
                        console.error('❌ Failed to connect to legacy device:', error);
                    }
                );
            }
        } catch (err) {
            console.error('❌ Error connecting to device:', err);
        }
    };

    // Function to reconnect to stored device
    const reconnectToStoredDevice = async () => {
        if (deviceState.value && deviceState.value.id) {
            console.log('🔄 Attempting to reconnect to stored device:', deviceState.value);
            try {
                await connectToDevice(deviceState.value);
                return true;
            } catch (error) {
                console.error('❌ Failed to reconnect to stored device:', error);
                // Clear device state if reconnection fails to avoid inconsistent state
                dispatch(clearDeviceState());
                return false;
            }
        }
        return false;
    };

    // Function to disconnect from device
    const disconnectDevice = async () => {
        try {
            // Get device ID from Redux state or local ref
            const deviceId = deviceState.value?.id || deviceIdRef.current;

            if (deviceId) {
                console.log('🔌 Disconnecting from device:', deviceId);

                // Disconnect using BleClient
                await BleClient.disconnect(deviceId);

                // Clear local state
                deviceIdRef.current = null;
                setIsConnected(false);
                setIsStreaming(false);

                // Clear Redux store completely
                dispatch(clearDeviceState());

                console.log('✅ Device disconnected and Redux state cleared successfully');
            } else {
                console.warn('⚠️ No device ID found for disconnection');
                // Clear Redux state anyway in case of inconsistent state
                dispatch(clearDeviceState());
            }
        } catch (error) {
            console.error('❌ Error disconnecting device:', error);
            // Clear Redux state even if disconnect fails to avoid inconsistent state
            dispatch(clearDeviceState());
            deviceIdRef.current = null;
            setIsConnected(false);
            setIsStreaming(false);
        }
    };

    const startScan = async () => {
        console.log('🚀 Starting device scan...');

        // Check if Bluetooth is enabled before starting scan
        const bluetoothEnabled = await checkBluetoothStatus();
        if (!bluetoothEnabled) {
            console.log('❌ Cannot start scan - Bluetooth is disabled');
            return;
        }

        // First try to reconnect to stored device if available
        if (deviceState.value && deviceState.value.id && !isConnected) {
            console.log('🔄 Trying to reconnect to stored device first...');
            const reconnected = await reconnectToStoredDevice();
            if (reconnected) {
                console.log('✅ Successfully reconnected to stored device');
                return;
            }
        }

        // If no stored device or reconnection failed, start scanning
        if (props.connectToMetaWear) {
            console.log('🔄 Using props connectToMetaWear method');
            props.connectToMetaWear();
        } else {
            console.log('🔄 Using internal BLE scan method');
            startBLEScan();
        }
    }

    const getAndroidPermission = async () => {
        console.log('🔐 Requesting Android permissions...');

        // Check Bluetooth status first
        const bluetoothEnabled = await checkBluetoothStatus();
        if (!bluetoothEnabled) {
            console.log('❌ Cannot proceed - Bluetooth is disabled');
            return;
        }

        AndroidPermissions.checkPermission(AndroidPermissions.PERMISSION.ACCESS_COARSE_LOCATION).then(
            result => {
                console.log('✅ Location permission granted, starting scan...');
                startScan();
            },
            err => {
                console.log('⚠️ Location permission denied, requesting...');
                AndroidPermissions.requestPermission(AndroidPermissions.PERMISSION.ACCESS_COARSE_LOCATION);
            }
        );

        AndroidPermissions.requestPermissions([AndroidPermissions.PERMISSION.ACCESS_COARSE_LOCATION, AndroidPermissions.PERMISSION.GET_ACCOUNTS]);
    }

    // Start streaming sensor fusion and gyro
    const startSensorFusion = async () => {
        if (!deviceIdRef.current) return;
        try {
            console.log('Discovering services...');
            const services = await BleClient.getServices(deviceIdRef.current);
            if (!services.some(s => s.uuid === MW_SERVICE)) {
                throw new Error('MetaWear service not found');
            }

            console.log('Configuring gyroscope...');
            await writeCommand([0x13, 0x03, 0x26, 0x04]); // Set gyro range to 125dps
            await writeCommand([0x13, 0x05, 0x01]);       // Enable gyro output
            await writeCommand([0x13, 0x02, 0x01, 0x00]); // Enable gyro interrupt
            await writeCommand([0x13, 0x01, 0x01]);       // Power on gyro

            console.log('Configuring accelerometer...');
            await writeCommand([0x03, 0x01, 0x01]);       // Enable accelerometer

            console.log('Configuring magnetometer...');
            await writeCommand([0x15, 0x01, 0x01]);       // Enable magnetometer

            console.log('Configuring sensor fusion...');
            await writeCommand([0x19, 0x03, 0x01, 0x02, 0x00]); // Sensor Fusion config
            await writeCommand([0x19, 0x03, 0x02, 0x00, 0x00]); // Write config
            await writeCommand([0x19, 0x05, 0x03, 0x01]);       // Enable quaternion output
            await writeCommand([0x19, 0x01, 0x01]);             // Start sensor fusion

            console.log('Subscribing to notifications...');
            await BleClient.startNotifications(
                deviceIdRef.current,
                MW_SERVICE,
                MW_CHAR_NOTIF,
                handleNotification
            );
            notifStartedRef.current = true;
            setIsStreaming(true);

            console.log('Streaming started!');
        } catch (err) {
            console.error('Error starting sensor fusion:', err);
        }
    };

    // Handle notification data
    const handleNotification = (data: any) => {
        console.log("DATA RAW ", data);
        const bytes = new Uint8Array(data.buffer);
        console.log("BYTES ", bytes);
        const time = Date.now();

        // Check for Gyroscope data (0x13 0x05)
        if (bytes[0] === 0x13 && bytes[1] === 0x05) {
            const x = (bytes[3] << 8) | bytes[2];
            const y = (bytes[5] << 8) | bytes[4];
            const z = (bytes[7] << 8) | bytes[6];
            const xVal = (x & 0x8000) ? x - 0x10000 : x;
            const yVal = (y & 0x8000) ? y - 0x10000 : y;
            const zVal = (z & 0x8000) ? z - 0x10000 : z;

            setGyroData(prev => {
                const newData = [...prev, { time, x: xVal, y: yVal, z: zVal }];
                return newData.slice(-100);
            });

        // Check for Accelerometer data (0x03 0x04)
        } else if (bytes[0] === 0x03 && bytes[1] === 0x04) {
            const x = (bytes[3] << 8) | bytes[2];
            const y = (bytes[5] << 8) | bytes[4];
            const z = (bytes[7] << 8) | bytes[6];
            const xVal = (x & 0x8000) ? x - 0x10000 : x;
            const yVal = (y & 0x8000) ? y - 0x10000 : y;
            const zVal = (z & 0x8000) ? z - 0x10000 : z;

            console.log("ACCELERATION ", xVal, " ", yVal, " ", zVal);

            setAccelData(prev => {
                const newData = [...prev, { time, x: xVal, y: yVal, z: zVal }];
                return newData.slice(-100);
            });

        // Check for Magnetometer data (0x15 0x04)
        } else if (bytes[0] === 0x15 && bytes[1] === 0x04) {
            const x = (bytes[3] << 8) | bytes[2];
            const y = (bytes[5] << 8) | bytes[4];
            const z = (bytes[7] << 8) | bytes[6];
            const xVal = (x & 0x8000) ? x - 0x10000 : x;
            const yVal = (y & 0x8000) ? y - 0x10000 : y;
            const zVal = (z & 0x8000) ? z - 0x10000 : z;

            console.log("MAGNETOMETER ", xVal, " ", yVal, " ", zVal);

            setMagData(prev => {
                const newData = [...prev, { time, x: xVal, y: yVal, z: zVal }];
                return newData.slice(-100);
            });

        // Handle Quaternion data (0x19 0x07)
        } else if (bytes[0] === 0x19 && bytes[1] === 0x07) {
            const view = new DataView(bytes.buffer);

            const w = view.getFloat32(2, true); // little endian
            const x = view.getFloat32(6, true);
            const y = view.getFloat32(10, true);
            const z = view.getFloat32(14, true);

            setQuatData(prev => {
                const newData = [...prev, { time, w, x, y, z }];
                return newData.slice(-100);
            });
        }
    };

    // Stop streaming and disconnect
    const stopSensorFusion = async () => {
        if (!deviceIdRef.current) return;
        try {
            console.log('Stopping sensor fusion...');
            await writeCommand([0x13, 0x01, 0x00]); // Power off gyro
            await writeCommand([0x13, 0x02, 0x00, 0x00]); // Disable gyro interrupt
            await writeCommand([0x13, 0x05, 0x00]); // Disable gyro output
            await writeCommand([0x19, 0x01, 0x00]); // Stop sensor fusion
            await writeCommand([0x19, 0x05, 0x03, 0x00]); // Disable quaternion output

            if (notifStartedRef.current) {
                await BleClient.stopNotifications(deviceIdRef.current, MW_SERVICE, MW_CHAR_NOTIF);
            }

            await BleClient.disconnect(deviceIdRef.current);

            deviceIdRef.current = null;
            notifStartedRef.current = false;
            setIsConnected(false);
            setIsStreaming(false);

            console.log('Disconnected.');
        } catch (err) {
            console.error('Error during stop:', err);
        }
    };

    useEffect(() => {
        console.log('🔄 BleShow useEffect triggered, setting up scan timeout...');

        const scanTimeout = setInterval(() => {
            const currentScanResults = props.scanResults || scanResults;
            let scanLength = currentScanResults.length;
            console.log(`⏰ Scan timeout check: current=${scanLength}, last=${lastLength}`);

            if (lastLength !== scanLength) {
                console.log('📈 New devices found, updating count...');
                setLastLength(scanLength);
            } else {
                console.log('⏹️ No new devices found, stopping scan and showing results...');
                BLE.stopScan();
                setShowDevices(true);
                setScanStatus(scanLength > 0 ? `✅ Found ${scanLength} device(s)` : '❌ No devices found');
            }
        }, 15000); // 15 second intervals

        // Cleanup interval on unmount
        return () => {
            console.log('🧹 Cleaning up scan timeout interval...');
            clearInterval(scanTimeout);
        };
    }, [(props.scanResults || scanResults).length, lastLength])

    const finishSetup = () => {
        // Start sensor fusion if connected
        if (isConnected && !isStreaming) {
            startSensorFusion();
        }

        // Update device state with sensor data while preserving existing device info
        if (deviceState.value) {
            dispatch(setDevice({
                ...deviceState.value,
                sensorData: {
                    gyro: gyroData,
                    accel: accelData,
                    mag: magData,
                    quat: quatData
                }
            }));
        }

        history.push("/");
    }

    function writeToDevice(deviceId: string, serviceUUID: string, characteristicUUID: string, value: ArrayBuffer) {
        return new Promise((resolve, reject) => {
            BLE.write(deviceId, serviceUUID, characteristicUUID, value)
                .then((res) => {
                    resolve(res);

                })
                .catch((e) => {
                    reject(e)
                })
        })
    }

    function readFromDevice(deviceId: string, serviceUUID: string, characteristicUUID: string) {
        return new Promise((resolve, reject) => {
            BLE.read(deviceId, serviceUUID, characteristicUUID)
                .then((res) => {
                    resolve(res);
                })
                .catch((e) => {
                    reject(e)
                })
        })
    }

    const startProvision = (deviceId: string) => {

        return new Promise((resolve, reject) => {
            let config: AxiosRequestConfig = {
                method: 'post',
                url: `http://c888-117-96-227-142.ngrok.io/provision`,
                headers: {
                    'Content-Type': 'application/json'

                    // TODO: [HOM-202] Must send user authorisation to provision in form of bearer token
                },
                data: JSON.stringify({
                    deviceId: deviceId
                })
            };

            axios(config)
                .then(function (response) {

                    resolve(response.data);
                })
                .catch(function (error) {

                    reject(error);
                });

        })
    }

    const BLE_SAVE = (deviceId: string, serviceUUID: string, keyUUID: string, valueUUID: string, saveUUID: string, key: string, value: string, saveAttribute?: string) => {
        return new Promise((resolve, reject) => {
            writeToDevice(deviceId, serviceUUID, keyUUID, str2ab(key))
                .then((success) => {

                    writeToDevice(deviceId, serviceUUID, valueUUID, str2ab(value))
                        .then((res) => {

                            writeToDevice(deviceId, serviceUUID, saveUUID, str2ab(saveAttribute ? saveAttribute : "0"))
                                .then((res) => {

                                    resolve(ab2str(res))
                                })
                                .catch((e) => {

                                    reject(ab2str(e))
                                })

                        })
                        .catch((e) => {

                            reject(ab2str(e))
                        })
                })
                .catch((err) => {

                    reject(ab2str(err));
                })
        })

    }

    const BLE_READ = (deviceId: string, serviceUUID: string, keyUUID: string, valueUUID: string, key: string) => {

        return new Promise<string>((resolve, reject) => {
            writeToDevice(deviceId, serviceUUID, keyUUID, str2ab(key))
                .then((success) => {

                    readFromDevice(deviceId, serviceUUID, valueUUID)
                        .then((res) => {

                            resolve(JSON.parse(ab2str(res)))
                        })
                        .catch((e) => {

                            reject(ab2str(e))
                        })
                })
                .catch((err) => {

                    reject(ab2str(err));
                })
        })
    }

    const getRandomQuote = () => {
        var index = Math.floor(Math.random() * (324 - 0 + 1) + 0); // min - 0 , max - 324
        return quote[index];
    };

    function connectToAllDevices() {
        setQuoteObject(() => {
            let a = getRandomQuote()
            return a
        });
        let service_id = "5f6d4f53-5f43-4647-5f53-56435f49445f";
        let key_id = "306d4f53-5f43-4647-5f6b-65795f5f5f30";
        let value_id = "316d4f53-5f43-4647-5f76-616c75655f31";
        let save_id = "326d4f53-5f43-4647-5f73-6176655f5f32";
        if (scanResults.length > 0) {
            scanResults.forEach((scan, index) => {
                BLE.connect(scan.id).subscribe(
                    (success) => {

                        let key = 'smartcon.my_mac';

                        let deviceDetails: DeviceDetails = {};
                        deviceDetails.bleName = scan.name
                        BLE_READ(scan.id, service_id, key_id, value_id, "smartcon.my_mac")
                            .then((mac_response) => {
                                deviceDetails.mac = mac_response
                                setLoadingValue((prevState) => {
                                    if (prevState === 100) {
                                        return 0
                                    } else {
                                        return prevState + (100 / (scanResults.length * 6))
                                    }
                                })
                                BLE_READ(scan.id, service_id, key_id, value_id, "smartcon.devicetype")
                                    .then((devicetype_response) => {
                                        deviceDetails.name = devicetype_response;
                                        // TODO: [HOM-200] Get deviceType from environment
                                        // alert(devicetype_response);
                                        deviceDetails.connectedAppliances = [];
                                        if (devicetype_response === "SMARTSWITCH") {
                                            for (let i = 1; i <= 4; i++) {
                                                deviceDetails.connectedAppliances = [...deviceDetails.connectedAppliances!, {
                                                    isOnline: false,
                                                    routines: [],
                                                    offlineState: {},
                                                    applianceName: `${scan.name} | ${i}`,
                                                    uid : uid()
                                                }]
                                            }

                                        }
                                        setLoadingValue((prevState) => {
                                            if (prevState === 100) {
                                                return 0
                                            } else {
                                                return prevState + (100 / (scanResults.length * 6))
                                            }
                                        })
                                        // startProvision(scan.name)
                                        //     .then(async (kit) => {
                                        //         setLoadingValue((prevState) => {
                                        //             if (prevState === 100) {
                                        //                 return 0
                                        //             } else {
                                        //                 return prevState + (100 / (scanResults.length * 6))
                                        //             }
                                        //         })

                                        //         BLE_SAVE(scan.id, service_id, key_id, value_id, save_id, "wifi.sta.ssid", WIFI.ssid)
                                        //             .then(() => {
                                        //                 setLoadingValue((prevState) => {
                                        //                     if (prevState === 100) {
                                        //                         return 0
                                        //                     } else {
                                        //                         return prevState + (100 / (scanResults.length * 6))
                                        //                     }
                                        //                 })
                                        //                 BLE_SAVE(scan.id, service_id, key_id, value_id, save_id, "wifi.sta.pass", WIFI.password)
                                        //                     .then(() => {
                                        //                         setLoadingValue((prevState) => {
                                        //                             if (prevState === 100) {
                                        //                                 return 0
                                        //                             } else {
                                        //                                 return prevState + (100 / (scanResults.length * 6))
                                        //                             }
                                        //                         })
                                        //                         BLE_SAVE(scan.id, service_id, key_id, value_id, save_id, "wifi.sta.enable", "true", "2")
                                        //                             .then(() => {
                                        //                                 // Provision is now completed
                                        //                                 // alert(JSON.stringify(deviceDetails));
                                        //                                 dispatch(addDevice(deviceDetails))

                                        //                                 setLoadingValue((prevState) => {
                                        //                                     if (prevState === 100) {
                                        //                                         return 0
                                        //                                     } else {
                                        //                                         return prevState + (100 / (scanResults.length * 6))
                                        //                                     }
                                        //                                 })
                                        //                                 // Pop the add appliance dialog after 2000ms
                                        //                                 if (scanResults.length === index + 1) {
                                        //                                     history.push('/applianceSetup');
                                        //                                 }
                                        //                             })
                                        //                             .catch((e) => {

                                        //                             })
                                        //                     })
                                        //                     .catch((e) => {

                                        //                     })
                                        //             })
                                        //             .catch((e) => {

                                        //             })

                                        //     })
                                        //     .catch((e) => {

                                        //     })

                                        setLoadingValue((prevState) => {
                                            if (prevState === 100) {
                                                return 0
                                            } else {
                                                return prevState + (100 / (scanResults.length * 6))
                                            }
                                        })

                                        BLE_SAVE(scan.id, service_id, key_id, value_id, save_id, "wifi.sta.ssid", WIFI.ssid)
                                            .then(() => {
                                                setLoadingValue((prevState) => {
                                                    if (prevState === 100) {
                                                        return 0
                                                    } else {
                                                        return prevState + (100 / (scanResults.length * 6))
                                                    }
                                                })
                                                BLE_SAVE(scan.id, service_id, key_id, value_id, save_id, "wifi.sta.pass", WIFI.password)
                                                    .then(() => {
                                                        setLoadingValue((prevState) => {
                                                            if (prevState === 100) {
                                                                return 0
                                                            } else {
                                                                return prevState + (100 / (scanResults.length * 6))
                                                            }
                                                        })
                                                        BLE_SAVE(scan.id, service_id, key_id, value_id, save_id, "wifi.sta.enable", "true", "2")
                                                            .then(() => {
                                                                // Provision is now completed
                                                                // alert(JSON.stringify(deviceDetails));
                                                                dispatch(addDevice(deviceDetails))

                                                                setLoadingValue((prevState) => {
                                                                    if (prevState === 100) {
                                                                        return 0
                                                                    } else {
                                                                        return prevState + (100 / (scanResults.length * 6))
                                                                    }
                                                                })
                                                                // Pop the add appliance dialog after 2000ms
                                                                if (scanResults.length === index + 1) {
                                                                    history.push('/applianceSetup');
                                                                }
                                                            })
                                                            .catch((e) => {

                                                            })
                                                    })
                                                    .catch((e) => {

                                                    })
                                            })
                                            .catch((e) => {

                                            })


                                    })
                                    .catch((e) => {

                                    })
                            })
                            .catch((failure) => {

                            })
                        // writeToDevice(scan.id, service_id, key_id, str2ab(key))
                        //     .then((success) => {
                        //



                        //         readFromDevice(scan.id, service_id, value_id)
                        //             .then((res) => {
                        //
                        //                 deviceDetails.mac = ab2str(res);
                        //                 writeToDevice(scan.id, service_id, key_id, str2ab('smartcon.devicetype'))
                        //                     .then((success) => {
                        //
                        //                         readFromDevice(scan.id, service_id, value_id)
                        //                             .then((res) => {
                        //
                        //                                 deviceDetails.name = ab2str(res);
                        //                                 startProvision(scan.name)
                        //                                     .then(async (kit) => {
                        //
                        //                                         BLE_SAVE(scan.id, service_id, key_id, value_id, save_id, "wifi.sta.ssid", WIFI.ssid)
                        //                                             .then(() => {
                        //                                                 BLE_SAVE(scan.id, service_id, key_id, value_id, save_id, "wifi.sta.pass", WIFI.password)
                        //                                                     .then(() => {
                        //                                                         BLE_SAVE(scan.id, service_id, key_id, value_id, save_id, "wifi.sta.enable", "true", "2")
                        //                                                             .then(() => {
                        //                                                                 dispatch(addDevice(deviceDetails))
                        //                                                             })
                        //                                                     })
                        //                                             })

                        //                                     })

                        //                             })
                        //                             .catch((e) => {
                        //
                        //                             })
                        //                     })
                        //                     .catch((err) => {
                        //
                        //                     })
                        //             })
                        //             .catch((e) => {
                        //
                        //             })
                        //     })
                        //     .catch((err) => {
                        //
                        //     })
                    },
                    (failure) => {

                    }
                )
            })
        } else {

        }
    }

    function ab2str(buf: any) {
        //@ts-ignore
        return String.fromCharCode.apply(null, new Uint8Array(buf));
    }
    function str2ab(str: any) {
        var array = new Uint8Array(str.length);
        for (var i = 0, l = str.length; i < l; i++) {
            array[i] = str.charCodeAt(i);
        }
        return array.buffer;
    }

    const isDeviceProvisioned = (bleName: string) => {
        let allDevices: DeviceDetails[] = DEVICES
        const device = allDevices.filter((device) => {
            if (device.name === bleName) {

                return true
            } else {
                return false
            }
        })

        if (device) {
            return true
        } else {
            return false
        }
    }

    return <div className={'deviceSetupContainer'}>
        {/* Connected Device Info Section */}
        {(deviceState.connected && deviceState.value) && (
            <div style={{
                background: "#4CAF50",
                padding: "1em",
                margin: "1em",
                borderRadius: "12px",
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
                color: "white"
            }}>
                <div style={{ display: "flex", alignItems: "center", gap: "12px" }}>
                    <BluetoothConnected style={{ fontSize: "24px" }} />
                    <div>
                        <Typography style={{ fontSize: "18px", fontWeight: "bold", color: "white" }}>
                            {deviceState.value.name || 'Connected Device'}
                        </Typography>
                        <Typography style={{ fontSize: "12px", color: "rgba(255,255,255,0.8)" }}>
                            {deviceState.value.type === 'metawear' ? 'MetaWear Device' : 'BLE Device'} • Connected
                        </Typography>
                    </div>
                </div>
                <Button
                    variant="outlined"
                    size="small"
                    startIcon={<BluetoothDisabled />}
                    onClick={disconnectDevice}
                    style={{
                        borderColor: "white",
                        color: "white",
                        fontSize: "12px"
                    }}
                >
                    Disconnect
                </Button>
            </div>
        )}

        {!showAnimation && !showDevices ?
            <>
                <div className={'desc'}>
                    <Typography style={{ fontSize: "34px", color: "#607D8B" }}>Setup Device</Typography>
                    <Typography style={{ textAlign: "justify", color: "#607D8B" }}>If you have installed our device ,
                        your phone will connect to the device using
                        Bluetooth Low Energy (BLE) and will let you know the further steps</Typography>
                </div>
                <div className={'getStartedBtn'}>
                    <Button onClick={() => {
                        console.log('🚀 Get Started clicked - starting device setup flow...');
                        setShowAnimation(true);
                        setTimeout(async () => {
                            console.log('🎬 Starting animations and permissions...');
                            await getAndroidPermission();

                            // Wait a bit more for animations to be ready
                            setTimeout(() => {
                                console.log("RING 3 REF ", ring3ref);
                                console.log("RING 2 REF ", ring2ref);
                                console.log("RING 1 REF ", ring1ref);

                                // Add null checks before playing animations
                                if (ring3ref.current && ring3ref.current.animation) {
                                    console.log('🎬 Playing ring3 animation...');
                                    ring3ref.current.animation.play();
                                } else {
                                    console.warn('⚠️ ring3ref not available for animation');
                                }

                                if (ring2ref.current && ring2ref.current.animation) {
                                    console.log('🎬 Playing ring2 animation...');
                                    ring2ref.current.animation.play();
                                } else {
                                    console.warn('⚠️ ring2ref not available for animation');
                                }

                                // ring1ref.current.animation.play();
                            }, 500); // Additional delay for animations to be ready

                            // Start the scanning process after animations
                            startScan();
                        }, 1000)
                    }} variant={'contained'} style={{ background: rawThemePallete.palette.primary.main, color: "#ffffff", padding: "0.7em", borderRadius: "40px", width: "40%" }}>Get Started</Button>
                </div>
            </>
            :
            <>
                {!showDevices ? <div className={'ringContainer'}>
                    <div className={'outerRing'}>
                        <CreateAnimation
                            ref={ring3ref}
                            duration={2400}
                            iterations={Infinity}
                            keyframes={[
                                { offset: 0, transform: 'scale(1) translate(-50%,-50%)' },
                                { offset: 0.22, transform: 'scale(1.5) translate(-50%,-50%)' },
                                { offset: 0.32, transform: 'scale(1.5) translate(-50%,-50%)' },
                                { offset: 0.42, transform: 'scale(1.5) translate(-50%,-50%)' },
                                { offset: 0.52, transform: 'scale(1.5) translate(-50%,-50%)' },
                                { offset: 0.62, transform: 'scale(1.5) translate(-50%,-50%)' },
                                { offset: 0.72, transform: 'scale(1) translate(-50%,-50%)' },
                                { offset: 0.78, transform: 'scale(1.1) translate(-50%,-50%)' },
                                { offset: 0.82, transform: 'scale(1) translate(-50%,-50%)' },
                                { offset: 0.90, transform: 'scale(1.05) translate(-50%,-50%)' },
                                { offset: 0.94, transform: 'scale(1) translate(-50%,-50%)' },
                                { offset: 0.98, transform: 'scale(1) translate(-50%,-50%)' },
                                { offset: 1, transform: 'scale(1) translate(-50%,-50%)' }
                            ]}
                        >
                            <div className={'ring3'}></div>
                        </CreateAnimation>
                        <CreateAnimation
                            ref={ring2ref}
                            duration={2400}
                            iterations={Infinity}
                            keyframes={[
                                { offset: 0, transform: 'scale(1) translate(-50%,-50%)' },
                                { offset: 0.22, transform: 'scale(1) translate(-50%,-50%)' },
                                { offset: 0.32, transform: 'scale(1.5) translate(-50%,-50%)' },
                                { offset: 0.42, transform: 'scale(1.5) translate(-50%,-50%)' },
                                { offset: 0.52, transform: 'scale(1.5) translate(-50%,-50%)' },
                                { offset: 0.62, transform: 'scale(1) translate(-50%,-50%)' },
                                { offset: 0.72, transform: 'scale(1) translate(-50%,-50%)' },
                                { offset: 0.78, transform: 'scale(1) translate(-50%,-50%)' },
                                { offset: 0.82, transform: 'scale(1) translate(-50%,-50%)' },
                                { offset: 0.92, transform: 'scale(1) translate(-50%,-50%)' },
                                { offset: 1, transform: 'scale(1) translate(-50%,-50%)' }
                            ]}
                        >
                            <div className={'ring2'}></div>
                        </CreateAnimation>
                        <div className={'ring1'}>
                            <img src={BluetoothIcon} onClick={() => { }} style={{ height: "30px", width: "50px", fill: !on ? "green" : "white" }} />

                        </div>
                    </div>
                    <div style={{ display: "flex", justifyContent: "center", alignItems: "center", flexDirection: "column", gap: "1.5em", position: "absolute", bottom: "2em", }}>
                        <Typography className={'noOfDevices'} style={{ fontSize: "18px", margin: "1em" }}>
                            {scanResults.length} devices found
                        </Typography>
                        {scanStatus && (
                            <Typography style={{ fontSize: "14px", color: "#607D8B", textAlign: "center", margin: "0.5em" }}>
                                {scanStatus}
                            </Typography>
                        )}
                        <Button onClick={() => history.goBack()} variant={'outlined'} style={{ borderColor: "#F44336", color: "#F44336", fontSize: "14px" }}>Cancel</Button>

                    </div>

                </div>
                    :
                    <div className={'showDevices'}>
                        {!loading ? <><Typography style={{ fontSize: "18px", margin: "1em" }}>Found {(props.scanResults || scanResults).length} devices</Typography>
                            {(props.scanResults || scanResults).map((ele, index) => {
                                return <div key={index} className={'device'} style={{
                                    background: "#607D8B",
                                    display: "flex",
                                    justifyContent: "space-between",
                                    alignItems: "center",
                                    padding: "1em"
                                }}>
                                    <div>
                                        <Typography style={{ fontSize: "18px", color: "#ffffff" }}>{ele.name}</Typography>
                                        <Typography style={{ fontSize: "12px", color: "#cccccc" }}>
                                            {ele.type === 'metawear' ? '🔵 MetaWear Device' :
                                             ele.type === 'yellocanary' ? '🟡 YelloCanary Device' :
                                             '📱 BLE Device'}
                                        </Typography>
                                    </div>
                                    {!isConnected ? (
                                        <IconButton
                                            style={{
                                                color: "#ffffff",
                                                backgroundColor: "rgba(255, 255, 255, 0.1)",
                                                padding: "8px"
                                            }}
                                            onClick={() => {
                                                if (props.connectToSpecificDevice) {
                                                    props.connectToSpecificDevice(ele);
                                                } else {
                                                    connectToDevice(ele);
                                                }
                                            }}
                                        >
                                            <BluetoothConnected />
                                        </IconButton>
                                    ) : (
                                        <div style={{ display: "flex", alignItems: "center", color: "#4CAF50" }}>
                                            <BluetoothConnected style={{ marginRight: "4px" }} />
                                            <Typography style={{ fontSize: "12px", color: "#4CAF50" }}>
                                                Connected
                                            </Typography>
                                        </div>
                                    )}
                                </div>
                            })}

                            {/* Sensor Fusion Controls */}
                            {(props.isConnected !== undefined ? props.isConnected : isConnected) && (
                                <div style={{ display: "flex", gap: "1em", margin: "1em", flexDirection: "column", alignItems: "center" }}>
                                    <Typography style={{ fontSize: "16px", color: "#607D8B" }}>
                                        Sensor Status: {(props.isStreaming !== undefined ? props.isStreaming : isStreaming) ? 'Streaming' : 'Ready'}
                                    </Typography>
                                    <div style={{ display: "flex", gap: "1em" }}>
                                        {!(props.isStreaming !== undefined ? props.isStreaming : isStreaming) ? (
                                            <Button
                                                variant={'outlined'}
                                                style={{ borderColor: "#2A9D8F", color: "#2A9D8F" }}
                                                onClick={props.startSensorFusion || startSensorFusion}
                                            >
                                                Start Sensors
                                            </Button>
                                        ) : (
                                            <Button
                                                variant={'outlined'}
                                                style={{ borderColor: "#F44336", color: "#F44336" }}
                                                onClick={props.stopSensorFusion || stopSensorFusion}
                                            >
                                                Stop Sensors
                                            </Button>
                                        )}
                                    </div>
                                    {(props.isStreaming !== undefined ? props.isStreaming : isStreaming) && (
                                        <Typography style={{ fontSize: "12px", color: "#607D8B" }}>
                                            Collecting: Gyro({(props.sensorData?.gyro || gyroData).length}), Accel({(props.sensorData?.accel || accelData).length}), Mag({(props.sensorData?.mag || magData).length}), Quat({(props.sensorData?.quat || quatData).length})
                                        </Typography>
                                    )}
                                </div>
                            )}

                            <Button color={'primary'} onClick={() => {
                                finishSetup()
                            }} variant={'contained'} style={{ color: "#ffffff", margin: "1em" }}>Finish Setup</Button>
                            <div style={{ position: "fixed", bottom: "2em", display: "flex", justifyContent: "center", alignItems: "center", flexDirection: "column" }}>
                                <div className={'fallbackText'}>
                                    <Typography>Didn't find what you are looking for ?</Typography>
                                </div>
                                <div style={{ display: "flex", gap: "1em", width: "100%", justifyContent: "center", alignItems: "center" }}>
                                    <Button variant={'outlined'} style={{
                                        borderColor: "#2A9D8F",
                                        color: "#2A9D8F",

                                    }}
                                        onClick={async () => {
                                            // Check Bluetooth status before allowing scan again
                                            const bluetoothEnabled = await checkBluetoothStatus();
                                            if (bluetoothEnabled) {
                                                setShowDevices(false);
                                                setScanResults([]);
                                                setShowAnimation(false);
                                                setLastLength(0);
                                            }
                                        }}

                                    >
                                        Scan Again
                                    </Button>
                                    <Button variant={'outlined'} style={{
                                        borderColor: "#F44336",
                                        color: "#F44336",
                                    }}
                                        onClick={() => history.goBack()}
                                    >
                                        Cancel
                                    </Button>

                                </div>
                            </div></> : <div style={{ padding: "1em", display: "flex", justifyContent: "center", alignItems: "center", gap: "2em", flexDirection: "column" }}>

                            <CircularProgress
                                style={{ height: "100px", width: "100px" }}
                                variant={'determinate'}

                                /**
                                 * TODO: [HOM-199] QUESTION:
                                 * A single device takes 6 independent steps to fulfill its provisioning.
                                 * Whenever a step is completed a value of out 100 is added to the initial value(starting from 0).
                                 * For example for one device , every step will add 100/6 to the initial value when it completes.
                                 * How much every step will contribute if number of devices are two or more devices ?
                                 *  */
                                value={loadingValue}

                            />

                            <Typography style={{ fontSize: "14px", color: "#607D8B", transitionTimingFunction: "ease-in" }}>{Math.round(loadingValue) === 99 ? 100 : Math.round(loadingValue)} % completed</Typography>
                            <Typography style={{ fontSize: "16px", color: "#607D8B" }} >{quoteObject ? quoteObject!.text : ""}</Typography>
                            <Typography style={{ fontSize: "14px", color: "#cccccc", paddingLeft: "0.3em" }}>-- {quoteObject ? quoteObject!.from : ""}</Typography>
                        </div>}
                    </div>
                }
            </>
        }
        {/* Wifi setup dialog */}
        <Dialog fullScreen={fullScreen} style={{ display: "flex", flexDirection: "column", alignItems: "center" }} TransitionComponent={Transition} open={openModal} onClose={() => setOpenModal(false)} aria-labelledby="form-dialog-title">
            <DialogTitle id="form-dialog-title">Setup Wifi</DialogTitle>
            <DialogContent>
                <DialogContentText>
                    We need your wifi credentials for networking pusposes only
                </DialogContentText>
                <TextField
                    autoFocus
                    margin="dense"
                    id="name"
                    label="SSID"
                    type="text"
                    fullWidth
                    value={WIFI && WIFI.ssid ? WIFI.ssid : ssid}
                    placeholder={'WiFi Name/SSID'}

                    onChange={(e) => {
                        setSsid(e.currentTarget.value)
                    }}
                />
                <TextField
                    autoFocus
                    margin="dense"
                    id="name"
                    label="Password"
                    type="password"
                    fullWidth
                    value={password}
                    placeholder={'WiFi Password'}
                    onChange={(e) => {
                        setPassword(e.currentTarget.value)
                    }}
                />
            </DialogContent>
            <DialogActions>
                <Button onClick={() => setOpenModal(false)} color="primary">
                    Cancel
                </Button>
                <Button onClick={() => {
                    dispatch(setWifi({
                        ssid: ssid,
                        password: password
                    }))
                    setOpenModal(false);
                }} color="primary">
                    Save
                </Button>
            </DialogActions>
        </Dialog>

        {/* Bluetooth notification dialog */}
        <Dialog
            fullScreen={fullScreen}
            style={{ display: "flex", flexDirection: "column", alignItems: "center" }}
            TransitionComponent={Transition}
            open={bluetoothNotificationOpen}
            onClose={() => setBluetoothNotificationOpen(false)}
            aria-labelledby="bluetooth-dialog-title"
        >
            <DialogTitle id="bluetooth-dialog-title" style={{ display: "flex", alignItems: "center", gap: "12px" }}>
                <BluetoothDisabled style={{ color: "#f44336" }} />
                Bluetooth Required
            </DialogTitle>
            <DialogContent>
                <DialogContentText>
                    Bluetooth is currently turned off. Please enable Bluetooth to connect to your device and start scanning for available devices.
                </DialogContentText>
            </DialogContent>
            <DialogActions>
                <Button onClick={() => setBluetoothNotificationOpen(false)} color="primary">
                    Cancel
                </Button>
                <Button
                    onClick={() => {
                        setBluetoothNotificationOpen(false);
                        // Optionally, you can try to open Bluetooth settings
                        // BLE.showBluetoothSettings() if available
                    }}
                    color="primary"
                    variant="contained"
                >
                    OK
                </Button>
            </DialogActions>
        </Dialog>
    </div >
}

export default BleShow
