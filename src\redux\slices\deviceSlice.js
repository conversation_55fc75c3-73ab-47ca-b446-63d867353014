import { createSlice } from '@reduxjs/toolkit';
export const DeviceSlice = createSlice({
    name: 'devices',
    initialState: {
        value: "sometestdevice",
        connected : true,
        allDevices:['sometestdevice']
    },
    reducers: {
        setDevice: (state, action) => {
            // 
            // set fetched purchase
            console.log("From Redux", JSON.stringify(action.payload))
            state.value = action.payload;
        },
        setDeviceList: (state, action) => {
            // 
            // set fetched purchase
            console.log("From Redux", JSON.stringify(action.payload))
            state.allDevices = action.payload;
        },
        setDeviceConnected: (state, action) => {
            // 
            // set fetched purchase
            console.log("From Redux", JSON.stringify(action.payload))
            state.connected = action.payload;
        },
        clearDeviceState : (state) => {
            state.value = null;
            state.connected = null;
            state.allDevices = null;
        }


    }
})

export const selectDevice = (state) => {
    return state.devices
};
export const { setDevice , setDeviceList , setDeviceConnected , clearDeviceState } = DeviceSlice.actions;
export default DeviceSlice.reducer;

