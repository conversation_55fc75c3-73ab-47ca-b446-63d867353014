import { BleClient, numbersToDataView } from '@capacitor-community/bluetooth-le';

// MetaWear Service & Characteristics UUIDs
export const MW_SERVICE = '326a9000-85cb-9195-d9dd-464cfbbae75a';
export const MW_CHAR_CMD = '326a9001-85cb-9195-d9dd-464cfbbae75a';
export const MW_CHAR_NOTIF = '326a9006-85cb-9195-d9dd-464cfbbae75a';

export interface SensorData {
  gyro: { time: number; x: number; y: number; z: number; }[];
  accel: { time: number; x: number; y: number; z: number; }[];
  mag: { time: number; x: number; y: number; z: number; }[];
  quat: { time: number; w: number; x: number; y: number; z: number; }[];
}

export class MetaWearSensorFusion {
  private deviceId: string | null = null;
  private isConnected: boolean = false;
  private isStreaming: boolean = false;
  private notifStarted: boolean = false;
  private sensorData: SensorData = {
    gyro: [],
    accel: [],
    mag: [],
    quat: []
  };
  private onDataCallback?: (data: SensorData) => void;
  private onStatusCallback?: (status: { isConnected: boolean; isStreaming: boolean }) => void;

  constructor(
    onDataCallback?: (data: SensorData) => void,
    onStatusCallback?: (status: { isConnected: boolean; isStreaming: boolean }) => void
  ) {
    this.onDataCallback = onDataCallback;
    this.onStatusCallback = onStatusCallback;
  }

  // Helper: write bytes to command characteristic
  private async writeCommand(bytes: number[]): Promise<void> {
    if (!this.deviceId) return;
    await BleClient.write(
      this.deviceId,
      MW_SERVICE,
      MW_CHAR_CMD,
      numbersToDataView(bytes)
    );
  }

  // Initialize BLE client
  async initializeBLE(): Promise<boolean> {
    try {
      console.log('🔵 MetaWear: Initializing BLE client...');
      await BleClient.initialize({ androidNeverForLocation: true });
      console.log('✅ MetaWear: BLE client initialized successfully');
      return true;
    } catch (err) {
      console.error('❌ MetaWear: Error initializing BLE client:', err);
      return false;
    }
  }

  // Connect to MetaWear device
  async connectToDevice(): Promise<boolean> {
    try {
      console.log('🔍 MetaWear: Requesting MetaWear device...');
      const device = await BleClient.requestDevice({
        services: [MW_SERVICE]
      });
      this.deviceId = device.deviceId;

      console.log('🔗 MetaWear: Connecting to device:', device.deviceId);
      await BleClient.connect(device.deviceId);
      console.log('✅ MetaWear: Connected successfully!');
      
      this.isConnected = true;
      this.updateStatus();
      return true;
    } catch (err) {
      console.error('❌ MetaWear: Error connecting to device:', err);
      return false;
    }
  }

  // Connect to specific device by ID
  async connectToSpecificDevice(deviceId: string): Promise<boolean> {
    try {
      console.log('🔗 MetaWear: Connecting to specific device:', deviceId);
      await BleClient.connect(deviceId);
      this.deviceId = deviceId;
      this.isConnected = true;
      this.updateStatus();
      console.log('✅ MetaWear: Connected to specific device successfully');
      return true;
    } catch (err) {
      console.error('❌ MetaWear: Error connecting to specific device:', err);
      return false;
    }
  }

  // Start sensor fusion streaming (exact implementation from bleNDOF.tsx)
  async startSensorFusion(): Promise<boolean> {
    if (!this.deviceId) {
      console.error('❌ MetaWear: No device connected');
      return false;
    }

    try {
      console.log('🔍 MetaWear: Discovering services...');
      const services = await BleClient.getServices(this.deviceId);
      if (!services.some(s => s.uuid === MW_SERVICE)) {
        throw new Error('MetaWear service not found');
      }

      console.log('⚙️ MetaWear: Configuring gyroscope...');
      await this.writeCommand([0x13, 0x03, 0x26, 0x04]); // Set gyro range to 125dps
      await this.writeCommand([0x13, 0x05, 0x01]);       // Enable gyro output
      await this.writeCommand([0x13, 0x02, 0x01, 0x00]); // Enable gyro interrupt
      await this.writeCommand([0x13, 0x01, 0x01]);       // Power on gyro

      console.log('⚙️ MetaWear: Configuring accelerometer...');
      await this.writeCommand([0x03, 0x01, 0x01]);       // Enable accelerometer

      console.log('⚙️ MetaWear: Configuring magnetometer...');
      await this.writeCommand([0x15, 0x01, 0x01]);       // Enable magnetometer

      console.log('⚙️ MetaWear: Configuring sensor fusion...');
      await this.writeCommand([0x19, 0x03, 0x01, 0x02, 0x00]); // Sensor Fusion config
      await this.writeCommand([0x19, 0x03, 0x02, 0x00, 0x00]); // Write config
      await this.writeCommand([0x19, 0x05, 0x03, 0x01]);       // Enable quaternion output
      await this.writeCommand([0x19, 0x01, 0x01]);             // Start sensor fusion

      console.log('📡 MetaWear: Subscribing to notifications...');
      await BleClient.startNotifications(
        this.deviceId,
        MW_SERVICE,
        MW_CHAR_NOTIF,
        this.handleNotification.bind(this)
      );
      
      this.notifStarted = true;
      this.isStreaming = true;
      this.updateStatus();

      console.log('🎉 MetaWear: Sensor fusion streaming started successfully!');
      return true;
    } catch (err) {
      console.error('❌ MetaWear: Error starting sensor fusion:', err);
      return false;
    }
  }

  // Handle notification data (exact implementation from bleNDOF.tsx)
  private handleNotification = (data: any) => {
    console.log("📡 MetaWear: DATA RAW ", data);
    const bytes = new Uint8Array(data.buffer);
    console.log("📊 MetaWear: BYTES ", bytes);
    const time = Date.now();

    // Check for Gyroscope data (0x13 0x05)
    if (bytes[0] === 0x13 && bytes[1] === 0x05) {
      const x = (bytes[3] << 8) | bytes[2];
      const y = (bytes[5] << 8) | bytes[4];
      const z = (bytes[7] << 8) | bytes[6];
      const xVal = (x & 0x8000) ? x - 0x10000 : x;
      const yVal = (y & 0x8000) ? y - 0x10000 : y;
      const zVal = (z & 0x8000) ? z - 0x10000 : z;

      console.log("🌀 MetaWear: GYROSCOPE ", xVal, " ", yVal, " ", zVal);
      this.sensorData.gyro = [...this.sensorData.gyro, { time, x: xVal, y: yVal, z: zVal }].slice(-100);

    // Check for Accelerometer data (0x03 0x04)
    } else if (bytes[0] === 0x03 && bytes[1] === 0x04) {
      const x = (bytes[3] << 8) | bytes[2];
      const y = (bytes[5] << 8) | bytes[4];
      const z = (bytes[7] << 8) | bytes[6];
      const xVal = (x & 0x8000) ? x - 0x10000 : x;
      const yVal = (y & 0x8000) ? y - 0x10000 : y;
      const zVal = (z & 0x8000) ? z - 0x10000 : z;

      console.log("📈 MetaWear: ACCELERATION ", xVal, " ", yVal, " ", zVal);
      this.sensorData.accel = [...this.sensorData.accel, { time, x: xVal, y: yVal, z: zVal }].slice(-100);

    // Check for Magnetometer data (0x15 0x04)
    } else if (bytes[0] === 0x15 && bytes[1] === 0x04) {
      const x = (bytes[3] << 8) | bytes[2];
      const y = (bytes[5] << 8) | bytes[4];
      const z = (bytes[7] << 8) | bytes[6];
      const xVal = (x & 0x8000) ? x - 0x10000 : x;
      const yVal = (y & 0x8000) ? y - 0x10000 : y;
      const zVal = (z & 0x8000) ? z - 0x10000 : z;

      console.log("🧭 MetaWear: MAGNETOMETER ", xVal, " ", yVal, " ", zVal);
      this.sensorData.mag = [...this.sensorData.mag, { time, x: xVal, y: yVal, z: zVal }].slice(-100);

    // Handle Quaternion data (0x19 0x07)
    } else if (bytes[0] === 0x19 && bytes[1] === 0x07) {
      const view = new DataView(bytes.buffer);
      const w = view.getFloat32(2, true); // little endian
      const x = view.getFloat32(6, true);
      const y = view.getFloat32(10, true);
      const z = view.getFloat32(14, true);

      console.log("🔄 MetaWear: QUATERNION ", w, " ", x, " ", y, " ", z);
      this.sensorData.quat = [...this.sensorData.quat, { time, w, x, y, z }].slice(-100);
    } else {
      console.log("❓ MetaWear: Unknown data type:", bytes[0], bytes[1]);
    }

    // Notify callback with updated data
    if (this.onDataCallback) {
      this.onDataCallback({ ...this.sensorData });
    }
  };

  // Stop sensor fusion streaming (exact implementation from bleNDOF.tsx)
  async stopSensorFusion(): Promise<boolean> {
    if (!this.deviceId) return false;
    
    try {
      console.log('⏹️ MetaWear: Stopping sensor fusion...');
      await this.writeCommand([0x13, 0x01, 0x00]); // Power off gyro
      await this.writeCommand([0x13, 0x02, 0x00, 0x00]); // Disable gyro interrupt
      await this.writeCommand([0x13, 0x05, 0x00]); // Disable gyro output
      await this.writeCommand([0x19, 0x01, 0x00]); // Stop sensor fusion
      await this.writeCommand([0x19, 0x05, 0x03, 0x00]); // Disable quaternion output

      if (this.notifStarted) {
        await BleClient.stopNotifications(this.deviceId, MW_SERVICE, MW_CHAR_NOTIF);
      }

      await BleClient.disconnect(this.deviceId);

      this.deviceId = null;
      this.notifStarted = false;
      this.isConnected = false;
      this.isStreaming = false;
      this.updateStatus();

      console.log('✅ MetaWear: Disconnected successfully');
      return true;
    } catch (err) {
      console.error('❌ MetaWear: Error during stop:', err);
      return false;
    }
  }

  // Update status callback
  private updateStatus(): void {
    if (this.onStatusCallback) {
      this.onStatusCallback({
        isConnected: this.isConnected,
        isStreaming: this.isStreaming
      });
    }
  }

  // Getters
  getDeviceId(): string | null {
    return this.deviceId;
  }

  getIsConnected(): boolean {
    return this.isConnected;
  }

  getIsStreaming(): boolean {
    return this.isStreaming;
  }

  getSensorData(): SensorData {
    return { ...this.sensorData };
  }
}
