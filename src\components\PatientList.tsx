//@ts-nocheck
import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  Card, CardContent, Typography, Button, CardActions, IconButton, TextField,
  Modal, Box, MenuItem, Select, FormControl, InputLabel, Pagination, Stack,
  Tooltip, Alert, Grid, RadioGroup, FormControlLabel, Radio, FormHelperText, InputAdornment
} from '@mui/material';
import { Edit, Report, PlayCircleOutline, AddCircle } from '@mui/icons-material';
import { useHistory } from 'react-router-dom';
import CloseIcon from '@mui/icons-material/Close';
import { useForm, Controller, useWatch } from 'react-hook-form';
import { Patient, TestData } from './types';
import ReportModal from './ReportModal';
import { getPatientsByProvider, mapApiPatientToLocal } from '../api/getPatientsByProvider';
import { useSelector } from 'react-redux';
import { selectUser } from "../redux/slices/userSlice"
import { addOrEditPatient } from '../api/addOrEditPatient';
import { Sort } from '@mui/icons-material';
import { SelectChangeEvent } from '@mui/material/Select';


interface SortOption {
  key: 'lastAppointment' | 'lastName';
  label: string;
}

interface PatientListProps {
  patients: Patient[];
  setPatients: (patients: Patient[]) => void;
  sampleData: TestData[];
}

const isValidDate = (dateString: string) => {
  const regex = /^\d{2}\/\d{2}\/\d{4}$/;
  if (!regex.test(dateString)) return false;
  const [month, day, year] = dateString.split('/').map(Number);
  const date = new Date(year, month - 1, day);
  return (
    date.getFullYear() === year &&
    date.getMonth() === month - 1 &&
    date.getDate() === day
  );
};

const isAgeValid = (dateString: string) => {
  const [month, day, year] = dateString.split('/').map(Number);
  const dob = new Date(year, month - 1, day);
  const today = new Date();
  let age = today.getFullYear() - dob.getFullYear();
  const monthDiff = today.getMonth() - dob.getMonth();
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dob.getDate())) {
    age--;
  }
  return age >= 2 && age <= 100;
};

const validatePhone = (value: string) => {
  if (!value) return 'Phone number is required';
  const regex = /^[\d-]+$/;
  if (!regex.test(value)) return 'Only numbers and dashes allowed';
  return true;
};

const formatDOB = (value: string) => {
  const cleaned = value.replace(/\D/g, '');
  const match = cleaned.match(/^(\d{0,2})(\d{0,2})(\d{0,4})$/);
  if (!match) return '';
  return `${match[1]}${match[2] ? `/${match[2]}` : ''}${match[3] ? `/${match[3]}` : ''}`;
};

const PatientList: React.FC<PatientListProps> = ({ patients, setPatients, sampleData }) => {
  const history = useHistory();
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [patientsPerPage] = useState(5);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [successAlertOpen, setSuccessAlertOpen] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [modalMode, setModalMode] = useState<'create' | 'edit'>('create');
  const [reportModalOpen, setReportModalOpen] = useState(false);
  const [selectedReportPatient, setSelectedReportPatient] = useState<Patient | null>(null);
  const user = useSelector(selectUser).value;
  const modalRef = useRef(null);
  const [sortKey, setSortKey] = useState<'lastAppointment' | 'lastName'>('lastAppointment');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');

  const sortOptions: SortOption[] = [
    { key: 'lastAppointment', label: 'Last Appointment' },
    { key: 'lastName', label: 'Last Name' },
  ];
  const {
    handleSubmit,
    control,
    reset,
    setValue,
    formState: { errors }
  } = useForm({
    defaultValues: {
      firstName: '',
      lastName: '',
      heightFeet: '',
      heightInches: '',
      weight: '',
      dob: '',
      gender: '',
      mrn: '',
      status: 'active',
      remoteMonitor: 'no',
      email: '',
      phoneNumber: '',
      countryCode: '+1',
      weightUnits: 'lbs'
    },
  });

  const watchRemoteMonitor = useWatch({
    control,
    name: 'remoteMonitor',
    defaultValue: 'no',
  });

  const generateNewId = (): number => Math.max(...patients.map(p => p.id), 0) + 1;

  const sortPatients = (patients: Patient[], key: 'lastAppointment' | 'lastName', direction: 'asc' | 'desc') => {
    return [...patients].sort((a, b) => {
      // Handle undefined values first
      if (key === 'lastAppointment') {
        if (!a.lastAppointment && !b.lastAppointment) return 0;
        if (!a.lastAppointment) return -1;
        if (!b.lastAppointment) return 1;

        const dateA = new Date(a.lastAppointment).getTime();
        const dateB = new Date(b.lastAppointment).getTime();
        return direction === 'asc' ? dateA - dateB : dateB - dateA;
      }

      if (key === 'lastName') {
        const getLastName = (name?: string) => (name ? name.split(' ').pop()?.toLowerCase() || '' : '');
        const lastNameA = getLastName(a.name);
        const lastNameB = getLastName(b.name);

        if (lastNameA < lastNameB) return direction === 'asc' ? -1 : 1;
        if (lastNameA > lastNameB) return direction === 'asc' ? 1 : -1;
        return 0;
      }

      return 0;
    });
  };

  // Modify the filteredPatients line to include sorting
  const filteredPatients = sortPatients(
    patients.filter((patient) =>
      patient.name?.toLowerCase().includes(searchQuery.toLowerCase())
    ),
    sortKey,
    sortDirection
  );
  const indexOfLastPatient = currentPage * patientsPerPage;
  const indexOfFirstPatient = indexOfLastPatient - patientsPerPage;
  const currentPatients = filteredPatients.slice(indexOfFirstPatient, indexOfLastPatient);

  const handleSavePatient = useCallback(async (data) => {
    if (modalMode === 'create') {
      try {
        const token = user.accessToken;

        // Map form fields to API body structure
        const newPatient = {
          docId: "",
          cellPhoneNumber: data.cellPhoneNumber || "",
          dateOfBirth: data.dob || "",
          emailAddress: data.email || "",
          firstName: data.firstName || "",
          gender: data.gender || "",
          fullName: `${data.firstName || ""} ${data.middleName || ""} ${data.lastName || ""}`.trim(),
          heightFeetComponent: data.heightFeet || 0,
          heightInches: (data.heightFeet || 0) * 12 + (data.heightInches || 0),
          heightInchComponent: data.heightInches || 0,
          lastName: data.lastName || "",
          practiceDocId: user.practiceDocId,
          providerDocId: user.providerDocId,
          PracticeNoteId: user.ProviderNoteId,
          middleName: data.middleName || "",
          mrn: data.mrn || "",
          RemoteAccess: data.remoteMonitor === 'yes' ? "Yes" : "No",
          userStatus: data.status === 'active' ? "Active" : "Inactive",
          WeightUnits: data.WeightUnits || "Pounds",
          WeightValue: data.weight || 0,
          notesFullName: "",
        };

        const createdPatient = await addOrEditPatient(token, newPatient);

        setPatients((prevPatients) => [
          ...prevPatients,
          {
            id: createdPatient.docId,
            firstName: createdPatient.firstName,
            lastName: createdPatient.lastName,
            name: createdPatient.fullName,
            email: createdPatient.emailAddress,
            phoneNumber: createdPatient.cellPhoneNumber,
            gender: createdPatient.gender,
            age: '', // optional: compute from DOB
            dob: createdPatient.dateOfBirth,
            weight: createdPatient.WeightValue,
            height: createdPatient.heightInches,
            bloodGroup: '',
            address: '',
            city: '',
            state: '',
            pincode: '',
            remoteAccess: createdPatient.RemoteAccess === "Yes",
            lastAppointment: new Date().toISOString(),
          },
        ]);

        setIsModalOpen(false);
        setSuccessAlertOpen(true);
        setTimeout(() => setSuccessAlertOpen(false), 3000);
        reset();
        setSelectedPatient(null);
      } catch (error) {
        console.error('Error adding patient:', error);
      }
    }

    if (modalMode === 'edit' && selectedPatient) {
      const updatedPatients = [...patients];
      const index = updatedPatients.findIndex((p) => p.id === selectedPatient.id);
      if (index > -1) {
        const updated = {
          ...selectedPatient,
          ...data,
          name: `${data.firstName} ${data.lastName}`,
        };
        updatedPatients[index] = updated;
        setPatients(updatedPatients);
      }

      setIsModalOpen(false);
      setSuccessAlertOpen(true);
      setTimeout(() => setSuccessAlertOpen(false), 3000);
      reset();
      setSelectedPatient(null);
    }
  }, [modalMode, patients, reset, selectedPatient, setPatients, user]);

  const openModal = () => {
    setModalMode('create');
    reset();
    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedPatient(null);
  };

  const onOutsideClick = useCallback((event: MouseEvent) => {
    const target = event.target as HTMLElement;

    // Ignore clicks on MUI Popover/Select Menus
    if (document.querySelector('.MuiPopover-root')?.contains(target)) {
      return;
    }

    if (modalRef.current && !(modalRef.current as any).contains(target)) {
      closeModal();
    }
  }, []);

  const handleEdit = (patient: Patient) => {
    setModalMode('edit');
    setSelectedPatient(patient);
    setIsModalOpen(true);

    reset({
      firstName: patient.firstName ?? '',
      middleName: patient.middleName ?? '',
      lastName: patient.lastName ?? '',
      email: patient.email ?? '', // Add email field
      dob: patient.dob ?? '',
      mrn: patient.mrn ?? '',
      heightFeet: patient.heightFeet ?? '',
      heightInches: patient.heightInches ?? '',
      weight: patient.weight ?? '',
      gender: patient.gender ?? '', // Convert to lowercase
      provider: patient.provider ?? '',
      status: patient.status ?? 'active',
      remoteMonitor: patient.remoteAccess ? 'yes' : 'no',
    });
  };

  const handleReportToggle = (patient: Patient) => {
    setSelectedReportPatient(patient);
    console.log("SELECTED PATIENT ", patient);
    setReportModalOpen(prev => !prev);
  };

  const handleStartTest = (patient: Patient) => {
    // Navigate to test screen with patient ID
    history.push(`/test/${patient.id}`);
  };

  useEffect(() => {
    document.addEventListener('mousedown', onOutsideClick);
    return () => document.removeEventListener('mousedown', onOutsideClick);
  }, [onOutsideClick]);

  useEffect(() => {
    const loadPatients = async () => {
      try {
        const token = user.accessToken;
        const data = await getPatientsByProvider(token);
        const mapped = Array.isArray(data) ? data.map(mapApiPatientToLocal) : [];
        setPatients(mapped);
      } catch (err) {
        console.error('Failed to fetch patients', err);
      }
    };
    loadPatients();
  }, [setPatients]);
  return (
    <div>
      {successAlertOpen && <Alert severity="success">Patient saved successfully.</Alert>}

      <Grid container spacing={2} sx={{ marginBottom: '20px' }}>
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <InputLabel>Sort By</InputLabel>
            <Select
              value={sortKey}
              label="Sort By"
              onChange={(e: SelectChangeEvent<'lastAppointment' | 'lastName'>) =>
                setSortKey(e.target.value as 'lastAppointment' | 'lastName')
              }
              startAdornment={
                <InputAdornment position="start">
                  <Sort />
                </InputAdornment>
              }
            >
              {sortOptions.map((option) => (
                <MenuItem key={option.key} value={option.key}>
                  {option.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={4}>
          <FormControl fullWidth>
            <InputLabel>Sort Direction</InputLabel>
            <Select
              value={sortDirection}
              label="Sort Direction"
              onChange={(e: SelectChangeEvent<'asc' | 'desc'>) =>
                setSortDirection(e.target.value as 'asc' | 'desc')
              }
            >
              <MenuItem value="asc">Ascending</MenuItem>
              <MenuItem value="desc">Descending</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        <Grid item xs={12} sm={4}>
          <TextField
            label="Search Patients"
            variant="outlined"
            fullWidth
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </Grid>
      </Grid>

      <Grid container justifyContent="flex-end" sx={{ marginBottom: '20px' }}>
        <Grid item>
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddCircle />}
            onClick={openModal}
          >
            Add Patient
          </Button>
        </Grid>
      </Grid>

      {currentPatients.map((patient, index) => (
        <Card key={index} sx={{ marginBottom: '20px' }}>
          <CardContent>
            <Typography gutterBottom variant="h5">{patient.name}</Typography>
            <Typography variant="body2">Email: {patient.email}</Typography>
            <Typography variant="body2">DOB: {patient.dob}</Typography>
            <Typography variant="body2">Last Appointment: {patient.lastAppointment}</Typography>
            <Typography variant="body2">Remote Access: {patient.remoteAccess ? 'Yes' : 'No'}</Typography>
          </CardContent>
          <CardActions>
            <Tooltip title="Edit Patient"><IconButton color="primary" onClick={() => handleEdit(patient)}><Edit /></IconButton></Tooltip>
            <Tooltip title="Report on Patient"><IconButton color="secondary" onClick={() => handleReportToggle(patient)}><Report /></IconButton></Tooltip>
            <Tooltip title="Start Test"><IconButton color="success" onClick={() => handleStartTest(patient)}><PlayCircleOutline /></IconButton></Tooltip>
          </CardActions>
        </Card>
      ))}

      <Pagination
        count={Math.ceil(filteredPatients.length / patientsPerPage)}
        page={currentPage}
        onChange={(e, value) => setCurrentPage(value)}
        color="primary"
        sx={{ display: 'flex', justifyContent: 'center', marginTop: '20px' }}
      />

      <Modal open={isModalOpen} onClose={closeModal}>
        <Box
          ref={modalRef}
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            bgcolor: 'background.paper',
            boxShadow: 24,
            p: 4,
            width: '100%',
            height: '100%',
            borderRadius: 2,
            display: 'flex',
            flexDirection: 'column',
            padding: '20px',
            overflowY: 'auto',
          }}
        >
          <Stack direction="row" justifyContent="space-between" mb={2}>
            <Typography variant="h6">{modalMode === 'create' ? 'Add Patient' : 'Edit Patient'}</Typography>
            <IconButton onClick={closeModal}><CloseIcon /></IconButton>
          </Stack>

          <form onSubmit={handleSubmit(handleSavePatient)}>
            <Grid container spacing={2}>
              {/* First Name */}
              <Grid item xs={12} sm={6}>
                <Controller
                  name="firstName"
                  control={control}
                  rules={{ required: true }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="First Name *"
                      fullWidth
                      error={!!errors.firstName}
                      helperText={errors.firstName && "Required"}
                    />
                  )}
                />
              </Grid>

              {/* Last Name */}
              <Grid item xs={12} sm={6}>
                <Controller
                  name="lastName"
                  control={control}
                  rules={{ required: true }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Last Name *"
                      fullWidth
                      error={!!errors.lastName}
                      helperText={errors.lastName && "Required"}
                    />
                  )}
                />
              </Grid>

              {/* Date of Birth */}
              <Grid item xs={12} sm={6}>
                <Controller
                  name="dob"
                  control={control}
                  rules={{
                    required: 'Date of Birth is required',
                    validate: (value) =>
                      isValidDate(value) && isAgeValid(value) || 'Invalid date or age',
                  }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="Date of Birth * (MM/DD/YYYY)"
                      fullWidth
                      error={!!errors.dob}
                      helperText={errors.dob?.message}
                      onChange={(e) => {
                        const formatted = formatDOB(e.target.value);
                        field.onChange(formatted);
                      }}
                      inputProps={{ maxLength: 10 }}
                    />
                  )}
                />
              </Grid>

              {/* Height */}
              {/* Modified Height Fields */}
              <Grid item xs={12} sm={6}>
                <Stack direction="row" spacing={2}>
                  <Controller
                    name="heightFeet"
                    control={control}
                    rules={{
                      required: 'Height (ft) is required',
                      min: { value: 0, message: 'Must be ≥ 0' },
                      max: { value: 8, message: 'Must be ≤ 8' }
                    }}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Feet *"
                        type="number"
                        fullWidth
                        inputProps={{ min: 0, max: 8 }}
                        error={!!errors.heightFeet}
                        helperText={errors.heightFeet?.message}
                      />
                    )}
                  />
                  <Controller
                    name="heightInches"
                    control={control}
                    rules={{
                      required: 'Height (in) is required',
                      min: { value: 0, message: 'Must be ≥ 0' },
                      max: { value: 11, message: 'Must be ≤ 11' }
                    }}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Inches *"
                        type="number"
                        fullWidth
                        inputProps={{ min: 0, max: 11 }}
                        error={!!errors.heightInches}
                        helperText={errors.heightInches?.message}
                      />
                    )}
                  />
                </Stack>
              </Grid>
              {/* Weight */}
              {/* Weight */}
              <Grid item xs={12} sm={6}>
                <Grid container alignItems="center" spacing={2}>
                  <Grid item xs={6}>
                    <Controller
                      name="weight"
                      control={control}
                      rules={{
                        required: 'Weight is required',
                        min: { value: 0, message: 'Must be ≥ 0' }
                      }}
                      render={({ field }) => (
                        <TextField
                          {...field}
                          label="Weight *"
                          type="number"
                          fullWidth
                          error={!!errors.weight}
                          helperText={errors.weight?.message}
                        />
                      )}
                    />
                  </Grid>
                  <Grid item xs={6}>
                    <Controller
                      name="weightUnits"
                      control={control}
                      defaultValue="lbs"
                      rules={{ required: true }}
                      render={({ field }) => (
                        <FormControl component="fieldset">
                          <RadioGroup
                            row
                            {...field}
                            sx={{
                              height: '100%',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'flex-end'
                            }}
                          >
                            <FormControlLabel
                              value="lbs"
                              control={<Radio size="small" />}
                              label="lbs"
                              sx={{ marginRight: 2 }}
                            />
                            <FormControlLabel
                              value="kg"
                              control={<Radio size="small" />}
                              label="kg"
                            />
                          </RadioGroup>
                        </FormControl>
                      )}
                    />
                  </Grid>
                </Grid>
              </Grid>
              {/* Gender */}
              <Grid item xs={12} sm={6}>
                <Controller
                  name="gender"
                  control={control}
                  rules={{ required: true }}
                  render={({ field }) => (
                    <FormControl fullWidth error={!!errors.gender}>
                      <InputLabel>Gender *</InputLabel>
                      <Select {...field} label="Gender *">
                        <MenuItem value="Male">Male</MenuItem>
                        <MenuItem value="Female">Female</MenuItem>
                      </Select>
                      <FormHelperText>
                        {errors.gender && "Required"}
                      </FormHelperText>
                    </FormControl>
                  )}
                />
              </Grid>


              {/* MRN Field */}
              {modalMode !== 'create' && <Grid item xs={12} sm={6}>
                <Controller
                  name="mrn"
                  control={control}
                  rules={{ required: 'MRN is required' }}
                  render={({ field }) => (
                    <TextField
                      {...field}
                      label="MRN *"
                      fullWidth
                      error={!!errors.mrn}
                      helperText={errors.mrn?.message}
                    />
                  )}
                />
              </Grid>}

              <Grid item xs={12} sm={6}>
                <Controller
                  name="status"
                  control={control}
                  rules={{ required: 'Status is required' }}
                  render={({ field }) => (
                    <FormControl fullWidth error={!!errors.status}>
                      <InputLabel>Status *</InputLabel>
                      <Select {...field} label="Status *">
                        <MenuItem value="active">Active</MenuItem>
                        <MenuItem value="inactive">Inactive</MenuItem>
                      </Select>
                      <FormHelperText>
                        {errors.status?.message}
                      </FormHelperText>
                    </FormControl>
                  )}
                />
              </Grid>


              {/* Remote Monitor */}
              <Grid item xs={12} sm={6}>
                <Controller
                  name="remoteMonitor"
                  control={control}
                  rules={{ required: true }}
                  render={({ field }) => (
                    <FormControl fullWidth error={!!errors.remoteMonitor}>
                      <InputLabel>Remote Monitor *</InputLabel>
                      <Select {...field} label="Remote Monitor *">
                        <MenuItem value="yes">Yes</MenuItem>
                        <MenuItem value="no">No</MenuItem>
                      </Select>
                      <FormHelperText>
                        {errors.remoteMonitor && "Required"}
                      </FormHelperText>
                    </FormControl>
                  )}
                />
              </Grid>

              {/* Conditional Email */}
              {watchRemoteMonitor === 'yes' && (
                <Grid item xs={12} sm={6}>
                  <Controller
                    name="email"
                    control={control}
                    rules={{
                      required: 'Email is required',
                      pattern: {
                        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                        message: 'Invalid email address',
                      },
                    }}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Email *"
                        fullWidth
                        error={!!errors.email}
                        helperText={errors.email?.message}
                      />
                    )}
                  />
                </Grid>
              )}

              {/* Conditional Phone */}
              {watchRemoteMonitor === 'yes' && (
                // Update the phoneNumber Controller in the modal form
                <Grid item xs={12} sm={6}>
                  <Controller
                    name="phoneNumber"
                    control={control}
                    rules={{
                      required: 'Phone is required',
                      validate: validatePhone,
                    }}
                    render={({ field }) => (
                      <TextField
                        {...field}
                        label="Phone *"
                        fullWidth
                        error={!!errors.phoneNumber}
                        helperText={errors.phoneNumber?.message}
                        onKeyDown={(e) => {
                          // Allow only numbers, dashes, and navigation keys
                          if (!/[0-9-]/.test(e.key) &&
                            e.key !== 'Backspace' &&
                            e.key !== 'Delete' &&
                            e.key !== 'ArrowLeft' &&
                            e.key !== 'ArrowRight') {
                            e.preventDefault();
                          }
                        }}
                        onChange={(e) => {
                          // Sanitize any pasted input
                          const sanitized = e.target.value.replace(/[^\d-]/g, '');
                          field.onChange(sanitized);
                        }}
                        InputProps={{
                          startAdornment: (
                            <InputAdornment position="start">
                              <Controller
                                name="countryCode"
                                control={control}
                                defaultValue="+1"
                                render={({ field }) => (
                                  <Select {...field} sx={{ width: 140 }}>
                                    <MenuItem value="+1">+1 (US/Canada)</MenuItem>
                                    <MenuItem value="+972">+972 (Israel)</MenuItem>
                                    <MenuItem value="+44">+44 (UK)</MenuItem>
                                  </Select>
                                )}
                              />
                            </InputAdornment>
                          ),
                        }}
                      />
                    )}
                  />
                </Grid>
              )}
            </Grid>


            <Button
              type="submit"
              
              variant="contained"
              sx={{ mt: 3 }}
            >
              {modalMode === 'create' ? 'Add Patient' : 'Update Patient'}
            </Button>
            <Button
             onClick={()=>{
              navigate('/test')
             }}
              
              variant="contained"
              sx={{ mt: 3 }}
            >
              {modalMode === 'create' ? 'Add Patient & Start Testing' : 'Update Patient & Start Testing'}
            </Button>
          </form>
        </Box>
      </Modal>

      {selectedReportPatient && (
        <ReportModal
          open={reportModalOpen}
          patientName={selectedReportPatient.name}
          testData={sampleData}
          patientDocId={selectedReportPatient.docId}
          patientId={selectedReportPatient.patientId}
          onClose={() => handleReportToggle(selectedReportPatient)}
        />
      )}
    </div>
  );
};

export default PatientList;