//@ts-nocheck
import React, { useEffect, useState } from 'react';
import { Typo<PERSON>, Button, Box, CircularProgress, IconButton, Drawer, List, ListItem, ListItemText } from '@mui/material';
import { Menu as MenuIcon } from '@mui/icons-material';
import { useHistory } from 'react-router';
import PatientList from '../components/PatientList';
import { useSelector } from 'react-redux';
import { selectDevice } from "../redux/slices/deviceSlice.js"
import "./Home.css"
import PatientDashboard from '../components/PatientDashboard';
const Home = () => {
  const history = useHistory();
  const device = useSelector(selectDevice)
  const [menuOpen, setMenuOpen] = useState(false);

  const dummyChartData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
    datasets: [
      {
        label: 'Performance',
        data: [65, 59, 80, 81, 56],
        borderColor: '#3f51b5',
        tension: 0.4,
      },
    ],
  };

  const batteryLevel = 85;

  const patients = [
    {
      name: '<PERSON>',
      email: '<EMAIL>',
      dob: '1980-01-15',
      lastAppointment: '2025-01-10',
      remoteAccess: true,
    },
    {
      name: 'Jane Smith',
      email: '<EMAIL>',
      dob: '1992-03-22',
      lastAppointment: '2025-01-18',
      remoteAccess: false,
    },
    {
      name: 'Alice Johnson',
      email: '<EMAIL>',
      dob: '1985-07-30',
      lastAppointment: '2025-01-12',
      remoteAccess: true,
    },
    {
      name: 'Bob Williams',
      email: '<EMAIL>',
      dob: '1975-05-05',
      lastAppointment: '2025-01-14',
      remoteAccess: false,
    },
    {
      name: 'Emma Brown',
      email: '<EMAIL>',
      dob: '1990-09-10',
      lastAppointment: '2025-01-15',
      remoteAccess: true,
    },
    {
      name: 'Charlie Davis',
      email: '<EMAIL>',
      dob: '2000-11-25',
      lastAppointment: '2025-01-11',
      remoteAccess: true,
    },
    {
      name: 'Sophia Wilson',
      email: '<EMAIL>',
      dob: '1998-04-16',
      lastAppointment: '2025-01-19',
      remoteAccess: false,
    },
    {
      name: 'Michael Thomas',
      email: '<EMAIL>',
      dob: '1982-12-01',
      lastAppointment: '2025-01-13',
      remoteAccess: true,
    },
    {
      name: 'Isabella Martinez',
      email: '<EMAIL>',
      dob: '1994-02-08',
      lastAppointment: '2025-01-17',
      remoteAccess: false,
    },
    {
      name: 'Ethan Garcia',
      email: '<EMAIL>',
      dob: '1996-06-22',
      lastAppointment: '2025-01-16',
      remoteAccess: true,
    },
  ];

  useEffect(() => {
    console.log("DEVICE ", device);
    if (!device.value) history.push("/devicesetup")
  }, [device])

  return (
    <Box sx={{ padding: '2em', marginTop: "7em" }}>
      {/* Category Cards */}
      <Typography variant="h4" color="var(--text-color)" style={{color:"rgb(96, 125, 139)",textAlign:"center"}} gutterBottom>
        All Patients
      </Typography>
      <div className={'cardGrid'} >

       <PatientDashboard/>

      </div>

    </Box>
  );
};

export default Home;
