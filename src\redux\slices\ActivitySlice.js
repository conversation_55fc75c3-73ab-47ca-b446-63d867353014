import { createSlice } from '@reduxjs/toolkit';
export const activitySlice = createSlice({
    name: 'activity',
    initialState: false,
    reducers: {
        changeActivity: (state, action) => {
            // 
            // set fetched purchase
            // alert("From Redux",JSON.stringify(action.payload))
            state = action.payload;
        },
    }
})
export const selectActivity = state => state;

export const { changeActivity } = activitySlice.actions;
export default activitySlice.reducer;