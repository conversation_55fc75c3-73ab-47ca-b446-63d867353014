import React from 'react';
import { Typography, Button } from '@mui/material';
import { Line } from 'react-chartjs-2';

const ActivityDetail: React.FC = () => {
  const chartData = {
    labels: ['1', '2', '3', '4', '5'],
    datasets: [
      {
        label: 'Activity Progress',
        data: [10, 30, 50, 70, 90],
        fill: false,
        backgroundColor: '#ff5722',
        borderColor: '#ff5722',
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
  };

  return (
    <div style={{ padding: '16px' }}>
      <header style={{ marginBottom: '24px' }}>
        <Typography variant="h5" align="center">
          Activity Details
        </Typography>
      </header>
      <div style={{ height: '300px', marginBottom: '20px' }}>
        <Line data={chartData} options={chartOptions} />
      </div>
      <Button
        variant="contained"
        color="primary"
        href="/"
        style={{ display: 'block', margin: '0 auto' }}
      >
        Back to Dashboard
      </Button>
    </div>
  );
};

export default ActivityDetail;
