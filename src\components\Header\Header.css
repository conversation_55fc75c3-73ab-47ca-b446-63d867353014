.headerWrapper{
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1;
    width: 100%;
    margin-bottom: 1.6em;
}
.headerContainer{
    display:flex;
    position: relative;    
    justify-content: space-between;
    align-items: center;
    padding:1.6em;
    background-color: #ffffff;
}
.hamburgerWrapper{
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5em;
    /* position: absolute;
    left: 0; */
}
.hamburgerStick{
    height: 5px;
    border-radius: 5px;
    width: 45px;
    background-color: black;
}
.shortStick{
    width: 25px;
}
.notifications{
   position: absolute;
   right: 2em;
}
.palleteIndicator{
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    padding: 0.1em;
    flex-direction: row;
    /* background: #ffffff; */
}
.pallete{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: row;
    gap: 1em;
}