import { CapacitorHttp } from "@capacitor/core";

export const getTestListWithResults = async (accessToken: string, listType: string, docId: string): Promise<any> => {
    console.log("Fetching Joints...");
    console.log("accessToken",accessToken);
    console.log("ListType ",listType);
    console.log("docId ",docId);

    const url = `https://apps.exermetrix.com/april/Dictionary.nsf/getJoints.xsp/getOfficeDefault?listType=${listType}&docId=${docId}`;

    try {
        const response = await CapacitorHttp.get({
            url,
            headers: {
                'Authorization': `Bearer ${accessToken}`
            }
        });

        console.log("Joints response:", response);

        if (response.status !== 200) {
            throw new Error(`Failed to fetch joints. Status: ${response.status}`);
        }

        if (response.data) {
            console.log("Joints Data:", response.data);
            return response.data;
        } else {
            throw new Error("No data returned from the server.");
        }
    } catch (error: any) {
        console.log("GET JOINTS ERROR", error);
        throw new Error(`Error fetching joints: ${error.message || error}`);
    }
};
