
.setupContainer{
    display: flex;
    flex-direction: column;
    padding: 2em;
    padding-top: 8em; 
    height: 100%;
    gap: 1em;
    overflow: auto;
    position: relative;
}
.dragElement{
    display: flex;
    padding: 0.4em;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-direction: row;
    background: #ffffff;
    border-radius: 10px;
    padding:1em;
    width: 90%;
    user-select: none;
}
.roomName{
    font-size: 23px;
    color: #607D8B;
    font-weight: 600;
}
.indicatorBar{
    display: flex;
    gap: 0.7em;
    align-items: center;
}
.warningWrapper{
    display: flex;
    flex-direction: column;
    gap: 0.5em;
    border-radius: 10px;
    background: #607D8B;
    padding: 0.7em;
    /* border : 1px solid #607D8B */
}

