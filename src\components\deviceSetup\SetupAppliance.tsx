//@ts-ignore
// @ts-nocheck
import React, { useState } from "react";
import "./deviceSetup.css";
// import { BLE } from "@ionic-native/ble";
import { IonContent, IonInput, IonButton } from "@ionic/react";
import { useSelector, useDispatch } from 'react-redux';
import { getAllRooms, getHome, addRoom, setRooms, mobileInfo, setMoods, setMobile, updateApplianceById, updateAppliance } from "../../redux/slices/home/<USER>";
import firebase from "../../firebase";
import { NativeStorage } from "@ionic-native/native-storage";


export default function SetupAppliance (props) {
  // constructor(props) {
  //   super(props);
  //   this.state = {
  //     appliance_list: null,
  //   };
  //   this.addApp = this.addApp.bind(this);
  // }
  let rooms = useSelector(getAllRooms);
    const home = useSelector(getHome);
    const mobile = useSelector(mobileInfo);
    const dispatch = useDispatch();
    const [appliance_list,setappliance]=useState(null);

  function addApp() {
    let dummyDevice = {
      deviceName: props.payload.deviceName,
      deviceId: props.payload.mac, //# replace this with this.props.payload.mac
      isAuthorized: "Yes",
      credUrl: "firestoreUrl",
      deviceConfig: {
        connectedAppliances: appliance_list,
      },
    };
    alert(JSON.stringify(dummyDevice)); //#shivanshu this is the output of whole deviceSetup
    let ref = firebase
      .database()
      .ref("users/ZswBJzbQwDV4Cy8zCFF7bR8CbTC3/relationship");
    ref.once("value", (snapshot) => {
      let relationship = !!snapshot.val() ? snapshot.val() : [];
      relationship.push(dummyDevice);
      ref.set(relationship).then(
        (success) => initializeStorage(),
        (err) => {
          alert("error in adding device online");
        }
      );
    });
  }
  const initializeStorage = () => {

    
  
    if (mobile.internet === true) {
      let ref = firebase
        .database()
        .ref("users/ZswBJzbQwDV4Cy8zCFF7bR8CbTC3/relationship");
      ref.once("value", (snapshot) => {
        let relationship = !!snapshot.val() ? snapshot.val() : [];
        let room = [];
        relationship.map((device, dindex) => {
          if (device.deviceName === "SMARTSWITCH") {
            let mac = device.deviceId;
            device.deviceConfig.connectedAppliances.map((appliance, aindex) => {
              let exist = 0;
              let dummy_appliance=null;
              if(appliance.hasOwnProperty("dimmable")===true){
              dummy_appliance = {
                name: appliance.name,
                mac: mac,
                relay: appliance.id,
                val: false, //#doubt
                dimVal: appliance.dimmable,
                dynIp: "", //
                online: [dindex, aindex],
                routine: [],
              };
            }else{
              dummy_appliance = {
                name: appliance.name,
                mac: mac,
                relay: appliance.id,
                val: false, //#doubt
                dynIp: "", //
                online: [dindex, aindex],
                routine: [],
              };
            };
              let dummy_room = {
                roomName: appliance.roomName,
                appliance: [],
              };
              room.map((sroom, rindex) => {
                if (sroom.roomName === appliance.roomName) {
                  exist = 1;
                  room[rindex].appliance.push(dummy_appliance);
                }
              });
              if (exist === 0) {
                let new_room = dummy_room;
                new_room.appliance.push(dummy_appliance);
                room.push(new_room);
              }
            });
          }
        });
        /// adding moods (this will be saved in offline storage also)
        let moods = [];
        let ref1 = firebase
          .database()
          .ref("users/ZswBJzbQwDV4Cy8zCFF7bR8CbTC3/moods");
        ref1.once("value", (snapshot) => {
          moods = !!snapshot.val() ? snapshot.val() : [];
  
          NativeStorage.setItem("rooms", room).then(
            (success) => {
              dispatch(setRooms(room)); //
  
              
            },
            (err) => {
              alert("error in native storage of intializeStorage");
            }
          );
          NativeStorage.setItem("moods", moods).then(
            (success) => {
              dispatch(setMoods(moods));
            },
            (err) => {
              alert("error in native storage of intializeStorage");
            }
          );
        });
      });
    } else {
      NativeStorage.getItem("rooms")
        .then(
          (rooms) => {
            dispatch(setRooms(rooms));
            alert("room set");
          },
          (err) => {
            if (err.code === 2) {
              // this will run the very first time the app is run
              NativeStorage.setItem("rooms", []).catch((err) => alert(err)); //cannot push if not declared as array
              alert("set for first");
            }
          }
        )
    }
  };
  
    
    let appliance_list1 = props.payload.appliance_list;
    return (
      <>
        <h1>ADD DEVICES</h1>
        {JSON.stringify(rooms)}
        
          {!!appliance_list1
            ? appliance_list1.map((appliance, aindex) => {
                //   {
                //     
                //   }
                return (
                  <>
                    <h4>
                      Appliance <strong>{aindex + 1}</strong>
                    </h4>
                    <IonInput
                      key={aindex}
                      value={appliance.name}
                      placeholder={`enter appliance ${aindex + 1} name`}
                      type="text"
                      clearInput={true}
                      onIonChange={(e) => {
                        
                        
                        // let appl = that.state.appliance_list;
                        appliance.name = e.detail.value;
                        // that.setState({
                        //   ...that.state,
                        //   appliance_list: appliance_list,
                        // });
                        setappliance(appliance_list1);
                      }}
                    ></IonInput>
                    <IonInput
                      key={aindex}
                      value={appliance.roomName}
                      placeholder={`enter appliance ${aindex + 1} room`}
                      type="text"
                      clearInput={true}
                      onIonChange={(e) => {
                        // let appliance_list = that.state.appliance_list;
                        appliance.roomName = e.detail.value;
                        // that.setState({
                        //   ...that.state,
                        //   appliance_list: appliance_list,
                        // });
                        setappliance(appliance_list1)
                      }}
                    ></IonInput>
                  </>
                );
              })
            : null}

          <IonButton
            onClick={() => {
              addApp();
            }}
          >
            SUBMIT
          </IonButton>
        
      </>
    );
  }



