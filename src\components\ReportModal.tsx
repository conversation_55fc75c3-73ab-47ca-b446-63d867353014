// @ts-nocheck
import React, { useState, useEffect, useCallback, useRef } from 'react';
import {
  Modal, Box, Typography, IconButton, FormControl,
  InputLabel, Select, MenuItem, Grid, Tooltip,
  Table, TableHead, TableRow, TableCell, TableBody,
  TableContainer, Paper, Pagination, Stack
} from '@mui/material';
import {
  Close as CloseIcon,
  Restore,
  ZoomIn,
  ZoomOut,
  SaveAlt
} from '@mui/icons-material';
import { Chart } from 'react-google-charts';
import { saveAs } from 'file-saver';
import { useSelector } from 'react-redux';
import { selectUser } from '../redux/slices/userSlice';
import { getTestListForPatients } from '../api/getTestListForPatient';
import { getReportsForPatient } from '../api/getReportsForPatient';
import { v4 as uuidv4 } from 'uuid';

interface TestData {
  id: string;
  metricKey: string;
  metricLabel: string;
  side: string;
  time: string;
  value: number;
  units: string;
  color: string;
}

interface TestType {
  id: string;
  name: string;
}

interface ReportModalProps {
  open: boolean;
  onClose: () => void;
  patientName: string;
  patientDocId: string;
  patientId: string;
}

const rowsPerPage = 5;

const ReportModal: React.FC<ReportModalProps> = ({
  open, onClose, patientName, patientDocId, patientId
}) => {
  const user = useSelector(selectUser).value;
  const modalRef = useRef<HTMLDivElement>(null);

  const [testTypes, setTestTypes]           = useState<TestType[]>([]);
  const [selectedTest, setSelectedTest]     = useState<string>('');
  const [rawReport, setRawReport]           = useState<any>(null);
  const [flatData, setFlatData]             = useState<TestData[]>([]);
  const [availableTimes, setAvailableTimes] = useState<string[]>([]);
  const [filterTime, setFilterTime]         = useState<string>('');
  const [currentPage, setCurrentPage]       = useState(1);
  const [chartOptions, setChartOptions]     = useState<any>({
    legend: 'none',
    animation: { duration: 500, easing: 'out' }
  });

  // Fetch test list on open
  useEffect(() => {
    if (!open) return;
    getTestListForPatients(user.accessToken, patientDocId)
      .then(resp => {
        const raw = resp.item?.testTypes || [];
        const types: TestType[] = raw.flatMap((t: any) => {
          if (t.sideTypes === 'N/A') {
            return [{ id: String(t.testTypeId), name: t.fullTestTypeName }];
          }
          return t.sideTypes.split('!').map((side: string) => ({
            id: String(t.testTypeId),
            name: `${t.fullTestTypeName} – ${side}`
          }));
        });
        setTestTypes(types);
        if (types.length) {
          const first = types[0].id;
          setSelectedTest(first);
          handleTestSelect(first);
        }
      })
      .catch(console.error);
  }, [open, user.accessToken, patientDocId]);

  // Fetch report when selectedTest changes
  const handleTestSelect = (testId: string) => {
    setSelectedTest(testId);
    setRawReport(null);
    setFlatData([]);
    setFilterTime('');
    setCurrentPage(1);

    getReportsForPatient(user.accessToken, patientDocId, testId)
      .then(resp => {
        setRawReport(resp.data ?? resp);
      })
      .catch(console.error);
  };

  // Parse rawReport to flatData + availableTimes
  useEffect(() => {
    if (!rawReport?.reportData) return;

    const temp: TestData[] = [];
    const timesSet = new Set<string>();

    Object.entries(rawReport.reportData).forEach(([key, metric]: any) => {
      const { label, units, color } = metric;
      Object.keys(metric)
        .filter(k => !['label','units','color'].includes(k))
        .forEach(side => {
          const bucket = metric[side];
          bucket.labels.forEach((ts: string, i: number) => {
            temp.push({
              id: uuidv4(),
              metricKey: key,
              metricLabel: label,
              side,
              time: ts,
              value: bucket.data[i],
              units,
              color
            });
            timesSet.add(ts);
          });
        });
    });

    // Sort ascending by time
    temp.sort((a, b) => new Date(a.time).getTime() - new Date(b.time).getTime());
    const timesArr = Array.from(timesSet).sort((a,b) => new Date(a).getTime() - new Date(b).getTime());

    setFlatData(temp);
    setAvailableTimes(timesArr);
    setCurrentPage(1);
  }, [rawReport]);

  // Outside click to close
  const onOutsideClick = useCallback((e: MouseEvent) => {
    const tgt = e.target as Node;
    if (
      modalRef.current &&
      !modalRef.current.contains(tgt) &&
      !document.querySelector('.MuiPopover-root')?.contains(tgt)
    ) {
      onClose();
    }
  }, [onClose]);
  useEffect(() => {
    if (open) document.addEventListener('mousedown', onOutsideClick);
    else document.removeEventListener('mousedown', onOutsideClick);
    return () => document.removeEventListener('mousedown', onOutsideClick);
  }, [open, onOutsideClick]);

  // Filter & paginate
  const displayed = flatData.filter(d => !filterTime || d.time === filterTime);
  const pageCount = Math.ceil(displayed.length / rowsPerPage);
  const pageData  = displayed.slice((currentPage-1)*rowsPerPage, currentPage*rowsPerPage);

  // Chart controls
  const zoom = (delta: number) => {
    setChartOptions(opts => {
      const h = opts.hAxis?.viewWindow || {};
      const v = opts.vAxis?.viewWindow || {};
      return {
        ...opts,
        hAxis: {
          viewWindow: {
            min: (h.min ?? 0) + delta,
            max: (h.max ?? displayed.length) - delta
          }
        },
        vAxis: {
          viewWindow: {
            min: (v.min ?? 0) + delta,
            max: (v.max ?? Math.max(...displayed.map(d => d.value))) - delta
          }
        }
      };
    });
  };

  const resetAxes = () => {
    setChartOptions({ legend: 'none', animation: { duration: 500, easing: 'out' } });
  };

  const takeSnapshot = () => {
    const svg = modalRef.current?.querySelector('svg');
    if (!svg) return;
    svg.setAttribute('xmlns','http://www.w3.org/2000/svg');
    const serialized = new XMLSerializer().serializeToString(svg);
    saveAs(`report-${selectedTest}.png`, `data:image/svg+xml;utf8,${encodeURIComponent(serialized)}`);
  };

  return (
    <Modal open={open} onClose={onClose}>
      <Box
        ref={modalRef}
        sx={{
          position: 'absolute', top: 0, left: 0,
          width: '100%', height: '100%',
          bgcolor: 'background.paper', p: 4,
          overflowY: 'auto'
        }}
      >
        <Stack direction="row" justifyContent="space-between" mb={2}>
          <Typography variant="h6">
            Report: {patientName} {rawReport?.ReportName ? `— ${rawReport.ReportName}` : ''}
          </Typography>
          <IconButton onClick={onClose}><CloseIcon/></IconButton>
        </Stack>

        <Grid container spacing={2} alignItems="center" mb={3}>
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <InputLabel>Test Type</InputLabel>
              <Select
                value={selectedTest}
                label="Test Type"
                onChange={e => handleTestSelect(String(e.target.value))}
              >
                {testTypes.map(t => (
                  <MenuItem key={t.id} value={t.id}>{t.name}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={4}>
            <FormControl fullWidth>
              <InputLabel>Filter Timestamp</InputLabel>
              <Select
                value={filterTime}
                label="Filter Timestamp"
                onChange={e => { setFilterTime(e.target.value); setCurrentPage(1); }}
              >
                <MenuItem value=''>All</MenuItem>
                {availableTimes.map(ts => (
                  <MenuItem key={ts} value={ts}>{ts}</MenuItem>
                ))}
              </Select>
            </FormControl>
          </Grid>
          <Grid item xs={12} sm={4} sx={{ textAlign: 'right' }}>
            <Tooltip title="Reset Axes"><IconButton onClick={resetAxes}><Restore/></IconButton></Tooltip>
            <Tooltip title="Zoom In"><IconButton onClick={() => zoom(-5)}><ZoomIn/></IconButton></Tooltip>
            <Tooltip title="Zoom Out"><IconButton onClick={() => zoom(5)}><ZoomOut/></IconButton></Tooltip>
            <Tooltip title="Snapshot"><IconButton onClick={takeSnapshot}><SaveAlt/></IconButton></Tooltip>
          </Grid>
        </Grid>

        {/* Charts */}
        {rawReport?.reportData && Object.entries(rawReport.reportData).map(([key, metric]: any) => {
          // Special combined Duration chart
          if (key === 'duration') {
            const left = metric.Left || { labels: [], data: [] };
            const right = metric.Right || { labels: [], data: [] };
            const allTimes = Array.from(new Set([...left.labels, ...right.labels]))
              .sort((a,b) => new Date(a).getTime() - new Date(b).getTime());

            const dataArr = [['Time', `${metric.label} – Left`, `${metric.label} – Right`]];
            allTimes.forEach(ts => {
              const li = left.labels.indexOf(ts);
              const ri = right.labels.indexOf(ts);
              dataArr.push([
                ts,
                li >= 0 ? left.data[li] : null,
                ri >= 0 ? right.data[ri] : null
              ]);
            });

            return (
              <Box key={key} mb={4}>
                <Typography variant="subtitle1" gutterBottom>
                  {metric.label} (Left vs Right) — {metric.units}
                </Typography>
                <Chart
                  chartType="LineChart"
                  width="100%" height="300px"
                  data={dataArr}
                  options={{
                    ...chartOptions,
                    colors: ['blue','red'],
                    hAxis: { title: 'Time' },
                    vAxis: { title: metric.units }
                  }}
                />
              </Box>
            );
          }

          // All other metrics: separate charts per side
          return ['Left','Right','N/A'].map(side => {
            const bucket = metric[side];
            if (!bucket) return null;
            const cData = [['Time', metric.label]];
            bucket.labels.forEach((ts: string, i: number) => {
              if (!filterTime || ts === filterTime) {
                cData.push([ts, bucket.data[i]]);
              }
            });
            return (
              <Box key={`${key}-${side}`} mb={4}>
                <Typography variant="subtitle1" gutterBottom>
                  {metric.label} – {side} ({metric.units})
                </Typography>
                <Chart
                  chartType="LineChart"
                  width="100%" height="300px"
                  data={cData}
                  options={{
                    ...chartOptions,
                    colors: [metric.color],
                    hAxis: { title: 'Time' },
                    vAxis: { title: metric.units }
                  }}
                />
              </Box>
            );
          });
        })}

        {/* Data Table */}
        <TableContainer component={Paper} sx={{ mt: 4 }}>
          <Table size="small">
            <TableHead>
              <TableRow>
                <TableCell>Metric</TableCell>
                <TableCell>Side</TableCell>
                <TableCell>Time</TableCell>
                <TableCell align="right">Value</TableCell>
                <TableCell>Notes</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {pageData.map(row => (
                <TableRow key={row.id}>
                  <TableCell>{row.metricLabel}</TableCell>
                  <TableCell>{row.side}</TableCell>
                  <TableCell>{row.time}</TableCell>
                  <TableCell align="right">{row.value}</TableCell>
                  <TableCell>
                    units: {row.units}, color: <span style={{color:row.color}}>{row.color}</span>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          <Box display="flex" justifyContent="center" p={2}>
            <Pagination
              count={pageCount}
              page={currentPage}
              onChange={(_,p) => setCurrentPage(p)}
            />
          </Box>
        </TableContainer>
      </Box>
    </Modal>
  );
};

export default ReportModal;
