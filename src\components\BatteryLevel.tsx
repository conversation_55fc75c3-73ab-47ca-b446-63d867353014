import React from 'react';
import { LinearProgress, Typography } from '@mui/material';

interface BatteryLevelProps {
  batteryPercentage: number; // Battery percentage value (0-100)
}

const BatteryLevel: React.FC<BatteryLevelProps> = ({ batteryPercentage }) => {
  // Determine the color of the progress bar based on battery percentage
  const getBatteryColor = (percentage: number) => {
    if (percentage > 75) return '#4caf50'; // Green
    if (percentage > 40) return '#ffeb3b'; // Yellow
    return '#f44336'; // Red
  };

  return (
    <div style={{ padding: '16px' }}>
      <Typography variant="h6">Device Battery Level</Typography>
      <LinearProgress
        variant="determinate"
        value={batteryPercentage}
        style={{
          height: '10px',
          borderRadius: '5px',
          backgroundColor: '#e0e0e0',
        }}
        sx={{
          '& .MuiLinearProgress-bar': {
            backgroundColor: getBatteryColor(batteryPercentage),
          },
        }}
      />
      <Typography variant="body2" style={{ marginTop: '8px' }}>
        {batteryPercentage}% Remaining
      </Typography>
    </div>
  );
};

export default BatteryLevel;
