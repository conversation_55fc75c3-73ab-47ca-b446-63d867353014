import { CapacitorHttp } from "@capacitor/core";

export const getReportsForPatient = async (
  accessToken: string,
  patientDocId: string,
  testId: string
): Promise<any> => {
  console.log("Fetching reports for patient...");

  try {
    const url = `https://apps.exermetrix.com/april/Dictionary.nsf/getReports.xsp/getReportsNewApp?patientDocId=${encodeURIComponent(
      patientDocId
    )}&TestId=${encodeURIComponent(testId)}`;

    const response: any = await CapacitorHttp.get({
      url,
      headers: {
        Authorization: `Bearer ${accessToken}`,
        // Cookie: 'SessionID=27A478074D6C36CFE0E53A3417C4AF8798038DE7', // include only if session required
      },
    });

    console.log("Report data response:", response);

    if (response.error) throw new Error(response.data?.ErrorMessage || "Unknown error");

    return response.data;
  } catch (error: any) {
    console.error("GET REPORTS ERROR", error);
    throw new Error(`Report fetch failed: ${error}`);
  }
};
