.profileContainer{
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
    flex:1;
    padding: 1em;
    gap:1em;
    padding-top: 1.6em;
    margin-top: auto;
}
.profileImage{
    display: flex;
    justify-content: center;
    align-items: center;
   flex-direction: column;
   gap: 1em;
}
.headlines{
    color: #cccccc;
    text-align: justify;
    padding: 1em;
    max-width: 70%;
}
.menuList{
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-direction: column;
    gap: 1.6em;
    flex:1;
    /* margin-top: auto; */
    padding: 2em;
}
.menuItem{
    padding: 0.2;
    font-size: 22px;
    display: flex;
    justify-content: flex-start !important;
    align-items: center;
    flex-direction: row;
    gap: 1em;
    width: 100%;
    /* border-bottom: 1px solid rgba(0,0,0,0.1); */
    padding: 0.3em;
}
.menuItemText{
    font-size: 22px;
    margin-left:1em;
}
.menuContent{
    display: flex;
    flex-direction: column;
    width: 100%;
    flex: 1;
}