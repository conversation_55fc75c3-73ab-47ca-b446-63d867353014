// @ts-nocheck
import React from 'react';
import Home from '../../pages/Home';
import { Route, Switch, useLocation } from 'react-router-dom';
// import InsideRoom from './components/insideRoom/insideRoom';
import BleShow from '../deviceSetup/BleShow';
import { AppLayout } from '../../App';
import Login from '../login/login';
import PatientResults from '../../pages/PatientResults';
import ProfilePage from '../../pages/ProfilePage';
import PatientPage from '../../pages/PatientPage';
import TestScreen from '../../pages/TestScreen';
import MetaWearSensorComponent from '../deviceSetup/bleNDOF';


const HBM = (props: any) => {

    const location = useLocation();



    return <>

        <Switch location={location}>

          <Route path="/" component={() => AppLayout(<Home/>,false)} exact={true} />
          <Route path="/devicesetup" component={() => AppLayout(<BleShow />,true)} exact={true} />
          <Route path="/scanner" component={() => AppLayout(<MetaWearSensorComponent />,true)} exact={true} />
          <Route path="/patientresults" component={() => AppLayout(<PatientResults />,true,true)} exact={true} />
          <Route path="/settings" component={() => AppLayout(<ProfilePage />,true,true)} exact={true} />
          <Route path="/patient/:category" component={() => AppLayout(<PatientPage />,true,true)} exact={true} />
          <Route path="/test/:patientId?" component={() => AppLayout(<TestScreen />,true,true)} exact={true} />
          <Route path="/login" component={() => AppLayout(<Login />,true)} exact={true} />
          {/* <Route path="/applianceSetup" component={() => AppLayout(<ApplianceSetup />,true)} exact={true} />
          <Route path="/control/:uid" component={() => AppLayout(<Control />,true)} exact={true} />
          <Route path="/routines" component={() => AppLayout(<Routines />,true)} exact={true} />
          <Route path="/testroom" component={() => AppLayout(<TestRoom />,true)} exact={true} />
          <Route path="/insideroom/:uid" component={()=>AppLayout(<InsideRoom/>,true)} exact={true}/>
          <Route path="/remotecontrol/:rid/:uid" component={()=>AppLayout(<RemoteControl rcode={rcode} setrcode={setrcode} setflaguse={setflaguse} />,true)} exact={true}/>
          <Route path="/controlremote/:rid/:uid" component={()=>(<ControlRemote rcode={rcode} setrcode={setrcode} setflaguse={setflaguse} />)} exact={true}/> */}
        </Switch>
    </>
}

export default HBM;



