{"logs": [{"outputFile": "com.wellmetrix.wellmetrixprovider.app-mergeDebugResources-24:/values-v16/values-v16.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.9/transforms/21e8fde1c723f1784c968ebe032c8280/transformed/core-1.12.0/res/values-v16/values-v16.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}}, {"source": "/Users/<USER>/.gradle/caches/8.9/transforms/3c980233d99f79c1041090d50e04c5a7/transformed/appcompat-1.6.1/res/values-v16/values-v16.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "223"}, "to": {"startLines": "3", "startColumns": "4", "startOffsets": "121", "endLines": "6", "endColumns": "12", "endOffsets": "289"}}]}]}