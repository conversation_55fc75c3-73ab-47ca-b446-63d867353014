import React, { useEffect, useState } from "react";
import { Box, Typography, CircularProgress } from "@mui/material";
import { useSpring, animated } from "react-spring";
import { CapacitorHttp } from '@capacitor/core';

type QuoteType =  {
  text: string;
  credit: string;
};

interface RandomQuoteProps {
  quote?: QuoteType;
}

const RandomQuote: React.FC<RandomQuoteProps> = ({ quote }) => {
  // API URL for ZenQuotes
  const API_URL = "https://zenquotes.io/api/random";

  // Fallback wellness quote if the API fails
  const FALLBACK_QUOTE: any = quote?.text || "It is health that is real wealth and not pieces of gold and silver.";
  const FALLBACK_AUTHOR: any = quote?.credit || "Mahat<PERSON> Gandhi";

  const [quoteText, setQuoteText] = useState<string>(FALLBACK_QUOTE);
  const [author, setAuthor] = useState<string>(FALLBACK_AUTHOR);
  const [loading, setLoading] = useState<boolean>(true);

  // Spring animation for fading the quote in
  const fadeInProps = useSpring({
    opacity: loading ? 0 : 1,
    transform: loading ? "translateY(10px)" : "translateY(0px)",
    config: { tension: 180, friction: 20 },
  });

  const fetchQuote = async () => {
    setLoading(true);
    try {
      const response = await CapacitorHttp.get({ url: API_URL });
      const data = response.data[0]; // ZenQuotes returns an array
      setQuoteText(data.q); // Wellness quote
      setAuthor(data.a); // Quote author
    } catch (error) {
      console.error("Error fetching quote:", error);
      // If API fails, fallback to the default quote
      setQuoteText(FALLBACK_QUOTE);
      setAuthor(FALLBACK_AUTHOR);
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchQuote(); // Fetch initial quote on load
  }, []);

  return (
    <Box
      sx={{
        maxWidth: 600,
        margin: "auto",
        textAlign: "center",
        padding: 3,
        borderRadius: 2,
        bgcolor: "background.paper",
      }}
    >
      <animated.div style={fadeInProps}>
        <Typography variant="h6" fontStyle="italic" color="text.secondary">
          "{quoteText}"
        </Typography>
        <Typography variant="body2" color="text.primary" mt={2}>
          - {author}
        </Typography>
      </animated.div>

      {loading && (
        <Box mt={2}>
          <CircularProgress />
        </Box>
      )}
    </Box>
  );
};

export default RandomQuote;
