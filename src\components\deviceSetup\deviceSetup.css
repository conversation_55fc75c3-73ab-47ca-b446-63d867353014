.deviceSetupContainer{
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
    flex-direction: column;
}
.ringContainer{
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    width: 100%;
    flex-direction: column;
    gap: 1em;
    position: relative;
}
.outerRing{
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    
}
.ring3{
    display: flex;
    justify-content: center;
    align-items: center;
    height: 150px;
    width: 150px;
    border-radius: 50%;
    background: var(--primary-main);
    opacity: 0.23;
    padding: 1em;
    position: absolute;
    top:50%;
    left:50%;
    transform: scale(1) translate(-50%,-50%);
    transform-origin: left top;
}
.ring2{
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100px;
    width: 100px;
    border-radius: 50%;
    background: var(--primary-main);
    opacity: 0.23;
    padding: 1em;
    position: absolute;
    top:50%;
    left:50%;
    transform: scale(1) translate(-50%,-50%);
    transform-origin: left top;

}
.ring1{
    display: flex;
    justify-content: center;
    align-items: center;
    height: 50px;
    width: 50px;
    border-radius: 50%;
    background: var(--primary-main);
    padding: 1em;
    position: absolute;
    top:50%;
    left:50%;
    transform: translate(-50%,-50%);

}
.desc{
    display: flex;
    flex-direction: column;
    /* max-width: 45%; */
    /* width: 100%; */
    padding: 1em;
    margin: 1em;
    gap:0.5em;
}
.getStartedBtn{
    display: flex;
    width: 100%;
    justify-content: center;
    align-items: center;
}
.showDevices{
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 1em;
    padding: 1em;
}
.device{
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 1em;
    border-radius: 10px;
    background: #607D8B;
    /* border : 1px solid #2A9D8F; */
    min-width: 85%;
}
.fallbackText{
    margin: 1em;
}