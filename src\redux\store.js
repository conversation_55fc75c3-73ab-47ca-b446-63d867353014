import { combineReducers, configureStore } from '@reduxjs/toolkit'
import {
    persistStore,
    persistReducer,
    FLUSH,
    REHYDRATE,
    PAUSE,
    PERSIST,
    PURGE,
    REGISTER,
} from 'redux-persist'
// import storage from 'redux-persist/lib/storage'
// Logger with default options
import logger from 'redux-logger'
import UserReducer from './slices/userSlice';
import ActivityReducer from './slices/ActivitySlice';
import HomeReducer from './slices/home/<USER>';
import DeviceReducer from './slices/deviceSlice';
import AsyncStorage from '@react-native-async-storage/async-storage';
const persistConfig = {
    key: 'root',
    version: 1,
    storage: AsyncStorage,
    blacklist:[] // how will you blacklist home.dragValue ? 
}
const rootReducer = combineReducers({
    user: UserReducer,
    activity: ActivityReducer,
    devices : DeviceReducer,
    home: HomeReducer
})
const persistedReducer = persistReducer(persistConfig, rootReducer)


// TODO: [HOM-201] Remove Redux Logger in production

export const store = configureStore({
    reducer: persistedReducer,
    middleware: (getDefaultMiddleware) => getDefaultMiddleware({
        serializableCheck: {
            ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
        },
    })
    .concat(logger),
})

export const persistor = persistStore(store)

export default store;