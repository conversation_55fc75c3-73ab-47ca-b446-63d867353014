//@ts-nocheck
import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux'
import _ROOT_ from './components/root/root';
import { createStyles, makeStyles, ThemeProvider } from '@material-ui/core';
import Theme from './theme/theme';
import Login from './components/login/login';
// import { ReactComponent as Logo } from './assets/logo.svg';
import HBM from './components/HBM/HBM';
import { selectUser, login, logout } from './redux/slices/userSlice';
import { FirebaseAuthentication } from '@ionic-native/firebase-authentication';
import Header from './components/Header/Header';
import CssBaseline from '@mui/material/CssBaseline';
import { useLocation } from 'react-router-dom';
// import BleShow from './components/deviceSetup/BleShow';
// import ApplianceSetup from './components/applianceSetup/ApplianceSetup';
// import ExploreContainer from './demoGesture';
// import AssignToRoom from './components/assignToRoom/AssignToRoom';
// import Control from './components/control/Control';
// import { DeviceHubOutlined, HomeOutlined, ScheduleOutlined } from '@material-ui/icons';
// import Profile from './components/Profile/Profile';
import BackHandler from './components/backHandler';
// import AddRoutines from './components/routines/AddRoutines';
// import { useTransition, config } from 'react-spring';
import BottomBar from './components/bottomBar';
import BleShow from './components/deviceSetup/BleShow';
import Home from './pages/Home';
import { config, useTransition } from 'react-spring';
// import useRouter from './useRouter';
// import Routines from './components/routines/Routines';
// import TestRoom from './components/remotes/TestRoom';
// import InsideRoom from './components/insideRoom/insideRoom';

// import ControlRemoteCustom from './components/control/ControlRemoteCustom';
// import AddRemote from './components/control/AddRemote';
// import { selectUser , logout } from "./redux/slices/UserSlice"
const App = () => {
    const dispatch = useDispatch();
    const user = useSelector(selectUser).value
    const useStyles = makeStyles(() =>
        createStyles({
            appBar: {
                top: 'auto',
                bottom: 0,
                background: "#ffffff",
            },
        }),
    );
    // const transitions = useTransition(location, location => location.pathname, {
    //   from: { opacity: 0,  },
    //   enter: { opacity: 1, },
    //   leave: { opacity: 0,  },
    //   config: config.gentle
    // })

    useEffect(() => {
        const root = document.documentElement;

        // Set palette colors as CSS variables
        root.style.setProperty('--primary-main', Theme.palette.primary.main);
        root.style.setProperty('--secondary-main', Theme.palette.secondary.main);

        // Set typography styles
        root.style.setProperty('--font-family', Theme.typography.fontFamily || 'Roboto');
        root.style.setProperty('--text-color', Theme.typography.color || '#ffffff');
    }, []);



    // everything is wrapped in a root component 
    useEffect(() => {

        if (user && user.accessToken) {
            dispatch(login(user));
        } else {
            dispatch(logout(null));
        }

    }, [])


    // let APP =()=> (<HBM />);
    return <>
        {/* {transitions.map(({ item: location, props, key }) => {
      return <><animated.div style={{ height: "100%", width: "100%", ...props, display: "flex" }} key={key}>
        {APP()}
      </animated.div></>
    })} */}
        <HBM />
        {/* <ControlRemoteCustom/> */}
        {/* <AddRemote/> */}
    </>

}

export const AppLayout = (child: any, noHeader?: boolean, backhandler?: boolean) => {
    const user = useSelector(selectUser);

    if (
        user.value
        // true
    ) {
        return <ThemeProvider theme={Theme}>
            <CssBaseline />
            <div style={{ height: "100%", width: "100%", position: "relative", flex: 1, paddingBottom: "8em" }}>
                {noHeader ? backhandler ? <BackHandler /> : "" : <Header />}
                {child}
            </div>
            {/* <BottomBar /> */}
        </ThemeProvider>
    } else {
        return <ThemeProvider theme={Theme}>
            {/* <div style={{
        boxShadow: `0 0px 50px rgba(247, 176, 8, 0.35), 0 0px 6px rgba(247, 176, 8, 0.35)`,
        height: "40px",
        width: "40px",
        position: "fixed",
        top: "30px",
        left: "30px",
        padding: "5px",
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        borderRadius: "50%"
      }}>
         <Logo></Logo>
        </div>
      */}
            <CssBaseline />
            <Login />
        </ThemeProvider>
    }
}
export default App