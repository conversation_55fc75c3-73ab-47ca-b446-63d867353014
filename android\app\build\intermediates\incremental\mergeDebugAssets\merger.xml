<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":capacitor-cordova-android-plugins" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\work\wellmetrixproviderionic\android\capacitor-cordova-android-plugins\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":capacitor-android" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\work\wellmetrixproviderionic\node_modules\@capacitor\android\capacitor\build\intermediates\library_assets\debug\packageDebugAssets\out"><file name="native-bridge.js" path="C:\work\wellmetrixproviderionic\node_modules\@capacitor\android\capacitor\build\intermediates\library_assets\debug\packageDebugAssets\out\native-bridge.js"/></source></dataSet><dataSet config=":capacitor-status-bar" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\work\wellmetrixproviderionic\node_modules\@capacitor\status-bar\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":capacitor-keyboard" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\work\wellmetrixproviderionic\node_modules\@capacitor\keyboard\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":capacitor-haptics" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\work\wellmetrixproviderionic\node_modules\@capacitor\haptics\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":capacitor-app" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\work\wellmetrixproviderionic\node_modules\@capacitor\app\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":capacitor-firebase-authentication" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\work\wellmetrixproviderionic\node_modules\@capacitor-firebase\authentication\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":capacitor-community-http" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\work\wellmetrixproviderionic\node_modules\@capacitor-community\http\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":capacitor-community-bluetooth-le" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\work\wellmetrixproviderionic\node_modules\@capacitor-community\bluetooth-le\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\work\wellmetrixproviderionic\android\app\src\main\assets"><file name="capacitor.config.json" path="C:\work\wellmetrixproviderionic\android\app\src\main\assets\capacitor.config.json"/><file name="capacitor.plugins.json" path="C:\work\wellmetrixproviderionic\android\app\src\main\assets\capacitor.plugins.json"/><file name="public/assets/index-CDRj9eL2.css" path="C:\work\wellmetrixproviderionic\android\app\src\main\assets\public\assets\index-CDRj9eL2.css"/><file name="public/assets/polyfills-legacy-XxLwpc_A.js" path="C:\work\wellmetrixproviderionic\android\app\src\main\assets\public\assets\polyfills-legacy-XxLwpc_A.js"/><file name="public/assets/welcomeDoor-DOOiolLQ.svg" path="C:\work\wellmetrixproviderionic\android\app\src\main\assets\public\assets\welcomeDoor-DOOiolLQ.svg"/><file name="public/cordova.js" path="C:\work\wellmetrixproviderionic\android\app\src\main\assets\public\cordova.js"/><file name="public/cordova_plugins.js" path="C:\work\wellmetrixproviderionic\android\app\src\main\assets\public\cordova_plugins.js"/><file name="public/favicon.png" path="C:\work\wellmetrixproviderionic\android\app\src\main\assets\public\favicon.png"/><file name="public/index.html" path="C:\work\wellmetrixproviderionic\android\app\src\main\assets\public\index.html"/><file name="public/manifest.json" path="C:\work\wellmetrixproviderionic\android\app\src\main\assets\public\manifest.json"/><file name="public/models/Robot.glb" path="C:\work\wellmetrixproviderionic\android\app\src\main\assets\public\models\Robot.glb"/><file name="public/plugins/cordova-plugin-ble-central/www/ble.js" path="C:\work\wellmetrixproviderionic\android\app\src\main\assets\public\plugins\cordova-plugin-ble-central\www\ble.js"/><file name="public/assets/index-Bi2p0M8f.js" path="C:\work\wellmetrixproviderionic\android\app\src\main\assets\public\assets\index-Bi2p0M8f.js"/><file name="public/assets/index-legacy-Dpa1fPz0.js" path="C:\work\wellmetrixproviderionic\android\app\src\main\assets\public\assets\index-legacy-Dpa1fPz0.js"/><file name="public/assets/ios.transition-CgV132MZ.js" path="C:\work\wellmetrixproviderionic\android\app\src\main\assets\public\assets\ios.transition-CgV132MZ.js"/><file name="public/assets/ios.transition-legacy-CweljDtC.js" path="C:\work\wellmetrixproviderionic\android\app\src\main\assets\public\assets\ios.transition-legacy-CweljDtC.js"/><file name="public/assets/md.transition-Jb0L-NQc.js" path="C:\work\wellmetrixproviderionic\android\app\src\main\assets\public\assets\md.transition-Jb0L-NQc.js"/><file name="public/assets/md.transition-legacy-CYGYo6ve.js" path="C:\work\wellmetrixproviderionic\android\app\src\main\assets\public\assets\md.transition-legacy-CYGYo6ve.js"/><file name="public/assets/swipe-back-Bi297RFi.js" path="C:\work\wellmetrixproviderionic\android\app\src\main\assets\public\assets\swipe-back-Bi297RFi.js"/><file name="public/assets/swipe-back-legacy-GCwsdTvO.js" path="C:\work\wellmetrixproviderionic\android\app\src\main\assets\public\assets\swipe-back-legacy-GCwsdTvO.js"/><file name="public/assets/web-legacy-D9yuND8S.js" path="C:\work\wellmetrixproviderionic\android\app\src\main\assets\public\assets\web-legacy-D9yuND8S.js"/><file name="public/assets/web-w7NVNHA6.js" path="C:\work\wellmetrixproviderionic\android\app\src\main\assets\public\assets\web-w7NVNHA6.js"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\work\wellmetrixproviderionic\android\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\work\wellmetrixproviderionic\android\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>