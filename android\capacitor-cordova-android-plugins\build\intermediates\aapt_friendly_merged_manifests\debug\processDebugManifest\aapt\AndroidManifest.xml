<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:amazon="http://schemas.amazon.com/apk/res/android"
    xmlns:android="http://schemas.android.com/apk/res/android"
    package="capacitor.cordova.android.plugins" >

    <uses-sdk android:minSdkVersion="22" />

    <uses-permission
        android:name="android.permission.ACCESS_COARSE_LOCATION"
        android:maxSdkVersion="28" />
    <uses-permission
        android:name="android.permission.ACCESS_FINE_LOCATION"
        android:maxSdkVersion="30" />
    <uses-permission
        android:name="android.permission.BLUETOOTH"
        android:maxSdkVersion="30" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_ADMIN"
        android:maxSdkVersion="30" />
    <uses-permission
        android:name="android.permission.BLUETOOTH_SCAN"
        android:usesPermissionFlags="neverForLocation" />
    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />

    <application>
    </application>

</manifest>