1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.wellmetrix.wellmetrixprovider"
4    android:versionCode="6"
5    android:versionName="1.4" >
6
7    <uses-sdk
8        android:minSdkVersion="22"
9        android:targetSdkVersion="34" />
10
11    <!-- Permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:41:5-67
12-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:41:22-64
13
14    <!-- Location Permissions for BLE (Android 11 and below) -->
15    <uses-permission
15-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:44:5-47:49
16        android:name="android.permission.ACCESS_COARSE_LOCATION"
16-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:45:9-65
17        android:maxSdkVersion="30" />
17-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:46:9-35
18    <uses-permission
18-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:48:5-50:38
19        android:name="android.permission.ACCESS_FINE_LOCATION"
19-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:49:9-63
20        android:maxSdkVersion="30" />
20-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:50:9-35
21
22    <!-- Basic Bluetooth Permissions -->
23    <uses-permission
23-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:53:5-68
24        android:name="android.permission.BLUETOOTH"
24-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:53:22-65
25        android:maxSdkVersion="30" />
25-->[:capacitor-community-bluetooth-le] C:\work\wellmetrixproviderionic\node_modules\@capacitor-community\bluetooth-le\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:9-35
26    <uses-permission
26-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:54:5-74
27        android:name="android.permission.BLUETOOTH_ADMIN"
27-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:54:22-71
28        android:maxSdkVersion="30" />
28-->[:capacitor-community-bluetooth-le] C:\work\wellmetrixproviderionic\node_modules\@capacitor-community\bluetooth-le\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:9-35
29
30    <!-- BLE Permissions for Android 12+ -->
31    <uses-permission
31-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:57:5-60:32
32        android:name="android.permission.BLUETOOTH_SCAN"
32-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:58:9-57
33        android:usesPermissionFlags="neverForLocation" />
33-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:59:9-55
34    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
34-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:62:5-64:32
34-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:63:9-60
35    <uses-permission android:name="android.permission.BLUETOOTH_ADVERTISE" />
35-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:66:5-68:32
35-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:67:9-62
36
37    <!-- Hardware Features -->
38    <uses-feature
38-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:71:5-90
39        android:name="android.hardware.bluetooth_le"
39-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:71:19-63
40        android:required="true" />
40-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:71:64-87
41    <uses-feature
41-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:72:5-87
42        android:name="android.hardware.bluetooth"
42-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:72:19-60
43        android:required="true" />
43-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:72:61-84
44
45    <!-- Required for Bluetooth device discovery on Android 11+ -->
46    <queries>
46-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:75:5-82:15
47        <intent>
47-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:76:9-78:18
48            <action android:name="android.bluetooth.device.action.FOUND" />
48-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:77:13-75
48-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:77:21-73
49        </intent>
50        <!-- Add Bluetooth LE permissions -->
51        <package android:name="android.bluetooth" />
51-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:80:9-53
51-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:80:18-50
52        <package android:name="android.bluetooth.le" />
52-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:81:9-56
52-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:81:18-53
53    </queries>
54
55    <uses-permission android:name="android.permission.VIBRATE" />
55-->[:capacitor-haptics] C:\work\wellmetrixproviderionic\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-66
55-->[:capacitor-haptics] C:\work\wellmetrixproviderionic\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-63
56    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
56-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:26:5-79
56-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:26:22-76
57    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
57-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd5197332a73a4f6df8094d24c7db750\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:5-98
57-->[com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd5197332a73a4f6df8094d24c7db750\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:22-95
58
59    <permission
59-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\99505e3f6474bdf8a578561614447e5e\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
60        android:name="com.wellmetrix.wellmetrixprovider.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
60-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\99505e3f6474bdf8a578561614447e5e\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
61        android:protectionLevel="signature" />
61-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\99505e3f6474bdf8a578561614447e5e\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
62
63    <uses-permission android:name="com.wellmetrix.wellmetrixprovider.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
63-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\99505e3f6474bdf8a578561614447e5e\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
63-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\99505e3f6474bdf8a578561614447e5e\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
64
65    <application
65-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:6:5-38:19
66        android:allowBackup="true"
66-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:7:9-35
67        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
67-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\99505e3f6474bdf8a578561614447e5e\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
68        android:extractNativeLibs="true"
69        android:icon="@mipmap/ic_launcher"
69-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:8:9-43
70        android:label="@string/app_name"
70-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:9:9-41
71        android:roundIcon="@mipmap/ic_launcher_round"
71-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:10:9-54
72        android:supportsRtl="true"
72-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:11:9-35
73        android:theme="@style/AppTheme"
73-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:12:9-40
74        android:usesCleartextTraffic="true" >
74-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:13:9-44
75        <activity
75-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:15:9-27:20
76            android:name="com.wellmetrix.wellmetrixprovider.MainActivity"
76-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:17:13-41
77            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode"
77-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:16:13-129
78            android:exported="true"
78-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:21:13-36
79            android:label="@string/title_activity_main"
79-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:18:13-56
80            android:launchMode="singleTask"
80-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:20:13-44
81            android:theme="@style/AppTheme.NoActionBarLaunch" >
81-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:19:13-62
82            <intent-filter>
82-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:23:13-26:29
83                <action android:name="android.intent.action.MAIN" />
83-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:24:17-69
83-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:24:25-66
84
85                <category android:name="android.intent.category.LAUNCHER" />
85-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:25:17-77
85-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:25:27-74
86            </intent-filter>
87        </activity>
88
89        <provider
90            android:name="androidx.core.content.FileProvider"
90-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:30:13-62
91            android:authorities="com.wellmetrix.wellmetrixprovider.fileprovider"
91-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:31:13-64
92            android:exported="false"
92-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:32:13-37
93            android:grantUriPermissions="true" >
93-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:33:13-47
94            <meta-data
94-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:34:13-36:54
95                android:name="androidx.core.content.FileProvider"
95-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:35:17-66
96                android:resource="@xml/file_paths" />
96-->C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:36:17-51
97        </provider>
98        <provider
98-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc4e29fa4ee28d48c5b619f978911597\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
99            android:name="androidx.startup.InitializationProvider"
99-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc4e29fa4ee28d48c5b619f978911597\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
100            android:authorities="com.wellmetrix.wellmetrixprovider.androidx-startup"
100-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc4e29fa4ee28d48c5b619f978911597\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
101            android:exported="false" >
101-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc4e29fa4ee28d48c5b619f978911597\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
102            <meta-data
102-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc4e29fa4ee28d48c5b619f978911597\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
103                android:name="androidx.emoji2.text.EmojiCompatInitializer"
103-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc4e29fa4ee28d48c5b619f978911597\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
104                android:value="androidx.startup" />
104-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc4e29fa4ee28d48c5b619f978911597\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
105            <meta-data
105-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4b574b42109befa3412f700e5ce3a6b\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
106                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
106-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4b574b42109befa3412f700e5ce3a6b\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
107                android:value="androidx.startup" />
107-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4b574b42109befa3412f700e5ce3a6b\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
108            <meta-data
108-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
109                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
109-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
110                android:value="androidx.startup" />
110-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
111        </provider>
112
113        <activity
113-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
114            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
114-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
115            android:excludeFromRecents="true"
115-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
116            android:exported="true"
116-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
117            android:launchMode="singleTask"
117-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
118            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
118-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
119            <intent-filter>
119-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
120                <action android:name="android.intent.action.VIEW" />
120-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
120-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
121
122                <category android:name="android.intent.category.DEFAULT" />
122-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
122-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
123                <category android:name="android.intent.category.BROWSABLE" />
123-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
123-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
124
125                <data
125-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:41:17-44:51
126                    android:host="firebase.auth"
126-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:42:21-49
127                    android:path="/"
127-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:43:21-37
128                    android:scheme="genericidp" />
128-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:44:21-48
129            </intent-filter>
130        </activity>
131        <activity
131-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
132            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
132-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
133            android:excludeFromRecents="true"
133-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
134            android:exported="true"
134-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
135            android:launchMode="singleTask"
135-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
136            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
136-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
137            <intent-filter>
137-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
138                <action android:name="android.intent.action.VIEW" />
138-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
138-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
139
140                <category android:name="android.intent.category.DEFAULT" />
140-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
140-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
141                <category android:name="android.intent.category.BROWSABLE" />
141-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
141-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
142
143                <data
143-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:41:17-44:51
144                    android:host="firebase.auth"
144-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:42:21-49
145                    android:path="/"
145-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:43:21-37
146                    android:scheme="recaptcha" />
146-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:44:21-48
147            </intent-filter>
148        </activity>
149
150        <service
150-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:66:9-72:19
151            android:name="com.google.firebase.components.ComponentDiscoveryService"
151-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:67:13-84
152            android:directBootAware="true"
152-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4634e7a34cac4085f2f71ebed9045a1b\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
153            android:exported="false" >
153-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:68:13-37
154            <meta-data
154-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
155                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
155-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
156                android:value="com.google.firebase.components.ComponentRegistrar" />
156-->[com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
157            <meta-data
157-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad0bd39603d6c8c193d36922433f87e9\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
158                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
158-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad0bd39603d6c8c193d36922433f87e9\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
159                android:value="com.google.firebase.components.ComponentRegistrar" />
159-->[com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad0bd39603d6c8c193d36922433f87e9\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
160            <meta-data
160-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4634e7a34cac4085f2f71ebed9045a1b\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
161                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
161-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4634e7a34cac4085f2f71ebed9045a1b\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
162                android:value="com.google.firebase.components.ComponentRegistrar" />
162-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4634e7a34cac4085f2f71ebed9045a1b\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
163        </service>
164
165        <provider
165-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4634e7a34cac4085f2f71ebed9045a1b\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
166            android:name="com.google.firebase.provider.FirebaseInitProvider"
166-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4634e7a34cac4085f2f71ebed9045a1b\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
167            android:authorities="com.wellmetrix.wellmetrixprovider.firebaseinitprovider"
167-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4634e7a34cac4085f2f71ebed9045a1b\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
168            android:directBootAware="true"
168-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4634e7a34cac4085f2f71ebed9045a1b\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
169            android:exported="false"
169-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4634e7a34cac4085f2f71ebed9045a1b\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
170            android:initOrder="100" />
170-->[com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4634e7a34cac4085f2f71ebed9045a1b\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
171
172        <activity
172-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1b43ee28020c0b2d0ddc25e34972ceb2\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
173            android:name="com.google.android.gms.common.api.GoogleApiActivity"
173-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1b43ee28020c0b2d0ddc25e34972ceb2\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
174            android:exported="false"
174-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1b43ee28020c0b2d0ddc25e34972ceb2\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
175            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
175-->[com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1b43ee28020c0b2d0ddc25e34972ceb2\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
176
177        <meta-data
177-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28aaba99947946d399258e439182cfe1\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
178            android:name="com.google.android.gms.version"
178-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28aaba99947946d399258e439182cfe1\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
179            android:value="@integer/google_play_services_version" />
179-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28aaba99947946d399258e439182cfe1\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
180
181        <receiver
181-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
182            android:name="androidx.profileinstaller.ProfileInstallReceiver"
182-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
183            android:directBootAware="false"
183-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
184            android:enabled="true"
184-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
185            android:exported="true"
185-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
186            android:permission="android.permission.DUMP" >
186-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
187            <intent-filter>
187-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
188                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
188-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
188-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
189            </intent-filter>
190            <intent-filter>
190-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
191                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
191-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
191-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
192            </intent-filter>
193            <intent-filter>
193-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
194                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
194-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
194-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
195            </intent-filter>
196            <intent-filter>
196-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
197                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
197-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
197-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
198            </intent-filter>
199        </receiver>
200    </application>
201
202</manifest>
