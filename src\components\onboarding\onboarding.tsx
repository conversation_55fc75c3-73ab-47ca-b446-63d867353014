//@ts-ignore
// @ts-nocheck
import React from 'react';
import { IonCard, IonContent,IonInput,IonItem, IonButton, IonSlides,IonSlide,ViewItem} from '@ionic/react';
import './onboarding.css';



// @ViewChild('slides', {static: true}) slides: IonSlides;
export default class Onboarding extends React.Component{
    constructor(props){
        super(props);
      this.state={
          slide:0,
          skip:false
      }
    }
    Slides = React.createRef();
    render(){
        var slideOpts = {
            initialSlide: 0,
            speed: 400,
            
          };
          
        return<>
        <IonContent>
        <IonSlides   pager={true} options={slideOpts} ref={this.Slides} onIonSlideDidChange={()=>{
            this.Slides.current.getActiveIndex().then((success)=>{
                let currentslide=JSON.parse(success);
                let skip = false;
                if(currentslide===3){
                    skip=true
                }
                this.setState({slides:currentslide,skip:skip})
            },failure=>{alert('failure : '+failure)});
    
        }}
        >
            <IonSlide >
                this is slide 1 
            </IonSlide>
            <IonSlide>
                this is slide 2
            </IonSlide> 
            <IonSlide>
                this is slide 3
            </IonSlide>
            <IonSlide>
                this is slide 4
            </IonSlide>
        </IonSlides>
        
        </IonContent>
        <IonButton color='danger' onClick={()=>this.Slides.current.slideTo(3)} hidden={this.state.skip} >
                skip
                </IonButton>
        
        <IonButton onClick={()=>alert('MODULE DONE')} hidden={!this.state.skip} >
                ok
        </IonButton>
</>
    }
}