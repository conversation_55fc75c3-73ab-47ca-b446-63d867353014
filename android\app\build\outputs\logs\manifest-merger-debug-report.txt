-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:29:9-37:20
	android:grantUriPermissions
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:33:13-47
	android:authorities
		INJECTED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:31:13-64
	android:exported
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:32:13-37
	android:name
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:30:13-62
manifest
ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:2:1-84:12
INJECTED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:2:1-84:12
INJECTED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:2:1-84:12
INJECTED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:2:1-84:12
MERGED from [:capacitor-community-bluetooth-le] C:\work\wellmetrixproviderionic\node_modules\@capacitor-community\bluetooth-le\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-27:12
MERGED from [:capacitor-community-http] C:\work\wellmetrixproviderionic\node_modules\@capacitor-community\http\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-firebase-authentication] C:\work\wellmetrixproviderionic\node_modules\@capacitor-firebase\authentication\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-app] C:\work\wellmetrixproviderionic\node_modules\@capacitor\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-haptics] C:\work\wellmetrixproviderionic\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-9:12
MERGED from [:capacitor-keyboard] C:\work\wellmetrixproviderionic\node_modules\@capacitor\keyboard\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-status-bar] C:\work\wellmetrixproviderionic\node_modules\@capacitor\status-bar\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-android] C:\work\wellmetrixproviderionic\node_modules\@capacitor\android\capacitor\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [:capacitor-cordova-android-plugins] C:\work\wellmetrixproviderionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-28:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5135ef5465b6d259ae2d29fe7564d535\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\cfa929e238e2c59333af3756454deca9\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9f5d58573c9dff7bc7a91d6a295decf5\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b040dbb15142231338a9469429dc3dac\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\49c784fdbf20daedb180b68534f46efc\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3fd156050cb3305cd84bd74a869ce08c\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d008fdf93d7f27e2f23f6afffc0aaad\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc4e29fa4ee28d48c5b619f978911597\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:17:1-75:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:17.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\270c4e929418eb8303dd45b04c676864\transformed\play-services-auth-api-phone-17.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd5197332a73a4f6df8094d24c7db750\transformed\recaptcha-18.4.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ed321f50280658b635a7c6812fc22965\transformed\integrity-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d835e2213c8d2564173011b109f17c51\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:15:1-25:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\199e3941a6058dc383653b67c3097612\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad0bd39603d6c8c193d36922433f87e9\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4634e7a34cac4085f2f71ebed9045a1b\transformed\firebase-common-20.4.2\AndroidManifest.xml:15:1-41:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e610305554e9015432ba1e298d43f01a\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.webkit:webkit:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2a3c9d49aa24b6d1ffcb78c63ed48a5a\transformed\webkit-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\873bc401e2c41cf1461c051231c31823\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e6567b85908008c372857b57ff777f34\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\303e69fd2548f63d8eda17427351d4f7\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2e8d07c9fe81b5fe96033fbf3dcb8c70\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3f9c330ef4f6885447f839ef57ba1bbf\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1b43ee28020c0b2d0ddc25e34972ceb2\transformed\play-services-base-18.0.1\AndroidManifest.xml:16:1-24:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\99505e3f6474bdf8a578561614447e5e\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\da31bbbd398a29428a45c9dca59071e0\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b61d6bbe38732e28e0b55729faeec186\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4b574b42109befa3412f700e5ce3a6b\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d9882c6c6300e216203661867e675fcb\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\92e617c3d680f48a076a676c2bedcd7a\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1b6aeb841faf77c48f79b258a538c817\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\fbff70d4b295d89bd9cafbcb89df0972\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\0b64fa10ce59e6deea24c7be2929e653\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:2:1-6:12
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28aaba99947946d399258e439182cfe1\transformed\play-services-basement-18.1.0\AndroidManifest.xml:16:1-26:12
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\272367bf5319cf2bfbf609b5d9828f86\transformed\fragment-1.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9e1ca12e72a32dcf41a7baab8925d580\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\68c01c2aac9822a728c14c89a1d66eab\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5845b7a14572cc225f4cefda33aee0f9\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8c9b6e26de2481aea9d6fba0417a4694\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d5f67cf8ab6c20577f25f9c7a8daf4fc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c146f3e964651284d67fbccd605a4d86\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\52f0858b6e96f7af8a8e4a359bbed827\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.9\transforms\719ef118c296ae32d23ed25e62bc337c\transformed\firebase-components-17.1.5\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7e4b3f5ddbe65c52c015ea992d1cfaa5\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6688f90d010687505df84e1fa6a8fcd8\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [org.apache.cordova:framework:10.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\7a5f7f40ef2376ab8ac46a273b8f71d4\transformed\framework-10.1.1\AndroidManifest.xml:20:1-27:12
	package
		INJECTED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:4:5-51
	android:versionCode
		INJECTED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:3:5-63
application
ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:6:5-38:19
INJECTED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:6:5-38:19
MERGED from [:capacitor-cordova-android-plugins] C:\work\wellmetrixproviderionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:5-26:19
MERGED from [:capacitor-cordova-android-plugins] C:\work\wellmetrixproviderionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:5-26:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc4e29fa4ee28d48c5b619f978911597\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc4e29fa4ee28d48c5b619f978911597\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ed321f50280658b635a7c6812fc22965\transformed\integrity-1.2.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ed321f50280658b635a7c6812fc22965\transformed\integrity-1.2.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d835e2213c8d2564173011b109f17c51\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d835e2213c8d2564173011b109f17c51\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:23:5-20
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\199e3941a6058dc383653b67c3097612\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\199e3941a6058dc383653b67c3097612\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad0bd39603d6c8c193d36922433f87e9\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad0bd39603d6c8c193d36922433f87e9\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4634e7a34cac4085f2f71ebed9045a1b\transformed\firebase-common-20.4.2\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4634e7a34cac4085f2f71ebed9045a1b\transformed\firebase-common-20.4.2\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1b43ee28020c0b2d0ddc25e34972ceb2\transformed\play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1b43ee28020c0b2d0ddc25e34972ceb2\transformed\play-services-base-18.0.1\AndroidManifest.xml:19:5-23:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\99505e3f6474bdf8a578561614447e5e\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\99505e3f6474bdf8a578561614447e5e\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4b574b42109befa3412f700e5ce3a6b\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4b574b42109befa3412f700e5ce3a6b\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\0b64fa10ce59e6deea24c7be2929e653\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\0b64fa10ce59e6deea24c7be2929e653\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:5:5-20
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28aaba99947946d399258e439182cfe1\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28aaba99947946d399258e439182cfe1\transformed\play-services-basement-18.1.0\AndroidManifest.xml:20:5-24:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5845b7a14572cc225f4cefda33aee0f9\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5845b7a14572cc225f4cefda33aee0f9\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d5f67cf8ab6c20577f25f9c7a8daf4fc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d5f67cf8ab6c20577f25f9c7a8daf4fc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\99505e3f6474bdf8a578561614447e5e\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:11:9-35
	android:label
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:9:9-41
	android:roundIcon
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:10:9-54
	android:icon
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:8:9-43
	android:allowBackup
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:7:9-35
	android:theme
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:12:9-40
	android:usesCleartextTraffic
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:13:9-44
activity#com.wellmetrix.wellmetrixprovider.MainActivity
ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:15:9-27:20
	android:label
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:18:13-56
	android:launchMode
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:20:13-44
	android:exported
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:21:13-36
	android:configChanges
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:16:13-129
	android:theme
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:19:13-62
	android:name
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:17:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:23:13-26:29
action#android.intent.action.MAIN
ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:24:17-69
	android:name
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:24:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:25:17-77
	android:name
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:25:27-74
uses-permission#android.permission.INTERNET
ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:41:5-67
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd5197332a73a4f6df8094d24c7db750\transformed\recaptcha-18.4.0\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd5197332a73a4f6df8094d24c7db750\transformed\recaptcha-18.4.0\AndroidManifest.xml:7:5-67
	android:name
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:41:22-64
uses-permission#android.permission.ACCESS_COARSE_LOCATION
ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:44:5-47:49
MERGED from [:capacitor-community-bluetooth-le] C:\work\wellmetrixproviderionic\node_modules\@capacitor-community\bluetooth-le\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-81
MERGED from [:capacitor-community-bluetooth-le] C:\work\wellmetrixproviderionic\node_modules\@capacitor-community\bluetooth-le\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-81
MERGED from [:capacitor-cordova-android-plugins] C:\work\wellmetrixproviderionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-10:38
MERGED from [:capacitor-cordova-android-plugins] C:\work\wellmetrixproviderionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-10:38
	android:maxSdkVersion
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:46:9-35
		REJECTED from [:capacitor-cordova-android-plugins] C:\work\wellmetrixproviderionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:9-35
	tools:replace
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:47:9-46
	android:name
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:45:9-65
uses-permission#android.permission.ACCESS_FINE_LOCATION
ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:48:5-50:38
MERGED from [:capacitor-community-bluetooth-le] C:\work\wellmetrixproviderionic\node_modules\@capacitor-community\bluetooth-le\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [:capacitor-community-bluetooth-le] C:\work\wellmetrixproviderionic\node_modules\@capacitor-community\bluetooth-le\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
MERGED from [:capacitor-cordova-android-plugins] C:\work\wellmetrixproviderionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-13:38
MERGED from [:capacitor-cordova-android-plugins] C:\work\wellmetrixproviderionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:5-13:38
	android:maxSdkVersion
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:50:9-35
	android:name
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:49:9-63
uses-permission#android.permission.BLUETOOTH
ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:53:5-68
MERGED from [:capacitor-community-bluetooth-le] C:\work\wellmetrixproviderionic\node_modules\@capacitor-community\bluetooth-le\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-12:38
MERGED from [:capacitor-community-bluetooth-le] C:\work\wellmetrixproviderionic\node_modules\@capacitor-community\bluetooth-le\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:5-12:38
MERGED from [:capacitor-cordova-android-plugins] C:\work\wellmetrixproviderionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:5-16:38
MERGED from [:capacitor-cordova-android-plugins] C:\work\wellmetrixproviderionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:5-16:38
	android:maxSdkVersion
		ADDED from [:capacitor-community-bluetooth-le] C:\work\wellmetrixproviderionic\node_modules\@capacitor-community\bluetooth-le\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-35
	android:name
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:53:22-65
uses-permission#android.permission.BLUETOOTH_ADMIN
ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:54:5-74
MERGED from [:capacitor-community-bluetooth-le] C:\work\wellmetrixproviderionic\node_modules\@capacitor-community\bluetooth-le\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-15:38
MERGED from [:capacitor-community-bluetooth-le] C:\work\wellmetrixproviderionic\node_modules\@capacitor-community\bluetooth-le\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:5-15:38
MERGED from [:capacitor-cordova-android-plugins] C:\work\wellmetrixproviderionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:5-19:38
MERGED from [:capacitor-cordova-android-plugins] C:\work\wellmetrixproviderionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:5-19:38
	android:maxSdkVersion
		ADDED from [:capacitor-community-bluetooth-le] C:\work\wellmetrixproviderionic\node_modules\@capacitor-community\bluetooth-le\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:9-35
	android:name
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:54:22-71
uses-permission#android.permission.BLUETOOTH_SCAN
ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:57:5-60:32
MERGED from [:capacitor-community-bluetooth-le] C:\work\wellmetrixproviderionic\node_modules\@capacitor-community\bluetooth-le\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:5-18:31
MERGED from [:capacitor-community-bluetooth-le] C:\work\wellmetrixproviderionic\node_modules\@capacitor-community\bluetooth-le\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:5-18:31
MERGED from [:capacitor-cordova-android-plugins] C:\work\wellmetrixproviderionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:5-22:58
MERGED from [:capacitor-cordova-android-plugins] C:\work\wellmetrixproviderionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:5-22:58
	android:usesPermissionFlags
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:59:9-55
	tools:targetApi
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:60:9-29
	android:name
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:58:9-57
uses-permission#android.permission.BLUETOOTH_CONNECT
ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:62:5-64:32
MERGED from [:capacitor-community-bluetooth-le] C:\work\wellmetrixproviderionic\node_modules\@capacitor-community\bluetooth-le\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:5-21:31
MERGED from [:capacitor-community-bluetooth-le] C:\work\wellmetrixproviderionic\node_modules\@capacitor-community\bluetooth-le\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:5-21:31
MERGED from [:capacitor-cordova-android-plugins] C:\work\wellmetrixproviderionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:5-76
MERGED from [:capacitor-cordova-android-plugins] C:\work\wellmetrixproviderionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:5-76
	tools:targetApi
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:64:9-29
	android:name
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:63:9-60
uses-permission#android.permission.BLUETOOTH_ADVERTISE
ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:66:5-68:32
	tools:targetApi
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:68:9-29
	android:name
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:67:9-62
uses-feature#android.hardware.bluetooth_le
ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:71:5-90
MERGED from [:capacitor-community-bluetooth-le] C:\work\wellmetrixproviderionic\node_modules\@capacitor-community\bluetooth-le\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:5-25:36
MERGED from [:capacitor-community-bluetooth-le] C:\work\wellmetrixproviderionic\node_modules\@capacitor-community\bluetooth-le\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:5-25:36
	android:required
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:71:64-87
	android:name
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:71:19-63
uses-feature#android.hardware.bluetooth
ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:72:5-87
	android:required
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:72:61-84
	android:name
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:72:19-60
queries
ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:75:5-82:15
intent#action:name:android.bluetooth.device.action.FOUND
ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:76:9-78:18
action#android.bluetooth.device.action.FOUND
ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:77:13-75
	android:name
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:77:21-73
package#android.bluetooth
ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:80:9-53
	android:name
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:80:18-50
package#android.bluetooth.le
ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:81:9-56
	android:name
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:81:18-53
meta-data#androidx.core.content.FileProvider
ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:34:13-36:54
	android:resource
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:36:17-51
	android:name
		ADDED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml:35:17-66
uses-sdk
INJECTED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml
INJECTED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml
MERGED from [:capacitor-community-bluetooth-le] C:\work\wellmetrixproviderionic\node_modules\@capacitor-community\bluetooth-le\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:capacitor-community-bluetooth-le] C:\work\wellmetrixproviderionic\node_modules\@capacitor-community\bluetooth-le\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:capacitor-community-http] C:\work\wellmetrixproviderionic\node_modules\@capacitor-community\http\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-community-http] C:\work\wellmetrixproviderionic\node_modules\@capacitor-community\http\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-firebase-authentication] C:\work\wellmetrixproviderionic\node_modules\@capacitor-firebase\authentication\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-firebase-authentication] C:\work\wellmetrixproviderionic\node_modules\@capacitor-firebase\authentication\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-app] C:\work\wellmetrixproviderionic\node_modules\@capacitor\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-app] C:\work\wellmetrixproviderionic\node_modules\@capacitor\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-haptics] C:\work\wellmetrixproviderionic\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-haptics] C:\work\wellmetrixproviderionic\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-keyboard] C:\work\wellmetrixproviderionic\node_modules\@capacitor\keyboard\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-keyboard] C:\work\wellmetrixproviderionic\node_modules\@capacitor\keyboard\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-status-bar] C:\work\wellmetrixproviderionic\node_modules\@capacitor\status-bar\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-status-bar] C:\work\wellmetrixproviderionic\node_modules\@capacitor\status-bar\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-android] C:\work\wellmetrixproviderionic\node_modules\@capacitor\android\capacitor\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-android] C:\work\wellmetrixproviderionic\node_modules\@capacitor\android\capacitor\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:capacitor-cordova-android-plugins] C:\work\wellmetrixproviderionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [:capacitor-cordova-android-plugins] C:\work\wellmetrixproviderionic\android\capacitor-cordova-android-plugins\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:6:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5135ef5465b6d259ae2d29fe7564d535\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5135ef5465b6d259ae2d29fe7564d535\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\cfa929e238e2c59333af3756454deca9\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\cfa929e238e2c59333af3756454deca9\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9f5d58573c9dff7bc7a91d6a295decf5\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9f5d58573c9dff7bc7a91d6a295decf5\transformed\coordinatorlayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b040dbb15142231338a9469429dc3dac\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core-splashscreen:1.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b040dbb15142231338a9469429dc3dac\transformed\core-splashscreen-1.0.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\49c784fdbf20daedb180b68534f46efc\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\49c784fdbf20daedb180b68534f46efc\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3fd156050cb3305cd84bd74a869ce08c\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3fd156050cb3305cd84bd74a869ce08c\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d008fdf93d7f27e2f23f6afffc0aaad\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3d008fdf93d7f27e2f23f6afffc0aaad\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc4e29fa4ee28d48c5b619f978911597\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc4e29fa4ee28d48c5b619f978911597\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:21:5-23:64
MERGED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:21:5-23:64
MERGED from [com.google.android.gms:play-services-auth-api-phone:17.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\270c4e929418eb8303dd45b04c676864\transformed\play-services-auth-api-phone-17.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:17.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\270c4e929418eb8303dd45b04c676864\transformed\play-services-auth-api-phone-17.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd5197332a73a4f6df8094d24c7db750\transformed\recaptcha-18.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd5197332a73a4f6df8094d24c7db750\transformed\recaptcha-18.4.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ed321f50280658b635a7c6812fc22965\transformed\integrity-1.2.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\ed321f50280658b635a7c6812fc22965\transformed\integrity-1.2.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d835e2213c8d2564173011b109f17c51\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-appcheck-interop:17.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\d835e2213c8d2564173011b109f17c51\transformed\firebase-appcheck-interop-17.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\199e3941a6058dc383653b67c3097612\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\199e3941a6058dc383653b67c3097612\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad0bd39603d6c8c193d36922433f87e9\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad0bd39603d6c8c193d36922433f87e9\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4634e7a34cac4085f2f71ebed9045a1b\transformed\firebase-common-20.4.2\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4634e7a34cac4085f2f71ebed9045a1b\transformed\firebase-common-20.4.2\AndroidManifest.xml:19:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e610305554e9015432ba1e298d43f01a\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e610305554e9015432ba1e298d43f01a\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.webkit:webkit:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2a3c9d49aa24b6d1ffcb78c63ed48a5a\transformed\webkit-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.9.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2a3c9d49aa24b6d1ffcb78c63ed48a5a\transformed\webkit-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\873bc401e2c41cf1461c051231c31823\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\873bc401e2c41cf1461c051231c31823\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e6567b85908008c372857b57ff777f34\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\e6567b85908008c372857b57ff777f34\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\303e69fd2548f63d8eda17427351d4f7\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\303e69fd2548f63d8eda17427351d4f7\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2e8d07c9fe81b5fe96033fbf3dcb8c70\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\2e8d07c9fe81b5fe96033fbf3dcb8c70\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3f9c330ef4f6885447f839ef57ba1bbf\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\3f9c330ef4f6885447f839ef57ba1bbf\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1b43ee28020c0b2d0ddc25e34972ceb2\transformed\play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1b43ee28020c0b2d0ddc25e34972ceb2\transformed\play-services-base-18.0.1\AndroidManifest.xml:18:5-43
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\99505e3f6474bdf8a578561614447e5e\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\99505e3f6474bdf8a578561614447e5e\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\da31bbbd398a29428a45c9dca59071e0\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\da31bbbd398a29428a45c9dca59071e0\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b61d6bbe38732e28e0b55729faeec186\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\b61d6bbe38732e28e0b55729faeec186\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4b574b42109befa3412f700e5ce3a6b\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4b574b42109befa3412f700e5ce3a6b\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d9882c6c6300e216203661867e675fcb\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d9882c6c6300e216203661867e675fcb\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\92e617c3d680f48a076a676c2bedcd7a\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\92e617c3d680f48a076a676c2bedcd7a\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1b6aeb841faf77c48f79b258a538c817\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1b6aeb841faf77c48f79b258a538c817\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\fbff70d4b295d89bd9cafbcb89df0972\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\fbff70d4b295d89bd9cafbcb89df0972\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\0b64fa10ce59e6deea24c7be2929e653\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-tasks:18.0.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\0b64fa10ce59e6deea24c7be2929e653\transformed\play-services-tasks-18.0.2\AndroidManifest.xml:4:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28aaba99947946d399258e439182cfe1\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28aaba99947946d399258e439182cfe1\transformed\play-services-basement-18.1.0\AndroidManifest.xml:18:5-43
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\272367bf5319cf2bfbf609b5d9828f86\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.6.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\272367bf5319cf2bfbf609b5d9828f86\transformed\fragment-1.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9e1ca12e72a32dcf41a7baab8925d580\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\9e1ca12e72a32dcf41a7baab8925d580\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\68c01c2aac9822a728c14c89a1d66eab\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\68c01c2aac9822a728c14c89a1d66eab\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5845b7a14572cc225f4cefda33aee0f9\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\5845b7a14572cc225f4cefda33aee0f9\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8c9b6e26de2481aea9d6fba0417a4694\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\8c9b6e26de2481aea9d6fba0417a4694\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d5f67cf8ab6c20577f25f9c7a8daf4fc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d5f67cf8ab6c20577f25f9c7a8daf4fc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c146f3e964651284d67fbccd605a4d86\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\c146f3e964651284d67fbccd605a4d86\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\52f0858b6e96f7af8a8e4a359bbed827\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\52f0858b6e96f7af8a8e4a359bbed827\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.9\transforms\719ef118c296ae32d23ed25e62bc337c\transformed\firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:17.1.5] C:\Users\<USER>\.gradle\caches\8.9\transforms\719ef118c296ae32d23ed25e62bc337c\transformed\firebase-components-17.1.5\AndroidManifest.xml:18:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7e4b3f5ddbe65c52c015ea992d1cfaa5\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\7e4b3f5ddbe65c52c015ea992d1cfaa5\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6688f90d010687505df84e1fa6a8fcd8\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\6688f90d010687505df84e1fa6a8fcd8\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [org.apache.cordova:framework:10.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\7a5f7f40ef2376ab8ac46a273b8f71d4\transformed\framework-10.1.1\AndroidManifest.xml:25:5-44
MERGED from [org.apache.cordova:framework:10.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\7a5f7f40ef2376ab8ac46a273b8f71d4\transformed\framework-10.1.1\AndroidManifest.xml:25:5-44
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:23:9-61
	android:targetSdkVersion
		INJECTED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\work\wellmetrixproviderionic\android\app\src\main\AndroidManifest.xml
uses-permission#android.permission.VIBRATE
ADDED from [:capacitor-haptics] C:\work\wellmetrixproviderionic\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-66
	android:name
		ADDED from [:capacitor-haptics] C:\work\wellmetrixproviderionic\node_modules\@capacitor\haptics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-63
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc4e29fa4ee28d48c5b619f978911597\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4b574b42109befa3412f700e5ce3a6b\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4b574b42109befa3412f700e5ce3a6b\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d5f67cf8ab6c20577f25f9c7a8daf4fc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d5f67cf8ab6c20577f25f9c7a8daf4fc\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc4e29fa4ee28d48c5b619f978911597\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc4e29fa4ee28d48c5b619f978911597\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc4e29fa4ee28d48c5b619f978911597\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc4e29fa4ee28d48c5b619f978911597\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc4e29fa4ee28d48c5b619f978911597\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc4e29fa4ee28d48c5b619f978911597\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dc4e29fa4ee28d48c5b619f978911597\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd5197332a73a4f6df8094d24c7db750\transformed\recaptcha-18.4.0\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd5197332a73a4f6df8094d24c7db750\transformed\recaptcha-18.4.0\AndroidManifest.xml:8:5-79
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:26:22-76
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:35:13-45:29
action#android.intent.action.VIEW
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:36:25-66
category#android.intent.category.DEFAULT
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:38:17-76
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:38:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:39:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:39:27-75
data
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:41:17-44:51
	android:path
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:43:21-37
	android:host
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:42:21-49
	android:scheme
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:44:21-48
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:53:13-63:29
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad0bd39603d6c8c193d36922433f87e9\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad0bd39603d6c8c193d36922433f87e9\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4634e7a34cac4085f2f71ebed9045a1b\transformed\firebase-common-20.4.2\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4634e7a34cac4085f2f71ebed9045a1b\transformed\firebase-common-20.4.2\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:68:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4634e7a34cac4085f2f71ebed9045a1b\transformed\firebase-common-20.4.2\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4634e7a34cac4085f2f71ebed9045a1b\transformed\firebase-common-20.4.2\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:67:13-84
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:22.3.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\328ee16612dcbac44c5e929cb0af93ff\transformed\firebase-auth-22.3.1\AndroidManifest.xml:70:17-109
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd5197332a73a4f6df8094d24c7db750\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.4.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\dd5197332a73a4f6df8094d24c7db750\transformed\recaptcha-18.4.0\AndroidManifest.xml:9:22-95
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad0bd39603d6c8c193d36922433f87e9\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad0bd39603d6c8c193d36922433f87e9\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\ad0bd39603d6c8c193d36922433f87e9\transformed\firebase-common-ktx-20.4.2\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4634e7a34cac4085f2f71ebed9045a1b\transformed\firebase-common-20.4.2\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4634e7a34cac4085f2f71ebed9045a1b\transformed\firebase-common-20.4.2\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4634e7a34cac4085f2f71ebed9045a1b\transformed\firebase-common-20.4.2\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4634e7a34cac4085f2f71ebed9045a1b\transformed\firebase-common-20.4.2\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4634e7a34cac4085f2f71ebed9045a1b\transformed\firebase-common-20.4.2\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4634e7a34cac4085f2f71ebed9045a1b\transformed\firebase-common-20.4.2\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4634e7a34cac4085f2f71ebed9045a1b\transformed\firebase-common-20.4.2\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4634e7a34cac4085f2f71ebed9045a1b\transformed\firebase-common-20.4.2\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:20.4.2] C:\Users\<USER>\.gradle\caches\8.9\transforms\4634e7a34cac4085f2f71ebed9045a1b\transformed\firebase-common-20.4.2\AndroidManifest.xml:36:17-109
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1b43ee28020c0b2d0ddc25e34972ceb2\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:9-22:45
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1b43ee28020c0b2d0ddc25e34972ceb2\transformed\play-services-base-18.0.1\AndroidManifest.xml:22:19-43
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1b43ee28020c0b2d0ddc25e34972ceb2\transformed\play-services-base-18.0.1\AndroidManifest.xml:21:19-78
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.0.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\1b43ee28020c0b2d0ddc25e34972ceb2\transformed\play-services-base-18.0.1\AndroidManifest.xml:20:19-85
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\99505e3f6474bdf8a578561614447e5e\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\99505e3f6474bdf8a578561614447e5e\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\99505e3f6474bdf8a578561614447e5e\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.wellmetrix.wellmetrixprovider.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\99505e3f6474bdf8a578561614447e5e\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\99505e3f6474bdf8a578561614447e5e\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\99505e3f6474bdf8a578561614447e5e\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\99505e3f6474bdf8a578561614447e5e\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\99505e3f6474bdf8a578561614447e5e\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.wellmetrix.wellmetrixprovider.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\99505e3f6474bdf8a578561614447e5e\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\99505e3f6474bdf8a578561614447e5e\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4b574b42109befa3412f700e5ce3a6b\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4b574b42109befa3412f700e5ce3a6b\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.9\transforms\d4b574b42109befa3412f700e5ce3a6b\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28aaba99947946d399258e439182cfe1\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28aaba99947946d399258e439182cfe1\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\28aaba99947946d399258e439182cfe1\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.9\transforms\daef0be3dd95f597e52aa483a22a6e83\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
