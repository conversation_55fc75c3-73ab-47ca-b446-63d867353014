workflows:
  build_android_ios:
    name: Build Android and iOS apps
    environment:
      node: "18"
    scripts:
      # Android Build Steps
      - name: Install dependencies
        script: |
          npm install --legacy-peer-deps

      - name: Sync Capacitor with Android
        script: |
          npx cap sync android

      - name: Build Android app
        script: |
          cd android
          ./gradlew assembleRelease

      # Check if APK exists
      - name: Check if <PERSON>K exists
        script: |
          if [ -f "android/app/build/outputs/apk/release/app-release.apk" ]; then
            echo "APK found at android/app/build/outputs/apk/release/app-release.apk"
            APK_PATH="android/app/build/outputs/apk/release/app-release.apk"
          elif [ -f "android/app/build/outputs/apk/release/app-release-unsigned.apk" ]; then
            echo "APK found at android/app/build/outputs/apk/release/app-release-unsigned.apk"
            APK_PATH="android/app/build/outputs/apk/release/app-release-unsigned.apk"
          else
            echo "Error: APK file not found!"
            exit 1
          fi

      # iOS Build Steps
      - name: Sync Capacitor with iOS
        script: |
          npx cap sync ios

      - name: Generate exportOptions.plist
        script: |
          # Generate exportOptions.plist for iOS Development export
          cat > ios/App/exportOptions.plist <<EOF
          <?xml version="1.0" encoding="UTF-8"?>
          <plist version="1.0">
            <dict>
              <key>method</key>
              <string>development</string>
              <key>destination</key>
              <string>export</string>
              <key>signingStyle</key>
              <string>manual</string>
              <key>provisioningProfiles</key>
              <dict>
                <key>com.wellmetrix.wellmetrixproviderios</key>
                <string>match Development com.wellmetrix.wellmetrixproviderios</string>
              </dict>
            </dict>
          </plist>
          EOF

      - name: Build iOS app
        script: |
          cd ios/App
          # Archive the app using xcodebuild
          xcodebuild -workspace App.xcworkspace -scheme App -configuration Release -archivePath $PWD/App.xcarchive archive
          # Export the archive using exportOptions.plist for development
          xcodebuild -exportArchive -archivePath $PWD/App.xcarchive -exportPath $PWD -exportOptionsPlist $PWD/exportOptions.plist

      # Check IPA existence
      - name: Check if IPA exists
        script: |
          if [ -f "ios/App/app-release.ipa" ]; then
            echo "IPA found at ios/App/app-release.ipa"
          else
            echo "Error: IPA file not found!"
            exit 1
          fi

    artifacts:
      - android/app/build/outputs/apk/release/app-release.apk  # Save APK for download
      - ios/App/app-release.ipa  # Save IPA for download
      - android/app/build/outputs/apk/release/app-release-unsigned.apk
