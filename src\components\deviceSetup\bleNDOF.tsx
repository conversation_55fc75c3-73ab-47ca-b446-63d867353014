import React, { useEffect, useState, useRef } from 'react';
import { BleClient, numbersToDataView } from '@capacitor-community/bluetooth-le';
import { <PERSON><PERSON><PERSON>, Line, XAxis, YA<PERSON><PERSON>, <PERSON>lt<PERSON>, Legend, CartesianGrid } from 'recharts';

// MetaWear Service & Characteristics UUIDs
const MW_SERVICE = '326a9000-85cb-9195-d9dd-464cfbbae75a';
const MW_CHAR_CMD = '326a9001-85cb-9195-d9dd-464cfbbae75a';
const MW_CHAR_NOTIF = '326a9006-85cb-9195-d9dd-464cfbbae75a';

const MetaWearSensorComponent: React.FC = () => {
  const [gyroData, setGyroData] = useState<{ time: number; x: number; y: number; z: number; }[]>([]);
  const [quatData, setQuatData] = useState<{ time: number; w: number; x: number; y: number; z: number; }[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [accelData, setAccelData] = useState([]);
  const [magData, setMagData] = useState([]);
  const deviceIdRef = useRef<string | null>(null);
  const notifStartedRef = useRef<boolean>(false);

  // Helper: write bytes to command characteristic
  const writeCommand = async (bytes: number[]) => {
    if (!deviceIdRef.current) return;
    await BleClient.write(
      deviceIdRef.current,
      MW_SERVICE,
      MW_CHAR_CMD,
      numbersToDataView(bytes)
    );
  };

  // Connect to MetaWear device
  const connectToDevice = async () => {
    try {
      console.log('Initializing BLE client...');
      await BleClient.initialize({ androidNeverForLocation: true });

      console.log('Requesting MetaWear device...');
      const device = await BleClient.requestDevice({
        services: [MW_SERVICE]
      });
      deviceIdRef.current = device.deviceId;

      console.log('Connecting to device:', device.deviceId);
      await BleClient.connect(device.deviceId);
      console.log('Connected!');
      setIsConnected(true);
    } catch (err) {
      console.error('Error connecting to device:', err);
    }
  };

  // Start streaming sensor fusion and gyro
  // const startSensorFusion = async () => {
  //   if (!deviceIdRef.current) return;
  //   try {
  //     console.log('Discovering services...');
  //     const services = await BleClient.getServices(deviceIdRef.current);
  //     if (!services.some(s => s.uuid === MW_SERVICE)) {
  //       throw new Error('MetaWear service not found');
  //     }

  //     console.log('Configuring gyroscope...');
  //     await writeCommand([0x13, 0x03, 0x26, 0x04]); // Set gyro range to 125dps
  //     await writeCommand([0x13, 0x05, 0x01]);       // Enable gyro output
  //     await writeCommand([0x13, 0x02, 0x01, 0x00]); // Enable gyro interrupt
  //     await writeCommand([0x13, 0x01, 0x01]);       // Power on gyro

  //     console.log('Configuring sensor fusion...');
  //     await writeCommand([0x19, 0x03, 0x01, 0x02, 0x00]); // Sensor Fusion config
  //     await writeCommand([0x19, 0x03, 0x02, 0x00, 0x00]); // Write config
  //     await writeCommand([0x19, 0x05, 0x03, 0x01]);       // Enable quaternion output
  //     await writeCommand([0x19, 0x01, 0x01]);             // Start sensor fusion

  //     console.log('Subscribing to notifications...');
  //     await BleClient.startNotifications(
  //       deviceIdRef.current,
  //       MW_SERVICE,
  //       MW_CHAR_NOTIF,
  //       handleNotification
  //     );
  //     notifStartedRef.current = true;
  //     setIsStreaming(true);

  //     console.log('Streaming started!');
  //   } catch (err) {
  //     console.error('Error starting sensor fusion:', err);
  //   }
  // };

  const startSensorFusion = async () => {
    if (!deviceIdRef.current) return;
    try {
      console.log('Discovering services...');
      const services = await BleClient.getServices(deviceIdRef.current);
      if (!services.some(s => s.uuid === MW_SERVICE)) {
        throw new Error('MetaWear service not found');
      }

      console.log('Configuring gyroscope...');
      await writeCommand([0x13, 0x03, 0x26, 0x04]); // Set gyro range to 125dps
      await writeCommand([0x13, 0x05, 0x01]);       // Enable gyro output
      await writeCommand([0x13, 0x02, 0x01, 0x00]); // Enable gyro interrupt
      await writeCommand([0x13, 0x01, 0x01]);       // Power on gyro

      console.log('Configuring accelerometer...');
      await writeCommand([0x03, 0x01, 0x01]);       // Enable accelerometer

      console.log('Configuring magnetometer...');
      await writeCommand([0x15, 0x01, 0x01]);       // Enable magnetometer

      console.log('Configuring sensor fusion...');
      await writeCommand([0x19, 0x03, 0x01, 0x02, 0x00]); // Sensor Fusion config
      await writeCommand([0x19, 0x03, 0x02, 0x00, 0x00]); // Write config
      await writeCommand([0x19, 0x05, 0x03, 0x01]);       // Enable quaternion output
      await writeCommand([0x19, 0x01, 0x01]);             // Start sensor fusion

      console.log('Subscribing to notifications...');
      await BleClient.startNotifications(
        deviceIdRef.current,
        MW_SERVICE,
        MW_CHAR_NOTIF,
        handleNotification
      );
      notifStartedRef.current = true;
      setIsStreaming(true);

      console.log('Streaming started!');
    } catch (err) {
      console.error('Error starting sensor fusion:', err);
    }
  };


  // const handleNotification = (data: any) => {
  //   console.log("DATA RAW ",data)
  //   const bytes = new Uint8Array(data.buffer);
  //   console.log("BYTES ",bytes);
  //   const time = Date.now();

  //   if (bytes[0] === 0x13 && bytes[1] === 0x05) {
  //     // Gyroscope data: bytes[2-7] are the raw x, y, z values in 16-bit integers
  //     const x = (bytes[3] << 8) | bytes[2];
  //     const y = (bytes[5] << 8) | bytes[4];
  //     const z = (bytes[7] << 8) | bytes[6];
  //     const xVal = (x & 0x8000) ? x - 0x10000 : x; // Handle signed 16-bit integer
  //     const yVal = (y & 0x8000) ? y - 0x10000 : y;
  //     const zVal = (z & 0x8000) ? z - 0x10000 : z;

  //     // Update state with new gyro data
  //     setGyroData(prev => {
  //       const newData = [...prev, { time, x: xVal, y: yVal, z: zVal }];
  //       return newData.slice(-100); // Keep only the latest 100 data points
  //     });
  //   } else if (bytes[0] === 0x19 && bytes[1] === 0x07) {
  //     // Quaternion data: bytes[2-15] are the raw float32 values for w, x, y, z
  //     const view = new DataView(bytes.buffer);

  //     const w = view.getFloat32(2, true); // Parse w (4 bytes starting at index 2)
  //     const x = view.getFloat32(6, true); // Parse x (4 bytes starting at index 6)
  //     const y = view.getFloat32(10, true); // Parse y (4 bytes starting at index 10)
  //     const z = view.getFloat32(14, true); // Parse z (4 bytes starting at index 14)

  //     // Update state with new quaternion data
  //     setQuatData(prev => {
  //       const newData = [...prev, { time, w, x, y, z }];
  //       return newData.slice(-100); // Keep only the latest 100 data points
  //     });
  //   }
  // };

  // Stop streaming and disconnect


  const handleNotification = (data: any) => {
    console.log("DATA RAW ", data);
    const bytes = new Uint8Array(data.buffer);
    console.log("BYTES ", bytes);
    const time = Date.now();
  
    // Check for Gyroscope data (0x13 0x05)
    if (bytes[0] === 0x13 && bytes[1] === 0x05) {
      const x = (bytes[3] << 8) | bytes[2];
      const y = (bytes[5] << 8) | bytes[4];
      const z = (bytes[7] << 8) | bytes[6];
      const xVal = (x & 0x8000) ? x - 0x10000 : x;
      const yVal = (y & 0x8000) ? y - 0x10000 : y;
      const zVal = (z & 0x8000) ? z - 0x10000 : z;
  
      setGyroData(prev => {
        const newData = [...prev, { time, x: xVal, y: yVal, z: zVal }];
        return newData.slice(-100);
      });
  
    // Check for Accelerometer data (0x03 0x04)
    } else if (bytes[0] === 0x03 && bytes[1] === 0x04) {
      // Accelerometer data: bytes[2-7] are the raw x, y, z values
      const x = (bytes[3] << 8) | bytes[2];
      const y = (bytes[5] << 8) | bytes[4];
      const z = (bytes[7] << 8) | bytes[6];
      const xVal = (x & 0x8000) ? x - 0x10000 : x;
      const yVal = (y & 0x8000) ? y - 0x10000 : y;
      const zVal = (z & 0x8000) ? z - 0x10000 : z;
  
      console.log("ACCELERATION ", xVal, " ", yVal, " ", zVal);
  
      setAccelData(prev => {
        const newData = [...prev, { time, x: xVal, y: yVal, z: zVal }];
        return newData.slice(-100);
      });
  
    // Check for Magnetometer data (0x15 0x04)
    } else if (bytes[0] === 0x15 && bytes[1] === 0x04) {
      // Magnetometer data: bytes[2-7] are the raw x, y, z values
      const x = (bytes[3] << 8) | bytes[2];
      const y = (bytes[5] << 8) | bytes[4];
      const z = (bytes[7] << 8) | bytes[6];
      const xVal = (x & 0x8000) ? x - 0x10000 : x;
      const yVal = (y & 0x8000) ? y - 0x10000 : y;
      const zVal = (z & 0x8000) ? z - 0x10000 : z;
  
      console.log("MAGNETOMETER ", xVal, " ", yVal, " ", zVal);
  
      setMagData(prev => {
        const newData = [...prev, { time, x: xVal, y: yVal, z: zVal }];
        return newData.slice(-100);
      });
  
    // Handle Quaternion data (0x19 0x07)
    } else if (bytes[0] === 0x19 && bytes[1] === 0x07) {
      const view = new DataView(bytes.buffer);
  
      const w = view.getFloat32(2, true); // little endian
      const x = view.getFloat32(6, true);
      const y = view.getFloat32(10, true);
      const z = view.getFloat32(14, true);
  
      setQuatData(prev => {
        const newData = [...prev, { time, w, x, y, z }];
        return newData.slice(-100);
      });
    }
  };
  



  const stopSensorFusion = async () => {
    if (!deviceIdRef.current) return;
    try {
      console.log('Stopping sensor fusion...');
      await writeCommand([0x13, 0x01, 0x00]); // Power off gyro
      await writeCommand([0x13, 0x02, 0x00, 0x00]); // Disable gyro interrupt
      await writeCommand([0x13, 0x05, 0x00]); // Disable gyro output
      await writeCommand([0x19, 0x01, 0x00]); // Stop sensor fusion
      await writeCommand([0x19, 0x05, 0x03, 0x00]); // Disable quaternion output

      if (notifStartedRef.current) {
        await BleClient.stopNotifications(deviceIdRef.current, MW_SERVICE, MW_CHAR_NOTIF);
      }

      await BleClient.disconnect(deviceIdRef.current);

      deviceIdRef.current = null;
      notifStartedRef.current = false;
      setIsConnected(false);
      setIsStreaming(false);

      console.log('Disconnected.');
    } catch (err) {
      console.error('Error during stop:', err);
    }
  };

  return (
    <div style={{ padding: '1rem' }}>
      <h2>MetaWear Gyro & Quaternion Data</h2>

      <div style={{ marginBottom: '1rem' }}>
        {!isConnected && (
          <button onClick={connectToDevice}>Connect Device</button>
        )}
        {isConnected && !isStreaming && (
          <button onClick={startSensorFusion} style={{ marginLeft: '1rem' }}>Start Sensor Fusion</button>
        )}
        {isConnected && isStreaming && (
          <button onClick={stopSensorFusion} style={{ marginLeft: '1rem' }}>Stop Sensor Fusion</button>
        )}
      </div>

      <div style={{ marginBottom: '2rem' }}>
        <h3>Quaternion (W, X, Y, Z)</h3>
        <LineChart width={500} height={300} data={quatData}>
          <XAxis dataKey="time" domain={['auto', 'auto']} name="Time" />
          <YAxis />
          <Tooltip />
          <Legend />
          <CartesianGrid stroke="#eee" strokeDasharray="5 5" />
          <Line type="monotone" dataKey="w" stroke="#8884d8" dot={false} isAnimationActive={false} />
          <Line type="monotone" dataKey="x" stroke="#82ca9d" dot={false} isAnimationActive={false} />
          <Line type="monotone" dataKey="y" stroke="#ff7300" dot={false} isAnimationActive={false} />
          <Line type="monotone" dataKey="z" stroke="#387908" dot={false} isAnimationActive={false} />
        </LineChart>
      </div>

      <div>
        <h3>Gyroscope (X, Y, Z)</h3>
        <LineChart width={500} height={300} data={gyroData}>
          <XAxis dataKey="time" domain={['auto', 'auto']} name="Time" />
          <YAxis />
          <Tooltip />
          <Legend />
          <CartesianGrid stroke="#eee" strokeDasharray="5 5" />
          <Line type="monotone" dataKey="x" stroke="#8884d8" dot={false} isAnimationActive={false} />
          <Line type="monotone" dataKey="y" stroke="#82ca9d" dot={false} isAnimationActive={false} />
          <Line type="monotone" dataKey="z" stroke="#ff7300" dot={false} isAnimationActive={false} />
        </LineChart>
      </div>

      <div>
        <h3>MAG (X, Y, Z)</h3>
        <LineChart width={500} height={300} data={magData}>
          <XAxis dataKey="time" domain={['auto', 'auto']} name="Time" />
          <YAxis />
          <Tooltip />
          <Legend />
          <CartesianGrid stroke="#eee" strokeDasharray="5 5" />
          <Line type="monotone" dataKey="x" stroke="#8884d8" dot={false} isAnimationActive={false} />
          <Line type="monotone" dataKey="y" stroke="#82ca9d" dot={false} isAnimationActive={false} />
          <Line type="monotone" dataKey="z" stroke="#ff7300" dot={false} isAnimationActive={false} />
        </LineChart>
      </div>

      <div>
        <h3>accel (X, Y, Z)</h3>
        <LineChart width={500} height={300} data={accelData}>
          <XAxis dataKey="time" domain={['auto', 'auto']} name="Time" />
          <YAxis />
          <Tooltip />
          <Legend />
          <CartesianGrid stroke="#eee" strokeDasharray="5 5" />
          <Line type="monotone" dataKey="x" stroke="#8884d8" dot={false} isAnimationActive={false} />
          <Line type="monotone" dataKey="y" stroke="#82ca9d" dot={false} isAnimationActive={false} />
          <Line type="monotone" dataKey="z" stroke="#ff7300" dot={false} isAnimationActive={false} />
        </LineChart>
      </div>
    </div>
  );
};

export default MetaWearSensorComponent;
