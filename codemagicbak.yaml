workflows:
  android_ios:
    name: Build, Upload, and Deploy Android & iOS apps to Firebase
    environment:
      node: "18"
      vars:
        GOOGLE_APPLICATION_CREDENTIALS: $CM_GOOGLE_CREDENTIALS  # Set in Codemagic's UI environment variables
    scripts:
      - name: Install dependencies
        script: |
          npm install --legacy-peer-deps
      - name: Sync Capacitor with Android
        script: |
          npx cap sync android
      - name: Build Android app
        script: |
          cd android
          ./gradlew assembleRelease
      - name: Authenticate with Firebase using Service Account
        script: |
          # Ensure GOOGLE_APPLICATION_CREDENTIALS is set
          if [ -z "$GOOGLE_APPLICATION_CREDENTIALS" ]; then
            echo "Error: GOOGLE_APPLICATION_CREDENTIALS environment variable is not set."
            exit 1
          fi

          # Authenticate using the service account JSON key
          export GOOGLE_APPLICATION_CREDENTIALS=$GOOGLE_APPLICATION_CREDENTIALS
          firebase --project wellmetrixionic appdistribution:distribute android/app/build/outputs/apk/release/app-release.apk --groups "Testers"
      - name: Upload Android to Firebase App Distribution
        script: |
          # Ensure GOOGLE_APPLICATION_CREDENTIALS is set
          if [ -z "$GOOGLE_APPLICATION_CREDENTIALS" ]; then
            echo "Error: GOOGLE_APPLICATION_CREDENTIALS environment variable is not set."
            exit 1
          fi

          # Check the APK output location
          if [ -f "android/app/build/outputs/apk/release/app-release.apk" ]; then
            APK_PATH="android/app/build/outputs/apk/release/app-release.apk"
          elif [ -f "android/app/build/outputs/apk/release/app-release-unsigned.apk" ]; then
            APK_PATH="android/app/build/outputs/apk/release/app-release-unsigned.apk"
          else
            echo "Error: APK file not found!"
            exit 1
          fi

          # Upload APK to Firebase App Distribution
          firebase --project wellmetrixionic appdistribution:distribute $APK_PATH \
            --app 1:627568308338:android:1859d19eacb078391592eb \
            --groups "Testers" || exit 1
      - name: Sync Capacitor with iOS
        script: |
          npx cap sync ios
      - name: Build iOS app
        script: |
          cd ios/App
          xcodebuild archive -workspace App.xcworkspace -scheme App -configuration Release -archivePath $PWD/App.xcarchive
          xcodebuild -exportArchive -archivePath $PWD/App.xcarchive -exportPath $PWD -exportOptionsPlist $PWD/exportOptions.plist
      - name: Upload iOS to Firebase App Distribution
        script: |
          # Ensure GOOGLE_APPLICATION_CREDENTIALS is set
          if [ -z "$GOOGLE_APPLICATION_CREDENTIALS" ]; then
            echo "Error: GOOGLE_APPLICATION_CREDENTIALS environment variable is not set."
            exit 1
          fi

          # Upload IPA to Firebase App Distribution
          firebase --project wellmetrixionic appdistribution:distribute ios/App/app-release.ipa \
            --app 1:627568308338:ios:0c393a2b0d8a503d1592eb \
            --groups "Testers" || exit 1
    artifacts:
      - android/app/build/outputs/apk/release/app-release.apk
      - ios/App/app-release.ipa
