// @ts-nocheck
import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { login } from '../../redux/slices/userSlice';
import {
    Box,
    IconButton,
    TextField,
    InputAdornment,
    Button,
    CircularProgress
} from '@material-ui/core';
import { ArrowRightTwoTone, Visibility, VisibilityOff } from '@material-ui/icons';
import { Alert } from '@mui/material';
import { useSpring, animated, config } from 'react-spring';
import OAuthService from './OauthService'; // Import OAuthService

import WelcomeDoor from '../../assets/WelcomeDoor.svg';
import './login.css';
import { getProfile } from '../../api/getProfile';

const Login: React.FC = () => {
    const dispatch = useDispatch();

    // State for input fields
    const [email, setEmail] = useState<string>('aws.test');
    const [password, setPassword] = useState<string>('Test1234!@');

    const [showPasswordText, setShowPasswordText] = useState<boolean>(false);
    const [loading, setLoading] = useState<boolean>(false);
    const [alert, setAlert] = useState<JSX.Element | null>(null);

    const springProp = useSpring({
        from: { transform: 'scale(1)' },
        to: { transform: 'scale(1)' },
        config: config.gentle,
    });

    const handleLogin = async () => {
        if (!email || !password) {
            setAlert(<Alert severity="error">Please enter email and password</Alert>);
            return;
        }

        setLoading(true);
        try {
            const oauthService = new OAuthService();
            const accessToken = await oauthService.authenticateUser(email, password);

            // Authenticate user and get profile
            const user = await getProfile(accessToken);

            console.log('Login successful, access token:', accessToken);

            if (user.userType === 'Patients') {
                setAlert(<Alert severity="error">Only providers are allowed to login</Alert>);
            } else {
                const stateObj = { ...user, accessToken };
                dispatch(login(stateObj)); // Save token in Redux state
                setAlert(<Alert severity="success">Login Successful!</Alert>);
            }
        } catch (error) {
            console.error('Login Error:', error);
            setAlert(<Alert severity="error">Invalid Credentials</Alert>);
        }
        setLoading(false);
    };

    return (
        <div id="loginWrapper">
            <div className="login">
                <div className="welcomeDoor">
                    <img src={WelcomeDoor} height={'300px'} width={'300px'} alt="Welcome" />
                </div>
                {alert}

                <div className="phoneOrEmailInput">
                    <TextField
                        variant="outlined"
                        fullWidth
                        placeholder="Enter your email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                    />
                </div>

                <div className="phoneOrEmailInput">
                    <TextField
                        variant="outlined"
                        fullWidth
                        type={showPasswordText ? 'text' : 'password'}
                        placeholder="Enter your password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        InputProps={{
                            endAdornment: (
                                <InputAdornment position="end">
                                    <IconButton
                                        onClick={() => setShowPasswordText(!showPasswordText)}
                                    >
                                        {showPasswordText ? <Visibility /> : <VisibilityOff />}
                                    </IconButton>
                                </InputAdornment>
                            ),
                        }}
                    />
                </div>

                {!loading ? (
                    <animated.div style={springProp} className={'submit'}>
                        <Button
                            onClick={handleLogin}
                            variant={'contained'}
                            size={'medium'}
                            color={'primary'}
                            style={{ color: '#ffffff' }}
                        >
                            Login <ArrowRightTwoTone fontSize={'large'} />
                        </Button>
                    </animated.div>
                ) : (
                    <animated.div style={springProp} className={'submit'}>
                        <CircularProgress color={'primary'} />
                    </animated.div>
                )}
            </div>
        </div>
    );
};

export default Login;
