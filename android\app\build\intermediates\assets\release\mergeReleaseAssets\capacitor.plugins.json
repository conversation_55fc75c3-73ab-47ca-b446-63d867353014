[{"pkg": "@capacitor-community/bluetooth-le", "classpath": "com.capacitorjs.community.plugins.bluetoothle.BluetoothLe"}, {"pkg": "@capacitor-community/http", "classpath": "com.getcapacitor.plugin.http.Http"}, {"pkg": "@capacitor-firebase/authentication", "classpath": "io.capawesome.capacitorjs.plugins.firebase.authentication.FirebaseAuthenticationPlugin"}, {"pkg": "@capacitor/app", "classpath": "com.capacitorjs.plugins.app.AppPlugin"}, {"pkg": "@capacitor/haptics", "classpath": "com.capacitorjs.plugins.haptics.HapticsPlugin"}, {"pkg": "@capacitor/keyboard", "classpath": "com.capacitorjs.plugins.keyboard.KeyboardPlugin"}, {"pkg": "@capacitor/status-bar", "classpath": "com.capacitorjs.plugins.statusbar.StatusBarPlugin"}]