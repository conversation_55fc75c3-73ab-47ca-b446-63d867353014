//@ts-nocheck
import React, { useState } from 'react';
// import {CssTransition} from 'react-transition-group';
import {
    Typo<PERSON>,
    Badge,
    Drawer,
    IconButton
} from '@material-ui/core';
import './Header.css';
import AlertIcon from '@material-ui/icons/NotificationsActiveOutlined';
import { useLocation } from 'react-router-dom';
import { SideBar } from '../Drawer';
// import { ReactComponent as Logo } from '../../assets/logo.svg';
import { Menu as MenuIcon } from '@mui/icons-material';
import { rawThemePallete } from '../../theme/theme';
import { useSelector } from 'react-redux';
import { selectUser, login , logout } from '../../redux/slices/userSlice';
import { selectDevice } from '../../redux/slices/deviceSlice';
import { BluetoothConnected, Bluetooth } from '@material-ui/icons';
import { useHistory } from 'react-router-dom';


const Header: React.FC = () => {
    const location = useLocation();
    const history = useHistory();
    const [menuOpen, setMenuOpen] = useState(false);
    const user = useSelector(selectUser).value;
    const deviceState = useSelector(selectDevice);

    // Toggle menu open/close
    const toggleMenu = () => {
        setMenuOpen(!menuOpen);
    };

    // Handle device connection click
    const handleDeviceConnection = () => {
        if (deviceState.value && deviceState.value.id && !deviceState.connected) {
            // Try to reconnect to stored device - navigate to device setup which will handle reconnection
            history.push('/devicesetup');
        } else if (deviceState.connected) {
            // Already connected, navigate to device setup to show device info
            history.push('/devicesetup');
        } else {
            // No stored device, navigate to device setup for new connection
            history.push('/devicesetup');
        }
    };
    return <>
        {location.pathname !== '/devicesetup' ? <div className={'headerWrapper'}>
            <div className={'headerContainer'}>
                <div style={{
                    boxShadow: `0 0px 50px ${rawThemePallete.palette.primary.main}, 0 0px 6px ${rawThemePallete.palette.primary.main}`,

                    height: "40px",
                    width: "40px",
                    padding: "5px",
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    borderRadius: "50%"
                }}>
                    {/* <Logo></Logo> */}
                </div>
                <div style={{ display: "flex", justifyContent: "center", alignItems: "center", flexDirection: "column", gap: "4px" }}>
                    <Typography style={{ fontSize: "17px", color: "#607D8B" }}>Welcome {user.name} !</Typography>

                    {/* Device Connection Status */}
                    {deviceState.connected && deviceState.value ? (
                        <div style={{
                            display: "flex",
                            alignItems: "center",
                            gap: "4px",
                            backgroundColor: "rgba(76, 175, 80, 0.1)",
                            padding: "4px 8px",
                            borderRadius: "12px",
                            cursor: "pointer"
                        }}
                        onClick={handleDeviceConnection}
                        >
                            <BluetoothConnected style={{ fontSize: "14px", color: "#4CAF50" }} />
                            <Typography style={{ fontSize: "12px", color: "#4CAF50" }}>
                                {deviceState.value.name || 'Device Connected'}
                            </Typography>
                        </div>
                    ) : (
                        <div style={{
                            display: "flex",
                            alignItems: "center",
                            gap: "4px",
                            backgroundColor: "rgba(96, 125, 139, 0.1)",
                            padding: "4px 8px",
                            borderRadius: "12px",
                            cursor: "pointer"
                        }}
                        onClick={handleDeviceConnection}
                        >
                            <Bluetooth style={{ fontSize: "14px", color: "#607D8B" }} />
                            <Typography style={{ fontSize: "12px", color: "#607D8B" }}>
                                Connect Device
                            </Typography>
                        </div>
                    )}
                </div>
                <div style={{ display: "flex", justifyContent: "flex-end", alignItems: "center" }}>
                    {/* <Badge badgeContent={4} color="primary">
                        <AlertIcon style={{ fill: "#F7B008", height: "30px", width: "30px" }} />
                    </Badge> */}
                    {/* Hamburger Button */}
                    <IconButton
                        edge="end"
                        color="#cdcdcd"
                        onClick={toggleMenu}
                        size='medium'
                        sx={{
                            backgroundColor: 'transparent', // Make the background transparent
                            color: '#000', // Set the icon color to white
                        }}
                    >
                        <MenuIcon />
                    </IconButton>
                </div>
            </div>
            <SideBar menuOpen={menuOpen} toggleMenu={() => toggleMenu()} />

        </div> : ""}
    </>
}

export default Header